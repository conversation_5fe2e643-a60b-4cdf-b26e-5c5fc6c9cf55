package cn.com.duiba.activity.center.api.dto.creditgame;

import cn.com.duiba.activity.center.api.dto.BaseActivityDto;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * 积分游戏实体传输类
 * <AUTHOR>
 * @since 2016-09-05
 */
public class CreditGameDto extends BaseActivityDto implements Serializable {

    private static final long serialVersionUID = 1L;
    
    public static final byte STATUS_OPEN = 1; // 开启且可见
    public static final byte STATUS_CLOSE_SHOW = 2; // 关闭且可见
    public static final byte STATUS_CLOSE = 3; // 关闭不可见
    
    public static final int SWITCHES_DIRECT = 0;        // 定向发放
    public static final int SWITCHES_DEV_BLACKLIST = 1; // 黑名单商品
    public static final int SWITCHES_ANTICHEAT_LIMIT_RULE = 3; // 开启直充类奖品防刷
    
    public static final byte Game_Type_WLD = 1;//胜负平
    public static final byte Game_Type_WL = 2;//胜负
    public static final byte Game_Type_Monopoly = 3;//大富翁
    
    public static final String Limit_Everyday = "everyday"; //每天
    public static final String Limit_Forever = "forever";   //永久

    //游戏类型定义(三个值不单独定义枚举)
    public static final String CREDIS_GAME_TYPE_MAP="MapCreditGame";//地图类
    /**
     *  胜负平类;
     */
    public static final String CREDIS_GAME_TYPE_VICTORY_OR_DEFEAT= "VictoryOrDefeatCreditGame";



    private Long id;

    /**
     * 标题
     */
    private String creditGameTitle;

    /**
     * 积分游戏类型(1:胜负平,2:胜负,3:大富翁)
     */
    private Byte creditGameType;

    /**
     * 游戏状态    1-开启 2-关闭可见 3-关闭不可见
     */
    private Byte creditGameStatus;

    /**
     * 兑吧预计消耗
     */
    private Long creditGameDuibaPrice;

    /**
     * 积分消耗价值
     */
    private Long creditGameCreditsPrice;

    /**
     * 开启的APP数
     */
    private Long creditGameAppCount;

    /**
     * 游戏限制类型 everyday:每天,forever:总共
     */
    private String creditGameDrawScope;

    /**
     * 游戏限制次数
     */
    private Long creditGameDrawLimit;

    /**
     * 免费限制类型 everyday:每天,forever:总共
     */
    private String creditGameFreeScope;

    /**
     * 免费限制次数
     */
    private Long creditGameFreeLimit;

    /**
     * 缩略图
     */
    private String creditGameSmallImage;

    /**
     * 白底缩略图
     */
    private String creditGameWhiteImage;

    /**
     * 首页banner图
     */
    private String creditGameBannerImage;

    /**
     * 推荐位横幅图
     */
    private String creditGameRecommendImage;
    
    /**
     * 图标
     */
    private String creditGameLogo;

    /**
     * 抽奖频次
     */
    private Long creditGameLotteryCount;

    /**
     * 标签
     */
    private String creditGameCustomTag;

    /**
     * 活动归类ID
     */
    private Long creditGameActivityCategoryId;

    /**
     * 结束时间
     */
    private Date creditGameAutoOffDate;

    /**
     * 开关项
     */
    private Long creditGameSwitches;

    /**
     * 活动规则
     */
    private String creditGameRule;

    /**
     * 免费参与活动规则
     */
    private String creditGameFreeRule;

    /**
     * 其他说明
     */
    private String creditGameDesc;

    /**
     * 扩展文本
     */
    private String creditGameExtDesc;

    /**
     * 奖项位置
     */
    private String creditGameAwardPosition;

    /**
     * 奖项配置
     */
    private String creditGameAwardConfig;

    /**
     * 概率/赔率配置
     */
    private String creditGameValveConfig;

    /**
     * 压注配置
     */
    private String creditGameBetConfig;

    /**
     * 规则脚本
     */
    private String creditGameRuleScript;

    /**
     * 删除标识0:未删除,1:已删除
     */
    private Byte deleted;

    /**
     * 记录创建时间
     */
    private Date gmtCreate;

    /**
     * 记录最后修改时间
     */
    private Date gmtModified;

    /**
     * 活动创建人
     */
    private String createPerson;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 普通活动：0；商业化活动1（default:0 兼容以前的数据）
     */
    private Byte activityActionType;



    // 不含在PO内的属性

    private String skinDom;

    private String skinBackgroudImageURL;

    /**
     * appId 获取游戏实例时从上架表拉取
     */
    private Long appId;
    /**
     * 游戏上架状态,拉取游戏实例时从上架表拉取
     */
    private Integer operationStatus;//上架状态

    /**
     * 游戏上架ID,拉取游戏实例时从上架表拉取
     */
    private Long operationId;//上架ID

    /**
     * 后来新增的
     */
    private String skinImgDom;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Byte getCreditGameType() {
        return creditGameType;
    }



    public void setCreditGameType(Byte creditGameType) {
        this.creditGameType = creditGameType;
    }

    public String getCreditGameTitle() {
        return creditGameTitle;
    }

    public void setCreditGameTitle(String creditGameTitle) {
        this.creditGameTitle = creditGameTitle == null ? null : creditGameTitle.trim();
    }

    public Byte getCreditGameStatus() {
        return creditGameStatus;
    }

    public void setCreditGameStatus(Byte creditGameStatus) {
        this.creditGameStatus = creditGameStatus;
    }

    public Long getCreditGameDuibaPrice() {
        return creditGameDuibaPrice;
    }

    public void setCreditGameDuibaPrice(Long creditGameDuibaPrice) {
        this.creditGameDuibaPrice = creditGameDuibaPrice;
    }

    public Long getCreditGameCreditsPrice() {
        return creditGameCreditsPrice;
    }

    public void setCreditGameCreditsPrice(Long creditGameCreditsPrice) {
        this.creditGameCreditsPrice = creditGameCreditsPrice;
    }

    public Long getCreditGameAppCount() {
        return creditGameAppCount;
    }

    public void setCreditGameAppCount(Long creditGameAppCount) {
        this.creditGameAppCount = creditGameAppCount;
    }

    public String getCreditGameDrawScope() {
        return creditGameDrawScope;
    }

    public void setCreditGameDrawScope(String creditGameDrawScope) {
        this.creditGameDrawScope = creditGameDrawScope == null ? null : creditGameDrawScope.trim();
    }

    public Long getCreditGameDrawLimit() {
        return creditGameDrawLimit;
    }

    public void setCreditGameDrawLimit(Long creditGameDrawLimit) {
        this.creditGameDrawLimit = creditGameDrawLimit;
    }

    public String getCreditGameFreeScope() {
        return creditGameFreeScope;
    }

    public void setCreditGameFreeScope(String creditGameFreeScope) {
        this.creditGameFreeScope = creditGameFreeScope == null ? null : creditGameFreeScope.trim();
    }

    public Long getCreditGameFreeLimit() {
        return creditGameFreeLimit;
    }

    public void setCreditGameFreeLimit(Long creditGameFreeLimit) {
        this.creditGameFreeLimit = creditGameFreeLimit;
    }

    public String getCreditGameSmallImage() {
        return creditGameSmallImage;
    }

    public void setCreditGameSmallImage(String creditGameSmallImage) {
        this.creditGameSmallImage = creditGameSmallImage == null ? null : creditGameSmallImage.trim();
    }

    public String getCreditGameWhiteImage() {
        return creditGameWhiteImage;
    }

    public void setCreditGameWhiteImage(String creditGameWhiteImage) {
        this.creditGameWhiteImage = creditGameWhiteImage == null ? null : creditGameWhiteImage.trim();
    }

    public String getCreditGameBannerImage() {
        return creditGameBannerImage;
    }

    public void setCreditGameBannerImage(String creditGameBannerImage) {
        this.creditGameBannerImage = creditGameBannerImage == null ? null : creditGameBannerImage.trim();
    }

    public String getCreditGameRecommendImage() {
        return creditGameRecommendImage;
    }

    public void setCreditGameRecommendImage(String creditGameRecommendImage) {
        this.creditGameRecommendImage = creditGameRecommendImage == null ? null : creditGameRecommendImage.trim();
    }
    
    public String getCreditGameLogo() {
        return creditGameLogo;
    }
    
    public void setCreditGameLogo(String creditGameLogo) {
        this.creditGameLogo = creditGameLogo == null ? null : creditGameLogo.trim();
    }

    public Long getCreditGameLotteryCount() {
        return creditGameLotteryCount;
    }

    public void setCreditGameLotteryCount(Long creditGameLotteryCount) {
        this.creditGameLotteryCount = creditGameLotteryCount;
    }

    public String getCreditGameCustomTag() {
        return creditGameCustomTag;
    }

    public void setCreditGameCustomTag(String creditGameCustomTag) {
        this.creditGameCustomTag = creditGameCustomTag == null ? null : creditGameCustomTag.trim();
    }

    public Long getCreditGameActivityCategoryId() {
        return creditGameActivityCategoryId;
    }

    public void setCreditGameActivityCategoryId(Long creditGameActivityCategoryId) {
        this.creditGameActivityCategoryId = creditGameActivityCategoryId;
    }

    public Date getCreditGameAutoOffDate() {
        return creditGameAutoOffDate;
    }

    public void setCreditGameAutoOffDate(Date creditGameAutoOffDate) {
        this.creditGameAutoOffDate = creditGameAutoOffDate;
    }

    public Long getCreditGameSwitches() {
        return creditGameSwitches;
    }

    public void setCreditGameSwitches(Long creditGameSwitches) {
        this.creditGameSwitches = creditGameSwitches;
    }


    public String getCreditGameRule() {
        return creditGameRule;
    }

    public void setCreditGameRule(String creditGameRule) {
        this.creditGameRule = creditGameRule == null ? null : creditGameRule.trim();
    }

    public String getCreditGameFreeRule() {
        return creditGameFreeRule;
    }

    public void setCreditGameFreeRule(String creditGameFreeRule) {
        this.creditGameFreeRule = creditGameFreeRule == null ? null : creditGameFreeRule.trim();
    }

    public String getCreditGameDesc() {
        return creditGameDesc;
    }

    public void setCreditGameDesc(String creditGameDesc) {
        this.creditGameDesc = creditGameDesc == null ? null : creditGameDesc.trim();
    }

    public String getCreditGameExtDesc() {
        return creditGameExtDesc;
    }

    public void setCreditGameExtDesc(String creditGameExtDesc) {
        this.creditGameExtDesc = creditGameExtDesc == null ? null : creditGameExtDesc.trim();
    }

    public String getCreditGameAwardPosition() {
        return creditGameAwardPosition;
    }

    public void setCreditGameAwardPosition(String creditGameAwardPosition) {
        this.creditGameAwardPosition = creditGameAwardPosition == null ? null : creditGameAwardPosition.trim();
    }

    public String getCreditGameAwardConfig() {
        return creditGameAwardConfig;
    }

    public void setCreditGameAwardConfig(String creditGameAwardConfig) {
        this.creditGameAwardConfig = creditGameAwardConfig == null ? null : creditGameAwardConfig.trim();
    }

    public String getCreditGameValveConfig() {
        return creditGameValveConfig;
    }

    public void setCreditGameValveConfig(String creditGameValveConfig) {
        this.creditGameValveConfig = creditGameValveConfig == null ? null : creditGameValveConfig.trim();
    }

    public String getCreditGameBetConfig() {
        return creditGameBetConfig;
    }

    public void setCreditGameBetConfig(String creditGameBetConfig) {
        this.creditGameBetConfig = creditGameBetConfig == null ? null : creditGameBetConfig.trim();
    }

    public String getCreditGameRuleScript() {
        return creditGameRuleScript;
    }

    public void setCreditGameRuleScript(String creditGameRuleScript) {
        this.creditGameRuleScript = creditGameRuleScript == null ? null : creditGameRuleScript.trim();
    }

    public Byte getDeleted(){
        return this.deleted;
    }
    
    public void setDeleted(Byte deleted){
        this.deleted=deleted;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getCreatePerson() {
        return createPerson;
    }

    public void setCreatePerson(String createPerson) {
        this.createPerson = createPerson == null ? null : createPerson.trim();
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks == null ? null : remarks.trim();
    }

    public Byte getActivityActionType() {
        return activityActionType;
    }

    public void setActivityActionType(Byte activityActionType) {
        this.activityActionType = activityActionType;
    }

    public String getSkinDom(){
        return this.skinDom;
    }
    public void setSkinDom(String skinDmo){
        this.skinDom=skinDmo;
    }
    public String getSkinBackgroudImageURL(){
        return this.skinBackgroudImageURL;
    }
    public void setSkinBackgroudImageURL(String skinBackgroudImageURL){
        this.skinBackgroudImageURL=skinBackgroudImageURL;
    }

    public Long getAppId(){
        return this.appId;
    }

    public void setAppId(Long appId){
        this.appId=appId;
    }

    public Integer getOperationStatus(){
        return this.operationStatus;
    }

    public void setOperationStatus(Integer operationStatus){
        this.operationStatus=operationStatus;
    }

    public String getSkinImgDom(){
        return this.skinImgDom;
    }

    public void setSkinImgDom(String skinImgDom){
        this.skinImgDom=skinImgDom;
    }

    public Long getOperationId(){
        return this.operationId;
    }

    public void setOperationId(Long operationId){
        this.operationId=operationId;
    }

    /**
     * 
     * openSwitch:(这里用一句话描述这个方法的作用). <br/>
     * 
     * @param s
     * @since JDK 1.6
     */
    public void openSwitch(int s) {
        int v = 1 << s;
        creditGameSwitches = creditGameSwitches | v;
    }

    /**
     * 
     * closeSwitch:(这里用一句话描述这个方法的作用). <br/>
     * 
     * @param s
     * @since JDK 1.6
     */
    public void closeSwitch(int s) {
        int v = 1 << s;
        v = ~v;
        creditGameSwitches = creditGameSwitches & v;
    }

    /**
     * 
     * isOpenSwitch:(这里用一句话描述这个方法的作用). <br/>
     * @param s
     * @return
     * @since JDK 1.6
     */
    public boolean isOpenSwitch(int s) {
        int v = 1 << s;
        long ret = creditGameSwitches & v;
        return ret != 0;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}