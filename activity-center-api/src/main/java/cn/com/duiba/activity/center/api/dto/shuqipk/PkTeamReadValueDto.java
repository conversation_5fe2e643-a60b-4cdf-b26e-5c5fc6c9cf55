package cn.com.duiba.activity.center.api.dto.shuqipk;

import java.io.Serializable;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/1/2 10:10
 * @description: 战队阅读值
 */
public class PkTeamReadValueDto implements Serializable {

	private static final long serialVersionUID = 5573624080583649819L;
	/**
	 * 战队id
	 */
	private Long teamId;
	/**
	 * 战队历史总阅读值
	 */
	private Long totalReadValue;
	/**
	 * 敌方战队id
	 */
	private Long enemyTeamId;
	/**
	 * 敌方战队历史总阅读值
	 */
	private Long enemyTotalReadValue;
	/**
	 * 当日pk记录
	 */
	private PkTeamRecordDto todayPkRecord;

	public Long getTeamId() {
		return teamId;
	}

	public void setTeamId(Long teamId) {
		this.teamId = teamId;
	}

	public Long getTotalReadValue() {
		return totalReadValue;
	}

	public void setTotalReadValue(Long totalReadValue) {
		this.totalReadValue = totalReadValue;
	}

	public Long getEnemyTeamId() {
		return enemyTeamId;
	}

	public void setEnemyTeamId(Long enemyTeamId) {
		this.enemyTeamId = enemyTeamId;
	}

	public Long getEnemyTotalReadValue() {
		return enemyTotalReadValue;
	}

	public void setEnemyTotalReadValue(Long enemyTotalReadValue) {
		this.enemyTotalReadValue = enemyTotalReadValue;
	}

	public PkTeamRecordDto getTodayPkRecord() {
		return todayPkRecord;
	}

	public void setTodayPkRecord(PkTeamRecordDto todayPkRecord) {
		this.todayPkRecord = todayPkRecord;
	}
}
