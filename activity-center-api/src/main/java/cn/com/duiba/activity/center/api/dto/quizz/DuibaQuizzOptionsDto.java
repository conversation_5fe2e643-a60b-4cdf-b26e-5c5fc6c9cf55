package cn.com.duiba.activity.center.api.dto.quizz;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * ClassName: DuibaQuizzOptionsDto <br/>
 * date: 2016年12月1日 上午10:31:29 <br/>
 *
 * @version 
 * @since JDK 1.6
 */
public class DuibaQuizzOptionsDto implements Serializable {

    private static final long serialVersionUID = 6827488720533607667L;
    public static final String TypeThankYou = "thanks"; // 谢谢参与
    
    private Long              id;
    private Long              duibaQuizzId;
    private Long              itemId;
    private Integer           payload;
    private String            description;
    private String            logo;
    private String            name;
    private String            prizeType;
    private Long              facePrice;
    private Integer           hidden;
    private String            rate;
    private Integer           limitCount;
    private Integer           minComein;
    private String            quizzResult;
    private Boolean           deleted;
    private Date              gmtCreate;
    private Date              gmtModified;

    /* 奖项个数 */
    private Integer           optionCount;
    /* 新增奖项个数 */
    private Integer           newOptionCount;

    /**
     * 
     * Creates a new instance of DuibaQuizzOptionsDto.
     *
     */
    public DuibaQuizzOptionsDto() {
    }

    /**
     * 
     * Creates a new instance of DuibaQuizzOptionsDto.
     *
     * @param id
     */
    public DuibaQuizzOptionsDto(Long id) {
        this.id = id;
    }
    
    public String getRate() {
        return rate;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    public Integer getLimitCount() {
        return limitCount;
    }

    public void setLimitCount(Integer limitCount) {
        this.limitCount = limitCount;
    }

    public Integer getMinComein() {
        return minComein;
    }

    public void setMinComein(Integer minComein) {
        this.minComein = minComein;
    }

    public Long getDuibaQuizzId() {
        return duibaQuizzId;
    }

    public void setDuibaQuizzId(Long duibaQuizzId) {
        this.duibaQuizzId = duibaQuizzId;
    }

    public String getQuizzResult() {
        return quizzResult;
    }

    public void setQuizzResult(String quizzResult) {
        this.quizzResult = quizzResult;
    }

    public Integer getOptionCount() {
        return optionCount;
    }

    public void setOptionCount(Integer optionCount) {
        this.optionCount = optionCount;
    }

    public Integer getNewOptionCount() {
        return newOptionCount;
    }

    public void setNewOptionCount(Integer newOptionCount) {
        this.newOptionCount = newOptionCount;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Long getFacePrice() {
        return facePrice;
    }

    public void setFacePrice(Long facePrice) {
        this.facePrice = facePrice;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Integer getPayload() {
        return payload;
    }

    public void setPayload(Integer payload) {
        this.payload = payload;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPrizeType() {
        return prizeType;
    }

    public void setPrizeType(String prizeType) {
        this.prizeType = prizeType;
    }

    public Integer getHidden() {
        return hidden;
    }

    public void setHidden(Integer hidden) {
        this.hidden = hidden;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }
}
