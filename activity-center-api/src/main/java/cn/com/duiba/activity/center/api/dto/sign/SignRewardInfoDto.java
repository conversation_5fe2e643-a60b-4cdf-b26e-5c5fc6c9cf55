package cn.com.duiba.activity.center.api.dto.sign;

import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;

/**
 * Created by xiaoxuda on 2017/12/29.
 */
public class SignRewardInfoDto implements Serializable{
    private static final long serialVersionUID = -3152619433276820318L;

    /**
     * 今日奖励的总积分
     */
    private Integer credits = 0;
    /**
     * 今日奖励的积分明细
     */
    private JSONObject creditsRemark = new JSONObject();
    /**
     * 今日奖励的总活动次数
     */
    private Integer activityCount = 0;
    /**
     * 今日奖励的活动次数明细
     */
    private JSONObject countRemark = new JSONObject();

    /**
     * 明日签到可得积分
     */
    private Integer creditsTomorrow;

    /**
     * 明日签到可得抽奖次数
     */
    private Integer activityCountTomorrow;

    public Integer getCredits() {
        return credits;
    }

    public void setCredits(Integer credits) {
        this.credits = credits;
    }

    public JSONObject getCreditsRemark() {
        return creditsRemark;
    }

    public void setCreditsRemark(JSONObject creditsRemark) {
        this.creditsRemark = creditsRemark;
    }

    public Integer getActivityCount() {
        return activityCount;
    }

    public void setActivityCount(Integer activityCount) {
        this.activityCount = activityCount;
    }

    public JSONObject getCountRemark() {
        return countRemark;
    }

    public void setCountRemark(JSONObject countRemark) {
        this.countRemark = countRemark;
    }

    public Integer getCreditsTomorrow() {
        return creditsTomorrow;
    }

    public void setCreditsTomorrow(Integer creditsTomorrow) {
        this.creditsTomorrow = creditsTomorrow;
    }

    public Integer getActivityCountTomorrow() {
        return activityCountTomorrow;
    }

    public void setActivityCountTomorrow(Integer activityCountTomorrow) {
        this.activityCountTomorrow = activityCountTomorrow;
    }
}
