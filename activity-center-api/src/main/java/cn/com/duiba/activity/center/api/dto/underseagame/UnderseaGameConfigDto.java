package cn.com.duiba.activity.center.api.dto.underseagame;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 海底游戏机配置
 * @Author: xuwei
 * @Date: 2020/07/16
 */
public class UnderseaGameConfigDto implements Serializable {

    private static final long serialVersionUID = 6912226019567304796L;

    /**
     * 当奖励方式为『通过三关后给到抽奖机会』时，默认的关卡编号
     */
    public static final int ROUND_DEFAULT = 0;

    /**
     * 最大关卡编号
     */
    public static final int MAX_ROUND_INDEX = 3;

    /**
     * 奖励方式-通过三关后给到抽奖机会
     */
    public static final int REWARD_WAY_GLOBAL = 1;
    /**
     * 奖励方式-每关通过后给到抽奖机会
     */
    public static final int REWARD_WAY_ROUND = 2;

    /**
     * 抽奖工具类型-拆盲盒
     */
    public static final int REWARD_TOOL_TYPE_BOX = 1;
    /**
     * 抽奖工具类型-摇奖机
     */
    public static final int REWARD_TOOL_TYPE_ERNIE = 2;
    /**
     * 抽奖工具类型-大转盘
     */
    public static final int REWARD_TOOL_TYPE_WHEEL = 3;


    /**
     * 主键
     */
    private Long id;

    /**
     * appID
     */
    private Long appId;

    /**
     * 入库活动ID
     */
    private Long opId;

    /**
     * 标题
     */
    private String title;

    /**
     * 活动规则
     */
    private String activityRule;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 缩略图
     */
    private String smallImage;

    /**
     * banner图
     */
    private String bannerImage;

    /**
     * 消耗积分
     */
    private Integer credits;

    /**
     * 限制次数
     */
    private Integer limitTimes;

    /**
     * 限制纬度 1:每天 2:永久
     */
    private Integer limitScope;

    /**
     * 免费次数
     */
    private Integer freeTimes;

    /**
     * 免费纬度 1:每天 2:永久
     */
    private Integer freeScope;

    /**
     * 是否开启复活 0:关闭 1:开启
     */
    private Boolean enableRevive;

    /**
     * 复活所需积分
     */
    private Integer reviveCredits;

    /**
     * 复活限制次数
     */
    private Integer reviveLimitTimes;

    /**
     * 奖励方式 1:通过三关后给到抽奖机会  2:每关通过后给到抽奖机会
     */
    private Integer rewardWay;

    /**
     * 抽奖工具类型，每关按逗号分隔 1:拆盲盒 2:摇奖机  3:大转盘
     */
    private String rewardToolType;

    /**
     * 是否已发布 0:未发布  1:已发布
     */
    private Boolean publish;

    /**
     * 是否删除 0:未删除 1:删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModified;

    /**
     * 界面配置
     */
    private String interfaceConfig;

    /**
     * 奖品列表
     */
    private List<UnderseaGamePrizeConfigDto> prizes;

    /**
     * 活动是否开启直冲防刷
     */
    private boolean openBrush;

    /**
     * 薪班班定制：微信分享配置
     */
    private String wxShareConfig;

    public boolean isOpenBrush() {
        return openBrush;
    }

    public void setOpenBrush(boolean openBrush) {
        this.openBrush = openBrush;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getOpId() {
        return opId;
    }

    public void setOpId(Long opId) {
        this.opId = opId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    public String getActivityRule() {
        return activityRule;
    }

    public void setActivityRule(String activityRule) {
        this.activityRule = activityRule == null ? null : activityRule.trim();
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getSmallImage() {
        return smallImage;
    }

    public void setSmallImage(String smallImage) {
        this.smallImage = smallImage;
    }

    public String getBannerImage() {
        return bannerImage;
    }

    public void setBannerImage(String bannerImage) {
        this.bannerImage = bannerImage;
    }

    public Integer getCredits() {
        return credits;
    }

    public void setCredits(Integer credits) {
        this.credits = credits;
    }

    public Integer getLimitTimes() {
        return limitTimes;
    }

    public void setLimitTimes(Integer limitTimes) {
        this.limitTimes = limitTimes;
    }

    public Integer getLimitScope() {
        return limitScope;
    }

    public void setLimitScope(Integer limitScope) {
        this.limitScope = limitScope;
    }

    public Integer getFreeTimes() {
        return freeTimes;
    }

    public void setFreeTimes(Integer freeTimes) {
        this.freeTimes = freeTimes;
    }

    public Integer getFreeScope() {
        return freeScope;
    }

    public void setFreeScope(Integer freeScope) {
        this.freeScope = freeScope;
    }

    public Boolean getEnableRevive() {
        return enableRevive;
    }

    public void setEnableRevive(Boolean enableRevive) {
        this.enableRevive = enableRevive;
    }

    public Integer getReviveCredits() {
        return reviveCredits;
    }

    public void setReviveCredits(Integer reviveCredits) {
        this.reviveCredits = reviveCredits;
    }

    public Integer getReviveLimitTimes() {
        return reviveLimitTimes;
    }

    public void setReviveLimitTimes(Integer reviveLimitTimes) {
        this.reviveLimitTimes = reviveLimitTimes;
    }

    public Integer getRewardWay() {
        return rewardWay;
    }

    public void setRewardWay(Integer rewardWay) {
        this.rewardWay = rewardWay;
    }

    public String getRewardToolType() {
        return rewardToolType;
    }

    public void setRewardToolType(String rewardToolType) {
        this.rewardToolType = rewardToolType == null ? null : rewardToolType.trim();
    }

    public Boolean getPublish() {
        return publish;
    }

    public void setPublish(Boolean publish) {
        this.publish = publish;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getInterfaceConfig() {
        return interfaceConfig;
    }

    public void setInterfaceConfig(String interfaceConfig) {
        this.interfaceConfig = interfaceConfig == null ? null : interfaceConfig.trim();
    }

    public List<UnderseaGamePrizeConfigDto> getPrizes() {
        return prizes;
    }

    public void setPrizes(List<UnderseaGamePrizeConfigDto> prizes) {
        this.prizes = prizes;
    }

    public String getWxShareConfig() {
        return wxShareConfig;
    }

    public void setWxShareConfig(String wxShareConfig) {
        this.wxShareConfig = wxShareConfig;
    }
}