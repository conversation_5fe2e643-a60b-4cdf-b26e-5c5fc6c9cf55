package cn.com.duiba.activity.center.api.remoteservice.bet;

import cn.com.duiba.activity.center.api.dto.bet.BetConfigDto;
import cn.com.duiba.activity.center.api.enums.AttributionTypeEnum;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/05/07
 */
@AdvancedFeignClient
public interface RemoteBetConfigService {
    /**
     * 通过id获取活动配置
     * @param id betId
     * @return 活动配置
     * @throws BizException bizException
     */
    BetConfigDto findById(Long id) throws BizException;

    /**
     * 根据状态、瓜分类型和结束时间范围查询活动列表
     * @param configStatus
     * @param bonusType
     * @param startTime
     * @param endTime
     * @return List<BetConfigDto>
     */
    @Deprecated
    List<BetConfigDto> getListByStatusAndEndTime(Integer configStatus, Integer bonusType, Date startTime, Date endTime);

    /**
     * 根据状态、瓜分类型和结束时间范围查询某个appid下活动列表
     * @param appId
     * @param configStatus
     * @param bonusType
     * @param startTime
     * @param endTime
     * @return List<BetConfigDto>
     */
    List<BetConfigDto> getListByAppAndStatusAndEndTime(Long appId, Integer configStatus, Integer bonusType, Date startTime, Date endTime);

    /**
     * 查询正在进行中的pk赛
     * @param configStatus
     * @param endTime
     * @param attributionTypeEnum
     * @param appId
     * @return
     */
    List<BetConfigDto> selectListByAppIdAndEndTime(Integer configStatus, Date endTime, AttributionTypeEnum attributionTypeEnum, Long appId);

    /**
     * 查询正在进行中的pk赛,没有option（选项信息）
     * @param configStatus
     * @param endTime
     * @param attributionTypeEnum
     * @param appId
     * @return
     */
    List<BetConfigDto> selectSimpleListByAppIdAndEndTime(Integer configStatus, Date endTime, AttributionTypeEnum attributionTypeEnum, Long appId);


    /**
     * 只更新截止时间
     * @param betConfigDto
     * @return List<BetConfigEntity>
     */
    Integer updateEndTime(BetConfigDto betConfigDto) throws BizException;

    /**
     * 返回app下的押注题目数量（仅开发者押注适用）
     * @param attributionTypeEnum
     * @param appId
     * @return
     */
    Integer countBetConfigByAppIdAndType(AttributionTypeEnum attributionTypeEnum, Long appId);

}
