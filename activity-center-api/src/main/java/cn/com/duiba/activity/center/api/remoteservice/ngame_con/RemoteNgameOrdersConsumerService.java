package cn.com.duiba.activity.center.api.remoteservice.ngame_con;

import cn.com.duiba.activity.center.api.dto.ngame.NgameOrdersDto;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;

import java.util.Date;
import java.util.List;

/**
 * ngame_orders按consumer分表
 */
@AdvancedFeignClient
public interface RemoteNgameOrdersConsumerService {

    /**
     * 
     * find:(这里用一句话描述这个方法的作用). <br/>
     * 
     * @param consumerId
     * @param gameOrderId
     * @return
     * @since JDK 1.6
     */
	 NgameOrdersDto find(Long consumerId, Long gameOrderId);
	
	/**
	 * 
	 * findConsumerFreeNumber:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param operatingActivityId
	 * @return
	 * @since JDK 1.6
	 */
	 Integer findConsumerFreeNumber(Long consumerId, Long operatingActivityId);
	/**
	 * 
	 * findConsumerFreeNumberByDate:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param operatingActivityId
	 * @param start
	 * @param end
	 * @return
	 * @since JDK 1.6
	 */
	 Integer findConsumerFreeNumberByDate(Long consumerId, Long operatingActivityId, Date start, Date end);

	/**
	 * 
	 * findConsumerLimitNumber:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param operatingActivityId
	 * @return
	 * @since JDK 1.6
	 */
	 Integer findConsumerLimitNumber(Long consumerId, Long operatingActivityId);

	/**
	 * 
	 * findConsumerLimitNumberByDate:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param operatingActivityId
	 * @param start
	 * @param end
	 * @return
	 * @since JDK 1.6
	 */
	 Integer findConsumerLimitNumberByDate(Long consumerId, Long operatingActivityId, Date start, Date end);
	
	/**
	 * 
	 * findByIds:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param ids
	 * @return
	 * @since JDK 1.6
	 */
	 List<NgameOrdersDto> findByIds(Long consumerId, List<Long> ids);

	/**
	 * 
	 * insert:(这里用一句话描述这个方法的作用). <br/>
	 * 添加活动工具订单
	 * @param ngameOrdersDto
	 * @return
	 * @since JDK 1.6
	 */
	 NgameOrdersDto insert(NgameOrdersDto ngameOrdersDto);

	/**
	 * 
	 * updateDeveloperBizId:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param id
	 * @param bizId
	 * @return
	 * @since JDK 1.6
	 */
	 int updateDeveloperBizId(Long consumerId, long id, String bizId);

	/**
	 * 
	 * updateMainOrderId:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param id
	 * @param mainOrderId
	 * @param mainOrderNum
	 * @return
	 * @since JDK 1.6
	 */
	 int updateMainOrderId(Long consumerId, long id, Long mainOrderId, String mainOrderNum);

	//from NgameOrdersStatusChangeDAO in credits

	/**
	 * 
	 * updateStatusToConsumeSuccess:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param gameOrderId
	 * @return
	 * @since JDK 1.6
	 */
	 int updateStatusToConsumeSuccess(Long consumerId, Long gameOrderId);

	/**
	 * 
	 * updateStatusToSuccess:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param gameOrderId
	 * @param gameOrderExtraId
	 * @return
	 * @since JDK 1.6
	 */
	 int updateStatusToSuccess(Long consumerId, Long gameOrderId, Long gameOrderExtraId);

	/**
	 * 
	 * updateStatusToFail:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param gameOrderId
	 * @param error4admin
	 * @param error4developer
	 * @param error4consumer
	 * @return
	 * @since JDK 1.6
	 */
	 int updateStatusToFail(Long consumerId, Long gameOrderId, String error4admin, String error4developer, String error4consumer);

	/**
	 * 
	 * updateExchangeStatusToWaitOpen:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param gameOrderId
	 * @param gameDataStr
	 * @return
	 * @since JDK 1.6
	 */
	 int updateExchangeStatusToWaitOpen(Long consumerId, Long gameOrderId, String gameDataStr);

	/**
	 * 
	 * updateExchangeStatusToWaitOpenAndExtraId:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param gameOrderId
	 * @param gameDataStr
	 * @param gameOrderExtraId
	 * @return
	 * @since JDK 1.6
	 */
	 int updateExchangeStatusToWaitOpenAndExtraId(Long consumerId, Long gameOrderId, String gameDataStr, Long gameOrderExtraId);

	/**
	 * 
	 * updateExchangeStatusToWait:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param gameOrderId
	 * @param gameDataStr
	 * @param itemId
	 * @param appItemId
	 * @param prizeId
	 * @param prizeType
	 * @param prizeName
	 * @param prizeFacePrice
	 * @param couponId
	 * @return
	 * @since JDK 1.6
	 */
	 int updateExchangeStatusToWait(Long consumerId, Long gameOrderId, String gameDataStr, Long itemId, Long appItemId, Long prizeId, String prizeType, String prizeName, String prizeFacePrice, Long couponId);

	/**
	 * 
	 * updateExchangeStatusToOverdue:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param gameOrderId
	 * @param error4admin
	 * @param error4developer
	 * @param error4consumer
	 * @return
	 * @since JDK 1.6
	 */
	 int updateExchangeStatusToOverdue(Long gameOrderId, String error4admin, String error4developer, String error4consumer);

	/**
	 * 
	 * updateExchangeStatusToFail:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param gameOrderId
	 * @param error4admin
	 * @param error4developer
	 * @param error4consumer
	 * @return
	 * @since JDK 1.6
	 */
	 int updateExchangeStatusToFail(Long consumerId, Long gameOrderId, String error4admin, String error4developer, String error4consumer);

	/**
	 * 
	 * doTakePrize:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param gameOrderId
	 * @return
	 * @since JDK 1.6
	 */
	 int doTakePrize(Long consumerId, Long gameOrderId);

	/**
	 * 
	 * rollbackTakePrize:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param gameOrderId
	 * @return
	 * @since JDK 1.6
	 */
	 int rollbackTakePrize(Long consumerId, Long gameOrderId);

	/**
	 * 
	 * updateManualOpenPrizeExchangeStatusToWait:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param gameOrderId
	 * @param itemId
	 * @param appItemId
	 * @param prizeId
	 * @param prizeType
	 * @param prizeName
	 * @param prizeFacePrice
	 * @param couponId
	 * @return
	 * @since JDK 1.6
	 */
	 int updateManualOpenPrizeExchangeStatusToWait(Long consumerId, Long gameOrderId, Long itemId, Long appItemId, Long prizeId, String prizeType, String prizeName, String prizeFacePrice, Long couponId);

	/**
	 * 
	 * updateExchangeStatusToWaitOpenAndExtraIdForLuck:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param consumerId
	 * @param gameOrderId
	 * @param gameOrderExtraId
	 * @return
	 * @since JDK 1.6
	 */
	 int updateExchangeStatusToWaitOpenAndExtraIdForLuck(Long consumerId, Long gameOrderId, Long gameOrderExtraId);

	/**
	 * 更新游戏子订单分数字段
	 */
	int updateScore(Long consumerId, Long orderId, Long score);

	/**
	 * 虚拟商品自动领奖
	 * @param consumerId
	 * @param gameOrderId
	 * @return
	 */
	int doTakeVirtualPrize(Long consumerId, Long gameOrderId);


}
