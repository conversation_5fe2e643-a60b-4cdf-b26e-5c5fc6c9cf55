package cn.com.duiba.activity.center.api.enums;

import cn.com.duiba.activity.center.api.exception.ActivityCenterException;

import java.util.HashMap;
import java.util.Map;

/**
 * 签到体系签到活动类型
 * Created by xiaoxuda on 2017/4/20.
 */
public enum SignActivityTypeEnum {
    SIGN_CONSTELLATION(1,"签到星座"),
    SIGN_CALENDAR(2,"日历签到"),
    SIGN_TEMPLATE(3,"模板配置"),
    SIGN_PET(4,"签到养成");

    private Integer code;
    private String desc;

    private static Map<Integer,SignActivityTypeEnum> typeMap = new HashMap<>();
    static{
        for(SignActivityTypeEnum typeEnum:values()){
            typeMap.put(typeEnum.getCode(),typeEnum);
        }
    }

    SignActivityTypeEnum(Integer code,String desc){
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取类型，获取失败则抛出异常
     * @param code
     * @return
     */
    public static SignActivityTypeEnum getSignTypeByCode(Integer code){
        if(code == null || !typeMap.containsKey(code)){
            throw new ActivityCenterException("不支持的签到体系活动类型,code="+code);
        }
        return typeMap.get(code);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
