package cn.com.duiba.activity.center.api.dto.scraperedpacket;

import cn.com.duiba.activity.center.api.enums.ScrapeRedPacketNodeEnum;

import java.io.Serializable;
import java.util.Date;

/**
 * 微信刮红包好友帮刮记录
 */
public class ScrapeRedpacketRecordDto implements Serializable {

    private static final long serialVersionUID = -4881523479137029788L;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long consumerId;

    /**
     * 红包id
     */
    private Long redPacketId;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 渠道类型，1：app；2：微信公众号；3：微信小程序
     */
    private Integer fromType;

    /**
     * 刮取所处节点
     * @see ScrapeRedPacketNodeEnum
     */
    private Integer scrapeNode;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户刮出占比
     */
    private String scrapeRate;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getConsumerId() {
        return consumerId;
    }

    public void setConsumerId(Long consumerId) {
        this.consumerId = consumerId;
    }

    public Long getRedPacketId() {
        return redPacketId;
    }

    public void setRedPacketId(Long redPacketId) {
        this.redPacketId = redPacketId;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public Integer getFromType() {
        return fromType;
    }

    public void setFromType(Integer fromType) {
        this.fromType = fromType;
    }

    public Integer getScrapeNode() {
        return scrapeNode;
    }

    public void setScrapeNode(Integer scrapeNode) {
        this.scrapeNode = scrapeNode;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getScrapeRate() {
        return scrapeRate;
    }

    public void setScrapeRate(String scrapeRate) {
        this.scrapeRate = scrapeRate;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    @Override
    public String toString() {
        return "ScrapeRedpacketRecordDto{" +
                "id=" + id +
                ", consumerId=" + consumerId +
                ", redPacketId=" + redPacketId +
                ", activityId=" + activityId +
                ", fromType=" + fromType +
                ", scrapeNode=" + scrapeNode +
                ", avatar='" + avatar + '\'' +
                ", nickname='" + nickname + '\'' +
                ", scrapeRate='" + scrapeRate + '\'' +
                ", gmtCreate=" + gmtCreate +
                ", gmtModified=" + gmtModified +
                '}';
    }
}
