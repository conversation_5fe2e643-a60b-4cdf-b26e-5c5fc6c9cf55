package cn.com.duiba.activity.center.api.remoteservice.direct;

import cn.com.duiba.activity.center.api.dto.direct.AppQuantityDto;
import cn.com.duiba.activity.center.api.dto.direct.DuibaActivityAppSpecifyNewDto;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.wolf.dubbo.DubboResult;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * Created by suyuan<PERSON> on 16/7/19.
 */
@AdvancedFeignClient
public interface RemoteDuibaActivityAppSpecifyNewService {


    /**
     * 此方法在WEB端存在大量的循环调用(请相关部门优化)
     * 已经提供service层的优化方法:DuibaActivityAppSpecifyNewService.batchFindIsSpecifyActivityIds
     *
     * @param activityId
     * @param appId
     * @param activityType
     * @return
     */
    DubboResult<DuibaActivityAppSpecifyNewDto> findAppSpecifyByActivityIdAndAppIdAndActivityType(Long activityId, Long appId, String activityType);

    /**
     * @param activityId
     * @param type
     * @return
     */

    DubboResult<List<DuibaActivityAppSpecifyNewDto>> findAppSpecifyByActivityIdAndActivityType(Long activityId, String type);

    /**
     * @param specifyBto
     * @return id
     */

    DubboResult<Long> insertAppSpecify(DuibaActivityAppSpecifyNewDto specifyBto);

    /**
     * @param id
     * @return 0 1
     */
    DubboResult<Boolean> deleteAppSpecifyById(Long id);

    /***
     * @param id
     * @return
     */
    DubboResult<DuibaActivityAppSpecifyNewDto> findAppSpecifyById(Long id);

    /**
     * @param activityId
     * @param appId
     * @param activityType
     * @return
     */
    DubboResult<Boolean> isNotAppSpecifyByActivityIdAndAppIdAndActivityType(Long activityId, Long appId, String activityType);

    /**
     * @param paras
     * @return 获取不是定向活动列表
     * <AUTHOR>
     * 批量获取,减少dubbo交互
     */
    public DubboResult<List<Long>> notInappSpecifyActivitys(List<DuibaActivityAppSpecifyNewDto> paras);

    /**
     * notInappSpecifyActivitys. <br/>
     * 方法同上，参数不同
     *
     * @param activityIds
     * @param appId
     * @param activityType
     * @return List<Long> 不是定向活动的ids
     * <AUTHOR>
     * @since JDK 1.6
     */
    @RequestMapping("/notInappSpecifyActivitys1")
    DubboResult<List<Long>> notInappSpecifyActivitys(List<Long> activityIds, Long appId, String activityType);

    /**
     * inappSpecifyActivitys. <br/>
     * 方法同上，逻辑相反
     *
     * @param activityIds
     * @param appId
     * @param activityType
     * @return List<Long> 是定向活动的ids
     * <AUTHOR>
     * @since JDK 1.6
     */
    DubboResult<List<Long>> inappSpecifyActivitys(List<Long> activityIds, Long appId, String activityType);

    /**
     * 根据活动id和活动类型批量查询被定向的appId
     * 最多支持查询100个activityId对应的定向信息
     * 若入参超出限制或入参为空，接口返回 null
     *
     * @param activityIds
     * @param activityType
     * @return
     */
    List<Long> findAllAppIdByActivityIdsAndRelationType(List<Long> activityIds, String activityType);


    List<Long> findByAppIdAndType(Long appId, String activityType);

    /**
     * 统计活动开启app的数量
     * 1.不存在的活动，不会返回数据。既activityIds.size()>=result.size()
     *
     * @param activityIds 活动id(关联id),暂定最多50
     * @return
     */
    List<AppQuantityDto> countByRelationIds(List<Long> activityIds, String activityType);
}
