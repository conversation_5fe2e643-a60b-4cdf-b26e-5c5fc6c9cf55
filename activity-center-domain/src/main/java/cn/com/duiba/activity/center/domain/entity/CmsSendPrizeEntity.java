package cn.com.duiba.activity.center.domain.entity;

import java.util.Date;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2024/5/24 18:08
 * @description: cms后台直出奖品实体
 */
public class CmsSendPrizeEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * appId
     */
    private Long appId;

    /**
     * 操作者ID
     */
    private Long operatorId;

    /**
     * 操作者名称
     */
    private Long operatorName;

    /**
     * 发奖文件的OSS地址
     */
    private String prizeFile;

    /**
     * 结果文件的OSS地址
     */
    private String resultFile;

    /**
     * 结果说明
     */
    private String result;

    /**
     * 处理结果 0 初始化 1 处理中 2 已完成
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Long getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(Long operatorName) {
        this.operatorName = operatorName;
    }

    public String getPrizeFile() {
        return prizeFile;
    }

    public void setPrizeFile(String prizeFile) {
        this.prizeFile = prizeFile;
    }

    public String getResultFile() {
        return resultFile;
    }

    public void setResultFile(String resultFile) {
        this.resultFile = resultFile;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }
}