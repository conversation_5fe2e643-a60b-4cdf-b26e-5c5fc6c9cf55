<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans.xsd"
       default-autowire="byName">


    <!-- ========================================针对myBatis的配置项============================== -->
    <!-- 配置sqlSessionFactory -->
    <bean id="actComConfSqlSessionTemplate" class="org.mybatis.spring.SqlSessionTemplate">
        <constructor-arg>
            <bean class="org.mybatis.spring.SqlSessionFactoryBean">
                <!-- 实例化sqlSessionFactory时需要使用上述配置好的数据源以及SQL映射文件 -->
                <property name="dataSource" ref="actComConfMasterDataSource"/>
                <property name="mapperLocations" >
                    <array>
                         <value>classpath*:mybatis/*/*/*.xml</value>
                         <value>classpath*:mybatis/*/*.xml</value>
                    </array>
		        </property>
                <property name="configLocation" value="classpath:mybatis/sqlMapConfig.xml"/>
                <property name="typeAliasesPackage" value="cn.com.duiba.service.domain.dataobject"/>
            </bean>
        </constructor-arg>
    </bean>


    <!-- 配置Spring的事务管理器 -->
    <bean id="actComConfTransactionManager"  class="cn.com.duiba.wolf.spring.datasource.AutoRoutingDataSourceTransactionManager">
        <property name="dataSource" ref="actComConfMasterDataSource"/>
        <qualifier value="actComConf"/>
    </bean>

</beans>
