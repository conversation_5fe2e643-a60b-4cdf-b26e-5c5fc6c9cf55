#tomcat
server.port=8779
server.tomcat.access-log-enabled=false
#æå¤§çº¿ç¨æ°
server.tomcat.uri-encoding=UTF-8
server.tomcat.protocol_header=x-forwarded-proto

#spring
spring.main.show-banner=false
spring.jmx.enabled=false


duiba.threadpool.enabled=true
duiba.threadpool.core-size=15

duiba.threadpool.extra.event.core-size=10
duiba.threadpool.extra.event.queue-size=500


#å¯ç¨çäº§è
duiba.rocketmq.producer.enable=true

duiba.server.internal-mode=true
duiba.feign.serialization=hessian2

#å®¹å¨çº¿ç¨æ± 
server.tomcat.max-threads=500

duiba.threadpool.scheduled.enabled=true

duiba.domain.service.enable=true
