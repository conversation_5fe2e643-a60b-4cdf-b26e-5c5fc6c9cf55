<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">

    <property name="logging.path" value="${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}/}"/>

    <appender name="PROJECT-FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logging.path}/application.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{HH:mm:ss.SSS} %-5level [%thread] %logger{32}[%file:%line] -&gt; %msg%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logging.path}/application.log.%d{yyyy-MM-dd}.log.gz</fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="PROJECT-CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{HH:mm:ss.SSS} %-5level [%thread] %logger{32}[%file:%line] -&gt; %msg%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>

    <!--异步输出-->
    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
    <!--不丢失日志.默认地,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志-->
    <discardingThreshold>0</discardingThreshold>
    <!--更改默认的队列的深度,该值会影响性能.默认值为256-->
    <queueSize>512</queueSize>
    <!--添加附加的appender,最多只能添加一个-->
    <appender-ref ref="PROJECT-FILE"/>
    </appender>

    <logger name="jdbc.connection" level="ERROR"/>
    <logger name="jdbc.resultset" level="ERROR"/>
    <logger name="java.sql.PreparedStatement" level="DEBUG"/>

    <logger name="jdbc.sqlonly" additivity="false" level="${activity.log.sql.level}">
        <appender-ref ref="PROJECT-CONSOLE"/>
    </logger>
    <logger name="cn.com.duiba.activity.center.biz.dao" additivity="false" level="${activity.log.sql.level}">
        <appender-ref ref="PROJECT-CONSOLE"/>
    </logger>

    <logger name="cn.com.duiba" level="INFO"/>
    <logger name="cn.com.duibabiz" level="INFO"/>
    <root level="WARN">
        <appender-ref ref="ASYNC_FILE"/>
        <appender-ref ref="PROJECT-CONSOLE"/>
    </root>

</configuration>
