<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>

    <settings>
        <setting name="mapUnderscoreToCamelCase" value="true"/>
    </settings>

    <typeAliases>
        <typeAlias alias="HappyCodeItemBasicEntity"
                   type="cn.com.duiba.activity.center.biz.entity.happycodenew.HappyCodeItemBasicEntity"/>
        <typeAlias alias="HappyCodeItemPhaseEntity"
                   type="cn.com.duiba.activity.center.biz.entity.happycodenew.HappyCodeItemPhaseEntity"/>
        <typeAlias alias="HappyCodeDetailEntity"
                   type="cn.com.duiba.activity.center.biz.entity.happycodenew.HappyCodeDetailEntity"/>
        <typeAlias alias="HappyCodeUserRecordEntity"
                   type="cn.com.duiba.activity.center.biz.entity.happycodenew.HappyCodeUserRecordEntity"/>
        <typeAlias alias="HappyCodeDailyDataEntity"
                   type="cn.com.duiba.activity.center.biz.entity.happycodenew.HappyCodeDailyDataEntity"/>



        <typeAlias alias="LotterySquareGradientRewardEntity"
                   type="cn.com.duiba.activity.center.biz.entity.lotterysquare.LotterySquareGradientRewardEntity"/>
        <typeAlias alias="LotterySquareConfigEntity"
                   type="cn.com.duiba.activity.center.biz.entity.lotterysquare.LotterySquareConfigEntity"/>
        <typeAlias alias="LotterySquareBonusRecordEntity"
                   type="cn.com.duiba.activity.center.biz.entity.lotterysquare.LotterySquareBonusRecordEntity"/>
        <typeAlias alias="LotterySquareBonusConfigEntity"
                   type="cn.com.duiba.activity.center.biz.entity.lotterysquare.LotterySquareBonusConfigEntity"/>


        <typeAlias alias="DuibaNgameQueryParam"
                   type="cn.com.duiba.activity.center.api.params.DuibaNgameQueryParam"/>

        <typeAlias alias="ManualLotteryOrderEntity"
                   type="cn.com.duiba.activity.center.biz.entity.manual.ManualLotteryOrderEntity"/>

        <typeAlias alias="AppManualLotteryEntity"
                   type="cn.com.duiba.activity.center.biz.entity.manual.AppManualLotteryEntity"/>

        <typeAlias alias="QuizzOrdersEntity"
                   type="cn.com.duiba.activity.center.biz.entity.quizz.QuizzOrdersEntity"/>

        <typeAlias alias="QuizzStockConsumeEntity"
                   type="cn.com.duiba.activity.center.biz.entity.quizz.QuizzStockConsumeEntity"/>

        <typeAlias alias="DuibaQuizzStockEntity"
                   type="cn.com.duiba.activity.center.biz.entity.quizz.DuibaQuizzStockEntity"/>

        <typeAlias alias="DuibaQuizzOptionsEntity"
                   type="cn.com.duiba.activity.center.biz.entity.quizz.DuibaQuizzOptionsEntity"/>

        <typeAlias alias="DuibaSeckillAppSpecifyEntity"
                   type="cn.com.duiba.activity.center.biz.entity.seckill.DuibaSeckillAppSpecifyEntity"/>
        <typeAlias alias="DuibaSeckillEntity"
                   type="cn.com.duiba.activity.center.biz.entity.seckill.DuibaSeckillEntity"/>
        <typeAlias alias="DuibaSeckillStockLogEntity"
                   type="cn.com.duiba.activity.center.biz.entity.seckill.DuibaSeckillStockLogEntity"/>
        <typeAlias alias="DuibaSeckillStockEntity"
                   type="cn.com.duiba.activity.center.biz.entity.seckill.DuibaSeckillStockEntity"/>
        <typeAlias alias="DuibaSeckillStockConfigEntity"
                   type="cn.com.duiba.activity.center.biz.entity.seckill.DuibaSeckillStockConfigEntity"/>
        <typeAlias alias="DuibaSecondsKillActivityBrickEntity"
                   type="cn.com.duiba.activity.center.biz.entity.seconds_kill.DuibaSecondsKillActivityBrickEntity"/>

        <typeAlias alias="SeckillOrdersEntity"
                   type="cn.com.duiba.activity.center.biz.entity.seconds_kill.SeckillOrdersEntity"/>

        <typeAlias alias="DuibaQuizzBrickEntity"
                   type="cn.com.duiba.activity.center.biz.entity.quizz.DuibaQuizzBrickEntity"/>

        <typeAlias alias="OperatingActivityEntity"
                   type="cn.com.duiba.activity.center.biz.entity.activity.OperatingActivityEntity"/>


        <typeAlias alias="DuibaQuizzEntity"
                   type="cn.com.duiba.activity.center.biz.entity.quizz.DuibaQuizzEntity"/>

        <typeAlias alias="OperatingActivityOptionsEntity"
                   type="cn.com.duiba.activity.center.biz.entity.activity.OperatingActivityOptionsEntity"/>


        <!--<typeAlias alias="DuibaNgameEntity"-->
                   <!--type="cn.com.duiba.activity.center.biz.entity.ngame.DuibaNgameEntity"/>-->

        <typeAlias alias="DuibaNgameAppSpecifyEntity"
                   type="cn.com.duiba.activity.center.biz.entity.ngame.DuibaNgameAppSpecifyEntity"/>

        <typeAlias alias="DuibaNgameStockConsumeEntity"
                   type="cn.com.duiba.activity.center.biz.entity.ngame.DuibaNgameStockConsumeEntity"/>
        <typeAlias alias="DuibaNgameStockManualChangeEntity"
                   type="cn.com.duiba.activity.center.biz.entity.ngame.DuibaNgameStockManualChangeEntity"/>
        <typeAlias alias="NgameConsumerRecordEntity"
                   type="cn.com.duiba.activity.center.biz.entity.ngame.NgameConsumerRecordEntity"/>
        <typeAlias alias="NgameOrdersEntity"
                   type="cn.com.duiba.activity.center.biz.entity.ngame.NgameOrdersEntity"/>
        <typeAlias alias="NgameOrdersExtendEntity"
                   type="cn.com.duiba.activity.center.biz.entity.ngame.NgameOrdersExtendEntity"/>
        <typeAlias alias="NgameOrdersExtraEntity"
                   type="cn.com.duiba.activity.center.biz.entity.ngame.NgameOrdersExtraEntity"/>
        <typeAlias alias="NgameOrdersSequenceEntity"
                   type="cn.com.duiba.activity.center.biz.entity.ngame.NgameOrdersSequenceEntity"/>
        <typeAlias alias="NgameStockEntity"
                   type="cn.com.duiba.activity.center.biz.entity.ngame.NgameStockEntity"/>
        <typeAlias alias="DuibaNgameOptionsEntity"
                   type="cn.com.duiba.activity.center.biz.entity.ngame.DuibaNgameOptionsEntity"/>
        <typeAlias alias="DuibaHdtoolEntity"
                   type="cn.com.duiba.activity.center.biz.entity.hdtool.DuibaHdtoolEntity"/>
        <typeAlias alias="DuibaHdtoolOptionsEntity"
                   type="cn.com.duiba.activity.center.biz.entity.hdtool.DuibaHdtoolOptionsEntity"/>
        <typeAlias alias="HdtoolAppSpecifyEntity"
                   type="cn.com.duiba.activity.center.biz.entity.hdtool.HdtoolAppSpecifyEntity"/>
        <typeAlias alias="HdtoolOrdersEntity"
                   type="cn.com.duiba.activity.center.biz.entity.hdtool.HdtoolOrdersEntity"/>
        <typeAlias alias="HdtoolSkinEntity"
                   type="cn.com.duiba.activity.center.biz.entity.hdtool.HdtoolSkinEntity"/>
        <typeAlias alias="HdtoolSkinDefaultEntity"
                   type="cn.com.duiba.activity.center.biz.entity.hdtool.HdtoolSkinDefaultEntity"/>
        <typeAlias alias="HdtoolStockConsumeEntity"
                   type="cn.com.duiba.activity.center.biz.entity.hdtool.HdtoolStockConsumeEntity"/>
        <typeAlias alias="HdtoolStockManualChangeEntity"
                   type="cn.com.duiba.activity.center.biz.entity.hdtool.HdtoolStockManualChangeEntity"/>
        <typeAlias alias="NgameSeparatorRankRecordEntity"
                   type="cn.com.duiba.activity.center.biz.entity.ngame.NgameSeparatorRankRecordEntity" />
        <typeAlias alias="DuibaQuizzAppSpecifyEntity"
                   type="cn.com.duiba.activity.center.biz.entity.quizz.DuibaQuizzAppSpecifyEntity"/>

        <typeAlias alias="OperatingActivityOptionsEntity"
                   type="cn.com.duiba.activity.center.biz.entity.activity.OperatingActivityOptionsEntity"/>

        <typeAlias alias="DuibaNgameEntity"
                   type="cn.com.duiba.activity.center.biz.entity.ngame.DuibaNgameEntity"/>
        <typeAlias alias="DuibaNgameBrickEntity"
                   type="cn.com.duiba.activity.center.biz.entity.ngame.DuibaNgameBrickEntity"/>

        <typeAlias alias="DuibaNgameAppSpecifyEntity"
                   type="cn.com.duiba.activity.center.biz.entity.ngame.DuibaNgameAppSpecifyEntity"/>


        <typeAlias alias="DuibaQuizzStockManualChangeEntity"
                   type="cn.com.duiba.activity.center.biz.entity.quizz.DuibaQuizzStockManualChangeEntity"/>

        <typeAlias alias="DuibaSecondsKillActivityEntity"
                   type="cn.com.duiba.activity.center.biz.entity.seconds_kill.DuibaSecondsKillActivityEntity"/>

        <typeAlias alias="DuibaHdtoolOptionsEntity"
                   type="cn.com.duiba.activity.center.biz.entity.hdtool.DuibaHdtoolOptionsEntity"/>
        <typeAlias alias="DuibaHdtoolEntity"
                   type="cn.com.duiba.activity.center.biz.entity.hdtool.DuibaHdtoolEntity"/>
        <typeAlias alias="HdtoolAppSpecifyEntity"
                   type="cn.com.duiba.activity.center.biz.entity.hdtool.HdtoolAppSpecifyEntity"/>
        <typeAlias alias="AddActivityEntity"
                   type="cn.com.duiba.activity.center.biz.entity.AddActivityEntity"/>
        <typeAlias alias="ActivityExtraInfoEntity"
                   type="cn.com.duiba.activity.center.biz.entity.ActivityExtraInfoEntity"/>
        <typeAlias alias="HdtoolStockConsumeEntity"
                   type="cn.com.duiba.activity.center.biz.entity.hdtool.HdtoolStockConsumeEntity"/>
        <typeAlias alias="HdtoolStockManualChangeEntity"
                   type="cn.com.duiba.activity.center.biz.entity.hdtool.HdtoolStockManualChangeEntity"/>

        <typeAlias alias="DuibaSecondsKillActivityAppSpecifyEntity"
                   type="cn.com.duiba.activity.center.biz.entity.seconds_kill.DuibaSecondsKillActivityAppSpecifyEntity"/>

        <typeAlias alias="DeveloperActivityStatisticsEntity"
                   type="cn.com.duiba.activity.center.biz.entity.DeveloperActivityStatisticsEntity"/>
        <typeAlias alias="SingleLotteryOrderEntity"
                   type="cn.com.duiba.activity.center.biz.entity.singlelottery.SingleLotteryOrderEntity"/>
        <typeAlias alias="DuibaSingleLotteryEntity"
                   type="cn.com.duiba.activity.center.biz.entity.singlelottery.DuibaSingleLotteryEntity"/>
        <typeAlias alias="SingleLotteryAppSpecifyEntity"
                   type="cn.com.duiba.activity.center.biz.entity.singlelottery.SingleLotteryAppSpecifyEntity"/>
        <typeAlias alias="AppSingleLotteryEntity"
                   type="cn.com.duiba.activity.center.biz.entity.singlelottery.AppSingleLotteryEntity"/>
        <typeAlias alias="SingleLotteryStockConsumeEntity"
                   type="cn.com.duiba.activity.center.biz.entity.singlelottery.SingleLotteryStockConsumeEntity"/>
        <typeAlias alias="SingleLotteryStockManualChangeEntity"
                   type="cn.com.duiba.activity.center.biz.entity.singlelottery.SingleLotteryStockManualChangeEntity"/>

        <typeAlias alias="ActPreChangeStockEntity"
                   type="cn.com.duiba.activity.center.biz.entity.chaos.ActPreChangeStockEntity"/>
        <typeAlias alias="ActPreConsumeStockEntity"
                   type="cn.com.duiba.activity.center.biz.entity.chaos.ActPreConsumeStockEntity"/>
        <typeAlias alias="ActPreStockEntity"
                   type="cn.com.duiba.activity.center.biz.entity.chaos.ActPreStockEntity"/>
        <typeAlias alias="RetryOrdersFasterEntity"
                   type="cn.com.duiba.activity.center.biz.entity.chaos.RetryOrdersFasterEntity"/>

        <typeAlias alias="DuibaActivityEntity"
                   type="cn.com.duiba.activity.center.biz.entity.duibaactivity.DuibaActivityEntity"/>
        <typeAlias alias="DuibaActivityAppSpecifyEntity"
                   type="cn.com.duiba.activity.center.biz.entity.duibaactivity.DuibaActivityAppSpecifyEntity"/>
        <typeAlias alias="DuibaQuestionAnswerEntity"
                   type="cn.com.duiba.activity.center.biz.entity.game.DuibaQuestionAnswerEntity"/>
        <typeAlias alias="DuibaQuestionAnswerAppSpecifyEntity"
                   type="cn.com.duiba.activity.center.biz.entity.game.DuibaQuestionAnswerAppSpecifyEntity"/>
        <typeAlias alias="DuibaQuestionAnswerBrickEntity"
                   type="cn.com.duiba.activity.center.biz.entity.game.DuibaQuestionAnswerBrickEntity"/>
        <typeAlias alias="DuibaQuestionAnswerOptionsEntity"
                   type="cn.com.duiba.activity.center.biz.entity.game.DuibaQuestionAnswerOptionsEntity"/>
        <typeAlias alias="DuibaQuestionAnswerOrdersEntity"
                   type="cn.com.duiba.activity.center.biz.entity.game.DuibaQuestionAnswerOrdersEntity"/>
        <typeAlias alias="DuibaQuestionAnswerOrdersExtraEntity"
                   type="cn.com.duiba.activity.center.biz.entity.game.DuibaQuestionAnswerOrdersExtraEntity"/>
        <typeAlias alias="DuibaQuestionBankEntity"
                   type="cn.com.duiba.activity.center.biz.entity.game.DuibaQuestionBankEntity"/>
        <typeAlias alias="DuibaQuestionRecordEntity"
                   type="cn.com.duiba.activity.center.biz.entity.game.DuibaQuestionRecordEntity"/>
        <typeAlias alias="DuibaQuestionStockEntity"
                   type="cn.com.duiba.activity.center.biz.entity.game.DuibaQuestionStockEntity"/>
        <typeAlias alias="QuestionStockConsumeEntity"
                   type="cn.com.duiba.activity.center.biz.entity.game.QuestionStockConsumeEntity"/>
        <typeAlias alias="DuibaQuestionStockManualChangeEntity"
                   type="cn.com.duiba.activity.center.biz.entity.game.DuibaQuestionStockManualChangeEntity"/>
        <typeAlias alias="GameOrdersEntity"
                   type="cn.com.duiba.activity.center.biz.entity.game.GameOrdersEntity"/>
        <typeAlias alias="GameOrdersSimpleEntity"
                   type="cn.com.duiba.activity.center.biz.entity.game.GameOrdersSimpleEntity"/>
        <typeAlias alias="GameConfigDuibaEntity"
                   type="cn.com.duiba.activity.center.biz.entity.game.GameConfigDuibaEntity"/>
        <typeAlias alias="GameAppSpecifyEntity"
                   type="cn.com.duiba.activity.center.biz.entity.game.GameAppSpecifyEntity"/>
        <typeAlias alias="GameOptionsDuibaEntity"
                   type="cn.com.duiba.activity.center.biz.entity.game.GameOptionsDuibaEntity"/>

        <typeAlias alias="GuessOrdersEntity"
                   type="cn.com.duiba.activity.center.biz.entity.guess.GuessOrdersEntity"/>
        <typeAlias alias="GuessOrdersExtraEntity"
                   type="cn.com.duiba.activity.center.biz.entity.guess.GuessOrdersExtraEntity"/>
        <typeAlias alias="DuibaGuessAppSpecifyEntity"
                   type="cn.com.duiba.activity.center.biz.entity.guess.DuibaGuessAppSpecifyEntity"/>
        <typeAlias alias="DuibaGuessBrickEntity"
                   type="cn.com.duiba.activity.center.biz.entity.guess.DuibaGuessBrickEntity"/>
        <typeAlias alias="DuibaGuessEntity"
                   type="cn.com.duiba.activity.center.biz.entity.guess.DuibaGuessEntity"/>
        <typeAlias alias="DuibaGuessOptionsEntity"
                   type="cn.com.duiba.activity.center.biz.entity.guess.DuibaGuessOptionsEntity"/>
        <typeAlias alias="DuibaGuessSelectionsEntity"
                   type="cn.com.duiba.activity.center.biz.entity.guess.DuibaGuessSelectionsEntity"/>
        <typeAlias alias="DuibaGuessStockManualChangeEntity"
                   type="cn.com.duiba.activity.center.biz.entity.guess.DuibaGuessStockManualChangeEntity"/>
        <typeAlias alias="GuessOrdersSequenceEntity"
                   type="cn.com.duiba.activity.center.biz.entity.guess.GuessOrdersSequenceEntity"/>
        <typeAlias alias="GuessStockConsumeEntity"
                   type="cn.com.duiba.activity.center.biz.entity.guess.GuessStockConsumeEntity"/>
        <typeAlias alias="DuibaGuessStockEntity"
                   type="cn.com.duiba.activity.center.biz.entity.guess.DuibaGuessStockEntity"/>
                   
		<typeAlias alias="SignConfigEntity"
                   type="cn.com.duiba.activity.center.biz.entity.sign.SignConfigEntity"/>
        <typeAlias alias="LittleGameEntity"
                   type="cn.com.duiba.activity.center.biz.entity.littlegame.LittleGameEntity"/>
        <typeAlias alias="GameSkinEntity"
                   type="cn.com.duiba.activity.center.biz.entity.gameskin.GameSkinEntity"/>
		<typeAlias alias="AppDirectEntity"
                   type="cn.com.duiba.activity.center.biz.entity.activity.AppDirectEntity"/>

        <typeAlias alias="SignActivityEntity"
                   type="cn.com.duiba.activity.center.biz.entity.sign.system.SignActivityEntity"/>
        <typeAlias alias="SignActivitySkinEntity"
                   type="cn.com.duiba.activity.center.biz.entity.sign.system.SignActivitySkinEntity"/>
        <typeAlias alias="SignConstellationRuleEntity"
                   type="cn.com.duiba.activity.center.biz.entity.sign.system.SignConstellationRuleEntity"/>
        <typeAlias alias="SignOperatingEntity"
                   type="cn.com.duiba.activity.center.biz.entity.sign.system.SignOperatingEntity"/>
		<typeAlias alias="ActivityThroughEntity" type="cn.com.duiba.activity.center.biz.entity.through.ActivityThroughEntity"/>
		<typeAlias alias="ActivityThroughStepInfoEntity" type="cn.com.duiba.activity.center.biz.entity.through.ActivityThroughStepInfoEntity"/>
		<typeAlias alias="QqWalletEntity" type="cn.com.duiba.activity.center.biz.entity.qqwallet.QqWalletEntity"/>

        <typeAlias alias="OptionRankRecordEntity" type="cn.com.duiba.activity.center.biz.entity.optionrank.OptionRankRecordEntity"/>

        <typeAlias alias="ActivityOrderEntity" type="cn.com.duiba.activity.center.biz.entity.activity_order.ActivityOrderEntity"/>

        <typeAlias alias="SignSkinTemplateEntity" type="cn.com.duiba.activity.center.biz.entity.sign.system.SignSkinTemplateEntity"/>

        <typeAlias alias="FriendPointLogEntity" type="cn.com.duiba.activity.center.biz.entity.zhuanzhuan.FriendPointLogEntity"/>

        <typeAlias alias="GiffGaffCardRecordEntity" type="cn.com.duiba.activity.center.biz.entity.card.GiffGaffCardRecordEntity"/>

        <typeAlias alias="HdtoolSpecifyConfigEntity" type="cn.com.duiba.activity.center.biz.entity.hdtool.HdtoolSpecifyConfigEntity"/>

        <typeAlias alias="BookInfoEntity" type="cn.com.duiba.activity.center.biz.entity.book.BookInfoEntity"/>

        <typeAlias alias="BookCheatRecordEntity" type="cn.com.duiba.activity.center.biz.entity.book.BookCheatRecordEntity"/>

        <typeAlias alias="CartoonBookVoteEntity" type="cn.com.duiba.activity.center.biz.entity.manhua.CartoonBookVoteEntity"/>

        <typeAlias alias="DuibaAppBookInfoEntity" type="cn.com.duiba.activity.center.biz.entity.book.DuibaAppBookInfoEntity" />

        <typeAlias alias="ActivityProyaEntity" type="cn.com.duiba.activity.center.biz.entity.activity_proya.ActivityProyaEntity" />

        <typeAlias alias="WxTemplatePushEntity" type="cn.com.duiba.activity.center.biz.entity.activity_proya.WxTemplatePushEntity" />

        <typeAlias alias="GuessRedPacketEntity" type="cn.com.duiba.activity.center.biz.entity.guessredpacket.GuessRedPacketEntity" />

        <typeAlias alias="GroupActivityRecordEntity" type="cn.com.duiba.activity.center.biz.entity.group.GroupActivityRecordEntity" />

        <typeAlias alias="GroupCountEntity" type="cn.com.duiba.activity.center.biz.entity.group.GroupCountEntity" />

        <typeAlias alias="BetActGroupEntity" type="cn.com.duiba.activity.center.biz.entity.bet.BetActGroupEntity" />

        <typeAlias alias="ActivityBudgetCheckEntity" type="cn.com.duiba.activity.center.biz.entity.bet.ActivityBudgetCheckEntity" />



        <typeAlias alias="DuibaBrickConfigEntity" type="cn.com.duiba.activity.center.biz.entity.movebrick.DuibaBrickConfigEntity" />
        <typeAlias alias="DuibaBrickPrizeEntity" type="cn.com.duiba.activity.center.biz.entity.movebrick.DuibaBrickPrizeEntity"/>

        <typeAlias alias="DuibaBrickAccountEntity" type="cn.com.duiba.activity.center.biz.entity.movebrick.DuibaBrickAccountEntity"/>
        <typeAlias alias="DuibaBrickExchangeRecordEntity" type="cn.com.duiba.activity.center.biz.entity.movebrick.DuibaBrickExchangeRecordEntity"/>
        <typeAlias alias="DuibaBrickWorkerEntity" type="cn.com.duiba.activity.center.biz.entity.movebrick.DuibaBrickWorkerEntity"/>
        <typeAlias alias="DuibaMarkEntity" type="cn.com.duiba.activity.center.biz.entity.movebrick.DuibaBrickMarkEntity"/>
        <typeAlias alias="PkTeamInfoEntity" type="cn.com.duiba.activity.center.biz.entity.shuqipk.PkTeamInfoEntity" />
        <typeAlias alias="PkTeamMemberInfoEntity" type="cn.com.duiba.activity.center.biz.entity.shuqipk.PkTeamMemberInfoEntity" />
        <typeAlias alias="PkTeamMemberRecordEntity" type="cn.com.duiba.activity.center.biz.entity.shuqipk.PkTeamMemberRecordEntity" />
        <typeAlias alias="PkTeamRecordEntity" type="cn.com.duiba.activity.center.biz.entity.shuqipk.PkTeamRecordEntity" />

        <typeAlias alias="FreeGroupRecordEntity" type="cn.com.duiba.activity.center.biz.entity.freegroup.FreeGroupRecordEntity" />
        <typeAlias alias="FreeGroupUserRecordEntity" type="cn.com.duiba.activity.center.biz.entity.freegroup.FreeGroupUserRecordEntity" />
        <typeAlias alias="GuessRedPacketInfoEntity" type="cn.com.duiba.activity.center.biz.entity.guessredpacket.GuessRedPacketInfoEntity" />
        <typeAlias alias="RedPacketGuessRecordEntity" type="cn.com.duiba.activity.center.biz.entity.guessredpacket.RedPacketGuessRecordEntity" />
	
        <typeAlias alias="LuckyCodeUserScratchRecordEntity" type="cn.com.duiba.activity.center.biz.entity.luckycode.LuckyCodeUserScratchRecordEntity"/>
        <typeAlias alias="LuckyCodeAwardConfigEntity" type="cn.com.duiba.activity.center.biz.entity.luckycode.LuckyCodeAwardConfigEntity"/>
        <typeAlias alias="LuckyCodeConfigEntity" type="cn.com.duiba.activity.center.biz.entity.luckycode.LuckyCodeConfigEntity"/>
        <typeAlias alias="LuckyCodeUserAwardRecordEntity" type="cn.com.duiba.activity.center.biz.entity.luckycode.LuckyCodeUserAwardRecordEntity"/>
        <typeAlias alias="LuckyCodeUserScratchInfoEntity" type="cn.com.duiba.activity.center.biz.entity.luckycode.LuckyCodeUserScratchInfoEntity"/>

        <typeAlias alias="PyramidSpreadConfigEntity" type="cn.com.duiba.activity.center.biz.entity.pyramidspread.PyramidSpreadConfigEntity"/>
        <typeAlias alias="SpreadConsumerDataEntity" type="cn.com.duiba.activity.center.biz.entity.pyramidspread.SpreadConsumerDataEntity"/>
        <typeAlias alias="SpreadConsumerBonusEntity" type="cn.com.duiba.activity.center.biz.entity.pyramidspread.SpreadConsumerBonusEntity"/>
        <typeAlias alias="PyramidAppletConfigEntity" type="cn.com.duiba.activity.center.biz.entity.pyramidspread.PyramidAppletConfigEntity"/>
    </typeAliases>

</configuration>
