<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.singlelottery.impl.DuibaSingleLotteryAppSpecifyDaoImpl">

    <select id="findAllSpecifyByDuibaSingleLottery" resultType="SingleLotteryAppSpecifyEntity" parameterType="Long">
        select
        id,
        duiba_single_lottery_id,
        app_id,
        order_count,
        remaining,
        gmt_create,
        gmt_modified
        from single_lottery_app_specify where duiba_single_lottery_id = #{duibaSingleLotteryId}
    </select>

    <select id="findSpecifyByDuibaSingleLotteryAndApp" resultType="SingleLotteryAppSpecifyEntity">
        select
        id,
        duiba_single_lottery_id,
        app_id,
        order_count,
        remaining,
        gmt_create,
        gmt_modified
        from single_lottery_app_specify where duiba_single_lottery_id = #{duibaSingleLotteryId} and app_id = #{appId} limit 1
    </select>

    <select id="findSpecifyByDuibaSingleLotterysAndApp" resultType="SingleLotteryAppSpecifyEntity">
        select
        id,
        duiba_single_lottery_id,
        app_id,
        order_count,
        remaining,
        gmt_create,
        gmt_modified
        from single_lottery_app_specify where duiba_single_lottery_id IN
        <foreach collection="duibaSingleLotteryIds" item="duibaSingleLotteryId" open="(" close=")" separator=",">
          #{duibaSingleLotteryId}
        </foreach>
        and app_id = #{appId}
    </select>

    <select id="findSpecifyById" resultType="SingleLotteryAppSpecifyEntity" parameterType="Long">
        select
        id,
        duiba_single_lottery_id,
        app_id,
        order_count,
        remaining,
        gmt_create,
        gmt_modified
        from single_lottery_app_specify
        where id = #{id}
    </select>

    <select id="findSpecifyForupdate" resultType="SingleLotteryAppSpecifyEntity" parameterType="Long">
        select
        id,
        duiba_single_lottery_id,
        app_id,
        order_count,
        remaining,
        gmt_create,
        gmt_modified
        from single_lottery_app_specify
        where id = #{id} for update
    </select>

    <select id="filterDirectByActivityIdsAndAppId" resultType="java.lang.Long" parameterType="java.util.Map">
        select duiba_single_lottery_id
        from single_lottery_app_specify
        where app_id=#{appId}
        AND duiba_single_lottery_id IN
        <foreach collection="activityIds" item="activityId" open="(" close=")" separator=",">
          #{activityId}
        </foreach>
    </select>

    <delete id="deleteSpecify" parameterType="Long">
        delete from single_lottery_app_specify where id = #{id}
    </delete>

    <update id="reduceSpecifyAppRemaining">
        update single_lottery_app_specify set remaining=(remaining-1),gmt_modified = NOW() where duiba_single_lottery_id = #{duibaSingleLotteryId} and app_id = #{appId} and remaining &gt; 0
    </update>

    <update id="addSpecifyAppRemaining">
        update single_lottery_app_specify set remaining=(remaining+1),gmt_modified = NOW() where duiba_single_lottery_id = #{duibaSingleLotteryId} and app_id = #{appId}
    </update>

    <update id="addSpecifyOrderCount">
        update single_lottery_app_specify set order_count=(order_count+1),gmt_modified = NOW() where duiba_single_lottery_id = #{duibaSingleLotteryId} and app_id = #{appId}
    </update>

    <insert id="insertAppSpecify" parameterType="SingleLotteryAppSpecifyEntity" useGeneratedKeys="true" keyProperty="id">
        insert into single_lottery_app_specify
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null"> id, </if>
            <if test="duibaSingleLotteryId != null"> duiba_single_lottery_id, </if>
            <if test="appId != null"> app_id, </if>
            <if test="orderCount != null"> order_count, </if>
            <if test="remaining != null"> remaining, </if>
            <if test="gmtCreate != null"> gmt_create, </if>
            <if test="gmtModified != null"> gmt_modified, </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null"> #{id}, </if>
            <if test="duibaSingleLotteryId != null"> #{duibaSingleLotteryId}, </if>
            <if test="appId != null"> #{appId}, </if>
            <if test="orderCount != null"> #{orderCount}, </if>
            <if test="remaining != null"> #{remaining}, </if>
            <if test="gmtCreate != null"> #{gmtCreate}, </if>
            <if test="gmtModified != null"> #{gmtModified}, </if>
        </trim>
    </insert>

    <update id="updateSpecifyRemaining">
        update single_lottery_app_specify set remaining = #{remaining} where id = #{id}
    </update>

</mapper>
