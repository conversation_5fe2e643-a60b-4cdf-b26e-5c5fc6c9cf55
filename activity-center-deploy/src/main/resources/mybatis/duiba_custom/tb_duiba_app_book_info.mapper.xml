<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.book.impl.DuibaAppBookInfoDaoImpl">

    <sql id="BASE_COLUMNS">
      id,
      app_id AS appId,
      act_id AS actId,
      book_id AS bookId,
      book_img AS bookImg,
      big_img AS bigImg,
      book_name AS bookName,
      book_author AS bookAuthor,
      book_url AS bookUrl,
      book_desc AS bookDesc,
      vote_num AS voteNum,
      category,
      cate_name AS cateName,
      in_link AS inLink,
      out_link AS outLink,
      book_status AS bookStatus,
      extra
    </sql>

    <select id="list" resultType="DuibaAppBookInfoEntity">
        select <include refid="BASE_COLUMNS"/> from tb_duiba_app_book_info
        <where>
            <if test="appId != null">
                app_id = #{appId}
            </if>
            <if test="actId != null">
                and act_id = #{actId}
            </if>
            <if test="category != null">
                and category = #{category}
            </if>
        </where>
        LIMIT #{offset}, #{limit}
    </select>

    <insert id="batchInsertBooks" parameterType="java.util.List">
        insert into tb_duiba_app_book_info
        (app_id,act_id,book_id,book_img,big_img,book_name,book_author,book_url,book_desc,category,cate_name,in_link,out_link,extra)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.appId},
            #{item.actId},
            #{item.bookId},
            #{item.bookImg},
            #{item.bigImg},
            #{item.bookName},
            #{item.bookAuthor},
            #{item.bookUrl},
            #{item.bookDesc},
            #{item.category},
            #{item.cateName},
            #{item.inLink},
            #{item.outLink},
            #{item.extra}
            )
        </foreach>
    </insert>

    <update id="batchUpdateBooks" parameterType="DuibaAppBookInfoEntity">
        update tb_duiba_app_book_info
        <set>
            <if test="bookImg != null"> book_img = #{bookImg},</if>
            <if test="bigImg != null"> big_img = #{bigImg},</if>
            <if test="bookName != null"> book_name = #{bookName},</if>
            <if test="bookAuthor != null"> book_author = #{bookAuthor},</if>
            <if test="bookUrl != null">book_url = #{bookUrl},</if>
            <if test="bookDesc != null"> book_desc = #{bookDesc},</if>
            <if test="category != null"> category = #{category},</if>
            <if test="cateName != null"> cate_name = #{cateName},</if>
            <if test="inLink != null"> in_link = #{inLink},</if>
            <if test="outLink != null"> out_link = #{outLink},</if>
            <if test="extra != null"> extra = #{extra},</if>
        </set>
        where app_id = #{appId}
        and act_id = #{actId}
        and book_id = #{bookId}
    </update>
    
    <delete id="deleteById" parameterType="long">
        DELETE FROM tb_duiba_app_book_info WHERE id=#{id}
    </delete>

</mapper>