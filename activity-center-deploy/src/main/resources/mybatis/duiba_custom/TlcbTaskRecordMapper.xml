<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.tlcb.impl.TlcbTaskRecordDaoImpl">

    <resultMap type="cn.com.duiba.activity.center.biz.entity.tlcb.TlcbTaskRecordEntity" id="tlcbTaskRecordMap">
        <result property="id" column="id"/>
        <result property="appId" column="app_id"/>
        <result property="actId" column="act_id"/>
        <result property="operatingActivityId" column="operating_activity_id"/>
        <result property="consumerId" column="consumer_id"/>
        <result property="taskId" column="task_id"/>
        <result property="taskStatus" column="task_status"/>
        <result property="awardType" column="award_type"/>
        <result property="appItemId" column="app_item_id"/>
        <result property="appItemType" column="app_item_type"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="guaranteedCredits" column="guaranteed_credits"/>
        <result property="orderNum" column="order_num"/>
        <result property="activityOrderNum" column="activity_order_num"/>
        <result property="extraInfo" column="extra_info"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="columns">
        id,
        app_id,
        act_id,
        operating_activity_id,
        consumer_id,
        task_id,
        task_status,
        award_type,
        app_item_id,
        app_item_type,
        error_msg,
        guaranteed_credits,
        order_num,
        activity_order_num,
        extra_info,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="save" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.biz.entity.tlcb.TlcbTaskRecordEntity">
        INSERT INTO tb_tlcb_task_record(app_id,act_id,operating_activity_id,consumer_id,task_id,task_status,award_type,app_item_id,app_item_type,error_msg,guaranteed_credits,order_num,activity_order_num,extra_info)
        VALUES(#{appId},#{actId},#{operatingActivityId},#{consumerId},#{taskId},#{taskStatus},#{awardType},#{appItemId},#{appItemType},#{errorMsg},#{guaranteedCredits},#{orderNum},#{activityOrderNum},#{extraInfo})
    </insert>

    <insert id="batchSave" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.biz.entity.tlcb.TlcbTaskRecordEntity">
        INSERT INTO tb_tlcb_task_record(app_id,act_id,operating_activity_id,consumer_id,task_id,task_status,award_type,app_item_id,app_item_type,error_msg,guaranteed_credits,order_num,activity_order_num,extra_info)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.appId},#{item.actId},#{item.operatingActivityId},#{item.consumerId},
            #{item.taskId},#{item.taskStatus},#{item.awardType},#{item.appItemId},#{item.appItemType},
            #{item.errorMsg},#{item.guaranteedCredits},#{item.orderNum},#{item.activityOrderNum},#{item.extraInfo})
        </foreach>
    </insert>

    <update id="updateById" parameterType="cn.com.duiba.activity.center.biz.entity.tlcb.TlcbTaskRecordEntity">
        UPDATE tb_tlcb_task_record
        <set>
            <if test="awardType != null">
                award_type = #{awardType},
            </if>
            <if test="appItemId != null">
                app_item_id = #{appItemId},
            </if>
            <if test="appItemType != null">
                app_item_type = #{appItemType},
            </if>
            <if test="errorMsg != null">
                error_msg = #{errorMsg},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
            <if test="activityOrderNum != null">
                activity_order_num = #{activityOrderNum},
            </if>
            <if test="extraInfo != null">
                extra_info = #{extraInfo},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="getById" resultMap="tlcbTaskRecordMap">
        SELECT <include refid="columns"/>
        FROM tb_tlcb_task_record
        WHERE id = #{id}
    </select>

    <select id="listByIds" resultMap="tlcbTaskRecordMap">
        SELECT <include refid="columns"/>
        FROM tb_tlcb_task_record
        WHERE id IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findConsumerTaskRecordList" parameterType="map" resultMap="tlcbTaskRecordMap">
        SELECT <include refid="columns"/>
        FROM tb_tlcb_task_record
        WHERE act_id = #{actId} and consumer_id = #{cid}
    </select>

    <select id="findConsumerTaskRecord" parameterType="map" resultMap="tlcbTaskRecordMap">
        SELECT <include refid="columns"/>
        FROM tb_tlcb_task_record
        WHERE consumer_id = #{cid} and task_id = #{taskId}
    </select>

    <update id="updateRecordByOrderNum" parameterType="map">
        update tb_tlcb_task_record
        set task_status = #{status}
        <where>
            <if test="orderNum != null and orderNum != ''">
                order_num =#{orderNum}
            </if>
            <if test="activityOrderNum != null and activityOrderNum != ''">
                activity_order_num =#{activityOrderNum}
            </if>
            and task_status = 2
        </where>
    </update>

    <update id="updateRecordStatus" parameterType="map">
        update tb_tlcb_task_record
        set task_status = #{status}
        where id = #{id} and task_status = #{preStatus}
    </update>

</mapper>