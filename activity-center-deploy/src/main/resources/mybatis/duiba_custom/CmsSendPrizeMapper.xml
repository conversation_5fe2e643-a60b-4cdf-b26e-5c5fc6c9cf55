<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.cms.impl.CmsSendPrizeDaoImpl">

    <resultMap id="BaseResultMap" type="cn.com.duiba.activity.center.api.dto.CmsSendPrizeDto">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="app_id" property="appId" jdbcType="BIGINT"/>
        <result column="operator_id" property="operatorId" jdbcType="BIGINT"/>
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR"/>
        <result column="prize_file" property="prizeFile" jdbcType="VARCHAR"/>
        <result column="result_file" property="resultFile" jdbcType="VARCHAR"/>
        <result column="result" property="result" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP"/>
        <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, app_id, operator_id, operator_name, prize_file, result_file, result, status,
        gmt_create, gmt_modified
    </sql>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="cn.com.duiba.activity.center.api.dto.CmsSendPrizeDto" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_cms_send_prize (
            app_id, operator_id, operator_name, prize_file, result_file,
            result, status, gmt_create, gmt_modified
        )
        VALUES (
            #{appId}, #{operatorId}, #{operatorName}, #{prizeFile}, #{resultFile},
            #{result}, #{status}, NOW(), NOW()
        )
    </insert>

    <!-- 更新记录 -->
    <update id="update" parameterType="cn.com.duiba.activity.center.api.dto.CmsSendPrizeDto">
        UPDATE tb_cms_send_prize
        <set>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="operatorName != null">operator_name = #{operatorName},</if>
            <if test="prizeFile != null">prize_file = #{prizeFile},</if>
            <if test="resultFile != null">result_file = #{resultFile},</if>
            <if test="result != null">result = #{result},</if>
            <if test="status != null">status = #{status},</if>
            gmt_modified = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 分页查询 -->
    <select id="pageList" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_cms_send_prize
        WHERE app_id = #{appId}
        ORDER BY id DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计总数 -->
    <select id="count" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM tb_cms_send_prize
        WHERE app_id = #{appId}
    </select>

    <!-- 根据id查询 -->
    <select id="findById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM tb_cms_send_prize
        WHERE id = #{id}
    </select>

</mapper>