<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.activity_proya.impl.AcitvityProyaDaoImpl">
    <resultMap id="BaseResultMap" type="cn.com.duiba.activity.center.biz.entity.activity_proya.ActivityProyaEntity" >
        <id column="id" property="id"/>
        <result column="cid" property="cid"/>
        <result column="phone" property="phone"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>
    <sql id="fields">
    id as id,
    cid as cid,
    phone as phone,
    gmt_create as gmtCreate,
    gmt_modified as gmtModified
    </sql>

    <select id="findPhoneByCid" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="fields"/>
        FROM tb_proya_consumer_phone
        WHERE cid = #{cid}
    </select>
    
    <insert id="saveProyaConsumer" parameterType="ActivityProyaEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_proya_consumer_phone
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="cid !=null">cid,</if>
            <if test="phone != null">phone,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="cid !=null">#{cid},</if>
            <if test="phone != null">#{phone},</if>
        </trim>

    </insert>


</mapper>