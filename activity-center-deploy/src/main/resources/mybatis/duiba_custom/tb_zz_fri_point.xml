<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.zhuanzhuan.impl.FriendPointLogDaoImpl">

	<resultMap type="FriendPointLogEntity" id="friendPointLog">
		<id column="id" property="id"/>
		<result column="app_id" property="appId"/>
		<result column="consumer_id" property="consumerId"/>
		<result column="activity_id" property="activityId"/>
		<result column="friend_point" property="friendPoint"/>
		<result column="is_old_user" property="isOldUser"/>
		<result column="is_helped" property="isHelped"/>
		<result column="old_helped" property="oldHelped"/>
	</resultMap>

	<sql id="all_column">
		id,app_id,consumer_id,activity_id,friend_point,is_old_user,is_helped,old_helped
	</sql>

	<insert id="insert" parameterType="FriendPointLogEntity" useGeneratedKeys="true" keyProperty="id">
		insert into tb_zz_fri_point
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="appId != null">app_id,</if>
			<if test="consumerId != null">consumer_id,</if>
			<if test="activityId != null">activity_id,</if>
			<if test="friendPoint != null">friend_point,</if>
			<if test="isOldUser != null">is_old_user,</if>
			<if test="isHelped != null">is_helped,</if>
			<if test="oldHelped != null">old_helped,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="appId != null">#{appId},</if>
			<if test="consumerId != null">#{consumerId},</if>
			<if test="activityId != null">#{activityId},</if>
			<if test="friendPoint != null">#{friendPoint},</if>
			<if test="isOldUser != null">#{isOldUser},</if>
			<if test="isHelped != null">#{isHelped},</if>
			<if test="oldHelped != null">#{oldHelped},</if>
		</trim>
	</insert>

	<select id="findByUserAndAct" resultType="FriendPointLogEntity" parameterType="cn.com.duiba.activity.center.api.params.FriendPointBaseQueryParam">
		select
		<include refid="all_column"/>
		from tb_zz_fri_point
		where consumer_id = #{consumerId}
		and activity_id = #{activityId}
		and app_id = #{appId}
	</select>

	<update id="updateFriendPoint" parameterType="FriendPointLogEntity">
		update tb_zz_fri_point set friend_point = (friend_point + #{friendPoint})
		where consumer_id = #{consumerId}
		and activity_id = #{activityId}
		and app_id = #{appId}
	</update>

	<update id="updateUserInfo" parameterType="FriendPointLogEntity">
		update tb_zz_fri_point set gmt_modified = now()
		<if test="isOldUser != null">
			,is_old_user = #{isOldUser}
		</if>
		<if test="isHelped != null ">
			,is_helped = #{isHelped}
		</if>
		where consumer_id = #{consumerId}
		and activity_id = #{activityId}
		and app_id = #{appId}
	</update>

	<update id="updateOldHelp" parameterType="FriendPointLogEntity">
		update tb_zz_fri_point set old_helped = (old_helped + #{oldHelped})
		where consumer_id = #{consumerId}
		and activity_id = #{activityId}
		and app_id = #{appId}
	</update>

</mapper>