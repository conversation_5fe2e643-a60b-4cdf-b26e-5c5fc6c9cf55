<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.duiba.activity.center.biz.dao.luckycode.impl.LuckyCodeConfigDaoImpl">

    <sql id="BaseColumn">
        id,
        app_id as appId,
        op_id as opId,
        title,
        activity_rule as activityRule,
        start_time as startTime,
        end_time as endTime,
        small_image as smallImage,
        banner_image as bannerImage,
        open_credits as openCredits,
        update_credits as updateCredits,
        update_limit as updateLimit,
        draw_prize_total_limit as drawPrizeTotalLimit,
        winner_total_limit as winnerTotalLimit,
        draw_people_total as drawPeopleTotal
    </sql>

    <insert id="insert" parameterType="LuckyCodeConfigEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_lucky_code_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">app_id,</if>
            <if test="opId != null"> op_id, </if>
            <if test="title != null"> title, </if>
            <if test="activityRule != null"> activity_rule, </if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="smallImage != null"> small_image, </if>
            <if test="bannerImage != null"> banner_image, </if>
            <if test="openCredits != null"> open_credits, </if>
            <if test="updateCredits != null"> update_credits, </if>
            <if test="updateLimit != null"> update_limit, </if>
            <if test="drawPrizeTotalLimit != null"> draw_prize_total_limit, </if>
            <if test="winnerTotalLimit != null"> winner_total_limit, </if>
            <if test="drawPeopleTotal != null"> draw_people_total, </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">#{appId},</if>
            <if test="opId != null"> #{opId}, </if>
            <if test="title != null"> #{title}, </if>
            <if test="activityRule != null"> #{activityRule}, </if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="smallImage != null"> #{smallImage}, </if>
            <if test="bannerImage != null"> #{bannerImage}, </if>
            <if test="openCredits != null"> #{openCredits}, </if>
            <if test="updateCredits != null"> #{updateCredits}, </if>
            <if test="updateLimit != null"> #{updateLimit}, </if>
            <if test="drawPrizeTotalLimit != null"> #{drawPrizeTotalLimit}, </if>
            <if test="winnerTotalLimit != null"> #{winnerTotalLimit}, </if>
            <if test="drawPeopleTotal != null"> #{drawPeopleTotal}, </if>
        </trim>
    </insert>

    <update id="update" parameterType="LuckyCodeConfigEntity">
        UPDATE tb_lucky_code_config
        <set>
            <if test="title != null"> title = #{title}, </if>
            <if test="activityRule != null"> activity_rule = #{activityRule}, </if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="smallImage != null"> small_image = #{smallImage}, </if>
            <if test="bannerImage != null"> banner_image = #{bannerImage}, </if>
            <if test="openCredits != null"> open_credits = #{openCredits}, </if>
            <if test="updateCredits != null"> update_credits = #{updateCredits}, </if>
            <if test="updateLimit != null"> update_limit = #{updateLimit}, </if>
            <if test="drawPrizeTotalLimit != null"> draw_prize_total_limit = #{drawPrizeTotalLimit}, </if>
            <if test="winnerTotalLimit != null"> winner_total_limit = #{winnerTotalLimit}, </if>
            <if test="drawPeopleTotal != null"> draw_people_total = #{drawPeopleTotal}, </if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="long" resultType="LuckyCodeConfigEntity">
        select <include refid="BaseColumn"/>
        from tb_lucky_code_config where id = #{id} and deleted = 0
    </select>

    <select id="findValid" resultType="LuckyCodeConfigEntity">
        select <include refid="BaseColumn"/>
        from tb_lucky_code_config
        where now() >= start_time  and end_time >= now() and deleted = 0
    </select>

    <update id="deleteById" parameterType="long">
        update tb_lucky_code_config set deleted = 1 where id = #{id}
    </update>

    <update id="updateOpIdById" parameterType="map">
        update tb_lucky_code_config set op_id = #{opId}
        where id = #{id}
    </update>

    <select id="findIdByOpId" parameterType="long" resultType="long">
        select id from tb_lucky_code_config where op_id = #{opId} and deleted = 0
    </select>

    <select id="findIdsByOpIds" parameterType="list" resultType="long">
        SELECT id FROM tb_lucky_code_config
        WHERE op_id in
        <foreach collection="opIdList" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
        and deleted = 0
    </select>

    <update id="deleteByIds" parameterType="list">
        UPDATE tb_lucky_code_config SET deleted = 1
        WHERE id in
        <foreach collection="ids" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
    </update>

    <select id="findByOpId" resultType="LuckyCodeConfigEntity" parameterType="long">
        select <include refid="BaseColumn"/>
        from tb_lucky_code_config
        where op_id = #{opId} and deleted = 0
    </select>

    <select id="findDrawPeopleTotal" parameterType="long" resultType="integer">
        select draw_people_total from tb_lucky_code_config where id = #{id}
    </select>

    <update id="addDrawPeopleTotal" parameterType="long">
        update tb_lucky_code_config set draw_people_total = draw_people_total + 1
        where id = #{id}
    </update>
</mapper>