<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.activity.center.biz.dao.diggold.impl.DigGoldPrizeDaoImpl">
    <resultMap id="BaseResultMap" type="cn.com.duiba.activity.center.biz.entity.diggold.DigGoldPrizeEntity">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="config_id" property="configId"/>
        <result column="app_item_id" property="appItemId"/>
        <result column="prize_type" property="type"/>
        <result column="prize_name" property="name"/>
        <result column="prize_value" property="value"/>
        <result column="img" property="img"/>
        <result column="title" property="title"/>
        <result column="probability" property="probability"/>
        <result column="remaind" property="remaind"/>
        <result column="has_limit" property="hasLimit"/>
        <result column="limit_count" property="limitCount"/>
        <result column="has_low" property="hasLow"/>
        <result column="low" property="low"/>
    </resultMap>

    <sql id="AllColumn">
        id, app_id, config_id, app_item_id, prize_type, prize_name, prize_value, img, title,
        probability, remaind, has_limit, limit_count,has_low,low
    </sql>

    <insert id="batchInsertPrizes" parameterType="cn.com.duiba.activity.center.biz.entity.diggold.DigGoldPrizeEntity">
        INSERT INTO tb_dig_gold_prize
        (app_id,
        config_id,
        app_item_id,
        prize_type,
        prize_name,
        prize_value,
        img,
        title,
        probability,
        remaind,
        has_limit,
        limit_count,
        has_low,
        low,
        is_delete)
        values (
        #{appId},
        #{configId},
        #{appItemId},
        #{type},
        #{name},
        #{value},
        #{img},
        #{title},
        #{probability},
        #{remaind},
        #{hasLimit},
        #{limitCount},
        #{hasLow},
        #{low},
        0
        )
    </insert>

    <update id="updateToDelete" >
        UPDATE tb_dig_gold_prize SET is_delete = 1 WHERE config_id = #{id}
    </update>

    <select id="listPrizes" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="AllColumn"/>
        from tb_dig_gold_prize
        WHERE is_delete = 0
        AND config_id = #{id}
    </select>

    <select id="getPrizeById" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="AllColumn"/>
        FROM tb_dig_gold_prize
        WHERE id = #{id}
    </select>

    <select id="listPrizeByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="AllColumn"/>
        FROM tb_dig_gold_prize
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="CASDecreasePrizeRemind">
        UPDATE tb_dig_gold_prize SET remaind = #{resultValue} WHERE id = #{id} AND remaind = #{expectedValue}
    </update>


    <!-- 更新商家编码 -->
    <update id="updateDigGoldPrizeMerchantCode">
        update tb_dig_gold_prize set prize_value = #{newMerchantCode}
            where config_id = #{activityId} and app_item_id = #{appItemId} and prize_type = 'virtual'
    </update>

</mapper>