<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.equity.impl.EquityMerchantEmployeeDaoImpl">

    <resultMap type="cn.com.duiba.activity.center.biz.entity.equity.EquityMerchantEmployeeEntity" id="equityMerchantEmployeeMap">
        <result property="id" column="id"/>
        <result property="appId" column="app_id"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="employeeCode" column="employee_code"/>
        <result property="employeeMobile" column="employee_mobile"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="columns">
        id,
        app_id,
        merchant_id,
        employee_code,
        employee_mobile,
        gmt_create,
        gmt_modified
    </sql>

    <!-- 批量更新question_category -->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            UPDATE tb_equity_merchant_employee
            <set>
                <if test="item.appId != null">
                    app_id = #{item.appId},
                </if>
                <if test="item.merchantId != null">
                    merchant_id = #{item.merchantId},
                </if>
                <if test="item.employeeCode != null">
                    employee_code = #{item.employeeCode},
                </if>
                <if test="item.employeeMobile != null">
                    employee_mobile = #{item.employeeMobile}
                </if>
            </set>
            WHERE id=#{item.id}
        </foreach>
    </update>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO tb_equity_merchant_employee(app_id,merchant_id,employee_code,employee_mobile)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.appId},
            #{item.merchantId},
            #{item.employeeCode},
            #{item.employeeMobile}
            )
        </foreach>
    </insert>

    <delete id="deleteBatchByIds">
        DELETE FROM tb_equity_merchant_employee WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectByMid" resultMap="equityMerchantEmployeeMap">
        SELECT <include refid="columns"/>
        FROM tb_equity_merchant_employee
        WHERE app_id=#{appId} and merchant_id=#{mid}
    </select>

    <select id="listByIds" resultMap="equityMerchantEmployeeMap">
        SELECT <include refid="columns"/>
        FROM tb_equity_merchant_employee
        WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByMids" resultMap="equityMerchantEmployeeMap">
        SELECT <include refid="columns"/>
        FROM tb_equity_merchant_employee
        WHERE
        app_id=#{appId}
        and
        merchant_id IN
        <foreach item="item" index="index" collection="merchantIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>