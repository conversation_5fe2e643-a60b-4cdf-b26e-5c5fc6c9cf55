<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.duiba.activity.center.biz.dao.richman.impl.RichManPieceDaoImpl">
    <resultMap id="piece" type="cn.com.duiba.activity.center.biz.entity.richman.RichManPieceEntity">
        <id column="id" property="id"/>
        <result column="operating_activity_id" property="operatingActivityId"/>
        <result column="consumer_id" property="consumerId"/>
        <result column="current_point" property="currentPoint"/>
        <result column="credits_cost" property="creditsCost"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <sql id="AllColumn">
    id, operating_activity_id, consumer_id, current_point, credits_cost, gmt_create, gmt_modified
    </sql>

    <select id="findById" parameterType="long" resultMap="piece">
        select <include refid="AllColumn"/>
        from tb_richman_piece
        where id = #{id}
    </select>

    <select id="findByOperatingActivityIdAndConsumerId" parameterType="map" resultMap="piece">
        select <include refid="AllColumn"/>
        from tb_richman_piece
        where operating_activity_id = #{operatingActivityId}
        and consumer_id = #{consumerId}
    </select>

    <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.richman.RichManPieceEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_richman_piece
        (operating_activity_id, consumer_id, current_point, credits_cost)
        values (#{operatingActivityId}, #{consumerId}, #{currentPoint}, #{creditsCost})
    </insert>

    <update id="increaseCreditsCost" parameterType="cn.com.duiba.activity.center.biz.entity.happycodenew.HappyCodeConfigEntity">
        update tb_richman_piece
        set credits_cost = credits_cost + #{delta}
        where id = #{id}
    </update>

    <update id="changeLocation" parameterType="map">
        UPDATE tb_richman_piece
        SET current_point = #{newLocation}
        WHERE id = #{id}
    </update>

    <update id="changeLocationByAidCid" parameterType="map">
        UPDATE tb_richman_piece
        SET current_point = #{newLocation}
        WHERE operating_activity_id = #{operatingActivityId}
        and consumer_id = #{consumerId}
    </update>
</mapper>