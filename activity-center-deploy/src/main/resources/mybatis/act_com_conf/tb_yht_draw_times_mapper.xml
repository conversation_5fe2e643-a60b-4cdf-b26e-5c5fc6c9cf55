<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.duiba.activity.center.biz.dao.yht.impl.YhtDrawTimesDaoImpl">
    
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_id, op_id, consumer_id, validity_type, draw_times, expire_time, gmt_create, gmt_modified
    </sql>

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="cn.com.duiba.activity.center.biz.entity.yht.YhtDrawTimesEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="app_id" property="appId" jdbcType="BIGINT"/>
        <result column="op_id" property="opId" jdbcType="BIGINT"/>
        <result column="consumer_id" property="consumerId" jdbcType="BIGINT"/>
        <result column="validity_type" property="validityType" jdbcType="VARCHAR"/>
        <result column="draw_times" property="drawTimes" jdbcType="BIGINT"/>
        <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP"/>
        <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 新增抽奖次数 -->
    <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.yht.YhtDrawTimesEntity">
        INSERT INTO tb_yht_draw_times (
            app_id, op_id, consumer_id, validity_type, draw_times, expire_time
        ) VALUES (
            #{appId}, #{opId}, #{consumerId}, #{validityType}, #{drawTimes}, #{expireTime}
        )
    </insert>

    <!-- 查询用户有效的抽奖次数 -->
    <select id="queryValidDrawTimes" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_yht_draw_times
        WHERE app_id = #{appId}
        AND op_id = #{opId}
        AND consumer_id = #{consumerId}
        AND expire_time > NOW()
    </select>

    <!-- 查询用户当日有效的抽奖次数 -->
    <select id="queryValidDayDrawTimes" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_yht_draw_times
        WHERE app_id = #{appId}
        AND op_id = #{opId}
        AND consumer_id = #{consumerId}
        AND validity_type = 'day'
        AND expire_time > NOW()
        ORDER BY expire_time ASC
    </select>

    <!-- 查询用户永久有效的抽奖次数 -->
    <select id="queryValidForeverDrawTimes" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_yht_draw_times
        WHERE app_id = #{appId}
        AND op_id = #{opId}
        AND consumer_id = #{consumerId}
        AND validity_type = 'forever'
        AND expire_time > NOW()
        ORDER BY expire_time ASC
    </select>

    <!-- 更新抽奖次数 -->
    <update id="updateDrawTimes">
        UPDATE tb_yht_draw_times
        SET draw_times = #{drawTimes},
            gmt_modified = NOW()
        WHERE id = #{id}
    </update>

</mapper> 