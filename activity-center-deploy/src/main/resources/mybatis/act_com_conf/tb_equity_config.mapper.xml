<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.equity.impl.EquityConfigDaoImpl">

    <resultMap type="cn.com.duiba.activity.center.biz.entity.equity.EquityConfigEntity" id="equityConfigMap">
        <result property="id" column="id"/>
        <result property="appId" column="app_id"/>
        <result property="name" column="equity_name"/>
        <result property="type" column="equity_type"/>
        <result property="image" column="image"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="appItemIds" column="app_item_ids"/>
        <result property="deleted" column="deleted"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="equityCode" column="equity_code"/>
        <result property="equityDesc" column="equity_desc"/>
        <result property="verificationType" column="verification_type"/>
        <result property="equitySubType" column="equity_sub_type"/>
        <result property="stationIds" column="station_ids"/>
        <result property="classification" column="classification"/>
        <result property="generalFlag" column="general_flag"/>
        <result property="merchantIds" column="merchant_ids"/>
        <result property="customSubTypeSwitch" column="custom_sub_type_switch"/>
        <result property="verificationLimit" column="verification_limit"/>
        <result property="extraInfo" column="extra_info"/>
    </resultMap>

    <sql id="columns">
        id,
        app_id,
        equity_name,
        equity_type,
        image,
        start_time,
        end_time,
        app_item_ids,
        deleted,
        gmt_create,
        gmt_modified,
        equity_code,
        equity_desc,
        verification_type,
        equity_sub_type,
        station_ids,
        classification,
        general_flag,
        merchant_ids,
        custom_sub_type_switch,
        verification_limit,
        extra_info
    </sql>

    <insert id="save" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.biz.entity.equity.EquityConfigEntity">
        insert into tb_equity_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">app_id,</if>
            <if test="name != null">equity_name,</if>
            <if test="type != null">equity_type,</if>
            <if test="image != null">image,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="appItemIds != null">app_item_ids,</if>
            <if test="equityCode != null">equity_code,</if>
            <if test="equityDesc != null">equity_desc,</if>
            <if test="verificationType != null">verification_type,</if>
            <if test="equitySubType != null">equity_sub_type,</if>
            <if test="stationIds != null">station_ids,</if>
            <if test="classification != null">classification,</if>
            <if test="generalFlag != null">general_flag,</if>
            <if test="merchantIds != null">merchant_ids,</if>
            <if test="customSubTypeSwitch != null">custom_sub_type_switch,</if>
            <if test="verificationLimit != null">verification_limit,</if>
            <if test="extraInfo != null">extra_info,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">#{appId},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="image != null">#{image},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="appItemIds != null">#{appItemIds},</if>
            <if test="equityCode != null">#{equityCode},</if>
            <if test="equityDesc != null">#{equityDesc},</if>
            <if test="verificationType != null">#{verificationType},</if>
            <if test="equitySubType != null">#{equitySubType},</if>
            <if test="stationIds != null">#{stationIds},</if>
            <if test="classification != null">#{classification},</if>
            <if test="generalFlag != null">#{generalFlag},</if>
            <if test="merchantIds != null">#{merchantIds},</if>
            <if test="customSubTypeSwitch != null">#{customSubTypeSwitch},</if>
            <if test="verificationLimit != null">#{verificationLimit},</if>
            <if test="extraInfo != null">#{extraInfo},</if>
        </trim>
    </insert>

    <update id="deleteById">
        update tb_equity_config set deleted = 1 WHERE id=#{id}
    </update>

    <update id="deleteBatchByIds">
        update tb_equity_config set deleted = 1 WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateById" parameterType="cn.com.duiba.activity.center.biz.entity.equity.EquityConfigEntity">
        UPDATE tb_equity_config
        <set>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="name != null">
                equity_name = #{name},
            </if>
            <if test="type != null">
                equity_type = #{type},
            </if>
            <if test="image != null">
                image = #{image},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="appItemIds != null">
                app_item_ids = #{appItemIds},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            <if test="equityCode != null">
                equity_code = #{equityCode},
            </if>
            <if test="equityDesc != null">
                equity_desc = #{equityDesc},
            </if>
            <if test="verificationType != null">
                verification_type = #{verificationType},
            </if>
            <if test="equitySubType != null">
                equity_sub_type = #{equitySubType},
            </if>
            <if test="stationIds != null">
                station_ids = #{stationIds},
            </if>
            <if test="classification != null">
                classification = #{classification},
            </if>
            <if test="generalFlag != null">
                general_flag = #{generalFlag},
            </if>
            <if test="merchantIds != null">
                merchant_ids = #{merchantIds},
            </if>
            <if test="customSubTypeSwitch != null">
                custom_sub_type_switch = #{customSubTypeSwitch},
            </if>
            <if test="verificationLimit != null">
                verification_limit = #{verificationLimit},
            </if>
            <if test="extraInfo != null">
                extra_info = #{extraInfo},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="equityConfigMap">
        SELECT <include refid="columns"/>
        FROM tb_equity_config
        WHERE id = #{id}
        and deleted = 0
    </select>

    <select id="selectByName" resultMap="equityConfigMap">
        SELECT <include refid="columns"/>
        FROM tb_equity_config
        WHERE app_id = #{appId}
        and equity_name = #{name}
    </select>

    <select id="selectByLikeName" resultMap="equityConfigMap">
        SELECT <include refid="columns"/>
        FROM tb_equity_config
        WHERE app_id = #{appId}
        and equity_name like CONCAT('%',#{name},'%')
    </select>

    <select id="listByIds" resultMap="equityConfigMap">
        SELECT <include refid="columns"/>
        FROM tb_equity_config
        WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findPageByConditions" parameterType="map" resultMap="equityConfigMap">
        select
        <include refid="columns" />
        from tb_equity_config
        where app_id = #{appId}
        and deleted = 0
        <include refid="condition" />
        order by gmt_create desc
        limit #{offset},#{max}
    </select>

    <select id="findPageCountByConditions" parameterType="map" resultType="Long">
        select
        count(1)
        from tb_equity_config
        where app_id = #{appId}
        and deleted = 0
        <include refid="condition" />
    </select>

    <sql id="condition">
        <if test="name != null and name != ''">
            and equity_name like CONCAT('%',#{name},'%')
        </if>
        <if test="type != null">
            and equity_type = #{type}
        </if>

        <if test="verificationType != null">
            and verification_type = #{verificationType}
        </if>


    </sql>

    <select id="selectByEquityCodes" resultMap="equityConfigMap">
        SELECT <include refid="columns"/>
        FROM tb_equity_config
        WHERE app_id = #{appId}
        and deleted = 0
        and equity_code IN
        <foreach item="item" index="index" collection="equityCodes" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByClassificationAndIds" resultMap="equityConfigMap">
        SELECT <include refid="columns"/>
        FROM tb_equity_config
        WHERE app_id = #{appId}
        <if test="ids != null">
            and id in
            <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="classification != null">
            and classification = #{classification}
        </if>
    </select>

    <select id="selectByEquityTypes" resultMap="equityConfigMap">
        SELECT <include refid="columns"/>
        FROM tb_equity_config
        WHERE app_id = #{appId}
        and verification_type = #{verificationType}
        and equity_type IN
        <foreach item="item" index="index" collection="types" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectGeneralList" resultMap="equityConfigMap">
        SELECT <include refid="columns"/>
        FROM tb_equity_config
        WHERE app_id = #{appId}
        and general_flag = 1 and deleted = 0
    </select>

    <select id="selectExpiredEquityByParam" resultMap="equityConfigMap">
        select <include refid="columns"/>
        from tb_equity_config
        where app_id = #{appId}
        and verification_type = #{verificationType}
        and equity_type = #{equityType}
        and deleted = 0
        and <![CDATA[ end_time <= #{endDay} ]]>
        and <![CDATA[ end_time >= #{startDay} ]]>
    </select>

    <select id="selectByAppIdAndAppItemIds" resultMap="equityConfigMap" parameterType="map">
        select <include refid="columns"/>
        from tb_equity_config
        where app_id = #{appId}
        AND deleted = 0
        AND <![CDATA[ end_time >=]]> now()
        AND
        <foreach collection="appItemIds" item="item" open="(" close=")" separator="or">
            find_in_set(#{item}, app_item_ids)
        </foreach>
    </select>


    <select id="listByAppId" resultMap="equityConfigMap">
        SELECT <include refid="columns"/>
        FROM tb_equity_config
        WHERE app_id = #{appId}
        and deleted = 0
    </select>
</mapper>