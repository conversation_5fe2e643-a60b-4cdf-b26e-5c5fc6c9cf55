<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.hxlc.impl.HxlcActivityCodeMapperImpl">

    <resultMap id="BaseResultMap" type="cn.com.duiba.activity.center.biz.entity.hxlc.TbHxlcActivityCodeEntity">
        <!-- 映射表中的列和实体类的属性 -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="code_status" property="codeStatus" jdbcType="TINYINT"/>
        <result column="bind_phone" property="bindPhone" jdbcType="VARCHAR" />
        <result column="op_id" property="opId" jdbcType="BIGINT" />
        <result column="gen_phone" property="genPhone" jdbcType="VARCHAR"/>
        <result column="merchant_name" property="merchantName" jdbcType="VARCHAR" />
        <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP"/>
        <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    </resultMap>

    <!-- 1. Batch insert -->
    <insert id="insertBatch" parameterType="list">
        INSERT INTO tb_hxlc_activity_code
        (code, bind_phone, op_id, gen_phone, merchant_name)
        VALUES
        <foreach collection="codes" item="item" separator=",">
            (#{item.code}, #{item.bindPhone}, #{item.opId}, #{item.genPhone},#{item.merchantName})
        </foreach>
    </insert>

    <!-- 2. Update by conditions -->
    <update id="updateByConditions" parameterType="cn.com.duiba.activity.center.biz.entity.hxlc.TbHxlcActivityCodeEntity">
        UPDATE tb_hxlc_activity_code
        <set>
            <if test="codeStatus != null"> code_status = #{codeStatus}, </if>
            <if test="bindPhone != null"> bind_phone = #{bindPhone}, </if>
            <if test="genPhone != null"> gen_phone = #{genPhone}, </if>
            <if test="merchantName != null"> merchant_name = #{merchantName}, </if>
            <if test="gmtModified != null"> gmt_modified = #{gmtModified}, </if>
        </set>
        WHERE code = #{code}
    </update>

    <!-- 3. Find by code -->
    <select id="findByCode" resultType="cn.com.duiba.activity.center.biz.entity.hxlc.TbHxlcActivityCodeEntity">
        SELECT * FROM tb_hxlc_activity_code WHERE code = #{code}
    </select>

    <!-- 3. Find by code -->
    <select id="findByCodes" parameterType="Map" resultType="cn.com.duiba.activity.center.biz.entity.hxlc.TbHxlcActivityCodeEntity">
        SELECT * FROM tb_hxlc_activity_code WHERE code in
        <foreach collection="codes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 4. Find by gen_phone and op_id -->
    <select id="findByGenPhoneAndOpId" resultType="cn.com.duiba.activity.center.biz.entity.hxlc.TbHxlcActivityCodeEntity">
        SELECT * FROM tb_hxlc_activity_code
        WHERE gen_phone = #{genPhone} AND op_id = #{opId} order by id desc
    </select>

    <!-- 5. Count by merchant_name -->
    <select id="countByMerchantNameAndAct" resultType="int">
        SELECT COUNT(*) FROM tb_hxlc_activity_code WHERE merchant_name = #{merchantName}  AND op_id = #{opId}
    </select>

</mapper>
