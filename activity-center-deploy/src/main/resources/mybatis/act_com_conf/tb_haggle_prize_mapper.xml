<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.activity.center.biz.dao.haggle.impl.HagglePrizeDaoImpl">
    <resultMap id="BaseResultMap" type="cn.com.duiba.activity.center.biz.entity.haggle.HagglePrizeEntity">
        <id column="id" property="id"/>
        <result column="config_id" property="configId"/>
        <result column="app_id" property="appId"/>
        <result column="app_item_id" property="appItemId"/>
        <result column="prize_name" property="prizeName"/>
        <result column="prize_price" property="prizePrice"/>
        <result column="help_num" property="helpNum"/>
        <result column="prize_num" property="prizeNum"/>
        <result column="prize_image" property="prizeImage"/>
        <result column="expire_hour" property="expireHour"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="bargain_ceiling" property="bargainCeiling"/>
    </resultMap>

    <sql id="AllColumn">
        id, config_id, app_id, app_item_id, prize_name, prize_price, help_num, prize_num, prize_image,
        expire_hour, start_time, end_time,bargain_ceiling
    </sql>

    <insert id="batchInsertPrizes" parameterType="cn.com.duiba.activity.center.biz.entity.haggle.HagglePrizeEntity">
        INSERT INTO tb_haggle_prize
        (config_id,
        app_id,
        app_item_id,
        prize_name,
        prize_price,
        help_num,
        prize_num,
        prize_image,
        expire_hour,
        start_time,
        end_time,
        bargain_ceiling)
        VALUES
        <foreach collection="list" item="it" separator=",">
            (#{it.configId},#{it.appId},#{it.appItemId},#{it.prizeName},#{it.prizePrice},#{it.helpNum},#{it.prizeNum},#{it.prizeImage},#{it.expireHour},#{it.startTime},#{it.endTime},#{it.bargainCeiling})
        </foreach>
    </insert>

    <delete id="deletePrizeByConfigId">
        DELETE FROM tb_haggle_prize WHERE config_id = #{configId}
    </delete>

    <select id="getPrizeById" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="AllColumn"/>
        FROM tb_haggle_prize
        WHERE id = #{id}
    </select>

    <select id="listPrizeByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="AllColumn"/>
        FROM tb_haggle_prize
        WHERE id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="listPrizeByConfigId" resultMap="BaseResultMap">
        SELECT
        <include refid="AllColumn"/>
        FROM tb_haggle_prize
        WHERE config_id = #{configId}
    </select>




</mapper>