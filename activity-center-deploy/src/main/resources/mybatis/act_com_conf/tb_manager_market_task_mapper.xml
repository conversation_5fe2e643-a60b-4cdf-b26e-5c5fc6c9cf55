<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.activity.center.biz.dao.managermarket.impl.ManagerMarketTaskDaoImpl">
    <resultMap id="BaseResultMap" type="cn.com.duiba.activity.center.biz.entity.managermarket.ManagerMarketTaskEntity">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="config_id" property="configId"/>
        <result column="identification" property="identification"/>
        <result column="task_type" property="type"/>
        <result column="task_name" property="name"/>
        <result column="task_context" property="context"/>
        <result column="task_image" property="image"/>
        <result column="staff_exclusive_url" property="staffExclusiveUrl"/>
        <result column="activity_url" property="activityUrl"/>
        <result column="share_image" property="shareImage"/>
        <result column="share_title" property="shareTitle"/>
        <result column="share_subtitle" property="shareSubtitle"/>
        <result column="wx_friend_score" property="wxFriendScore"/>
        <result column="wx_circle_score" property="wxCircleScore"/>
        <result column="url_opened_score" property="urlOpenedScore"/>
        <result column="binded_score" property="bindedScore"/>
        <result column="limit_score" property="limitScore"/>
        <result column="end_time" property="endTime"/>
        <result column="must_task_switch" property="mustTaskSwitch"/>
        <result column="topping_switch" property="toppingSwitch"/>
        <result column="topping_time" property="toppingTime"/>
    </resultMap>

    <sql id="AllColumn">
        id, app_id, config_id, identification, task_type, task_name, task_context, task_image, staff_exclusive_url,
        activity_url, share_image, share_title, share_subtitle, wx_friend_score,
        wx_circle_score, url_opened_score, binded_score, limit_score, end_time,
        must_task_switch, topping_switch, topping_time
    </sql>

    <insert id="batchInsert" parameterType="cn.com.duiba.activity.center.biz.entity.managermarket.ManagerMarketTaskEntity"
        useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_manager_market_task
        (
        app_id,
        config_id,
        identification,
        task_type,
        task_name,
        task_context,
        task_image,
        staff_exclusive_url,
        activity_url,
        share_image,
        share_title,
        share_subtitle,
        wx_friend_score,
        wx_circle_score,
        url_opened_score,
        binded_score,
        limit_score,
        end_time,
        must_task_switch,
        topping_switch,
        topping_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.appId},
            #{item.configId},
            #{item.identification},
            #{item.type},
            #{item.name},
            #{item.context},
            #{item.image},
            #{item.staffExclusiveUrl},
            #{item.activityUrl},
            #{item.shareImage},
            #{item.shareTitle},
            #{item.shareSubtitle},
            #{item.wxFriendScore},
            #{item.wxCircleScore},
            #{item.urlOpenedScore},
            #{item.bindedScore},
            #{item.limitScore},
            #{item.endTime},
            #{item.mustTaskSwitch},
            #{item.toppingSwitch},
            #{item.toppingTime}
            )
        </foreach>
    </insert>

    <update id="updateTopping" parameterType="map">
        UPDATE tb_manager_market_task set topping_switch = #{toppingSwitch}, topping_time = #{toppingTime} WHERE id = #{id}
    </update>

    <select id="selectByConfigId" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="AllColumn"/>
        from tb_manager_market_task
        where config_id = #{configId}
    </select>

    <delete id="delete">
        DELETE FROM tb_manager_market_task WHERE config_id = #{configId}
    </delete>

    <select id="selectTaskByConfigAndIdentification" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="AllColumn"/>
        from tb_manager_market_task
        where config_id = #{configId}
        and identification = #{identification}
    </select>

</mapper>