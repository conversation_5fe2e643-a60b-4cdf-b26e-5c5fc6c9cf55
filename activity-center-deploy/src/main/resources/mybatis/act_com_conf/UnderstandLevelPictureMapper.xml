<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.understandlevel.impl.UnderstandLevelPictureDaoImpl">

    <resultMap type="cn.com.duiba.activity.center.biz.entity.understandlevel.UnderstandLevelPictureEntity"
               id="understandLevelPictureMap">
        <result property="id" column="id"/>
        <result property="configId" column="config_id"/>
        <result property="pictureIndex" column="picture_index"/>
        <result property="pictureName" column="picture_name"/>
        <result property="pictureImage" column="picture_image"/>
        <result property="pictureLevel" column="picture_level"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="columns">
        id,
        config_id,
        picture_index,
        picture_name,
        picture_image,
        picture_level,
        gmt_create,
        gmt_modified
    </sql>

    <select id="getById" resultMap="understandLevelPictureMap">
        SELECT
        <include refid="columns"/>
        FROM tb_understand_level_picture
        WHERE id = #{id}
    </select>


    <delete id="deleteByConfigId">
        DELETE FROM tb_understand_level_picture WHERE config_id=#{configId}
    </delete>


    <insert id="batchInsertSelective" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.biz.entity.understandlevel.UnderstandLevelPictureEntity">
        INSERT INTO tb_understand_level_picture
        (config_id,picture_index,picture_name,picture_image,picture_level)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.configId},#{item.pictureIndex},#{item.pictureName},#{item.pictureImage},#{item.pictureLevel})
        </foreach>

    </insert>


    <select id="listByConfigId" resultMap="understandLevelPictureMap">
        SELECT
        <include refid="columns"/>
        FROM tb_understand_level_picture
        WHERE config_id = #{configId}
    </select>

    <select id="getRelaPictureList" resultMap="understandLevelPictureMap">
        SELECT
        <include refid="columns"/>
        FROM tb_understand_level_picture
        WHERE config_id = #{configId}
        and picture_index IN
        <foreach item="item" index="index" collection="pictureIndexList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>