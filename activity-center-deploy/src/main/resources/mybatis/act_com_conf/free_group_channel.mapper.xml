<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.duiba.activity.center.biz.dao.freegroup.impl.FreeGroupChannelDaoImpl">
  <resultMap id="BaseResultMap" type="cn.com.duiba.activity.center.biz.entity.freegroup.FreeGroupChannelEntity">
    <id column="id" property="id"/>
    <result column="config_id" property="configId"/>
    <result column="channel_type" property="channelType"/>
    <result column="open_status" property="openStatus"/>
    <result column="participate_condition_type" property="participateConditionType"/>
    <result column="log_on_type" property="logOnType"/>
  </resultMap>

  <sql id="BaseColumn">
    id, config_id, channel_type, open_status, participate_condition_type, log_on_type
  </sql>

  <select id="selectByConfigId" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="BaseColumn"/>
    from tb_free_group_channel
    where config_id = #{configId}
  </select>

  <select id="selectById" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="BaseColumn"/>
    from tb_free_group_channel
    where id = #{id}
  </select>

  <insert id="batchInsert" parameterType="cn.com.duiba.activity.center.biz.entity.freegroup.FreeGroupChannelEntity">
    INSERT INTO tb_free_group_channel
    (config_id, channel_type, open_status, participate_condition_type, log_on_type)
    VALUES
    <foreach collection="list" separator="," item="entity">
      (#{entity.configId}, #{entity.channelType}, #{entity.openStatus}, #{entity.participateConditionType}, #{entity.logOnType})
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="cn.com.duiba.activity.center.biz.entity.freegroup.FreeGroupChannelEntity">
    <foreach collection="list" separator=";" item="entity">
      update tb_free_group_channel
      <set>
        <if test="entity.openStatus != null">open_status = #{entity.openStatus},</if>
        <if test="entity.participateConditionType != null">participate_condition_type = #{entity.participateConditionType},</if>
        <if test="entity.logOnType != null">log_on_type = #{entity.logOnType},</if>
      </set>
      where id = #{entity.id}
    </foreach>
  </update>
</mapper>