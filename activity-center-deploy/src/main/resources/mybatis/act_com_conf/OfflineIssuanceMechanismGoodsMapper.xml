<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.offlineissuance.impl.OfflineIssuanceMechanismGoodsDaoImpl">

    <resultMap type="cn.com.duiba.activity.center.biz.entity.offlineissuance.OfflineIssuanceMechanismGoodsEntity" id="offlineIssuanceMechanismGoodsMap">
        <result property="id" column="id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="appItemId" column="app_item_id"/>
        <result property="appItemName" column="app_item_name"/>
        <result property="mechanismNo" column="mechanism_no"/>
        <result property="deleted" column="deleted"/>
        <result property="appId" column="app_id"/>
    </resultMap>

    <sql id="columns">
        id,
        gmt_create,
        gmt_modified,
        app_item_id,
        app_item_name,
        mechanism_no,
        deleted,
        app_id
    </sql>

    <insert id="save" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.biz.entity.offlineissuance.OfflineIssuanceMechanismGoodsEntity">
        INSERT INTO act_com_conf.tb_offline_issuance_mechanism_goods(app_item_id,app_item_name,mechanism_no,deleted,app_id)
        VALUES(#{appItemId},#{appItemName},#{mechanismNo},#{deleted},#{appId})
    </insert>

    <delete id="deleteById">
        DELETE FROM act_com_conf.tb_offline_issuance_mechanism_goods WHERE id=#{id}
    </delete>

    <delete id="deleteBatchByIds">
        DELETE FROM act_com_conf.tb_offline_issuance_mechanism_goods WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <update id="updateById" parameterType="cn.com.duiba.activity.center.biz.entity.offlineissuance.OfflineIssuanceMechanismGoodsEntity">
        UPDATE act_com_conf.tb_offline_issuance_mechanism_goods
        <set>
            <if test="appItemId != null">
                app_item_id = #{appItemId},
            </if>
            <if test="appItemName != null">
                app_item_name = #{appItemName},
            </if>
            <if test="mechanismNo != null">
                mechanism_no = #{mechanismNo},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="getById" resultMap="offlineIssuanceMechanismGoodsMap">
        SELECT <include refid="columns"/>
        FROM act_com_conf.tb_offline_issuance_mechanism_goods
        WHERE id = #{id}
    </select>

    <select id="listByIds" resultMap="offlineIssuanceMechanismGoodsMap">
        SELECT <include refid="columns"/>
        FROM act_com_conf.tb_offline_issuance_mechanism_goods
        WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listByAppItemIds" resultMap="offlineIssuanceMechanismGoodsMap">
        SELECT <include refid="columns"/>
        FROM act_com_conf.tb_offline_issuance_mechanism_goods
        WHERE app_item_id IN
        <foreach item="item" index="index" collection="appItemIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and deleted = 0
    </select>

    <select id="count" resultType="java.lang.Integer">
        select count(*)
        from act_com_conf.tb_offline_issuance_mechanism_goods
        where mechanism_no = #{mechanismNo} and app_id = #{appId} and deleted = 0
    </select>

    <select id="listByMechanismNo" resultMap="offlineIssuanceMechanismGoodsMap">
        select <include refid="columns"/>
        from act_com_conf.tb_offline_issuance_mechanism_goods
        where mechanism_no = #{mechanismNo} and app_id = #{appId} and deleted = 0
        order by id desc
        limit #{offset}, #{pageSize}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO act_com_conf.tb_offline_issuance_mechanism_goods
        (app_item_id,app_item_name,mechanism_no,deleted,app_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.appItemId},
            #{item.appItemName},
            #{item.mechanismNo},
            #{item.deleted},
            #{item.appId}
            )
        </foreach>
    </insert>

    <select id="getByAppIdAndNoAndAppItemId" resultMap="offlineIssuanceMechanismGoodsMap">
        select <include refid="columns"/>
        from act_com_conf.tb_offline_issuance_mechanism_goods
        where mechanism_no = #{mechanismNo}
        and app_id = #{appId}
        and app_item_id = #{appItemId}
        and deleted = 0
    </select>



</mapper>