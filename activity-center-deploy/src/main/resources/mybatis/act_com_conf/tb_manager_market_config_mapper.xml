<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.activity.center.biz.dao.managermarket.impl.ManagerMarketConfigDaoImpl">
    <resultMap id="BaseResultMap" type="cn.com.duiba.activity.center.biz.entity.managermarket.ManagerMarketConfigEntity">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="op_id" property="opId"/>
        <result column="config_title" property="title"/>
        <result column="config_unit" property="unit"/>
        <result column="staff_excel_name" property="staffExcelName"/>
        <result column="staff_excel_url" property="staffExcelUrl"/>
        <result column="organ_excel_name" property="organExcelName"/>
        <result column="organ_excel_url" property="organExcelUrl"/>
        <result column="rank_switch" property="rankSwitch"/>
        <result column="rank_period" property="rankPeriod"/>
        <result column="rank_latitude" property="rankLatitude"/>
        <result column="channel_type" property="channelType"/>
    </resultMap>

    <sql id="AllColumn">
        id, app_id, op_id, config_title, config_unit, staff_excel_name, staff_excel_url,
        organ_excel_name, organ_excel_url, rank_switch, rank_period,rank_latitude,channel_type
    </sql>

    <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.managermarket.ManagerMarketConfigEntity"
        useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_manager_market_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">app_id,</if>
            <if test="opId != null">op_id,</if>
            <if test="title != null">config_title,</if>
            <if test="unit != null">config_unit,</if>
            <if test="staffExcelName != null">staff_excel_name,</if>
            <if test="staffExcelUrl != null">staff_excel_url,</if>
            <if test="organExcelName != null">organ_excel_name,</if>
            <if test="organExcelUrl != null">organ_excel_url,</if>
            <if test="rankSwitch != null">rank_switch,</if>
            <if test="rankPeriod != null">rank_period,</if>
            <if test="rankLatitude != null">rank_latitude,</if>
            <if test="channelType != null">channel_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">#{appId},</if>
            <if test="opId != null">#{opId},</if>
            <if test="title != null">#{title},</if>
            <if test="unit != null">#{unit},</if>
            <if test="staffExcelName != null">#{staffExcelName},</if>
            <if test="staffExcelUrl != null">#{staffExcelUrl},</if>
            <if test="organExcelName != null">#{organExcelName},</if>
            <if test="organExcelUrl != null">#{organExcelUrl},</if>
            <if test="rankSwitch != null">#{rankSwitch},</if>
            <if test="rankPeriod != null">#{rankPeriod},</if>
            <if test="rankLatitude != null">#{rankLatitude},</if>
            <if test="channelType != null">#{channelType},</if>
        </trim>
    </insert>

    <update id="update" parameterType="cn.com.duiba.activity.center.biz.entity.managermarket.ManagerMarketConfigEntity">
        update tb_manager_market_config
        <set>
            <if test="title != null">config_title = #{title},</if>
            <if test="unit != null">config_unit = #{unit},</if>
            <if test="staffExcelName != null">staff_excel_name = #{staffExcelName},</if>
            <if test="staffExcelUrl != null">staff_excel_url = #{staffExcelUrl},</if>
            <if test="organExcelName != null">organ_excel_name = #{organExcelName},</if>
            <if test="organExcelUrl != null">organ_excel_url = #{organExcelUrl},</if>
            <if test="rankSwitch != null">rank_switch = #{rankSwitch},</if>
            <if test="rankPeriod != null">rank_period = #{rankPeriod},</if>
            <if test="rankLatitude != null">rank_latitude = #{rankLatitude},</if>
            <if test="channelType != null">channel_type = #{channelType},</if>
        </set>
        where id = #{id}
    </update>

    <update id="updateOpId" parameterType="map">
        UPDATE tb_manager_market_config set op_id = #{opId} WHERE id = #{id}
    </update>

    <select id="selectById" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="AllColumn"/>
        from tb_manager_market_config
        where id = #{id}
    </select>

    <delete id="delete">
        DELETE FROM tb_manager_market_config WHERE id = #{id}
    </delete>


</mapper>