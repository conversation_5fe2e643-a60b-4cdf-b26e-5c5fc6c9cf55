<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.activity.center.biz.dao.haggle.impl.HaggleConfigDaoImpl">
    <resultMap id="BaseResultMap" type="cn.com.duiba.activity.center.biz.entity.haggle.HaggleConfigEntity">
        <id column="id" property="id"/>
        <result column="operating_activity_id" property="opId"/>
        <result column="app_id" property="appId"/>
        <result column="channel" property="channel"/>
        <result column="title" property="title"/>
        <result column="rule" property="rule"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="small_image" property="smallImage"/>
        <result column="banner_image" property="bannerImage"/>
        <result column="barrage_switch" property="barrageSwitch"/>
        <result column="help_user_type" property="helpUserType"/>
        <result column="help_limit" property="helpLimit"/>
        <result column="help_limit_count" property="helpLimitCount"/>
        <result column="open_limit" property="openLimit"/>
        <result column="open_limit_count" property="openLimitCount"/>
        <result column="new_user_rate" property="newUserRate"/>
        <result column="interface_config" property="interfaceConfig"/>
        <result column="qr_code" property="qrCode"/>
        <result column="share_icon" property="shareIcon"/>
        <result column="share_title" property="shareTitle"/>
        <result column="share_vice_title" property="shareViceTitle"/>
        <result column="callback_url" property="callbackUrl"/>
    </resultMap>

    <sql id="AllColumn">
        id, operating_activity_id, app_id, channel, title, rule, start_time, end_time, small_image, banner_image,
        barrage_switch, help_user_type, help_limit, help_limit_count,open_limit,open_limit_count,new_user_rate,interface_config,qr_code,
        share_icon, share_title, share_vice_title, callback_url,extra
    </sql>

    <insert id="insertConfig" parameterType="cn.com.duiba.activity.center.biz.entity.haggle.HaggleConfigEntity"
        useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_haggle_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">app_id,</if>
            <if test="opId != null">operating_activity_id,</if>
            <if test="channel != null">channel,</if>
            <if test="title != null">title,</if>
            <if test="rule != null">rule,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="smallImage != null">small_image,</if>
            <if test="bannerImage != null">banner_image,</if>
            <if test="barrageSwitch != null">barrage_switch,</if>
            <if test="helpUserType != null">help_user_type,</if>
            <if test="helpLimit != null">help_limit,</if>
            <if test="helpLimitCount != null">help_limit_count,</if>
            <if test="openLimit != null">open_limit,</if>
            <if test="openLimitCount != null">open_limit_count,</if>
            <if test="newUserRate != null">new_user_rate,</if>
            <if test="interfaceConfig != null">interface_config,</if>
            <if test="qrCode != null">qr_code,</if>
            <if test="shareIcon != null">share_icon,</if>
            <if test="shareTitle != null">share_title,</if>
            <if test="shareViceTitle != null">share_vice_title,</if>
            <if test="callbackUrl != null">callback_url,</if>
            <if test="extra != null">extra,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">#{appId},</if>
            <if test="opId != null">#{opId},</if>
            <if test="channel != null">#{channel},</if>
            <if test="title != null">#{title},</if>
            <if test="rule != null">#{rule},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="smallImage != null">#{smallImage},</if>
            <if test="bannerImage != null">#{bannerImage},</if>
            <if test="barrageSwitch != null">#{barrageSwitch},</if>
            <if test="helpUserType != null">#{helpUserType},</if>
            <if test="helpLimit != null">#{helpLimit},</if>
            <if test="helpLimitCount != null">#{helpLimitCount},</if>
            <if test="openLimit != null">#{openLimit},</if>
            <if test="openLimitCount != null">#{openLimitCount},</if>
            <if test="newUserRate != null">#{newUserRate},</if>
            <if test="interfaceConfig != null">#{interfaceConfig},</if>
            <if test="qrCode != null">#{qrCode},</if>
            <if test="shareIcon != null">#{shareIcon},</if>
            <if test="shareTitle != null">#{shareTitle},</if>
            <if test="shareViceTitle != null">#{shareViceTitle},</if>
            <if test="callbackUrl != null">#{callbackUrl},</if>
            <if test="extra != null">#{extra},</if>
        </trim>
    </insert>

    <update id="updateConfig" parameterType="cn.com.duiba.activity.center.biz.entity.haggle.HaggleConfigEntity">
        update tb_haggle_config
        <set>
            <if test="channel != null">channel = #{channel},</if>
            <if test="title != null">title = #{title},</if>
            <if test="rule != null">rule = #{rule},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="smallImage != null">small_image = #{smallImage},</if>
            <if test="bannerImage != null">banner_image = #{bannerImage},</if>
            <if test="barrageSwitch != null">barrage_switch = #{barrageSwitch},</if>
            <if test="helpUserType != null">help_user_type = #{helpUserType},</if>
            <if test="helpLimit != null">help_limit = #{helpLimit},</if>
            <if test="helpLimitCount != null">help_limit_count = #{helpLimitCount},</if>
            <if test="openLimit != null">open_limit = #{openLimit},</if>
            <if test="openLimitCount != null">open_limit_count = #{openLimitCount},</if>
            <if test="newUserRate != null">new_user_rate = #{newUserRate},</if>
            <if test="interfaceConfig != null">interface_config = #{interfaceConfig},</if>
            <if test="qrCode != null">qr_code = #{qrCode},</if>
            <if test="shareIcon != null">share_icon = #{shareIcon},</if>
            <if test="shareTitle != null">share_title = #{shareTitle},</if>
            <if test="shareViceTitle != null">share_vice_title = #{shareViceTitle},</if>
            <if test="callbackUrl != null">callback_url = #{callbackUrl},</if>
            <if test="extra != null">extra = #{extra},</if>
        </set>
        where id = #{id}
    </update>

    <update id="updateOpId" parameterType="map">
        UPDATE tb_haggle_config set operating_activity_id = #{opId} WHERE id = #{id}
    </update>

    <select id="getConfig" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="AllColumn"/>
        from tb_haggle_config
        where id = #{id}
    </select>

    <delete id="deleteConfig">
        DELETE FROM tb_haggle_config WHERE id = #{id}
    </delete>


</mapper>