<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.activity.center.biz.dao.equitycard.impl.EquityCardConfigDaoImpl">
    <resultMap id="BaseResultMap" type="cn.com.duiba.activity.center.biz.entity.equitycard.EquityCardConfigEntity">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="op_id" property="opId"/>
        <result column="activity_name" property="activityName"/>
        <result column="payment_amount" property="paymentAmount"/>
        <result column="red_packet_amount" property="redPacketAmount"/>
        <result column="original_amount" property="originalAmount"/>
        <result column="payment_item_id" property="paymentItemId"/>
        <result column="payment_item_name" property="paymentItemName"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="view_attributes" property="viewAttributes"/>
        <result column="publish" property="publish"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="base_attributes" property="baseAttributes"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , app_id, op_id, activity_name, payment_amount, red_packet_amount,original_amount,payment_item_id,payment_item_name, start_time, end_time, view_attributes, publish, deleted, gmt_create, gmt_modified, base_attributes
    </sql>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true" parameterType="cn.com.duiba.activity.center.biz.entity.equitycard.EquityCardConfigEntity">
        INSERT INTO tb_equity_card_config (app_id, op_id, activity_name, payment_amount,red_packet_amount,original_amount, payment_item_id,payment_item_name, start_time,
                                           end_time, view_attributes, publish, base_attributes)
        VALUES (#{appId}, #{opId}, #{activityName}, #{paymentAmount},#{redPacketAmount},#{originalAmount}, #{paymentItemId},#{paymentItemName}, #{startTime}, #{endTime},
                #{viewAttributes}, #{publish}, #{baseAttributes})
    </insert>

    <update id="updateById" parameterType="cn.com.duiba.activity.center.biz.entity.equitycard.EquityCardConfigEntity">
        UPDATE tb_equity_card_config
        SET app_id          = #{appId},
            activity_name   = #{activityName},
            payment_amount  = #{paymentAmount},
            red_packet_amount  = #{redPacketAmount},
            original_amount  = #{originalAmount},
            payment_item_id = #{paymentItemId},
            payment_item_name = #{paymentItemName},
            start_time      = #{startTime},
            end_time        = #{endTime},
            view_attributes = #{viewAttributes},
            publish         = #{publish},
            base_attributes = #{baseAttributes},
            op_id = #{opId}
        WHERE id = #{id}
    </update>


    <select id="selectById" resultMap="BaseResultMap" parameterType="Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_equity_card_config
        WHERE id = #{id}
            and deleted = 0
    </select>

    <select id="findByOpId" resultMap="BaseResultMap" parameterType="Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_equity_card_config
        WHERE op_id = #{opId}
        and deleted = 0
    </select>

</mapper>
