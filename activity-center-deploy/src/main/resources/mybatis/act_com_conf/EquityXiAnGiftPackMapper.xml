<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.equity.impl.EquityXiAnGiftPackDaoImpl">

    <resultMap type="cn.com.duiba.activity.center.biz.entity.equity.EquityXiAnGiftPackEntity"
               id="equityXiAnGiftPackMap">
        <result property="id" column="id"/>
        <result property="equityId" column="equity_id"/>
        <result property="packId" column="pack_id"/>
        <result property="packIndex" column="pack_index"/>
        <result property="appItemId" column="app_item_id"/>
        <result property="totalStock" column="total_stock"/>
        <result property="perNum" column="per_num"/>
        <result property="usedStock" column="used_stock"/>
        <result property="deleted" column="is_deleted"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="columns">
        id,
        equity_id,
        pack_id,
        pack_index,
        app_item_id,
        total_stock,
        per_num,
        used_stock,
        is_deleted,
        gmt_create,
        gmt_modified
    </sql>


    <select id="listByIds" resultMap="equityXiAnGiftPackMap">
        SELECT
        <include refid="columns"/>
        FROM tb_equity_xi_an_gift_pack
        WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listByEquityId" resultMap="equityXiAnGiftPackMap">
        SELECT
        <include refid="columns"/>
        FROM tb_equity_xi_an_gift_pack
        WHERE equity_id = #{equityId} and is_deleted = 0
    </select>

    <update id="deleteByEquityId">
        update tb_equity_xi_an_gift_pack set is_deleted = 1 WHERE equity_id=#{equityId}
    </update>


    <update id="deleteExcludedSomePack">
        update tb_equity_xi_an_gift_pack set is_deleted = 1 WHERE equity_id = #{equityId} and pack_id not IN
        <foreach item="item" index="index" collection="packIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <insert id="save" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.biz.entity.equity.EquityXiAnGiftPackEntity">
        INSERT INTO tb_equity_xi_an_gift_pack(equity_id,pack_id,pack_index,app_item_id,total_stock,per_num,used_stock,is_deleted)
        VALUES(#{equityId},#{packId},#{packIndex},#{appItemId},#{totalStock},#{perNum},#{usedStock},#{deleted})
    </insert>


    <select id="listByPackIds" resultMap="equityXiAnGiftPackMap">
        SELECT
        <include refid="columns"/>
        FROM tb_equity_xi_an_gift_pack
        WHERE pack_id IN
        <foreach item="item" index="index" collection="packIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_deleted = 0
    </select>


    <update id="deleteByPackIdExcludedSomeAppItem">
        update tb_equity_xi_an_gift_pack set is_deleted = 1 WHERE pack_id = #{packId} and id not IN
        <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="deleteByPackId">
        update tb_equity_xi_an_gift_pack set is_deleted = 1 WHERE pack_id = #{packId}
    </update>

    <update id="updateById" parameterType="cn.com.duiba.activity.center.biz.entity.equity.EquityXiAnGiftPackEntity">
        UPDATE tb_equity_xi_an_gift_pack
        <set>
            <if test="equityId != null">
                equity_id = #{equityId},
            </if>
            <if test="packId != null">
                pack_id = #{packId},
            </if>
            <if test="packIndex != null">
                pack_index = #{packIndex},
            </if>
            <if test="appItemId != null">
                app_item_id = #{appItemId},
            </if>
            <if test="totalStock != null">
                total_stock = #{totalStock},
            </if>
            <if test="perNum != null">
                per_num = #{perNum},
            </if>
            <if test="usedStock != null">
                used_stock = #{usedStock},
            </if>
            <if test="deleted != null">
                is_deleted = #{deleted},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="listByPackId" resultMap="equityXiAnGiftPackMap">
        SELECT
        <include refid="columns"/>
        FROM tb_equity_xi_an_gift_pack
        WHERE pack_id = #{packId}
        and is_deleted = 0
    </select>

    <select id="listByAppItemIds" resultMap="equityXiAnGiftPackMap">
        SELECT
        <include refid="columns"/>
        FROM tb_equity_xi_an_gift_pack
        WHERE app_item_id IN
        <foreach item="item" index="index" collection="appItemIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_deleted = 0
    </select>


    <update id="saveXiAnGiftPackStock">
        update tb_equity_xi_an_gift_pack set total_stock = #{totalStock}, per_num = #{perNum}
        where id = #{id} and used_stock <![CDATA[ <= ]]> #{totalStock}
    </update>

    <update id="useXiAnGiftPackStock">
        update tb_equity_xi_an_gift_pack set used_stock = used_stock + per_num
        where id = #{id} and used_stock + per_num <![CDATA[ <= ]]> total_stock
    </update>

    <select id="listByEquityIds" resultMap="equityXiAnGiftPackMap">
        SELECT
        <include refid="columns"/>
        FROM tb_equity_xi_an_gift_pack
        WHERE equity_id IN
        <foreach item="item" index="index" collection="equityIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_deleted = 0
    </select>


</mapper>