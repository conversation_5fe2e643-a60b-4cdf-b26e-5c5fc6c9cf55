<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.activityredpack.impl.LinkCouponBatchRecordDaoImpl">

    <resultMap type="cn.com.duiba.activity.center.biz.entity.alipayactivityredpack.LinkCouponBatchRecordEntity" id="linkCouponBatchRecordMap">
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="id" column="id"/>
        <result property="redpackActivityId" column="redpack_activity_id"/>
        <result property="batchCount" column="batch_count"/>
        <result property="admin" column="admin"/>
    </resultMap>

    <sql id="columns">
        gmt_create,
        gmt_modified,
        id,
        redpack_activity_id,
        batch_count,
        admin
    </sql>

    <sql id="condition">
        <where>
            <if test="redpackActivityId != null and redpackActivityId != ''">
                and redpack_activity_id = #{redpackActivityId}
            </if>
            <if test="admin != null">
                and admin = #{admin}
            </if>
        </where>
    </sql>

    <insert id="save" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.api.dto.alipayactivityredpack.LinkCouponBatchRecordDto">
        INSERT INTO tb_link_coupon_batch_record(redpack_activity_id,batch_count,admin)
        VALUES (#{redpackActivityId},#{batchCount},#{admin})
    </insert>

    <select id="queryByCondition" resultType="cn.com.duiba.activity.center.api.dto.alipayactivityredpack.LinkCouponBatchRecordDto">
        SELECT <include refid="columns"/>,
        (@i :=@i + 1) AS seq
        FROM tb_link_coupon_batch_record,
        (SELECT @i := 0) AS it
        <include refid="condition"></include>
    </select>

</mapper>