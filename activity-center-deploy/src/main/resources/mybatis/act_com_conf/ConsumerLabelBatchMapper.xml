<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.label.impl.ConsumerLabelBatchDaoImpl">

    <resultMap type="cn.com.duiba.activity.center.biz.entity.label.ConsumerLabelBatchEntity" id="consumerLabelBatchMap">
        <result property="id" column="id"/>
        <result property="appId" column="app_id"/>
        <result property="userLabelId" column="user_label_id"/>
        <result property="userBatchName" column="user_batch_name"/>
        <result property="createId" column="create_id"/>
        <result property="modifiedId" column="modified_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="columns">
        id,
        app_id,
        user_label_id,
        user_batch_name,
        create_id,
        modified_id,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="saveBatch" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.biz.entity.label.ConsumerLabelBatchEntity">
        INSERT INTO act_com_conf.tb_consumer_label_batch(app_id,user_label_id,user_batch_name,create_id,modified_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.appId},#{item.userLabelId},#{item.userBatchName},#{item.createId},#{item.modifiedId})
        </foreach>
    </insert>

    <insert id="save" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.biz.entity.label.ConsumerLabelBatchEntity">
        INSERT INTO act_com_conf.tb_consumer_label_batch(app_id,user_label_id,user_batch_name,create_id,modified_id)
        VALUES(#{appId},#{userLabelId},#{userBatchName},#{createId},#{modifiedId})
    </insert>

    <delete id="deleteById">
        DELETE FROM act_com_conf.tb_consumer_label_batch WHERE id=#{id}
    </delete>
    <delete id="delByLabelId">
        DELETE FROM act_com_conf.tb_consumer_label_batch WHERE user_label_id=#{labelId}
    </delete>

    <delete id="deleteBatchByIds">
        DELETE FROM act_com_conf.tb_consumer_label_batch WHERE id IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <update id="updateById" parameterType="cn.com.duiba.activity.center.biz.entity.label.ConsumerLabelBatchEntity">
        UPDATE act_com_conf.tb_consumer_label_batch
        <set>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="userLabelId != null">
                user_label_id = #{userLabelId},
            </if>
            <if test="userBatchName != null">
                user_batch_name = #{userBatchName},
            </if>
            <if test="createId != null">
                create_id = #{createId},
            </if>
            <if test="modifiedId != null">
                modified_id = #{modifiedId},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="getById" resultMap="consumerLabelBatchMap">
        SELECT <include refid="columns"/>
        FROM act_com_conf.tb_consumer_label_batch
        WHERE id = #{id}
    </select>

    <select id="listByIds" resultMap="consumerLabelBatchMap">
        SELECT <include refid="columns"/>
        FROM act_com_conf.tb_consumer_label_batch
        WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="getByLabelId" resultMap="consumerLabelBatchMap">
        SELECT <include refid="columns"/>
        FROM act_com_conf.tb_consumer_label_batch
        WHERE user_label_id = #{labelId} and app_id = #{appId}
    </select>



</mapper>