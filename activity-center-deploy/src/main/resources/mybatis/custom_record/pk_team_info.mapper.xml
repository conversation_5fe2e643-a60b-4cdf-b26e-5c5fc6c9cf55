<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.activity.center.biz.dao.shuqipk.impl.PkTeamInfoDaoImpl">

    <resultMap id="Result" type="PkTeamInfoEntity">
        <result property="id" column="id"/>
        <result property="teamName" column="team_name"/>
        <result property="appId" column="app_id"/>
        <result property="activityId" column="activity_id"/>
        <result property="memberNum" column="member_num"/>
        <result property="totalReadValue" column="total_read_value"/>
        <result property="gmtCreate" column="gmt_create"/>
    </resultMap>

    <sql id="all_column">
        id,team_name,app_id,activity_id,member_num,total_read_value,gmt_create
    </sql>

    <insert id="insert" parameterType="PkTeamInfoEntity" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO tb_pk_team_info (team_name, app_id, activity_id, member_num)
        VALUES (#{teamName}, #{appId}, #{activityId}, 0)
    </insert>

    <select id="selectById" parameterType="long" resultMap="Result">
        SELECT <include refid="all_column"/>
        FROM tb_pk_team_info
        WHERE id = #{id}
    </select>

    <select id="selectByTeamName" parameterType="string" resultMap="Result">
        SELECT <include refid="all_column"/>
        FROM tb_pk_team_info
        WHERE team_name = #{teamName}
    </select>

    <update id="updateReadValue" parameterType="map">
        UPDATE tb_pk_team_info
        SET total_read_value = total_read_value + #{delta}
        WHERE id = #{teamId}
    </update>

    <update id="batchUpdateReadValue" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE tb_pk_team_info
            <set>
                <if test="item.totalReadValue != null">total_read_value = total_read_value + #{item.totalReadValue},</if>
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>

    <update id="joinTeam" parameterType="long">
        UPDATE tb_pk_team_info
        SET member_num = member_num + 1
        WHERE id= #{id}
    </update>

    <select id="getTeamPage" parameterType="cn.com.duiba.api.bo.page.PageQuery" resultMap="Result">
        SELECT <include refid="all_column"/>
        FROM tb_pk_team_info
        WHERE member_num > 2
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="selectListByIds" parameterType="java.util.List" resultMap="Result">
        SELECT <include refid="all_column"/>
        FROM tb_pk_team_info
        WHERE id in 
        <foreach collection="list" item="teamId" index="index" separator="," open="(" close=")">
            #{teamId}
        </foreach>
    </select>
</mapper>
