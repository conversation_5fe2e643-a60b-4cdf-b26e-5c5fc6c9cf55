<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.activity.center.biz.dao.bargain.impl.BargainItemInfoDaoImpl">

    <!--auto generated Code-->
    <resultMap id="AllColumnMap" type="cn.com.duiba.activity.center.biz.entity.bargain.BargainItemInfoEntity">
        <result column="id" property="id"/>
        <result column="bargain_activity_id" property="bargainActivityId"/>
        <result column="item_id" property="itemId"/>
        <result column="bargain_rule_id" property="bargainRuleId"/>
        <result column="image_url" property="imageUrl"/>
        <result column="remaining" property="remaining"/>
        <result column="bargain_duration" property="bargainDuration"/>
        <result column="cheat_base" property="cheatBase"/>
        <result column="cheat_multiple" property="cheatMultiple"/>
        <result column="completed_count" property="completedCount"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="gmt_create" property="gmtCreate"/>
    </resultMap>

    <!--auto generated Code-->
    <sql id="all_column">
        id,
        bargain_activity_id,
        item_id,
        bargain_rule_id,
        image_url,
        remaining,
        bargain_duration,
        cheat_base,
        cheat_multiple,
        completed_count,
        deleted,
        gmt_modified,
        gmt_create
    </sql>

    <!--auto generated Code-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_bargain_item_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bargainActivityId != null">bargain_activity_id,</if>
            <if test="itemId != null">item_id,</if>
            <if test="bargainRuleId != null">bargain_rule_id,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="remaining != null">remaining,</if>
            <if test="bargainDuration != null">bargain_duration,</if>
            <if test="cheatBase != null">cheat_base,</if>
            <if test="cheatMultiple != null">cheat_multiple,</if>
            <if test="completedCount != null">completed_count,</if>
            <if test="deleted != null">deleted,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bargainActivityId != null">#{bargainActivityId},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="bargainRuleId != null">#{bargainRuleId},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="remaining != null">#{remaining},</if>
            <if test="bargainDuration != null">#{bargainDuration},</if>
            <if test="cheatBase != null">#{cheatBase},</if>
            <if test="cheatMultiple != null">#{cheatMultiple},</if>
            <if test="completedCount != null">#{completedCount},</if>
            <if test="deleted != null">#{deleted},</if>
        </trim>
    </insert>


    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_bargain_item_info
        (bargain_activity_id,
        item_id,
        bargain_rule_id,
        image_url,
        remaining,
        bargain_duration,
        cheat_base,
        cheat_multiple,
        completed_count,
        deleted)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.bargainActivityId},
            #{item.itemId},
            #{item.bargainRuleId},
            #{item.imageUrl},
            #{item.remaining},
            #{item.bargainDuration},
            #{item.cheatBase},
            #{item.cheatMultiple},
            #{item.completedCount},
            #{item.deleted})
        </foreach>
    </insert>

    <!--auto generated Code-->
    <update id="update">
        UPDATE tb_bargain_item_info
        <set>
            <if test="bargainActivityId != null">bargain_activity_id = #{bargainActivityId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="bargainRuleId != null">bargain_rule_id = #{bargainRuleId},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="remaining != null">remaining = #{remaining},</if>
            <if test="bargainDuration != null">bargain_duration = #{bargainDuration},</if>
            <if test="cheatBase != null">cheat_base = #{cheatBase},</if>
            <if test="cheatMultiple != null">cheat_multiple = #{cheatMultiple},</if>
            <if test="completedCount != null">completed_count=#{completedCount},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="listByParam" resultMap="AllColumnMap">
        SELECT
        <include refid="all_column"/>
        FROM tb_bargain_item_info
        <include refid="whereByParam"/>
        order by gmt_create desc
        <if test="offset!=null and pageSize!=null">
            LIMIT #{offset} , #{pageSize}
        </if>
    </select>

    <select id="countByParam" resultType="java.lang.Integer">
        SELECT count(*)
        FROM tb_bargain_item_info
        <include refid="whereByParam"/>
    </select>

    <select id="findById" resultMap="AllColumnMap">
        SELECT
        <include refid="all_column"/>
        FROM tb_bargain_item_info
        WHERE id = #{id}
    </select>

    <select id="findByIds" resultMap="AllColumnMap">
        SELECT <include refid="all_column"/>
        FROM tb_bargain_item_info
        WHERE id IN
        <foreach collection="list" close=")" item="id" open="(" separator=",">
            #{id}
        </foreach>
    </select>

    <sql id="whereByParam">
        <where>
            <if test="bargainActivityId != null">AND bargain_activity_id = #{bargainActivityId}</if>
            <if test="deleted != null">AND deleted = #{deleted}</if>
            <if test="itemId != null">AND item_id = #{itemId}</if>
            <if test="bargainRuleId != null">AND bargain_rule_id = #{bargainRuleId}</if>
        </where>
    </sql>

    <update id="updateRemaining" parameterType="java.util.Map">
        UPDATE tb_bargain_item_info
        <set>
            remaining = remaining + #{num},
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateCompletedCountById" parameterType="java.util.Map">
        UPDATE tb_bargain_item_info
        <set>
            completed_count = completed_count + #{num},
        </set>
        WHERE id = #{id}
    </update>

</mapper>
