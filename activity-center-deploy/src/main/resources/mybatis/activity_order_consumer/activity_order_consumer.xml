<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.activity_order_consumer.ActivityOrderConsumerDao">
	<resultMap id="BaseResultMap" type="cn.com.duiba.activity.center.biz.entity.activity_order.ActivityOrderEntity" >
	    <id column="id" property="id" jdbcType="BIGINT" />
	    <result column="order_num" property="orderNum" jdbcType="VARCHAR" />
	    <result column="consumer_id" property="consumerId" jdbcType="BIGINT" />
	    <result column="app_id" property="appId" jdbcType="BIGINT" />
	    <result column="partner_user_id" property="partnerUserId" jdbcType="VARCHAR" />
	    <result column="duiba_activity_id" property="duibaActivityId" jdbcType="BIGINT" />
	    <result column="app_activity_id" property="appActivityId" jdbcType="BIGINT" />
	    <result column="activity_type" property="activityType" jdbcType="VARCHAR" />
	    <result column="consume_credits" property="consumeCredits" jdbcType="BIGINT" />
	    <result column="add_credits" property="addCredits" jdbcType="BIGINT" />
	    <result column="activity_option_id" property="activityOptionId" jdbcType="BIGINT" />
	    <result column="activity_option_name" property="activityOptionName" jdbcType="VARCHAR" />
	    <result column="activity_option_type" property="activityOptionType" jdbcType="VARCHAR" />
	    <result column="face_price" property="facePrice" jdbcType="VARCHAR" />
	    <result column="app_item_id" property="appItemId" jdbcType="BIGINT" />
	    <result column="item_id" property="itemId" jdbcType="BIGINT" />
	    <result column="gid" property="gid" jdbcType="BIGINT" />
	    <result column="gtype" property="gtype" jdbcType="VARCHAR" />
	    <result column="consume_credits_status" property="consumeCreditsStatus" jdbcType="INTEGER" />
	    <result column="add_credits_status" property="addCreditsStatus" jdbcType="INTEGER" />
	    <result column="exchange_status" property="exchangeStatus" jdbcType="INTEGER" />
	    <result column="ip" property="ip" jdbcType="VARCHAR" />
	    <result column="coupon_id" property="couponId" jdbcType="BIGINT" />
	    <result column="developer_bizid" property="developerBizId" jdbcType="VARCHAR" />
	    <result column="main_order_num" property="mainOrderNum" jdbcType="VARCHAR" />
	    <result column="error4admin" property="error4admin" jdbcType="VARCHAR" />
	    <result column="error4consumer" property="error4consumer" jdbcType="VARCHAR" />
	    <result column="error4developer" property="error4developer" jdbcType="VARCHAR" />
	    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
	    <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
	    <result column="add_credits_bizid" property="addCreditsDevBizId" jdbcType="VARCHAR" />
    </resultMap>
	
	<sql id="Base_Column_List" >
	    id, order_num, consumer_id, app_id, partner_user_id, duiba_activity_id, app_activity_id, 
	    activity_type, consume_credits, add_credits, activity_option_id, activity_option_name, 
	    activity_option_type, face_price, app_item_id, item_id, gid, gtype, consume_credits_status, 
	    add_credits_status, exchange_status, ip, coupon_id, developer_bizid, main_order_num, 
	    error4admin, error4consumer, error4developer, gmt_create, gmt_modified, add_credits_bizid
  	</sql>

	<select id="find" resultType="ActivityOrderEntity">
		select * from tb_trade_center_activity_order_${suffix} where order_num = #{orderNum}
	</select>
	
	<insert id="insert" parameterType="ActivityOrderEntity" useGeneratedKeys="true" keyProperty="id">
		insert into tb_trade_center_activity_order_${suffix} 
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="orderNum !=null">order_num,</if>
			<if test="consumerId != null">consumer_id,</if>
			<if test="appId != null">app_id,</if>
			<if test="partnerUserId != null">partner_user_id,</if>
			<if test="duibaActivityId != null">duiba_activity_id,</if>
			<if test="appActivityId != null">app_activity_id,</if>
			<if test="activityType != null">activity_type,</if>
			<if test="consumeCredits != null">consume_credits,</if>
			<if test="addCredits != null">add_credits,</if>
			<if test="activityOptionId != null">activity_option_id,</if>
			<if test="activityOptionName != null">activity_option_name,</if>
			<if test="activityOptionType != null">activity_option_type,</if>
			<if test="facePrice != null">face_price,</if>
			<if test="appItemId != null">app_item_id,</if>
			<if test="itemId != null">item_id,</if>
			<if test="gid != null">gid,</if>
			<if test="gtype != null">gtype,</if>
			<if test="consumeCreditsStatus != null">consume_credits_status,</if>
			<if test="addCreditsStatus != null">add_credits_status,</if>
			<if test="exchangeStatus != null">exchange_status,</if>
			<if test="ip != null">ip,</if>
			<if test="couponId != null">coupon_id,</if>
			<if test="developerBizId != null">developer_bizid,</if>
			<if test="addCreditsDevBizId != null">add_credits_bizid,</if>
			<if test="mainOrderNum != null">main_order_num,</if>
			<if test="error4admin != null">error4admin,</if>
			<if test="error4consumer != null">error4consumer,</if>
			<if test="error4developer != null">error4developer,</if>
			gmt_create,
			gmt_modified,
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="orderNum !=null">#{orderNum},</if>
			<if test="consumerId != null">#{consumerId},</if>
			<if test="appId != null">#{appId},</if>
			<if test="partnerUserId != null">#{partnerUserId},</if>
			<if test="duibaActivityId != null">#{duibaActivityId},</if>
			<if test="appActivityId != null">#{appActivityId},</if>
			<if test="activityType != null">#{activityType},</if>
			<if test="consumeCredits != null">#{consumeCredits},</if>
			<if test="addCredits != null">#{addCredits},</if>
			<if test="activityOptionId != null">#{activityOptionId},</if>
			<if test="activityOptionName != null">#{activityOptionName},</if>
			<if test="activityOptionType != null">#{activityOptionType},</if>
			<if test="facePrice != null">#{facePrice},</if>
			<if test="appItemId != null">#{appItemId},</if>
			<if test="itemId != null">#{itemId},</if>
			<if test="gid != null">#{gid},</if>
			<if test="gtype != null">#{gtype},</if>
			<if test="consumeCreditsStatus != null">#{consumeCreditsStatus},</if>
			<if test="addCreditsStatus != null">#{addCreditsStatus},</if>
			<if test="exchangeStatus != null">#{exchangeStatus},</if>
			<if test="ip != null">#{ip},</if>
			<if test="couponId != null">#{couponId},</if>
			<if test="developerBizId != null">#{developerBizId},</if>
			<if test="addCreditsDevBizId != null">#{addCreditsDevBizId},</if>
			<if test="mainOrderNum != null">#{mainOrderNum},</if>
			<if test="error4admin != null">#{error4admin},</if>
			<if test="error4consumer != null">#{error4consumer},</if>
			<if test="error4developer != null">#{error4developer},</if>
			now(),
			now()
		</trim>
	</insert>
	
	<update id="updateCreditsSuccess">
		update tb_trade_center_activity_order_${suffix} set 
		<if test="activityOptionId != null">activity_option_id=#{activityOptionId},</if>
		<if test="activityOptionName != null">activity_option_name=#{activityOptionName},</if>
		<if test="activityOptionType != null">activity_option_type=#{activityOptionType},</if>
		<if test="facePrice != null">face_price=#{facePrice},</if>
		<if test="appItemId != null">app_item_id=#{appItemId},</if>
		<if test="itemId != null">item_id=#{itemId},</if>
		<if test="gid != null">gid=#{gid},</if>
		<if test="gtype != null">gtype=#{gtype},</if>
		<if test="couponId != null">coupon_id=#{couponId},</if> 
		consume_credits_status = #{consumeCreditsStatus},
		gmt_modified = now()
		where order_num = #{orderNum} and consume_credits_status != #{consumeCreditsStatus}
	</update>
	
	<update id="updateCreditsSuccessDowngrade">
		update tb_trade_center_activity_order_${suffix} set 
		<if test="activityOptionId != null">activity_option_id=#{activityOptionId},</if>
		<if test="activityOptionName != null">activity_option_name=#{activityOptionName},</if>
		<if test="activityOptionType != null">activity_option_type=#{activityOptionType},</if>
		<if test="facePrice != null">face_price=#{facePrice},</if>
		<if test="appItemId != null">app_item_id=#{appItemId},</if>
		<if test="itemId != null">item_id=#{itemId},</if>
		<if test="gid != null">gid=#{gid},</if>
		<if test="gtype != null">gtype=#{gtype},</if>
		<if test="couponId != null">coupon_id=#{couponId},</if> 
		consume_credits_status = #{consumeCreditsStatus},
		gmt_modified = now()
		where order_num = #{orderNum}
	</update>
	
	<update id="updateCreditsFail">
		update tb_trade_center_activity_order_${suffix} set
		<if test="activityOptionId != null">activity_option_id=#{activityOptionId},</if>
		<if test="activityOptionName != null">activity_option_name=#{activityOptionName},</if>
		<if test="activityOptionType != null">activity_option_type=#{activityOptionType},</if>
		<if test="facePrice != null">face_price=#{facePrice},</if>
		<if test="appItemId != null">app_item_id=#{appItemId},</if>
		<if test="itemId != null">item_id=#{itemId},</if>
		<if test="gid != null">gid=#{gid},</if>
		<if test="gtype != null">gtype=#{gtype},</if>
		<if test="couponId != null">coupon_id=#{couponId},</if> 
		<if test="error4admin != null">error4admin=#{error4admin},</if>
		<if test="error4developer != null">error4developer=#{error4developer},</if>
		<if test="error4consumer != null">error4consumer=#{error4consumer},</if>
		consume_credits_status = #{consumeCreditsStatus},
		gmt_modified = now()
		where order_num = #{orderNum} and consume_credits_status != #{consumeCreditsStatus}
	</update>
	<update id="updateAddCreditsSuccess">
		update tb_trade_center_activity_order_${suffix} set
		<if test="activityOptionId != null">activity_option_id=#{activityOptionId},</if>
		<if test="activityOptionName != null">activity_option_name=#{activityOptionName},</if>
		<if test="activityOptionType != null">activity_option_type=#{activityOptionType},</if>
		<if test="facePrice != null">face_price=#{facePrice},</if>
		<if test="appItemId != null">app_item_id=#{appItemId},</if>
		<if test="itemId != null">item_id=#{itemId},</if>
		<if test="gid != null">gid=#{gid},</if>
		<if test="gtype != null">gtype=#{gtype},</if>
		<if test="couponId != null">coupon_id=#{couponId},</if>
		add_credits_status = #{addCreditsStatus},
		gmt_modified = now()
		where order_num = #{orderNum}
	</update>
	
	<update id="updateAddCreditsFail">
		update tb_trade_center_activity_order_${suffix} set
		<if test="activityOptionId != null">activity_option_id=#{activityOptionId},</if>
		<if test="activityOptionName != null">activity_option_name=#{activityOptionName},</if>
		<if test="activityOptionType != null">activity_option_type=#{activityOptionType},</if>
		<if test="facePrice != null">face_price=#{facePrice},</if>
		<if test="appItemId != null">app_item_id=#{appItemId},</if>
		<if test="itemId != null">item_id=#{itemId},</if>
		<if test="gid != null">gid=#{gid},</if>
		<if test="gtype != null">gtype=#{gtype},</if>
		<if test="couponId != null">coupon_id=#{couponId},</if>
		<if test="error4admin != null">error4admin=#{error4admin},</if>
		<if test="error4developer != null">error4developer=#{error4developer},</if>
		<if test="error4consumer != null">error4consumer=#{error4consumer},</if>
		add_credits_status = #{addCreditsStatus},
		gmt_modified = now()
		where order_num = #{orderNum} and add_credits_status != #{addCreditsStatus}
	</update>
	
	<update id="addCreditsStatusToProcessing">
		update tb_trade_center_activity_order_${suffix} set add_credits_status = #{status}, gmt_modified = now() where order_num = #{orderNum} and add_credits_status != #{status}
	</update>
	
	<update id="updateExchangeStatus">
		update tb_trade_center_activity_order_${suffix} set exchange_status = #{exchangeStatus}, gmt_modified = now() where order_num = #{orderNum} and exchange_status != #{exchangeStatus}
	</update>
	
	<update id="updateDeveloperBizId">
		update tb_trade_center_activity_order_${suffix} set developer_bizid = #{bizId}, gmt_modified = now() where order_num = #{orderNum}
	</update>

	<update id="updateAddDeveloperBizId">
		update tb_trade_center_activity_order_${suffix} set add_credits_bizid = #{bizId}, gmt_modified = now() where order_num = #{orderNum}
	</update>

	<update id="updateMainOrderNum">
		update tb_trade_center_activity_order_${suffix} set main_order_num = #{mainOrderNum}, gmt_modified = now() where order_num = #{orderNum}
	</update>
	
	<select id="countConsumerJoinNum" resultType="Integer">
		select count(*) from tb_trade_center_activity_order_${suffix} where consumer_id = #{consumerId} and duiba_activity_id = #{duibaActivityId} and activity_type = #{activityType}
	</select>

	<select id="findConsumerJoinTimes" resultType="Integer">
		select count(*) from tb_trade_center_activity_order_${suffix}
		where consumer_id = #{consumerId} and duiba_activity_id = #{duibaActivityId} and activity_type = #{activityType}
		AND consume_credits_status <![CDATA[ < ]]> 3
	</select>
	
	<select id="findConsumerJoinList" resultType="ActivityOrderEntity">
		select 
		order_num,
		consumer_id,
		app_id,
		duiba_activity_id,
		activity_type,
		consume_credits,
		app_item_id,
		item_id,
		activity_option_id,
		activity_option_name,
		activity_option_type,
		consume_credits_status,
		exchange_status,
		coupon_id,
		main_order_num,
		gmt_create 
		from tb_trade_center_activity_order_${suffix} where consumer_id = #{consumerId} and duiba_activity_id in
		<foreach collection="duibaActivityIds" index="index" item="it" open="(" separator="," close=")">
			#{it}
		</foreach>
		and activity_type = #{activityType}
	</select>
	
	<select id="findConsumerDuibaActivity4day" resultMap="BaseResultMap">
		select 
		<include refid="Base_Column_List"/>
		from tb_trade_center_activity_order_${suffix} 
		where consumer_id = #{consumerId} 
		and duiba_activity_id = #{duibaActivityId}
		and gmt_create between #{start} and #{end}
		and activity_type = #{activityType}
		limit 1
	</select>
	
	<select id="findByOrderNums" resultType="ActivityOrderEntity">
		select * from tb_trade_center_activity_order_${suffix} where order_num in 
		<foreach collection="orderNums" index="index" item="it" open="(" separator="," close=")">
			#{it}
		</foreach>
	</select>
	
	<select id="findOptionIds" resultType="java.lang.Long">
		select activity_option_id from tb_trade_center_activity_order_${suffix}
		where consumer_id = #{consumerId}
		and app_id = #{appId}
		and activity_type = #{activityType}
		and exchange_status = 2
	</select>
	
	<update id="updateAddCredits">
		update tb_trade_center_activity_order_${suffix} set add_credits = #{addCredits}, gmt_modified = now() where order_num = #{orderNum}
	</update>

	<select id="countConsumerJoinTimesByType" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM tb_trade_center_activity_order_${suffix}
        WHERE consumer_id = #{consumerId}
		AND app_activity_id IN
        <foreach collection="ids" item="id" close=")" open="(" separator=",">
			#{id}
		</foreach>
        AND activity_type = #{activityType}
        AND consume_credits_status = 2
    </select>

    <select id="listByConsumerIdAndActivityType" resultType="ActivityOrderEntity">
        SELECT
        order_num,
        consumer_id,
        consume_credits,
        activity_option_id,
        duiba_activity_id,
        add_credits,
        exchange_status,
        consume_credits_status
        FROM tb_trade_center_activity_order_${suffix}
        WHERE consumer_id = #{consumerId}
		AND app_activity_id IN
		<foreach collection="ids" item="id" close=")" open="(" separator=",">
			#{id}
		</foreach>
		AND activity_type = #{activityType}
        AND consume_credits_status = 2
        ORDER BY gmt_create DESC
        LIMIT #{offset}, #{limit}
    </select>


	<select id="listByConsumerAndActivityAndStatus" resultType="ActivityOrderEntity">
		select
		<include refid="Base_Column_List"/>
		from tb_trade_center_activity_order_${suffix}
		where consumer_id = #{consumerId}
		and duiba_activity_id = #{duibaActivityId}
		and activity_type = #{activityType}
		and consume_credits_status = #{consumeCreditsStatus}
		and exchange_status = #{exchangeStatus}
	</select>
</mapper>
