<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.seedredpacket.impl.SeedRedPacketUserMarkDaoImpl">

    <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.seedredpacket.SeedRedPacketUserMarkEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_seed_red_packet_mark
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="consumerId != null">consumer_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="activityId != null">activity_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="consumerId != null">#{consumerId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="activityId != null">#{activityId},</if>
        </trim>
    </insert>

    <update id="updateToEnableById" parameterType="long">
        UPDATE tb_seed_red_packet_mark
        SET deleted = 0
        WHERE id = #{id}
    </update>

    <select id="selectConsumer" resultType="cn.com.duiba.activity.center.biz.entity.seedredpacket.SeedRedPacketUserMarkEntity" parameterType="java.util.Map">
        select id,consumer_id,app_id,activity_id,deleted
        from tb_seed_red_packet_mark
        where app_id = #{appId}
        and activity_id = #{activityId}
        and consumer_id = #{consumerId}
    </select>

    <select id="selectListByAppAndActivity" resultType="cn.com.duiba.activity.center.biz.entity.seedredpacket.SeedRedPacketUserMarkEntity" parameterType="java.util.Map">
        select id,consumer_id,app_id,activity_id,deleted
        from tb_seed_red_packet_mark
        where deleted = 0
        and app_id = #{appId}
        and activity_id = #{activityId}
        <if test="start != null and size != null">
            limit #{start}, #{size}
        </if>
    </select>

    <select id="selectCountByAppAndActivity" resultType="int" parameterType="java.util.Map">
        select count(1)
        from tb_seed_red_packet_mark
        where deleted = 0
        and app_id = #{appId}
        and activity_id = #{activityId}
    </select>

    <select id="selectListAll" resultType="cn.com.duiba.activity.center.biz.entity.seedredpacket.SeedRedPacketUserMarkEntity" parameterType="java.util.Map">
        select id,consumer_id,app_id,activity_id,deleted
        from tb_seed_red_packet_mark
        where deleted = 0
        limit #{start}, #{size}
    </select>

    <select id="selectCountAll" resultType="int">
        select count(1)
        from tb_seed_red_packet_mark
        where deleted = 0
    </select>

    <update id="deleteByApp" parameterType="java.util.Map">
        UPDATE tb_seed_red_packet_mark
        SET deleted = 1
        WHERE app_id = #{appId}
        <if test="activityId != null">
            AND activity_id = #{activityId}
        </if>
        <if test="consumerIdList != null and consumerIdList.size != 0">
            AND  consumer_id in
            <foreach collection="consumerIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="deleteByIds" parameterType="java.util.Map">
        UPDATE tb_seed_red_packet_mark
        SET deleted = 1
        WHERE id in
        <foreach collection="idList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
</mapper>