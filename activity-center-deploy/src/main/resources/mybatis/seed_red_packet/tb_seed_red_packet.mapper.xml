<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.seedredpacket.impl.SeedRedPacketDaoImpl">

    <resultMap id="entity" type="cn.com.duiba.activity.center.biz.entity.seedredpacket.SeedRedPacketEntity">
        <id column="id" property="id"/>
        <id column="title" property="title"/>
        <id column="seed_credits_price" property="seedCreditsPrice"/>
        <id column="unlock_land_credits_price" property="unlockLandCreditsPrice"/>
        <id column="activity_status" property="status"/>
        <id column="gmt_create" property="gmtCreate"/>
        <id column="rule" property="rule"/>
        <id column="ad_switch" property="adSwitch"/>
        <id column="ad_positions" property="adPositions"/>
        <id column="decr_switch" property="decrSwitch"/>
        <id column="decr_begin_time" property="decrBeginTime"/>
        <id column="app_ids4adver" property="appIds4Adver"/>
        <id column="plant_num_daily" property="plantNumDaily"/>
        <id column="draw_num_daily" property="drawNumDaily"/>
        <id column="steal_red_packet_switch" property="stealRedPacketSwitch"/>
        <id column="month_budget" property="monthBudget"/>
        <id column="account_type" property="accountType"/>
        <id column="period_id" property="periodId"/>
        <id column="bonus_number" property="bonusNumber"/>
        <id column="bonus_limit" property="bonusLimit"/>
        <id column="un_bonus_limit" property="unBonusLimit"/>
        <id column="app_id" property="appId"/>
        <id column="start_time" property="startTime"/>
        <id column="end_time" property="endTime"/>
        <id column="operating_activity_id" property="operatingActivityId"/>
        <id column="data_json" property="dataJson"/>
        <id column="unit_id" property="unitId"/>
        <id column="reward_type" property="rewardType"/>
        <id column="ad_config" property="adConfig"/>
        <id column="seed_act_type" property="seedActType"/>
    </resultMap>

    <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.seedredpacket.SeedRedPacketEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_seed_red_packet
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="seedCreditsPrice != null">seed_credits_price,</if>
            <if test="unlockLandCreditsPrice != null">unlock_land_credits_price,</if>
            <if test="status != null">activity_status,</if>
            <if test="rule != null">rule,</if>
            <if test="adSwitch != null">ad_switch,</if>
            <if test="adPositions != null">ad_positions,</if>
            <if test="decrSwitch != null">decr_switch,</if>
            <if test="decrBeginTime != null">decr_begin_time,</if>
            <if test="appIds4Adver != null">app_ids4adver,</if>
            plant_num_daily,
            seed_act_type,
            <if test="drawNumDaily != null">draw_num_daily,</if>
            <if test="stealRedPacketSwitch != null">steal_red_packet_switch,</if>
            <if test="monthBudget != null">month_budget,</if>
            <if test="accountType != null">account_type,</if>
            <if test="periodId != null">period_id,</if>
            <if test="bonusNumber != null">bonus_number,</if>
            <if test="bonusLimit != null">bonus_limit,</if>
            <if test="unBonusLimit != null">un_bonus_limit,</if>
            <if test="appId != null">app_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="operatingActivityId != null">operating_activity_id,</if>
            <if test="dataJson != null">data_json,</if>
            <if test="unitId != null">unit_id,</if>
            <if test="rewardType != null">reward_type,</if>
            <if test="adConfig != null">ad_config,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="seedCreditsPrice != null">#{seedCreditsPrice},</if>
            <if test="unlockLandCreditsPrice != null">#{unlockLandCreditsPrice},</if>
            <if test="status != null">#{status},</if>
            <if test="rule != null">#{rule},</if>
            <if test="adSwitch != null">#{adSwitch},</if>
            <if test="adPositions != null">#{adPositions},</if>
            <if test="decrSwitch != null">#{decrSwitch},</if>
            <if test="decrBeginTime != null">#{decrBeginTime},</if>
            <if test="appIds4Adver != null">#{appIds4Adver},</if>
            #{plantNumDaily},
            #{seedActType},
            <if test="drawNumDaily != null">#{drawNumDaily},</if>
            <if test="stealRedPacketSwitch != null">#{stealRedPacketSwitch},</if>
            <if test="monthBudget != null">#{monthBudget},</if>
            <if test="accountType != null">#{accountType},</if>
            <if test="periodId != null">#{periodId},</if>
            <if test="bonusNumber != null">#{bonusNumber},</if>
            <if test="bonusLimit != null">#{bonusLimit},</if>
            <if test="unBonusLimit != null">#{unBonusLimit},</if>
            <if test="appId != null">#{appId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="operatingActivityId != null">#{operatingActivityId},</if>
            <if test="dataJson != null">#{dataJson},</if>
            <if test="unitId != null">#{unitId},</if>
            <if test="rewardType != null">#{rewardType},</if>
            <if test="adConfig != null">#{adConfig},</if>
        </trim>
    </insert>

    <select id="selectById" resultMap="entity" parameterType="java.util.Map">
        select <include refid="all_column"/>
        from tb_seed_red_packet
        where id = #{id} AND deleted = 0
    </select>


    <select id="findByIds" resultMap="entity" parameterType="java.util.Map">
        select <include refid="all_column"/>
        from tb_seed_red_packet
        where id IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="updateGeneral" parameterType="cn.com.duiba.activity.center.biz.entity.seedredpacket.SeedRedPacketEntity">
        UPDATE tb_seed_red_packet
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="seedCreditsPrice != null">seed_credits_price = #{seedCreditsPrice},</if>
            <if test="unlockLandCreditsPrice != null">unlock_land_credits_price = #{unlockLandCreditsPrice},</if>
            <if test="rule != null">rule = #{rule},</if>
            <if test="adSwitch != null">ad_switch = #{adSwitch},</if>
            ad_positions = #{adPositions},
            <if test="decrSwitch != null">decr_switch = #{decrSwitch},</if>
            decr_begin_time = #{decrBeginTime},
            app_ids4adver = #{appIds4Adver},
            plant_num_daily = #{plantNumDaily},
            <if test="drawNumDaily != null">draw_num_daily = #{drawNumDaily},</if>
            <if test="stealRedPacketSwitch != null">steal_red_packet_switch = #{stealRedPacketSwitch},</if>
            <if test="accountType != null">account_type = #{accountType},</if>
            <if test="periodId != null">period_id = #{periodId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="bonusNumber != null">bonus_number = #{bonusNumber},</if>
            <if test="bonusLimit != null">bonus_limit = #{bonusLimit},</if>
            <if test="unBonusLimit != null">un_bonus_limit = #{unBonusLimit},</if>
            <if test="dataJson != null">data_json = #{dataJson},</if>
            <if test="unitId != null">unit_id = #{unitId},</if>
            <if test="rewardType != null">reward_type = #{rewardType},</if>
            <if test="adConfig != null">ad_config = #{adConfig},</if>
            month_budget = #{monthBudget}
        </set>
        WHERE id = #{id}
    </update>

    <update id="deleted" parameterType="long">
        UPDATE tb_seed_red_packet
        SET deleted = 1
        WHERE id = #{id}
    </update>

    <update id="updateStatus" parameterType="map">
        UPDATE tb_seed_red_packet
        SET activity_status = #{status}
        WHERE id = #{id}
    </update>

    <select id="findByPage" parameterType="map" resultMap="entity">
        SELECT <include refid="all_column"/>
        FROM tb_seed_red_packet
        WHERE deleted = 0
        <if test="title != null"> AND title LIKE CONCAT('%',#{title},'%') </if>
        AND app_id IS NULL
        AND seed_act_type = 0
        ORDER BY gmt_create DESC
        limit #{offset}, #{pageSize}
    </select>

    <select id="findCount" resultType="int">
        SELECT count(id)
        FROM tb_seed_red_packet
        WHERE deleted = 0
        <if test="title != null"> AND title LIKE CONCAT('%',#{title},'%') </if>
        AND app_id IS NULL
        AND seed_act_type = 0
    </select>

    <update id="updatePeriod" parameterType="map">
        UPDATE tb_seed_red_packet
        SET period_id = #{periodId},
        start_time = #{startTime},
        end_time = #{endTime}
        WHERE id = #{id}
    </update>

    <select id="findAllNotEndingActivity" parameterType="long" resultMap="entity">
        SELECT <include refid="all_column"/>
        FROM tb_seed_red_packet
        WHERE app_id = #{appId}
        AND end_time > now()
        AND seed_act_type = 0
        AND deleted = 0
    </select>

    <select id="findLatestActivityByAppId" parameterType="long" resultMap="entity">
        SELECT <include refid="all_column"/>
        FROM tb_seed_red_packet
        WHERE app_id = #{appId}
        AND seed_act_type = 0
        ORDER BY end_time DESC
        Limit 1
    </select>

    <select id="findOneByAppIdAndStartTime" parameterType="long" resultMap="entity">
        SELECT <include refid="all_column"/>
        FROM tb_seed_red_packet
        WHERE app_id = #{appId}
        AND start_time &lt;= now()
        limit 1
    </select>

    <update id="updateOperatingActivityId" parameterType="map">
        UPDATE tb_seed_red_packet
        SET operating_activity_id = #{operatingActivityId}
        WHERE id = #{id}
    </update>

    <delete id="deletedPhysical" parameterType="long">
        DELETE FROM tb_seed_red_packet
        WHERE id = #{id}
    </delete>

    <sql id="all_column">
        id,title,seed_credits_price,unlock_land_credits_price,activity_status,gmt_create,rule,
        ad_switch,ad_positions,decr_switch,decr_begin_time,app_ids4adver,plant_num_daily,draw_num_daily,
        steal_red_packet_switch,month_budget,account_type,period_id,bonus_number,bonus_limit,un_bonus_limit,app_id,start_time,end_time,operating_activity_id,data_json,unit_id,reward_type,ad_config,seed_act_type
    </sql>

</mapper>