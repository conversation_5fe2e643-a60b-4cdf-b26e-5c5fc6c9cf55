<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.through.impl.ActivityThroughDaoImpl">

	<resultMap type="ActivityThroughEntity" id="throughConfigMap">
		<id column="id" property="id"/>
		<result column="through_id" property="throughId"/>
		<result column="through_activity_id" property="throughActivityId"/>
		<result column="brick_id" property="brickId"/>
		<result column="through_point_description" property="throughPointDescription"/>
	</resultMap>
	
	<sql id="all_column">
		id,through_id, through_activity_id, brick_id, through_point_description 
	</sql>
	
	<insert id="saveActivityThroughInfo" parameterType="ActivityThroughEntity" useGeneratedKeys="true" keyProperty="id">
		insert into tb_activity_through (through_id, through_activity_id, brick_id, through_point_description )
		values (#{throughId},#{throughActivityId},#{brickId},#{throughPointDescription})
	</insert>
	
	<select id="findByThroughActivityId" resultMap="throughConfigMap" parameterType="long">
		SELECT 
		<include refid="all_column"/> 
		FROM tb_activity_through 
		WHERE through_activity_id = #{throughActivityId}
	</select>
	
	<select id="findActivityThroughInfo" resultMap="throughConfigMap" parameterType="java.util.Map">
		SELECT 
		<include refid="all_column"/> 
		FROM tb_activity_through 
		WHERE through_activity_id = #{throughActivityId} and through_id = #{throughId} ORDER BY id DESC LIMIT 1
	</select>

	<delete id="deleteData" parameterType="java.lang.Long">
		delete from tb_activity_through where through_activity_id = #{throughActivityId}
	</delete>
</mapper>
