<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.happycode.impl.HappyCodeOrderDaoImpl">

	<resultMap id="order" type="cn.com.duiba.activity.center.biz.entity.happycode.HappyCodeOrderEntity">
		<result column="id" property="id"/>
		<result column="app_id" property="appId"/>
		<result column="consumer_id" property="consumerId"/>
		<result column="partner_user_id" property="partnerUserId"/>
		<result column="happy_code" property="happyCode"/>
		<result column="phase_id" property="phaseId"/>
		<result column="phase_number" property="phaseNumber"/>
		<result column="option_id" property="optionId"/>
		<result column="option_type" property="optionType"/>
		<result column="option_name" property="optionName"/>
		<result column="item_id" property="itemId"/>
		<result column="order_status" property="orderStatus"/>
		<result column="exchange_status" property="exchangeStatus"/>
		<result column="origin" property="origin"/>
		<result column="deleted" property="deleted"/>
		<result column="gmt_create" property="gmtCreate"/>
		<result column="gmt_modified" property="gmtModified"/>
		<result column="prize_level" property="prizeLevel"/>
		<result column="face_price" property="facePrice"/>
	</resultMap>

	<sql id="AllColumn">
		id,
		app_id,
		consumer_id,
		partner_user_id,
		happy_code,
		phase_id,
		phase_number,
		option_id,
		option_type,
		option_name,
		item_id,
		order_status,
		exchange_status,
		origin,
		deleted,
		gmt_create,
		gmt_modified,
		prize_level,
		face_price
	</sql>

	<select id="findById" parameterType="long" resultMap="order">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_order
		WHERE id = #{id}
	</select>

	<insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.happycode.HappyCodeOrderEntity" keyProperty="id" useGeneratedKeys="true">
		INSERT INTO tb_happy_code_order
		(app_id,
		consumer_id,
		partner_user_id,
		happy_code,
		phase_id,
		phase_number,
		option_id,
		option_type,
		option_name,
		item_id,
		order_status,
		exchange_status,
		origin,
		deleted,
		gmt_create,
		gmt_modified,
		<if test="facePrice != null">face_price,</if>
		prize_level
		)
		VALUES
		(#{appId},
		#{consumerId},
		#{partnerUserId},
		#{happyCode},
		#{phaseId},
		#{phaseNumber},
		#{optionId},
		#{optionType},
		#{optionName},
		#{itemId},
		#{orderStatus},
		#{exchangeStatus},
		#{origin},
		#{deleted},
		#{gmtCreate},
		#{gmtModified},
		<if test="facePrice != null">#{facePrice},</if>
		#{prizeLevel}
		)
	</insert>

	<select id="findOrderIdByPhaseIdAndOffset" parameterType="map" resultType="long">
		SELECT id
		FROM tb_happy_code_order
		WHERE phase_id = #{phaseId}
		AND happy_code IS NOT NULL
		<if test="startDate != null">
			AND gmt_create &gt; #{startDate}
		</if>
		<if test="endDate != null">
			AND gmt_create &lt; #{endDate}
		</if>
		AND deleted = 0
		limit #{offset}, 1
	</select>

	<select id="findOrderIdByPhaseIdAndConsumerId" parameterType="map" resultType="long">
		SELECT id
		FROM tb_happy_code_order
		WHERE consumer_id = #{consumerId}
		AND phase_id = #{phaseId}
		AND happy_code IS NOT NULL
		<if test="startDate != null">
			AND gmt_create &gt; #{startDate}
		</if>
		<if test="endDate != null">
			AND gmt_create &lt; #{endDate}
		</if>
		AND deleted = 0
		limit 1
	</select>

	<update id="updateHappyCodeAndPhase" parameterType="cn.com.duiba.activity.center.biz.entity.happycode.HappyCodeOrderEntity">
		UPDATE tb_happy_code_order
		<set>
			<if test="happyCode != null">happy_code = #{happyCode},</if>
			<if test="phaseId != null">phase_id = #{phaseId},</if>
			<if test="phaseNumber != null">phase_number = #{phaseNumber},</if>
			gmt_modified = now()
		</set>
		WHERE id = #{id}
	</update>

	<select id="findCountByPhaseId" parameterType="long" resultType="int">
		SELECT count(id)
		FROM tb_happy_code_order
		WHERE phase_id = #{phaseId}
		<if test="startDate != null">
			AND gmt_create &gt; #{startDate}
		</if>
		<if test="endDate != null">
			AND gmt_create &lt; #{endDate}
		</if>
		AND deleted = 0
	</select>

	<update id="updatePrizeInfo" parameterType="cn.com.duiba.activity.center.biz.entity.happycode.HappyCodeOrderEntity">
		UPDATE tb_happy_code_order
		<set>
			<if test="optionId != null">option_id = #{optionId},</if>
			<if test="optionType != null">option_type = #{optionType},</if>
			<if test="prizeLevel != null">prize_level = #{prizeLevel},</if>
			<if test="optionName != null">option_name = #{optionName},</if>
			<if test="facePrice != null">face_price = #{facePrice},</if>
			<if test="itemId != null">item_id = #{itemId},</if>
			<if test="exchangeStatus != null">exchange_status = #{exchangeStatus},</if>
			gmt_modified = now()
		</set>
		WHERE id = #{id}
	</update>

	<select id="findOrderByPhaseIdAndConsumerId" parameterType="map" resultMap="order">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_order
		WHERE consumer_id = #{consumerId}
		AND phase_id = #{phaseId}
		AND happy_code IS NOT NULL
	</select>

    <select id="findPrizeOrderByPhaseIdAndConsumerId" parameterType="map" resultMap="order">
        select <include refid="AllColumn"/>
        from tb_happy_code_order
        where consumer_id = #{consumerId}
        and phase_id = #{phaseId}
        and happy_code is not null
        and deleted = 0
        and prize_level is not null
    </select>

	<select id="findNewestOrderByPhaseIdAndCount" parameterType="map" resultMap="order">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_order
		WHERE phase_id = #{phaseId}
		ORDER BY gmt_create DESC
		limit #{count}
	</select>

	<select id="findOrderByIds" parameterType="list" resultMap="order">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_order
		WHERE id IN
		<foreach collection="list" separator="," open="(" close=")" item="id">
			#{id}
		</foreach>
	</select>

	<update id="doTakePrize" parameterType="map">
		UPDATE tb_happy_code_order
		SET exchange_status = 2
		WHERE id = #{orderId}
		AND exchange_status IN (1,4)
	</update>

	<update id="delWinnerOrder" parameterType="map">
		UPDATE tb_happy_code_order
		SET deleted = 1
		WHERE phase_id = #{phaseId}
		AND consumer_id = #{consumerId}
		<if test="startDate != null">
			AND gmt_create &gt; #{startDate}
		</if>
		<if test="endDate != null">
			AND gmt_create &lt; #{endDate}
		</if>
	</update>

	<select id="findOrderByPhaseIdsAndConsumerId" parameterType="map" resultMap="order">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_order
		WHERE consumer_id = #{consumerId}
		AND phase_id IN
		<foreach collection="phaseIds" separator="," open="(" close=")" item="id">
			#{id}
		</foreach>
		AND happy_code IS NOT NULL
		AND deleted = 0
		ORDER BY gmt_create DESC
		Limit #{offset}, #{pageSize}
	</select>

	<select id="findOrders" parameterType="map" resultMap="order">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_order
		WHERE consumer_id = #{consumerId}
		AND phase_id IN
		<foreach collection="phaseIds" separator="," open="(" close=")" item="id">
			#{id}
		</foreach>
		AND happy_code IS NOT NULL
		AND deleted = 0
		<if test="exchangeStatus != null">
			AND exchange_status = #{exchangeStatus}
		</if>
		ORDER BY gmt_create DESC
	</select>

	<select id="findCountByPhaseIdsAndConsumerId" parameterType="map" resultType="int">
		SELECT COUNT(id)
		FROM tb_happy_code_order
		WHERE consumer_id = #{consumerId}
		AND phase_id IN
		<foreach collection="phaseIds" separator="," open="(" close=")" item="id">
			#{id}
		</foreach>
		AND happy_code IS NOT NULL
		AND deleted = 0
	</select>

    <select id="findCountByPhaseIdAndPrizeLevel" parameterType="map" resultType="int">
		select count(id)
        from tb_happy_code_order
        where phase_id = #{phaseId}
        and prize_level = #{prizeLevel}
        and exchange_status = #{exchangeStatus}
        and deleted = 0
	</select>

    <select id="findOrderByPhaseIdAndPrizeLevelWithPagination" parameterType="map" resultMap="order">
		select <include refid="AllColumn"/>
        from tb_happy_code_order
        where phase_id = #{phaseId}
        and prize_level = #{prizeLevel}
        and exchange_status = #{exchangeStatus}
        and deleted = 0
        order by gmt_create asc
        limit #{offset}, #{pageSize}
	</select>

    <update id="doTakePrizeByIds" parameterType="list">
        update tb_happy_code_order
        set exchange_status = 2
        where id in
        <foreach collection="list" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        and exchange_status in (1, 4)
    </update>

    <select id="findPrizeOrderCountByPhaseIdAndConsumerId" parameterType="map" resultType="int">
        select count(id)
        from tb_happy_code_order
        where phase_id = #{phaseId}
        and consumer_id = #{consumerId}
        and prize_level is not null
        and deleted = 0
    </select>

    <select id="findCountByPhaseIdsAndConsumerIdAndPrizeLevel" parameterType="map" resultType="int">
        select count(id)
        from tb_happy_code_order
        where phase_id in
        <foreach collection="phaseIds" item="phaseId" open="(" separator="," close=")">
            #{phaseId}
        </foreach>
        and consumer_id = #{consumerId}
        and prize_level = #{prizeLevel}
        and deleted = 0
    </select>

</mapper>
