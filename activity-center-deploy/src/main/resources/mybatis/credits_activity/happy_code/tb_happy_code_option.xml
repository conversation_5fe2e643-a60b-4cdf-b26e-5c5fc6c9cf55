<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.happycode.impl.HappyCodeOptionDaoImpl">

	<resultMap id="option" type="cn.com.duiba.activity.center.biz.entity.happycode.HappyCodeOptionEntity">
		<result column="id" property="id"/>
		<result column="phase_id" property="phaseId"/>
		<result column="prize_name" property="prizeName"/>
		<result column="prize_type" property="prizeType"/>
		<result column="logo" property="logo"/>
		<result column="description" property="description"/>
		<result column="memo" property="memo"/>
		<result column="remaining" property="remaining"/>
		<result column="item_id" property="itemId"/>
		<result column="item_name" property="itemName"/>
		<result column="deleted" property="deleted"/>
		<result column="gmt_create" property="gmtCreate"/>
		<result column="gmt_modified" property="gmtModified"/>
		<result column="prize_level" property="prizeLevel"/>
		<result column="stock_id" property="stockId"/>
		<result column="face_price" property="facePrice"/>
		<result column="rate" property="rate"/>
		<result column="min_comein" property="minComein"/>
	</resultMap>

	<sql id="AllColumn">
		id,
		phase_id,
		prize_name,
		prize_type,
		logo,
		description,
		memo,
		remaining,
		item_id,
		item_name,
		deleted,
		gmt_create,
		gmt_modified,
		prize_level,
		stock_id,
		face_price,
		rate,
		min_comein
	</sql>

	<insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.happycode.HappyCodeBasicEntity" keyProperty="id" useGeneratedKeys="true">
		INSERT INTO tb_happy_code_option
		(phase_id,
		prize_name,
		prize_type,
		logo,
		description,
		memo,
		remaining,
		item_id,
		item_name,
		deleted,
		prize_level,
		<if test="facePrice != null">face_price,</if>
		stock_id,
		rate,
		min_comein
		)
		VALUES
		(#{phaseId},
		#{prizeName},
		#{prizeType},
		#{logo},
		#{description},
		#{memo},
		#{remaining},
		#{itemId},
		#{itemName},
		#{deleted},
		#{prizeLevel},
		<if test="facePrice != null">#{facePrice},</if>
		#{stockId},
		#{rate},
		#{minComein}
		)
	</insert>

	<insert id="batchInsert" keyProperty="id" useGeneratedKeys="true">
		INSERT INTO tb_happy_code_option
		(phase_id,
		prize_name,
		prize_type,
		logo,
		description,
		memo,
		remaining,
		item_id,
		item_name,
		deleted,
		prize_level,
		face_price,
		stock_id,
		rate,
		min_comein
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.phaseId},
			#{item.prizeName},
			#{item.prizeType},
			#{item.logo},
			#{item.description},
			#{item.memo},
			#{item.remaining},
			#{item.itemId},
			#{item.itemName},
			#{item.deleted},
			#{item.prizeLevel},
			#{item.facePrice},
			#{item.stockId},
			#{item.rate},
			#{item.minComein}
			)
		</foreach>
	</insert>



	<select id="findById" parameterType="long" resultMap="option">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_option
		WHERE id = #{id}
	</select>

	<update id="generalUpdate" parameterType="cn.com.duiba.activity.center.biz.entity.happycode.HappyCodeBasicEntity">
		UPDATE tb_happy_code_option
		<set>
			<if test="prizeName != null">prize_name = #{prizeName},</if>
			<if test="prizeType != null">prize_type = #{prizeType},</if>
			<if test="facePrice != null">face_price = #{facePrice},</if>
			<if test="logo != null">logo = #{logo},</if>
			<if test="description != null">description = #{description},</if>
			<if test="memo != null">memo = #{memo},</if>
			<if test="remaining != null">remaining = #{remaining},</if>
			<if test="itemId != null">item_id = #{itemId},</if>
			<if test="itemName != null">item_name = #{itemName},</if>
			<if test="rate != null">rate = #{rate},</if>
			<if test="minComein != null">min_comein = #{minComein},</if>
			gmt_modified = now()
		</set>
		WHERE id = #{id}
	</update>

	<select id="findOneByPhaseId" parameterType="long" resultMap="option">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_option
		WHERE phase_id = #{phaseId}
		limit 1
	</select>

	<select id="findByPhaseId" parameterType="long" resultMap="option">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_option
		WHERE phase_id = #{phaseId}
	</select>

    <select id="deleteOption" parameterType="long">
        DELETE FROM tb_happy_code_option
        WHERE id = #{id}
    </select>

	<select id="findByPhaseIds" parameterType="list" resultMap="option">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_option
		WHERE phase_id IN
		<foreach collection="list" item="id" close=")" open="(" separator=",">
			#{id}
		</foreach>
	</select>

</mapper>
