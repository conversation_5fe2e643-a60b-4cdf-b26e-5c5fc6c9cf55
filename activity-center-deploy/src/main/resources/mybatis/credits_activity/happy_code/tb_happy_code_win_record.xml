<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.happycode.impl.HappyCodeWinRecordDaoImpl">

	<resultMap id="winRecord" type="cn.com.duiba.activity.center.biz.entity.happycode.HappyCodeWinRecordEntity">
		<result column="id" property="id"/>
		<result column="app_id" property="appId"/>
		<result column="act_id" property="actId"/>
		<result column="consumer_id" property="consumerId"/>
		<result column="partner_user_id" property="partnerUserId"/>
		<result column="option_id" property="optionId"/>
		<result column="option_type" property="optionType"/>
		<result column="option_name" property="optionName"/>
		<result column="option_logo" property="optionLogo"/>
		<result column="order_id" property="orderId"/>
		<result column="win_code" property="winCode"/>
		<result column="basic_id" property="basicId"/>
		<result column="phase_id" property="phaseId"/>
		<result column="cheat" property="cheat"/>
		<result column="deleted" property="deleted"/>
		<result column="gmt_create" property="gmtCreate"/>
		<result column="gmt_modified" property="gmtModified"/>
		<result column="prize_level" property="prizeLevel"/>
	</resultMap>

	<sql id="AllColumn">
		id,
		app_id,
		act_id,
		consumer_id,
		partner_user_id,
		option_id,
		option_type,
		option_name,
		option_logo,
		order_id,
		win_code,
		basic_id,
		phase_id,
		cheat,
		deleted,
		gmt_create,
		gmt_modified,
		prize_level
	</sql>

	<insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.happycode.HappyCodeWinRecordEntity" keyProperty="id" useGeneratedKeys="true">
		INSERT INTO tb_happy_code_win_record
		(app_id,
		 act_id,
		consumer_id,
		partner_user_id,
		option_id,
		option_type,
		option_name,
		option_logo,
		order_id,
		win_code,
		basic_id,
		phase_id,
		cheat,
		deleted,
		prize_level)
		VALUES
		(#{appId},
		#{actId},
		#{consumerId},
		#{partnerUserId},
		#{optionId},
		#{optionType},
		#{optionName},
		#{optionLogo},
		#{orderId},
		#{winCode},
		#{basicId},
		#{phaseId},
		#{cheat},
		#{deleted},
		#{prizeLevel})
	</insert>

	<select id="findByBasicId" parameterType="long" resultMap="winRecord">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_win_record
		WHERE basic_id = #{basicId}
	</select>

	<select id="findByBasicIdAndPhaseId" parameterType="Map" resultMap="winRecord">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_win_record
		WHERE basic_id = #{basicId} and phase_id = #{phaseId}
		limit 1
	</select>

	<select id="findByBasicIdAndPhaseIdForPoker" parameterType="Map" resultMap="winRecord">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_win_record
		WHERE basic_id = #{basicId}
        and phase_id = #{phaseId}
        and option_id &gt; 0
        order by id desc
        limit 25
	</select>

	<select id="findByBasicIds" parameterType="list" resultMap="winRecord">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_win_record
		WHERE basic_id IN
		<foreach collection="list" close=")" separator="," item="id" open="(">
			#{id}
		</foreach>
 	</select>

	<select id="selectConsumerByAct" parameterType="map" resultType="java.lang.Long">
		SELECT consumer_id
		FROM tb_happy_code_win_record
		WHERE
		act_id = #{actId}
		order by gmt_create desc
		limit #{limit}
	</select>

    <select id="countByBasicIds" parameterType="list" resultType="cn.com.duiba.activity.center.api.dto.happy_code.HappyCodePrizeCountDto">
        SELECT basic_id as basicId, count(*) as prizeCount
        FROM tb_happy_code_win_record
        WHERE basic_id IN
        <foreach collection="list" close=")" separator="," item="id" open="(">
            #{id}
        </foreach>
        group by basic_id
    </select>

	<select id="listByPhaseIds" parameterType="list" resultMap="winRecord">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_win_record
		WHERE phase_id IN
		<foreach collection="list" close=")" separator="," item="id" open="(">
			#{id}
		</foreach>
	</select>

    <insert id="batchInsert" parameterType="list" keyProperty="id" useGeneratedKeys="true">
        insert into tb_happy_code_win_record(
        app_id,
        consumer_id,
        partner_user_id,
        option_id,
        option_type,
        option_name,
        option_logo,
        order_id,
        win_code,
        basic_id,
        phase_id,
        cheat,
        deleted,
        prize_level
        ) values
        <foreach collection="list" item="entity" open="" separator="," close="">
            (
            #{entity.appId},
            #{entity.consumerId},
            #{entity.partnerUserId},
            #{entity.optionId},
            #{entity.optionType},
            #{entity.optionName},
            #{entity.optionLogo},
            #{entity.orderId},
            #{entity.winCode},
            #{entity.basicId},
            #{entity.phaseId},
            #{entity.cheat},
            #{entity.deleted},
            #{entity.prizeLevel}
            )
        </foreach>
    </insert>
</mapper>
