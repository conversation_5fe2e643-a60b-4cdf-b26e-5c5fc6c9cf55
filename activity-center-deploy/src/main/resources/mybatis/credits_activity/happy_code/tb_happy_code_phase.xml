<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.happycode.impl.HappyCodePhaseDaoImpl">

	<resultMap id="phaseConfig" type="cn.com.duiba.activity.center.biz.entity.happycode.HappyCodePhaseEntity">
		<result column="id" property="id"/>
		<result column="basic_id" property="basicId"/>
		<result column="title" property="title"/>
		<result column="sub_title" property="subTitle"/>
		<result column="rule" property="rule"/>
		<result column="phase_number" property="phaseNumber"/>
		<result column="code_count" property="codeCount"/>
		<result column="phase_status" property="phaseStatus"/>
		<result column="payload" property="payload"/>
		<result column="lottery_type" property="lotteryType"/>
		<result column="code_type" property="codeType"/>
		<result column="winner_count" property="winnerCount"/>
		<result column="winner_id" property="winnerId"/>
		<result column="code_item_id" property="codeItemId"/>
		<result column="code_item_name" property="codeItemName"/>
		<result column="code_item_tips" property="codeItemTips"/>
		<result column="deleted" property="deleted"/>
		<result column="gmt_create" property="gmtCreate"/>
		<result column="gmt_modified" property="gmtModified"/>
		<result column="first_prize_date" property="firstPrizeDate"/>
		<result column="next_prize_date" property="nextPrizeDate"/>
		<result column="prize_loop_type" property="prizeLoopType"/>
		<result column="prize_order_area" property="prizeOrderArea"/>
		<result column="start_date" property="startDate"/>
		<result column="end_date" property="endDate"/>
		<result column="cycle_type" property="cycleType"/>
		<result column="cycle_number" property="cycleNumber"/>
		<result column="del_winner_order" property="delWinnerOrder"/>
		<result column="win_poker" property="winPoker"/>
		<result column="poker_take_prize_cycle" property="pokerTakePrizeCycle"/>
		<result column="poker_take_prize_time" property="pokerTakePrizeTime"/>
		<result column="fallback_type" property="fallbackType"/>
	</resultMap>

	<sql id="AllColumn">
		id,
		basic_id,
		title,
		sub_title,
		rule,
		phase_number,
		code_count,
		phase_status,
		payload,
		lottery_type,
		code_type,
		winner_count,
		winner_id,
		code_item_id,
		code_item_name,
		code_item_tips,
		deleted,
		gmt_create,
		gmt_modified,
		first_prize_date,
		next_prize_date,
		prize_loop_type,
		prize_order_area,
		start_date,
		end_date,
		cycle_type,
		cycle_number,
		del_winner_order,
		win_poker,
		poker_take_prize_cycle,
		poker_take_prize_time,
		fallback_type
	</sql>

	<insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.happycode.HappyCodePhaseEntity" keyProperty="id" useGeneratedKeys="true">
		INSERT INTO tb_happy_code_phase
		(basic_id,
		title,
		sub_title,
		rule,
		phase_number,
		code_count,
		phase_status,
		payload,
		lottery_type,
		code_type,
		winner_count,
		winner_id,
		code_item_id,
		code_item_name,
		code_item_tips,
		deleted,
		first_prize_date,
		next_prize_date,
		prize_loop_type,
		prize_order_area,
		start_date,
		end_date,
		cycle_type,
		cycle_number,
		del_winner_order,
		win_poker,
		poker_take_prize_cycle,
		poker_take_prize_time
		<if test="fallbackType != null">
			,fallback_type
		</if>
		)
		VALUES
		(#{basicId},
		#{title},
		#{subTitle},
		#{rule},
		#{phaseNumber},
		#{codeCount},
		#{phaseStatus},
		#{payload},
		#{lotteryType},
		#{codeType},
		#{winnerCount},
		#{winnerId},
		#{codeItemId},
		#{codeItemName},
		#{codeItemTips},
		#{deleted},
		#{firstPrizeDate},
		#{nextPrizeDate},
		#{prizeLoopType},
		#{prizeOrderArea},
		#{startDate},
		#{endDate},
		#{cycleType},
		#{cycleNumber},
		#{delWinnerOrder},
		#{winPoker},
		#{pokerTakePrizeCycle},
		#{pokerTakePrizeTime}
		<if test="fallbackType != null">
			,#{fallbackType}
		</if>
		)
	</insert>

	<select id="findById" parameterType="long" resultMap="phaseConfig">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_phase
		WHERE id = #{id}
	</select>

	<update id="deleteById" parameterType="long">
		UPDATE tb_happy_code_phase
		SET deleted = 1
		WHERE id = #{id}
	</update>

	<select id="findByBasicIds" parameterType="list" resultMap="phaseConfig">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_phase
		WHERE basic_id IN
		<foreach collection="list" separator="," open="(" close=")" item="basicId">
			#{basicId}
		</foreach>
	</select>

	<update id="updateBasicId" parameterType="map">
		UPDATE tb_happy_code_phase
		SET basic_id = #{basicId}
		WHERE id IN
		<foreach collection="phaseIds" item="id" close=")" open="(" separator=",">
			#{id}
		</foreach>
	</update>

	<select id="findByPhaseId" parameterType="long" resultMap="phaseConfig">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_phase
		WHERE id = #{PhaseId}
		limit 1
	</select>

	<select id="findByPhaseIds" parameterType="list" resultMap="phaseConfig">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_phase
		WHERE id IN
		<foreach collection="list" separator="," open="(" close=")" item="id">
			#{id}
		</foreach>
	</select>

	<select id="findAllWaitOpenPhase" resultMap="phaseConfig">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_phase
		WHERE phase_status = 2
	</select>

	<update id="updateStatusToEnd">
		UPDATE tb_happy_code_phase
		SET phase_status = 3
		WHERE id = #{phaseId}
		AND phase_status = 2
	</update>

	<update id="updateStatusToOpen">
		UPDATE tb_happy_code_phase
		SET phase_status = 1
		WHERE id = #{phaseId}
		AND phase_status = 0
	</update>

	<update id="updateStatusToWaitOpenPrize">
		UPDATE tb_happy_code_phase
		SET phase_status = 2
		WHERE id = #{phaseId}
		AND phase_status = 1
	</update>

	<update id="updateStatusToOpening">
		UPDATE tb_happy_code_phase
		SET phase_status = 1
		WHERE id = #{phaseId}
		AND phase_status = 0
	</update>

	<update id="updateWinner" parameterType="cn.com.duiba.activity.center.biz.entity.happycode.HappyCodePhaseEntity">
		UPDATE tb_happy_code_phase
		SET winner_id = #{winnerId},
		winner_count = #{winnerCount}
		WHERE id = #{id}
	</update>

	<select id="findByEndDate" parameterType="java.util.Date" resultMap="phaseConfig">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_phase
		WHERE end_date = #{queryDate}
        AND lottery_type IN (1,2)
	</select>

	<select id="findAllPhaseByNextPrizeDate" resultMap="phaseConfig">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_phase
		WHERE next_prize_date &lt; now()
		AND next_prize_date &gt; DATE_ADD(now() ,INTERVAL -90 SECOND)
	</select>

	<select id="getPhaseByBasicIdAndPayload" parameterType="map" resultMap="phaseConfig">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_phase
		WHERE basic_id = #{basicId}
		AND payload = #{payload}
	</select>

	<update id="updateNextPrizeTime" parameterType="map">
		UPDATE tb_happy_code_phase
		SET next_prize_date = #{nextPrizeDate}
		WHERE id = #{id}
	</update>

	<update id="updatePhaseNumber" parameterType="map">
		UPDATE tb_happy_code_phase
		SET phase_number = #{phaseNumber}
		WHERE id = #{id}
	</update>

	<update id="updateNextPhase" parameterType="cn.com.duiba.activity.center.biz.entity.happycode.HappyCodePhaseEntity">
		UPDATE tb_happy_code_phase
		<set>
			<if test="title != null">
				title = #{title},
			</if>
			<if test="subTitle != null">
				sub_title = #{subTitle},
			</if>
			<if test="rule != null">
				rule = #{rule},
			</if>
			<if test="codeType != null">
				code_type = #{codeType},
			</if>
			<if test="codeCount != null">
				code_count = #{codeCount},
			</if>
			<if test="firstPrizeDate != null">
				first_prize_date = #{firstPrizeDate},
			</if>
			<if test="nextPrizeDate != null">
				next_prize_date = #{nextPrizeDate},
			</if>
			<if test="prizeLoopType != null">
				prize_loop_type = #{prizeLoopType},
			</if>
			<if test="prizeOrderArea != null">
				prize_order_area = #{prizeOrderArea},
			</if>
			<if test="delWinnerOrder != null">
				del_winner_order = #{delWinnerOrder},
			</if>
			<if test="cycleType != null">
				cycle_type = #{cycleType},
			</if>
			<if test="cycleNumber != null">
				cycle_number = #{cycleNumber},
			</if>
			<if test="endDate != null">
				end_date = #{endDate},
			</if>
			<if test="winPoker != null">
				win_poker = #{winPoker},
			</if>
			<if test="pokerTakePrizeCycle != null">
				poker_take_prize_cycle = #{pokerTakePrizeCycle},
			</if>
			<if test="pokerTakePrizeTime != null">
				poker_take_prize_time = #{pokerTakePrizeTime},
			</if>
			<if test="fallbackType != null">
				fallback_type = #{fallbackType},
			</if>
			gmt_modified = now()
		</set>
		WHERE id = #{id}
		AND phase_status = 0
	</update>

	<select id="listFirstPhaseByBasicIds" resultMap="phaseConfig">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_phase
		WHERE basic_id IN
		<foreach collection="list" separator="," open="(" close=")" item="basicId">
			#{basicId}
		</foreach>
		and payload = 1
	</select>


	<select id="findByPastPhaseByBasicIdAndPayload" resultMap="phaseConfig">
		SELECT <include refid="AllColumn"/>
		FROM tb_happy_code_phase
		WHERE basic_id = #{basicId}
		AND payload  <![CDATA[ < ]]> #{payload}
		ORDER BY payload desc
		LIMIT #{offset},#{pageSize}
	</select>

	<select id="getPastPhaseCountByBasicIdAndPayload" resultType="int">
		SELECT count(1)
		FROM tb_happy_code_phase
		WHERE basic_id = #{basicId}
		AND payload  <![CDATA[ < ]]> #{payload}
	</select>

</mapper>
