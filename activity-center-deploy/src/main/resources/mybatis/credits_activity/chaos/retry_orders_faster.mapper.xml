<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.chaos.impl.RetryOrdersFasterDaoImpl">

    <delete id="deleteByOrderId" parameterType="java.util.Map">
        delete from retry_orders_faster where order_id = #{orderId}
    </delete>

    <select id="findByOrderId" parameterType="java.util.Map" resultType="RetryOrdersFasterEntity">
        select id as id,
        order_id as orderId,
        type as type,
        lottery_type as lotteryType,
        end_time as endTime,
        gmt_create as gmtCreate,
        gmt_modified as gmtModified
        from retry_orders_faster where order_id = #{orderId}
    </select>

    <select id="findEndtimeRetryOrder" resultType="RetryOrdersFasterEntity">
        select id as id,
        order_id as orderId,
        type as type,
        lottery_type as lotteryType,
        end_time as endTime,
        gmt_create as gmtCreate,
        gmt_modified as gmtModified
        from retry_orders_faster where end_time &lt; now()
    </select>

    <insert id="insert" parameterType="RetryOrdersFasterEntity" useGeneratedKeys="true" keyProperty="id">
        insert into retry_orders_faster
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="type != null">type,</if>
            <if test="lotteryType != null">lottery_type,</if>
            <if test="endTime != null">end_time,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="type != null">#{type},</if>
            <if test="lotteryType != null">#{lotteryType},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

</mapper>
