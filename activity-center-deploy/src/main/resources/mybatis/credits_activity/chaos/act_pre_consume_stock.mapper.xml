<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.chaos.impl.ActPreConsumerStockDaoImpl">

    <select id="findPreConsumerByBizPay" parameterType="java.util.Map" resultType="ActPreConsumeStockEntity">
        select id as id,
        pre_stock_id as preStockId,
        biz_id as bizId,
        biz_source as bizSource,
        action as action,
        quantity as quantity,
        app_id as appId,
        gmt_create as gmtCreate,
        gmt_modified as gmtModified
        from act_pre_consume_stock
        where biz_id = #{bizId} and biz_source = #{bizSource} and action = 'pay' limit 1
    </select>

    <insert id="insert" parameterType="ActPreConsumeStockEntity" useGeneratedKeys="true" keyProperty="id">
        insert IGNORE into act_pre_consume_stock
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="preStockId != null">pre_stock_id,</if>
            <if test="bizId != null">biz_id,</if>
            <if test="bizSource != null">biz_source,</if>
            <if test="action != null">action,</if>
            <if test="quantity != null">quantity,</if>
            <if test="appId != null">app_id,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="preStockId != null">#{preStockId},</if>
            <if test="bizId != null">#{bizId},</if>
            <if test="bizSource != null">#{bizSource},</if>
            <if test="action != null">#{action},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="appId != null">#{appId},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

</mapper>