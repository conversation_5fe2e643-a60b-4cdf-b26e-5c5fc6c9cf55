<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.chaos.impl.ActPreStockDaoImpl">

    <select id="findActStockByConfigId" parameterType="java.util.Map" resultType="ActPreStockEntity">
        select id as id,
        relation_config_id as relationConfigId,
        relation_prize_id as relationPrizeId,
        relation_type as relationType,
        prize_name as prizeName,
        prize_quantity as prizeQuantity,
        app_id as appId,
        deleted as deleted,
        gmt_create as gmtCreate,
        gmt_modified as gmtModified
        from act_pre_stock
        where relation_config_id = #{configId} and relation_type = #{relationType}
        and deleted = false
    </select>

    <select id="findPreStockByShare" parameterType="java.util.Map" resultType="ActPreStockEntity">
        select id as id,
        relation_config_id as relationConfigId,
        relation_prize_id as relationPrizeId,
        relation_type as relationType,
        prize_name as prizeName,
        prize_quantity as prizeQuantity,
        app_id as appId,
        deleted as deleted,
        gmt_create as gmtCreate,
        gmt_modified as gmtModified
        from act_pre_stock
        where relation_prize_id = #{relationPrizeId} and relation_type = #{relationType}
        and deleted = false and app_id = 0 limit 1
    </select>

    <select id="findPreStockByApp" parameterType="java.util.Map" resultType="ActPreStockEntity">
        select id as id,
        relation_config_id as relationConfigId,
        relation_prize_id as relationPrizeId,
        relation_type as relationType,
        prize_name as prizeName,
        prize_quantity as prizeQuantity,
        app_id as appId,
        deleted as deleted,
        gmt_create as gmtCreate,
        gmt_modified as gmtModified
        from act_pre_stock
        where relation_prize_id = #{relationPrizeId} and relation_type = #{relationType}
        and deleted = false and app_id = #{appId} limit 1
    </select>
    
    <select id="findPreStockListByApp" parameterType="java.util.Map" resultType="ActPreStockEntity">
        select id as id,
        relation_config_id as relationConfigId,
        relation_prize_id as relationPrizeId,
        relation_type as relationType,
        prize_name as prizeName,
        prize_quantity as prizeQuantity,
        app_id as appId,
        deleted as deleted,
        gmt_create as gmtCreate,
        gmt_modified as gmtModified
        from act_pre_stock
        where relation_prize_id in
        <foreach collection="ids" item="activityId" index="index" open="(" close=")" separator=",">
			#{activityId}
		</foreach>
        and relation_type = #{relationType}
        and deleted = false and app_id = #{appId} limit 1
    </select>

    <select id="findPreStockListByPrizeIds" parameterType="java.util.Map" resultType="ActPreStockEntity">
        select id as id,
        relation_config_id as relationConfigId,
        relation_prize_id as relationPrizeId,
        relation_type as relationType,
        prize_name as prizeName,
        prize_quantity as prizeQuantity,
        app_id as appId,
        deleted as deleted,
        gmt_create as gmtCreate,
        gmt_modified as gmtModified
        from act_pre_stock
        where relation_prize_id in
        <foreach collection="ids" item="activityId" index="index" open="(" close=")" separator=",">
            #{activityId}
        </foreach>
        and relation_type = #{relationType}
        and deleted = false and app_id = #{appId}
    </select>

    <select id="findByLock" parameterType="java.util.Map" resultType="ActPreStockEntity">
        select id as id,
        relation_config_id as relationConfigId,
        relation_prize_id as relationPrizeId,
        relation_type as relationType,
        prize_name as prizeName,
        prize_quantity as prizeQuantity,
        app_id as appId,
        deleted as deleted,
        gmt_create as gmtCreate,
        gmt_modified as gmtModified
        from act_pre_stock where id = #{id} for update
    </select>
    
    <select id="find" parameterType="java.util.Map" resultType="ActPreStockEntity">
        select id as id,
        relation_config_id as relationConfigId,
        relation_prize_id as relationPrizeId,
        relation_type as relationType,
        prize_name as prizeName,
        prize_quantity as prizeQuantity,
        app_id as appId,
        deleted as deleted,
        gmt_create as gmtCreate,
        gmt_modified as gmtModified
        from act_pre_stock where id = #{id}
    </select>

    <update id="decrementRemaining" parameterType="java.util.Map">
        update act_pre_stock set prize_quantity = (prize_quantity-1) where id = #{id} and prize_quantity > 0
    </update>

    <update id="incrementRemaining" parameterType="java.util.Map">
        update act_pre_stock set prize_quantity = (prize_quantity+1) where id = #{id}
    </update>

    <update id="addRemainingById" parameterType="java.util.Map">
        update
        act_pre_stock
        set prize_quantity = (ifnull(prize_quantity, 0) + #{addRemaining})
        where id = #{id}
    </update>

    <update id="subRemainingById" parameterType="java.util.Map">
        update
        act_pre_stock
        set prize_quantity = (ifnull(prize_quantity, 0) - #{subRemaining})
        where id = #{id} and prize_quantity &gt;= #{subRemaining}
    </update>

    <insert id="insert" parameterType="ActPreStockEntity" useGeneratedKeys="true" keyProperty="id">
        insert into act_pre_stock
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="relationConfigId != null">relation_config_id,</if>
            <if test="relationPrizeId != null">relation_prize_id,</if>
            <if test="relationType != null">relation_type,</if>
            <if test="prizeName != null">prize_name,</if>
            <if test="prizeQuantity != null">prize_quantity,</if>
            <if test="appId != null">app_id,</if>
            <if test="deleted != null">deleted,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
            <if test="stockId != null">stock_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="relationConfigId != null">#{relationConfigId},</if>
            <if test="relationPrizeId != null">#{relationPrizeId},</if>
            <if test="relationType != null">#{relationType},</if>
            <if test="prizeName != null">#{prizeName},</if>
            <if test="prizeQuantity != null">#{prizeQuantity},</if>
            <if test="appId != null">#{appId},</if>
            <if test="deleted != null">#{deleted},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
            <if test="stockId != null">#{stockId},</if>
        </trim>
    </insert>


    <update id="update" parameterType="ActPreStockEntity">
        update act_pre_stock
        <set>
            <if test="relationConfigId != null">relation_config_id=#{relationConfigId},</if>
            <if test="relationPrizeId != null">relation_prize_id=#{relationPrizeId},</if>
            <if test="relationType != null">relation_type=#{relationType},</if>
            <if test="prizeName != null">prize_name=#{prizeName},</if>
            <if test="prizeQuantity != null">prize_quantity=#{prizeQuantity},</if>
            <if test="appId != null">app_id=#{appId},</if>
            <if test="deleted != null">deleted=#{deleted},</if>
            <if test="gmtCreate != null">gmt_create=#{gmtCreate},</if>
            <if test="gmtModified != null">gmt_modified=#{gmtModified},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteActStock" parameterType="java.util.Map">
        delete from act_pre_stock where relation_config_id = #{configId} and relation_type = #{relationType}
    </delete>

    <delete id="deleteActPrizeStock" parameterType="java.util.Map">
        delete from act_pre_stock where relation_config_id = #{configId} and relation_prize_id = #{prizeId} and relation_type = #{relationType}
    </delete>

    <delete id="deleteActStockAppId" parameterType="java.util.Map">
        delete from act_pre_stock where relation_prize_id = #{prizeId} and relation_type = #{relationType} and app_id = #{appId}
    </delete>
</mapper>
