<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.elasticgifts.impl.ElasticGiftsDaoImpl">

    <select id="findPage" resultType="cn.com.duiba.activity.center.biz.entity.elasticgifts.ElasticGiftsEntity" parameterType="Map">
        SELECT
        <include refid="fields"/>
        from tb_elastic_gifts
        where
        biz_code = #{bizCode}
        <if test="elasticGiftsId != null">
            and id = #{elasticGiftsId}
        </if>
        <if test="title4admin != null and title4admin != ''">
            and title4admin LIKE CONCAT('%', #{title4admin}, '%')
        </if>
        and deleted = 0
        order by id desc
        limit #{offset}, #{max}
    </select>

    <select id="findPageCount" resultType="Integer" parameterType="Map">
        SELECT
        count(id)
        from tb_elastic_gifts
        where
        biz_code = #{bizCode}
        <if test="elasticGiftsId != null">
            and id = #{elasticGiftsId}
        </if>
        <if test="title4admin != null and title4admin != ''">
            and title4admin LIKE CONCAT('%', #{title4admin}, '%')
        </if>
        and deleted = 0
    </select>

    <update id="updateStatus" parameterType="Map">
        update tb_elastic_gifts
        set status = #{targetStatus}
        where id = #{id}
    </update>

    <update id="delete" parameterType="Map">
        update tb_elastic_gifts
        set deleted = 1, status = 0
        where id = #{id}
    </update>

    <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.elasticgifts.ElasticGiftsEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_elastic_gifts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title4admin != null">title4admin,</if>
            <if test="title != null">title,</if>
            <if test="status != null">status,</if>
            <if test="bizCode != null">biz_code,</if>
            <if test="deleted != null">deleted,</if>
            gmt_create,
            gmt_modified,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title4admin != null">#{title4admin},</if>
            <if test="title != null">#{title},</if>
            <if test="status != null">#{status},</if>
            <if test="bizCode != null">#{bizCode},</if>
            <if test="deleted != null">#{deleted},</if>
            now(),
            now(),
        </trim>
    </insert>

    <update id="update" parameterType="cn.com.duiba.activity.center.biz.entity.elasticgifts.ElasticGiftsEntity">
        update tb_elastic_gifts
        <set>
            <if test="title4admin != null">title4admin = #{title4admin},</if>
            <if test="title != null">title = #{title},</if>
            <if test="status != null">status = #{status},</if>
            <if test="bizCode != null">biz_code = #{bizCode},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
            gmt_modified = now(),
        </set>
        where id = #{id}
    </update>

    <select id="find" resultType="cn.com.duiba.activity.center.biz.entity.elasticgifts.ElasticGiftsEntity" parameterType="Map">
        select
        <include refid="fields"/>
        from tb_elastic_gifts
        where id = #{id}
    </select>

    <select id="findByIdAndBizCode" resultType="cn.com.duiba.activity.center.biz.entity.elasticgifts.ElasticGiftsEntity" parameterType="Map">
        select
        <include refid="fields"/>
        from tb_elastic_gifts
        where id = #{id}
        and biz_code = #{bizCode}
        and deleted = 0
    </select>

    <select id="findAllByTitle4adminAndBizCode" resultType="cn.com.duiba.activity.center.biz.entity.elasticgifts.ElasticGiftsEntity" parameterType="Map">
        select
        <include refid="fields"/>
        from tb_elastic_gifts
        where
        biz_code = #{bizCode}
        <if test="text != null and text != ''">
            and title4admin LIKE CONCAT('%', #{text}, '%')
        </if>
        and deleted = 0
    </select>

    <sql id="fields">
        id,
        title4admin,
        title,
        status,
        biz_code as bizCode,
        deleted,
        gmt_create as gmtCreate,
        gmt_modified as gmtModified
    </sql>
</mapper>
