<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.activity.impl.AppDirectDaoImpl">

    <select id="findByAppId" parameterType="Long" resultType="Long">
		select app_id as appId from tb_app_direct where app_id=#{appId} limit 1
	</select>

    <insert id="insert" parameterType="AppDirectEntity">
		insert into tb_app_direct(
			app_id,
			gmt_create,
			gmt_modified
		) values(
			#{appId},
			now(),
			now()
		)
	</insert>

    <delete id="deleteByAppId" parameterType="Long">
		delete
		FROM tb_app_direct
		where app_id = #{appId}
	</delete>

    <select id="selectByIds" resultType="Long"  parameterType="java.util.Map">
        SELECT app_id
        FROM tb_app_direct
        WHERE app_id in
        <foreach collection="ids" index="index" item="it" open="(" separator="," close=")">
        #{it}
       </foreach>
    </select>

</mapper>
