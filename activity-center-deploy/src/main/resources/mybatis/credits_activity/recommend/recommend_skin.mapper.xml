<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.activity.center.biz.dao.recommend.impl.RecommendSkinDaoImpl">
	<select id="selectList"
			resultType="cn.com.duiba.activity.center.biz.entity.activity.RecommendSkinEntity" >
		select <include refid="fields"/> from tb_activity_recommend_skin
		<if test="recommendSkin.showStatus != null">
			<where>
				show_status=#{recommendSkin.showStatus}
			</where>
		</if>

		order by gmt_create desc
		limit #{pageNum},#{pageSize}
	</select>

	<insert id="insert"
			parameterType="cn.com.duiba.activity.center.biz.entity.activity.RecommendSkinEntity"
			useGeneratedKeys="true" keyProperty="id">
		insert  into tb_activity_recommend_skin(
		skin_name,
		show_status,
		html_context,
		gmt_create,
		gmt_modified
		)values(
		#{skinName},
		#{showStatus},
		#{htmlContext},
		now(),
		now()
		)
	</insert>

	<update id="update" parameterType="cn.com.duiba.activity.center.biz.entity.activity.RecommendSkinEntity">
		update tb_activity_recommend_skin set
			<if test="skinName !=null">
				skin_name=#{skinName},
			</if>
			<if test="showStatus != null">
				show_status=#{showStatus},
			</if>
			<if test="htmlContext != null">
				html_context=#{htmlContext},
			</if>
			gmt_modified = now()
			where id = #{id}

	</update>

	<select id="selectCount" resultType="java.lang.Integer"
			parameterType="cn.com.duiba.activity.center.biz.entity.activity.RecommendSkinEntity">
		select count(1)  from tb_activity_recommend_skin
	</select>

	<select id="select" resultType="cn.com.duiba.activity.center.biz.entity.activity.RecommendSkinEntity"
		parameterType="java.lang.Long">
		select html_context as htmlContext,<include refid="fields"/>  from tb_activity_recommend_skin where id=#{id}
	</select>




	<delete id="delete" parameterType="java.lang.Long">
		delete from tb_activity_recommend_skin
		where id=#{id}
	</delete>
	

	



	<sql id="fields">
		id as id,
		skin_name as skinName,
		show_status as showStatus,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified
	</sql>
</mapper>
