<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.activity.impl.InsuranceUserInfoDaoImpl">
	<insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.activity.InsuranceUserInfoEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_insurance_user_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="consumerId != null">consumer_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="userPhone != null">user_phone,</if>
            <if test="identification != null">identification,</if>
            <if test="advertId != null">advert_id,</if>
            <if test="activityId != null">activity_id,</if>
            <if test="advertPlanId != null">advert_plan_id,</if>
            <if test="advertMediaId != null">advert_media_id,</if>
            <if test="channelName != null">channel_name,</if>
            <if test="info !=null">info,</if>
            <!-- <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if> -->
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
        	<if test="consumerId != null">#{consumerId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="userPhone != null">#{userPhone},</if>
            <if test="identification != null">#{identification},</if>
            <if test="advertId != null">#{advertId},</if>
            <if test="activityId != null">#{activityId},</if>
            <if test="advertPlanId != null">#{advertPlanId},</if>
            <if test="advertMediaId != null">#{advertMediaId},</if>
            <if test="channelName != null">#{channelName},</if>
            <if test="info !=null">#{info},</if>
            <!-- <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if> -->
        </trim>
    </insert>
    
    <select id="selectExist" parameterType="java.util.Map" resultType="int">
		SELECT count(id) FROM tb_insurance_user_info
		where (user_phone = #{userPhone} or identification = #{identification}) and channel_name = #{channelName}
		limit 1
	</select>
	
	<select id="selectList" parameterType="java.util.Map" resultType="cn.com.duiba.activity.center.biz.entity.activity.InsuranceUserInfoEntity">
		select * from tb_insurance_user_info
		where channel_name = #{channelName}
		<if test="start!=null and end!=null">
		and gmt_create between #{start} and #{end}
		</if>
		limit #{offset},#{max}
	</select>
	
	<select id="selectCount" parameterType="java.util.Map" resultType="int">
		select count(*) from tb_insurance_user_info
	    where channel_name = #{channelName}
		<if test="start!=null and end!=null">
		and gmt_create between #{start} and #{end}
		</if>
	</select>
</mapper>
