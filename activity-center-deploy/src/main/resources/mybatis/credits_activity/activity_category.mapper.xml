<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.category.impl.ActivityCategoryDaoImpl">

	<insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.ActivityCategoryEntity" useGeneratedKeys="true" keyProperty="id">
		insert into tb_activity_category (name,content,enable,deleted,specify,gmt_create,gmt_modified)
		values(#{name},#{content},0,0,0,now(),now())
	</insert>
	
	<update id="updateCategoryContent">
		update tb_activity_category set name=#{name}, content=#{content}, gmt_modified=now() where id=#{id}
	</update>
	
	<update id="updateCategorySpecify">
		update tb_activity_category set specify=#{specifyStatus} where id=#{id}
	</update>

	<update id="updateCategoryEnable">
		update tb_activity_category set enable=1 where id=#{id}
	</update>
	
	<update id="updateCategoryDisable">
		update tb_activity_category set enable=0,gmt_modified=now() where id=#{id}
	</update>
	
	<select id="selectAll" resultType="cn.com.duiba.activity.center.biz.entity.ActivityCategoryEntity">
		select * from tb_activity_category where deleted=false order by gmt_create desc
	</select>
	
	<select id="findByIds" resultType="cn.com.duiba.activity.center.biz.entity.ActivityCategoryEntity" parameterType="java.util.Map">
		select
		* from tb_activity_category
		where id in
		<foreach collection="ids" index="index" item="it" open="(" separator="," close=")">
			#{it}
		</foreach>
		and deleted=false
	</select>
	
	<select id="select" resultType="cn.com.duiba.activity.center.biz.entity.ActivityCategoryEntity">
		select * from tb_activity_category where  id=#{id}  and deleted=0
	</select>
	
	<update id="deleteById">
		update tb_activity_category set deleted=1,gmt_modified=now() where id=#{id}
	</update>

	<select id="selectEnableCategory" resultType="cn.com.duiba.activity.center.biz.entity.ActivityCategoryEntity">
		select * from tb_activity_category where deleted=0 and enable=1
	</select>
</mapper>
