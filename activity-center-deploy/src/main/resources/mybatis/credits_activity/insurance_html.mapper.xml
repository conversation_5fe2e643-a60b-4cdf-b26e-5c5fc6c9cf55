<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.activity.impl.InsuranceHtmlDaoImpl">
	<select id="findById" resultType="cn.com.duiba.activity.center.biz.entity.activity.InsuranceHtmlEntity">
		SELECT id, html_content, channel_name, gmt_create, gmt_modified 
		FROM tb_insurance_html 
		WHERE id=#{id}
	</select>
</mapper>
