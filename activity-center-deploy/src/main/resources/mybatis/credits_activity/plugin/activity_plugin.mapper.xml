<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.plugin.impl.ActivityPluginDaoImpl">

	<insert id="createActivityPluginInfo" parameterType="cn.com.duiba.activity.center.biz.entity.plugin.ActivityPluginEntity" useGeneratedKeys="true" keyProperty="id">
		insert into tb_activity_plugin 
			<trim prefix="(" suffix=")" suffixOverrides=",">
				<if test="brickId != null">brick_id,</if>
				<if test="title != null">title,</if>
				<if test="ruleDescription != null">rule_description,</if>
				<if test="rateDescription != null">rate_description,</if>
				<if test="scenarios != null">scenarios,</if>
				<if test="signType != null">sign_type,</if>
				<if test="triggerTime != null">trigger_time,</if>
				<if test="deleted != null">deleted,</if>
				<if test="status != null">plugin_status,</if>
				<if test="switches != null">switches,</if>
				<if test="tag != null">tag,</if>
				<if test="triggerTpye != null">trigger_tpye,</if>
				<if test="triggerUrl != null">trigger_url,</if>
				<if test="autoOffDate != null">auto_off_date,</if>
				<if test="gmtCreate != null">gmt_create,</if>
				<if test="gmtModified != null">gmt_modified,</if>
				<if test="limitCount != null">limit_count,</if>
				<if test="limitScope != null">limit_scope,</if>
				<if test="subType != null">sub_type,</if>
				<if test="exitDays != null">exit_days,</if>
				<if test="autoOnDate != null">auto_on_date,</if>
				<if test="activityRelate != null">activity_relate,</if>
				<if test="activityId != null">activity_id,</if>
				<if test="hiddenForDeveloper !=null">hidden_for_developer,</if>
				<if test="notifyEmail != null">notify_email,</if>
				<if test="creditsPrice != null">credits_price,</if>
				<if test="channelType != null">channel_type,</if>
				ext_json
			</trim>
			<trim prefix="values (" suffix=")" suffixOverrides=",">
				<if test="brickId != null">#{brickId},</if>
				<if test="title != null">#{title},</if>
				<if test="ruleDescription != null">#{ruleDescription},</if>
				<if test="rateDescription != null">#{rateDescription},</if>
				<if test="scenarios != null">#{scenarios},</if>
				<if test="signType != null">#{signType},</if>
				<if test="triggerTime != null">#{triggerTime},</if>
				<if test="deleted != null">#{deleted},</if>
				<if test="status != null">#{status},</if>
				<if test="switches != null">#{switches},</if>
				<if test="tag != null">#{tag},</if>
				<if test="triggerTpye != null">#{triggerTpye},</if>
				<if test="triggerUrl != null">#{triggerUrl},</if>
				<if test="autoOffDate != null">#{autoOffDate},</if>
				<if test="gmtCreate != null">#{gmtCreate},</if>
				<if test="gmtModified != null">#{gmtModified},</if>
				<if test="limitCount != null">#{limitCount},</if>
				<if test="limitScope != null">#{limitScope},</if>
				<if test="subType != null">#{subType},</if>
				<if test="exitDays != null">#{exitDays},</if>
				<if test="autoOnDate != null">#{autoOnDate},</if>
				<if test="activityRelate != null">#{activityRelate},</if>
				<if test="activityId != null">#{activityId},</if>
				<if test="hiddenForDeveloper !=null">#{hiddenForDeveloper},</if>
				<if test="notifyEmail != null">#{notifyEmail},</if>
				<if test="creditsPrice != null">#{creditsPrice},</if>
				<if test="channelType != null">#{channelType},</if>
				#{extJson}
			</trim>
	</insert>
	
	<update id="updateActivityPluginInfo" parameterType="cn.com.duiba.activity.center.biz.entity.plugin.ActivityPluginEntity">
		update tb_activity_plugin
		set gmt_modified = #{gmtModified}
			<if test="brickId != null">,brick_id = #{brickId}</if>
			<if test="title != null">,title = #{title}</if>
			<if test="ruleDescription != null">,rule_description = #{ruleDescription}</if>
			<if test="rateDescription != null">,rate_description = #{rateDescription}</if>
			<if test="scenarios != null">,scenarios = #{scenarios}</if>
			<if test="signType != null">,sign_type = #{signType}</if>
			<if test="triggerTime != null">,trigger_time = #{triggerTime}</if>
			<if test="deleted != null">,deleted = #{deleted}</if>
			<if test="status != null">,plugin_status = #{status}</if>
			<if test="autoOffDate != null">,auto_off_date = #{autoOffDate}</if>
			<if test="switches != null">,switches = #{switches}</if>
			<if test="tag != null">,tag = #{tag}</if>
			<if test="triggerTpye != null">,trigger_tpye = #{triggerTpye}</if>
			<if test="triggerUrl != null">,trigger_url = #{triggerUrl}</if>
			<if test="limitCount != null">,limit_count = #{limitCount}</if>
			<if test="limitScope != null">,limit_scope = #{limitScope}</if>
			<if test="subType != null">,sub_type = #{subType}</if>
			<if test="exitDays != null">,exit_days=#{exitDays}</if>
			<if test="autoOnDate != null">,auto_on_date=#{autoOnDate}</if>
			<if test="activityRelate != null">,activity_relate=#{activityRelate}</if>
			<if test="activityId != null">,activity_id=#{activityId}</if>
			<if test="hiddenForDeveloper !=null">,hidden_for_developer=#{hiddenForDeveloper}</if>
		    <if test="extJson !=null">,ext_json=#{extJson}</if>
			<if test="notifyEmail != null">,notify_email = #{notifyEmail}</if>
			<if test="creditsPrice != null">,credits_price = #{creditsPrice}</if>
			<if test="channelType != null">,channel_type = #{channelType}</if>
		where id = #{id}
	</update>

	<sql id="fields">
			id,
			brick_id as brickId,
			title,
			rule_description as ruleDescription,
			rate_description as rateDescription,
			scenarios,
			sign_type as signType,
			trigger_time as triggerTime,
			plugin_status as status,
			switches as switches,
			deleted,
			tag,
			trigger_tpye as triggerTpye,
			trigger_url as triggerUrl,
			auto_off_date as autoOffDate,
			limit_count as limitCount,
			limit_scope as limitScope,
			sub_type as subType,
			exit_days as exitDays,
			auto_on_date as autoOnDate,
			activity_relate as activityRelate,
			activity_id as activityId ,
			hidden_for_developer as hiddenForDeveloper,
			notify_email as notifyEmail,
			ext_json as extJson,
			credits_price as creditsPrice,
			channel_type as  channelType
	</sql>

	<select id="findByPage" resultType="cn.com.duiba.activity.center.biz.entity.plugin.ActivityPluginEntity" parameterType="Map">
		select <include refid="fields"/>
		from tb_activity_plugin 
		where deleted = 0
			<if test="title!= null"> and title like CONCAT('%',#{title},'%') </if>
			<if test="id!= null"> and id = #{id} </if>
			<if test="status!= null"> and plugin_status = #{status} </if>
			order by gmt_create desc
			<if test="offset!= null"> limit #{offset},#{max} </if>
	</select>
	
	<select id="findPageCount" resultType="Long" parameterType="Map">
		select count(id) 
		from tb_activity_plugin 
		where deleted = 0
			<if test="title!= null"> and title like CONCAT('%',#{title},'%') </if>
			<if test="id!= null"> and id = #{id} </if>
	</select>
	
	<select id="findById" resultType="cn.com.duiba.activity.center.biz.entity.plugin.ActivityPluginEntity" parameterType="Long">
		select <include refid="fields"/>
		from tb_activity_plugin where  id=#{id}
	</select>

	<select id="findAndSortByIds" resultType="cn.com.duiba.activity.center.biz.entity.plugin.ActivityPluginEntity" parameterType="Map">
		select <include refid="fields"/>
		from tb_activity_plugin
		where id IN
			<foreach collection="ids" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
			<if test="status!= null"> and plugin_status = #{status} </if>
			order by  FIND_IN_SET(id, #{sequence})
	</select>

	<select id="findByIds" resultType="cn.com.duiba.activity.center.biz.entity.plugin.ActivityPluginEntity" parameterType="Map">
		select <include refid="fields"/>
		from tb_activity_plugin
		where  deleted = 0 and
			id IN
			<foreach collection="ids" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
			<if test="status!= null"> and plugin_status = #{status} </if>
	</select>

	<update id="updateAutoOnDateNull">
		update tb_activity_plugin set auto_on_date = #{autoOnDate} where id = #{id}
	</update>
	
	<update id="updateAutoOffDateNull">
		update tb_activity_plugin set auto_off_date = #{autoOffDate} where id = #{id}
	</update>

	<!-- 查找自动开启的插件活动id -->
	<select id="findAutoOnPlugin" resultType="Long">
		select id from tb_activity_plugin where plugin_status = 4 and auto_on_date &lt;= now() and ( auto_off_date &gt; now() or auto_off_date is null ) and sub_type = 'plugin' and deleted = 0 order by gmt_create
	</select>

	<update id="updateStatus" parameterType="Map" >
		update tb_activity_plugin set plugin_status = #{status} where id IN
		<foreach collection="list" item="id" separator="," open="(" close=")" >
			#{id}
		</foreach>
	</update>

	<select id="findAllOpenPlugin" resultType="cn.com.duiba.activity.center.biz.entity.plugin.ActivityPluginEntity">
		SELECT <include refid="fields"/>
		FROM tb_activity_plugin
		WHERE plugin_status = 1
		AND sub_type = 'plugin'
		AND deleted = 0
	</select>
</mapper>
