<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.rob.impl.TodayRobSeckillSpecifyDaoImpl">
	<sql id="fields">
		id as id,
	    today_rob_seckill_id as todayRobSeckillId,
	    activity_id as activityId,
	    app_id as appId,
	    app_name as appName,
	    gmt_create as gmtCreate,
	    gmt_modified as gmtModified
	</sql>

	<insert id="insertBatch" useGeneratedKeys="true" parameterType="java.util.List" keyProperty="id">
	    insert into tb_activity_today_rob_seckill_specify (today_rob_seckill_id, activity_id, app_id, app_name, gmt_create, gmt_modified)
	    values
	    <foreach collection="list" item="item" index="index" separator="," >
	        (#{item.todayRobSeckillId}, #{item.activityId}, #{item.appId}, #{item.appName}, now(), now())
	    </foreach>
	</insert>

	<delete id="deleteByTodayRobSeckillId" parameterType="java.lang.Long">
		DELETE FROM tb_activity_today_rob_seckill_specify WHERE today_rob_seckill_id=#{todayRobSeckillId}
	</delete>

	<select id="selectByAppId" parameterType="java.util.Map" resultType="cn.com.duiba.activity.center.biz.entity.rob.TodayRobSeckillSpecifyEntity" >
         select
         	t2.id as id,
		    t2.today_rob_seckill_id as todayRobSeckillId,
		    t2.activity_id as activityId,
		    t2.app_id as appId,
		    t2.app_name as appName,
		    t2.gmt_create as gmtCreate,
		    t2.gmt_modified as gmtModified
         from
         	tb_activity_today_rob_seckill t1
         join
         	tb_activity_today_rob_seckill_specify t2
         on
         	t1.id = t2.today_rob_seckill_id
         where
         	t1.enable = 1
         	and t1.deleted = 0
         	and t1.show_entrance = #{showEntrance}
         	and t2.app_id=#{appId}
         order by t1.start_time
    </select>

    <select id="selectByTodayRobSeckillId" parameterType="java.util.Map" resultType="cn.com.duiba.activity.center.biz.entity.rob.TodayRobSeckillSpecifyEntity">
         select <include refid="fields" /> from tb_activity_today_rob_seckill_specify
         where today_rob_seckill_id = #{todayRobSeckillId}
    </select>

</mapper>