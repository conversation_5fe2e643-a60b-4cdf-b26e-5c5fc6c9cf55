<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.rob.impl.TodayRobSeckillDaoImpl">


    <select id="selectSeckillById" parameterType="java.util.Map" resultType="cn.com.duiba.activity.center.biz.entity.rob.TodayRobSeckillEntity">
         select <include refid="fields" /> from tb_activity_today_rob_seckill
         where id = #{todayRobSeckillId}
    </select>
    
    <select id="selectTodayRobSeckillList" parameterType="java.util.Map" resultType="cn.com.duiba.activity.center.biz.entity.rob.TodayRobSeckillEntity">
         select <include refid="fields" /> from tb_activity_today_rob_seckill
         where deleted = false
		<if test="todayRobSeckillEntity.showEntrance != null and todayRobSeckillEntity.showEntrance !=''">
			and	show_entrance = #{todayRobSeckillEntity.showEntrance}
		</if>
		<if test="todayRobSeckillEntity.secondType != null and todayRobSeckillEntity.secondType !=''">
			and	second_type = #{todayRobSeckillEntity.secondType}
		</if>
         order by start_time desc
         limit #{pageNum},#{pageSize} 
    </select>
    
    <select id="selectTodayRobSeckillCount"  resultType="java.lang.Long"
			parameterType="cn.com.duiba.activity.center.biz.entity.rob.TodayRobSeckillEntity">
         select count(1) from tb_activity_today_rob_seckill where deleted = false
		<if test="showEntrance != null and showEntrance !=''">
			and show_entrance = #{showEntrance}
		</if>
		<if test="secondType != null and secondType !=''">
			and	second_type = #{secondType}
		</if>
    </select>
	
 	<update id="deleteTodayRobSeckill" parameterType="java.lang.Long">
 		update  tb_activity_today_rob_seckill set deleted =1 where id= #{todayRobSeckillId}
 	</update>
    
    <update id="updateTodayRobSeckill" parameterType="java.util.Map">
    	update tb_activity_today_rob_seckill set enable = #{enable} where id =#{todayRobSeckillId}
    </update>
    
   
    
    <insert id="insertSeckill" parameterType="cn.com.duiba.activity.center.biz.entity.rob.TodayRobSeckillEntity" useGeneratedKeys="true" keyProperty="id">
    	insert into tb_activity_today_rob_seckill
    	(
	    	start_time,
	    	enable,
	    	deleted,
	    	second_type,
			<if test="image != null and image !=''">
				image,
			</if>
	    	gmt_create,
	    	gmt_modified,
	    	show_entrance
    	)values
    	(
    		#{startTime},
	        0,
	        0,
	        #{secondType},
			<if test="image != null and image !=''">
				#{image},
			</if>
	        now(),
	        now(),
	        #{showEntrance}
    	)
    </insert>
    
    <update id="updateSeckill" parameterType="cn.com.duiba.activity.center.biz.entity.rob.TodayRobSeckillEntity" >
    	update tb_activity_today_rob_seckill set
    		start_time =#{startTime},
    	 	second_type = #{secondType},
    	 	enable =#{enable},
			image = #{image},
    	 	show_entrance = #{showEntrance}, gmt_modified = now() where id = #{id}
    </update>
    
	<sql id="fields">
		id as id,
	    start_time as startTime,
	    enable as enable,
	    deleted as deleted,
	    image as image,
	    second_type as secondType,
	    gmt_create as gmtCreate,
	    gmt_modified as gmtModified,
	    show_entrance as showEntrance
	</sql>
	
	<update id="disableActivityBeforeDate" parameterType="java.util.Map">
		update tb_activity_today_rob_seckill set 
		enable = 0
		<![CDATA[ 
		where start_time < #{date}
		]]>
		and second_type ='secondActivity'
		and start_time is not null
	</update>
	
	
	<select id="selectSeckillBeforeTime" parameterType="java.util.Map" resultType="cn.com.duiba.activity.center.biz.entity.rob.TodayRobSeckillEntity">
         select <include refid="fields" /> from tb_activity_today_rob_seckill
         where deleted=false and enable=true
         and <![CDATA[start_time <= #{beforeDate}]]>
         and show_entrance = #{showEntrance}
         order by start_time desc limit #{limit}
    </select>
    
    <select id="selectSeckillAfterTime" parameterType="java.util.Map" resultType="cn.com.duiba.activity.center.biz.entity.rob.TodayRobSeckillEntity">
         select <include refid="fields" /> from tb_activity_today_rob_seckill
         where deleted=false and enable=true
         and <![CDATA[start_time > #{afterDate}]]>
         and show_entrance = #{showEntrance}
		 and second_type = 'secondActivity'
         order by start_time limit #{limit}
    </select>
    
    <select id="findEnableTodayRobSeckillIds" resultType="java.lang.Long">
         select id from tb_activity_today_rob_seckill
         where deleted=0 and enable=1 and show_entrance='todayRob'
    </select>
</mapper>
