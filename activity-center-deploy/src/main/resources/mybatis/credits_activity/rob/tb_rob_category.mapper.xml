<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.rob_category.impl.RobCategoryDaoImpl">
    <sql id="Base_Column_List">
		  id,category_name as name,title
	</sql>

    <select id="getRobCategoryByIds" parameterType="java.util.Map" resultType="cn.com.duiba.activity.center.biz.entity.robcategory.RobCategoryEntity">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_rob_category
        WHERE deleted = false and id IN
        <foreach item="id" index="index" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


</mapper>
