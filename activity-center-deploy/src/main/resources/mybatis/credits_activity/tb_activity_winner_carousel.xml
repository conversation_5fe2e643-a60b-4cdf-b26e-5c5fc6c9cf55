<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.activity.center.biz.dao.activity.impl.ActivityWinnerCarouselDaoImpl">

    <resultMap id="winners" type="cn.com.duiba.activity.center.biz.entity.activity.ActivityWinnerCarouselEntity" autoMapping="true">
        <id column="id" property="id"/>
        <id column="operating_activity_id" property="operatingActivityId"/>
        <id column="operating_activity_type" property="operatingActivityType"/>
        <id column="sub_order_id" property="subOrderId"/>
        <id column="option_id" property="optionId"/>
        <id column="option_type" property="optionType"/>
        <id column="option_name" property="optionName"/>
        <id column="consumer_id" property="consumerId"/>
        <id column="app_id" property="appId"/>
        <id column="origin_type" property="originType"/>
        <id column="gmt_create" property="gmtCreate"/>
        <id column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <sql id="all_column">
        id,
        operating_activity_id,
        operating_activity_type,
        sub_order_id,
        option_id,
        option_type,
        option_name,
        consumer_id,
        app_id,
        origin_type,
        gmt_create,
        gmt_modified
    </sql>


    <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.activity.ActivityWinnerCarouselEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_activity_winner_carousel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operatingActivityId != null">operating_activity_id,</if>
            <if test="operatingActivityType != null">operating_activity_type,</if>
            <if test="subOrderId != null">sub_order_id,</if>
            <if test="optionId != null">option_id,</if>
            <if test="optionType != null">option_type,</if>
            <if test="optionName != null">option_name,</if>
            <if test="consumerId != null">consumer_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="originType != null">origin_type,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operatingActivityId != null">#{operatingActivityId},</if>
            <if test="operatingActivityType != null">#{operatingActivityType},</if>
            <if test="subOrderId != null">#{subOrderId},</if>
            <if test="optionId != null">#{optionId},</if>
            <if test="optionType != null">#{optionType},</if>
            <if test="optionName != null">#{optionName},</if>
            <if test="consumerId != null">#{consumerId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="originType != null">#{originType},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified}</if>
        </trim>
    </insert>

    <select id="findByOperationActivityId" parameterType="map" resultMap="winners">
        SELECT <include refid="all_column"/>
        FROM tb_activity_winner_carousel
        WHERE operating_activity_id = #{operatingActivityId}
        AND gmt_create &gt; #{gmtCreate}
        ORDER BY gmt_create DESC
        LIMIT #{count}
    </select>

    <select id="findByWinnerReq" parameterType="map" resultMap="winners">
        SELECT <include refid="all_column"/>
        FROM tb_activity_winner_carousel
        WHERE operating_activity_id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        AND origin_type = #{originType}
        AND app_id = #{appId}
        <if test="gmtCreate != null">
            AND gmt_create &gt; #{gmtCreate}
        </if>
        ORDER BY gmt_create DESC
        LIMIT #{count}
    </select>

</mapper>