<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.category.impl.ActivityCategoryRelationDaoImpl">

	<select id="selectByAppCategory" resultType="cn.com.duiba.activity.center.biz.entity.ActivityCategoryRelationEntity" >
		select * from tb_activity_category_relation 
		where app_id =#{appId}
		and category_id=#{categoryId} order by payload asc,gmt_create desc
	</select> 

	<update id="updatePayload">
		update tb_activity_category_relation set payload=#{payload} where id=#{id}
	</update>
	
 	<insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.ActivityCategoryRelationEntity" useGeneratedKeys="true" keyProperty="id">
		insert IGNORE into tb_activity_category_relation(
			category_id,
			operating_activity_id,
			app_id,
			payload,
			gmt_create,
			gmt_modified
		)values(
			#{categoryId},
			#{operatingActivityId},
			#{appId},
			#{payload},
			now(),
			now()
		)
	</insert>
	
	<delete id="deleteByOperatingActivityId" parameterType="long">
		delete from tb_activity_category_relation 
		where operating_activity_id = #{operatingActivityId}
	</delete>
	
	<select id="select" resultType="cn.com.duiba.activity.center.biz.entity.ActivityCategoryRelationEntity">
		select * from tb_activity_category_relation where id=#{id};
	</select>
	
	<select id="selectWithConditions" resultType="cn.com.duiba.activity.center.biz.entity.ActivityCategoryRelationEntity">
		select * from tb_activity_category_relation
		<trim prefix="where" prefixOverrides="and">
			<if test="id !=null ">
				and id = #{id}
			</if>
			<if test="categoryId != null">
				and category_id=#{categoryId}
			</if>
			<if test="operatingActivityId != null">
				and operating_activity_id=#{operatingActivityId}
			</if>
			<if test="appId != null">
				and app_id=#{appId}
			</if>
			<if test="payload != null">
				and payload=#{payload}
			</if>
		</trim>
	</select>
	
	<select id="selectByAppCategoryNew" resultType="cn.com.duiba.activity.center.biz.entity.ActivityCategoryRelationEntity" >
		select * from tb_activity_category_relation 
		where app_id =#{appId}
		and category_id=#{categoryId} order by payload asc,gmt_create desc
		LIMIT #{start}, #{pageSize}
	</select> 
	<delete id="deleteByOperatingActivityIdList" parameterType="Map">
		delete from tb_activity_category_relation 
		where operating_activity_id in
		<foreach collection="operatingActivityIdList" item="operatingActivityId" index="index" open="(" close=")" separator=",">
			#{operatingActivityId}
		</foreach>
	</delete>
	
	<select id="findMaxPlayload" resultType="Integer" parameterType="java.util.Map">
		select ifnull(max(payload),0) from tb_activity_category_relation where app_id = #{appId} and category_id=#{categoryId}
	</select>
	
<!-- 	<update id="updateCategoryContent">
		update tb_activity_category set content=#{content} where id=#{id}
	</update>

	<update id="updateCategoryEnable">
		update tb_activity_category set enable=1 where id=#{id}
	</update>
	
	<update id="updateCategoryDisable">
		update tb_activity_category set enable=0 where id=#{id}
	</update>
	
	<select id="selectAll" resultType="cn.com.duiba.activity.center.biz.entity.ActivityCategoryEntity">
		select * from tb_activity_category
	</select>
	
	<select id="select" resultType="cn.com.duiba.activity.center.biz.entity.ActivityCategoryEntity">
		select * from tb_activity_category where id=#{id}
	</select> -->

</mapper>
