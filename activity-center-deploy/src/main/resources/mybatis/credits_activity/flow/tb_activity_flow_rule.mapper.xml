<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.flow.impl.ActivityFlowRuleDaoImpl">

    <resultMap id="ActivityFlowRuleEntityMapper"
               type="cn.com.duiba.activity.center.biz.entity.flow.ActivityFlowRuleEntity">
        <result column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="rule_type" property="ruleType"/>
        <result column="activity_type" property="activityType"/>
        <result column="activity_ids" property="activityIds"/>
        <result column="proxy_flag" property="proxyFlag"/>
        <result column="valid_regions" property="validRegions"/>
        <result column="valid_period" property="validPeriod"/>
        <result column="invalid_sdate" property="invalidSdate"/>
        <result column="invalid_edate" property="invalidEdate"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <sql id="all_column">
		id, app_id, rule_type, activity_type, activity_ids, proxy_flag, valid_regions, valid_period,
		invalid_sdate, invalid_edate, gmt_create, gmt_modified
	</sql>

    <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.flow.ActivityFlowRuleEntity"
            useGeneratedKeys="true" keyProperty="id">
		insert into tb_activity_flow_rule (app_id, rule_type, activity_type, activity_ids,
		proxy_flag, valid_regions, valid_period, invalid_sdate, invalid_edate)
		values
		(#{appId}, #{ruleType}, #{activityType}, #{activityIds},
		#{proxyFlag}, #{validRegions}, #{validPeriod}, #{invalidSdate}, #{invalidEdate})
	</insert>

    <update id="update" parameterType="cn.com.duiba.activity.center.biz.entity.flow.ActivityFlowRuleEntity">
        update tb_activity_flow_rule
        set valid_regions=#{validRegions}, valid_period=#{validPeriod},invalid_sdate=#{invalidSdate},invalid_edate=#{invalidEdate}
        <if test="proxyFlag!=null">
            , proxy_flag=#{proxyFlag}
        </if>
        <if test="activityIds!=null">
            ,activity_ids=#{activityIds}
        </if>

        where app_id=#{appId} and rule_type=#{ruleType} and activity_type=#{activityType}
    </update>

    <select id="findByKey" parameterType="Map" resultMap="ActivityFlowRuleEntityMapper">
        select
        <include refid="all_column"/>
        from tb_activity_flow_rule
        where app_id=#{appId} and rule_type=#{ruleType} and activity_type=#{activityType}
    </select>


    <select id="findListByKey" parameterType="Map" resultMap="ActivityFlowRuleEntityMapper">
        select
        <include refid="all_column"/>
        from tb_activity_flow_rule
        where app_id in
        <foreach collection="appIds" item="appId" separator="," open="(" close=")" >
            #{appId}
        </foreach>
        and rule_type=#{ruleType} and activity_type=#{activityType}
    </select>


    <delete id="delete" parameterType="Long">
        delete from tb_activity_flow_rule where id=#{id}
    </delete>

    <select id="find" parameterType="Long" resultMap="ActivityFlowRuleEntityMapper">
        select
        <include refid="all_column"/>
        from tb_activity_flow_rule
        where id=#{id}
    </select>


</mapper>
