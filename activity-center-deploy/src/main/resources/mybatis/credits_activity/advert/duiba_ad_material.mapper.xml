<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper
	namespace="cn.com.duiba.activity.center.biz.dao.advert.impl.DuibaAdMaterialDaoImpl">
	<resultMap id="BaseResultMap"
		type="cn.com.duiba.activity.center.biz.entity.advert.DuibaAdMaterialEntity">
		<id column="id" property="id" />
		<result column="activity_id" property="activityId"/>
		<result column="activity_type" property="activityType" />
		<result column="ad_material_title" property="adMaterialTitle" />
		<result column="ad_describe" property="adDescribe" />
		<result column="ad_material_img" property="adMaterialImg" />
		<result column="remarks" property="remarks"/>
		<result column="gmt_create" property="gmtCreate"/>
		<result column="gmt_modified" property="gmtModified"/>
	</resultMap>
	<sql id="Base_Column_List">
		id, activity_id,activity_type,ad_material_title, ad_describe, ad_material_img, remarks, gmt_create,
		gmt_modified
	</sql>
	<select id="selectByPrimaryKey" resultMap="BaseResultMap"
		parameterType="java.lang.Long">
		select
		<include refid="Base_Column_List" />
		from tb_duiba_ad_material
		where id = #{id}
	</select>
	<select id="selectByIds" resultMap="BaseResultMap"
		parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from tb_duiba_ad_material
		where id in
		<foreach collection="ids" index="index" item="it" open="(" separator="," close=")">
			#{it}
		</foreach>
	</select>
	<select id = "selectByActivity" resultMap="BaseResultMap" parameterType = "java.util.Map">
		select
		<include refid="Base_Column_List" />
		from tb_duiba_ad_material
		where activity_id = #{activityId}
		and activity_type = #{activityType}
	</select>

	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		delete from tb_duiba_ad_material
		where id = #{id}
	</delete>
	<insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.advert.DuibaAdMaterialEntity" useGeneratedKeys="true" keyProperty="id">
		insert into tb_duiba_ad_material (activity_id,activity_type,ad_material_title, ad_describe,
		ad_material_img, remarks, gmt_create,
		gmt_modified)
		values 
		(#{activityId},#{activityType},
		#{adMaterialTitle}, #{adDescribe},
		#{adMaterialImg}, #{remarks},
		now(),now())
	</insert>
	<insert id="insertSelective" parameterType="cn.com.duiba.activity.center.biz.entity.advert.DuibaAdMaterialEntity" useGeneratedKeys="true" keyProperty="id">
		insert into tb_duiba_ad_material
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="activityId !=null">
				activity_id,
			</if>
			<if test="activityType !=null">
				activity_type,
			</if>
			<if test="adMaterialTitle != null">
				ad_material_title,
			</if>
			<if test="adDescribe != null">
				ad_describe,
			</if>
			<if test="adMaterialImg != null">
				ad_material_img,
			</if>
			<if test="remarks != null">
				remarks,
			</if>
				gmt_create,
				gmt_modified,
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="activityId !=null">
				#{activityId},
			</if>
			<if test="activityType !=null">
				#{activityType},
			</if>
			<if test="adMaterialTitle != null">
				#{adMaterialTitle},
			</if>
			<if test="adDescribe != null">
				#{adDescribe},
			</if>
			<if test="adMaterialImg != null">
				#{adMaterialImg},
			</if>
			<if test="remarks != null">
				#{remarks},
			</if>
				now(),
				now(),
		</trim>
	</insert>
	<update id="updateByPrimaryKeySelective" parameterType="cn.com.duiba.activity.center.biz.entity.advert.DuibaAdMaterialEntity">
		update tb_duiba_ad_material
		<set>
			<if test="adMaterialTitle != null">
				ad_material_title = #{adMaterialTitle,jdbcType=VARCHAR},
			</if>
			<if test="adDescribe != null">
				ad_describe = #{adDescribe,jdbcType=VARCHAR},
			</if>
			<if test="adMaterialImg != null">
				ad_material_img = #{adMaterialImg,jdbcType=VARCHAR},
			</if>
			<if test="remarks != null">
				remarks = #{remarks,jdbcType=VARCHAR},
			</if>
			gmt_modified = now()
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	<update id="updateByPrimaryKey" parameterType="cn.com.duiba.activity.center.biz.entity.advert.DuibaAdMaterialEntity">
		update tb_duiba_ad_material
		set ad_material_title = #{adMaterialTitle,jdbcType=VARCHAR},
		ad_describe = #{adDescribe,jdbcType=VARCHAR},
		ad_material_img = #{adMaterialImg,jdbcType=VARCHAR},
		remarks = #{remarks,jdbcType=VARCHAR},
		gmt_modified = now()
		where id = #{id,jdbcType=BIGINT}
	</update>
</mapper>