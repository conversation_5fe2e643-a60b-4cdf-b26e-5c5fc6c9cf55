<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.activity.impl.InsuranceResultDaoImpl">
	<insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.activity.InsuranceResultEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_insurance_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="consumerId != null">consumer_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="status != null">status,</if>
            <if test="message != null">message,</if>
            <if test="insuredOrder != null">insured_order,</if>
            <if test="insuredUrl != null">insured_url,</if>
            <if test="channelName != null">channel_name,</if>
            <if test="htmlId != null">html_id,</if>
            <if test="resultContent !=null">result_content,</if>
            <!-- <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if> -->
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
        	<if test="consumerId != null">#{consumerId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="status != null">#{status},</if>
            <if test="message != null">#{message},</if>
            <if test="insuredOrder != null">#{insuredOrder},</if>
            <if test="insuredUrl != null">#{insuredUrl},</if>
            <if test="channelName != null">#{channelName},</if>
            <if test="htmlId != null">#{htmlId},</if>
            <if test="resultContent !=null">#{resultContent}</if>
            <!-- <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if> -->
        </trim>
    </insert>
    
    <select id="findById" resultType="cn.com.duiba.activity.center.biz.entity.activity.InsuranceResultEntity">
		SELECT id, consumer_id, user_id, status, message, insured_order, insured_url, channel_name, gmt_create, gmt_modified,result_content
		FROM tb_insurance_result 
		WHERE id=#{id}
	</select>
	
	<select id="countSuccess" parameterType="java.util.Map" resultType="int">
		SELECT count(id) FROM tb_insurance_result
		<where>
			status = '0'
			<if test="channelName != null">
				AND channel_name=#{channelName}
			</if>
		</where>
	</select>
	
	<select id="countTotal" parameterType="java.util.Map" resultType="int">
		SELECT count(id) FROM tb_insurance_result
		<where>
			<if test="channelName != null">
				channel_name=#{channelName}
			</if>
		</where>
	</select>
	
	<!-- <select id="countList" resultType="com.alibaba.fastjson.JSONObject">
		SELECT channelName, SUM(total), SUM(successTotal) FROM 
		(
			SELECT channel_name AS channelName, COUNT(id) AS total, 0 AS successTotal FROM tb_insurance_result GROUP BY channel_name
			UNION ALL
			SELECT channel_name AS channelName, 0 AS total, COUNT(id) AS successTotal FROM tb_insurance_result WHERE status = '0' GROUP BY channel_name
		) t GROUP BY channelName
	</select> -->
</mapper>
