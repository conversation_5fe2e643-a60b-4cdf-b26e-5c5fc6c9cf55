<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.activity.impl.ActivityGroupRelationDaoImpl">

    <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.activity.ActivityGroupRelationEntity"
            useGeneratedKeys="true" keyProperty="id">
		insert into tb_activity_group_relation (
			group_id,
			activity_type,
			activity_id,
			payload)
		values(
			#{groupId},
			#{activityType},
			#{activityId},
			#{payload})
	</insert>

    <delete id="deleteById" parameterType="java.lang.Long">
		delete from tb_activity_group_relation where id = #{id}
	</delete>

    <delete id="deleteByIds" parameterType="java.util.Map">
        delete from tb_activity_group_relation
        where id in
        <foreach collection="ids" item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
    </delete>

    <delete id="deleteByGroupId" parameterType="java.lang.Long">
		delete from tb_activity_group_relation where group_id= #{groupId}
	</delete>

    <update id="updateById"
            parameterType="cn.com.duiba.activity.center.biz.entity.activity.ActivityGroupRelationEntity">
        update tb_activity_group_relation
        set id=#{id}
        <if test="groupId!=null">,group_id=#{groupId}</if>
        <if test="activityType!=null">,activity_type=#{activityType}</if>
        <if test="activityId!=null">,activity_id=#{activityId}</if>
        <if test="payload!=null">,payload=#{payload}</if>
        where id=#{id}
    </update>

    <select id="findByGroupIdAndTypeAndActivityId" parameterType="java.util.Map"
            resultType="cn.com.duiba.activity.center.biz.entity.activity.ActivityGroupRelationEntity">
        select
        <include refid="fileds"/>
        from tb_activity_group_relation
        where group_id=#{groupId} and activity_type = #{activityType} and activity_id = #{activityId}
        limit 1
    </select>

    <select id="findByActivityIdAndType" parameterType="java.util.Map"
            resultType="cn.com.duiba.activity.center.biz.entity.activity.ActivityGroupRelationEntity">
        select
        <include refid="fileds"/>
        from tb_activity_group_relation
        where activity_type = #{activityType} and activity_id = #{activityId}
        limit 1
    </select>

    <select id="findByGroupId" resultType="cn.com.duiba.activity.center.biz.entity.activity.ActivityGroupRelationEntity"
            parameterType="java.lang.Long">
        select
        <include refid="fileds"/>
        from tb_activity_group_relation
        where group_id=#{groupId}
        order by payload ASC
    </select>

    <select id="findMaxPayloadByGroupId" parameterType="java.lang.Long"
            resultType="java.lang.Integer">
		select max(payload) from tb_activity_group_relation where group_id = #{groupId}
    </select>

    <sql id="fileds">
		id as id,
		group_id as groupId,
		activity_type as activityType,
		activity_id as activityId,
		payload as payload,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified
	</sql>
</mapper>
