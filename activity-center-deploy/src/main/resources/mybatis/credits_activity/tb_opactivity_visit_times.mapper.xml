<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.activity.impl.OpActivityVisitTimesDaoImpl">

	<insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.activity.OpActivityVisitTimesEntity">
		insert ignore into tb_opactivity_visit_times (
		op_activity_id,
		visit_times)
		values(
		#{opActivityId},
		#{visitTimes})
	</insert>

	<sql id="all_column">
		op_activity_id as opActivityId,
		visit_times as visitTimes
	</sql>

	<update id="updateByOpActivityId" parameterType="cn.com.duiba.activity.center.biz.entity.activity.OpActivityVisitTimesEntity">
		update tb_opactivity_visit_times

		set  visit_times=(visit_times+#{visitTimes})

		where op_activity_id=#{opActivityId}
	</update>

	<select id="findByOpActivityId" resultType="cn.com.duiba.activity.center.biz.entity.activity.OpActivityVisitTimesEntity"
			parameterType="java.util.Map">
		select
		<include refid="all_column"/>
		from tb_opactivity_visit_times
		where op_activity_id=#{opActivityId}
	</select>

	<select id="findByOpActivityIds" resultType="cn.com.duiba.activity.center.biz.entity.activity.OpActivityVisitTimesEntity"
			parameterType="java.util.Map">
		select
		<include refid="all_column"/>
		from tb_opactivity_visit_times
		where op_activity_id in
		<foreach collection="opActivityIds" index="index" open="(" close=")" item="it" separator=",">
			#{it}
		</foreach>
	</select>
</mapper>
