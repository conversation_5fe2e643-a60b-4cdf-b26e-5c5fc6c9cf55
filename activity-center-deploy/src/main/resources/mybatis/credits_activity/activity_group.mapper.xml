<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.activity.impl.ActivityGroupDaoImpl">

    <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.activity.ActivityGroupEntity"
            useGeneratedKeys="true" keyProperty="id">
		insert into tb_activity_group (
			title)
		values(
			#{title})
	</insert>

    <update id="deleteById" parameterType="java.lang.Long">
		update tb_activity_group set deleted=1 where id=#{id}
	</update>

    <update id="updateById" parameterType="cn.com.duiba.activity.center.biz.entity.activity.ActivityGroupEntity">
        update tb_activity_group
        set id=#{id}
        <if test="title!=null">,title=#{title}</if>
        <if test="deleted!=null">,deleted=#{deleted}</if>
        where id=#{id}
    </update>

    <select id="findById" resultType="cn.com.duiba.activity.center.biz.entity.activity.ActivityGroupEntity"
            parameterType="java.util.Map">
        select
        <include refid="fileds"/>
        from tb_activity_group
        where id=#{id} <if test="deleted!=null"> and deleted=#{deleted}</if>
    </select>

    <select id="countGroup" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(*) from tb_activity_group where 1=1
        <if test="deleted!=null"> and deleted=#{deleted}</if>
    </select>

    <select id="findActivityGroupByPage" resultType="cn.com.duiba.activity.center.biz.entity.activity.ActivityGroupEntity"
            parameterType="java.util.Map">
        select
        <include refid="fileds"/>
        from tb_activity_group
        where 1=1
        <if test="deleted!=null">and deleted=#{deleted}</if>
        limit #{start},#{pageSize}
    </select>

    <sql id="fileds">
		id as id,
		title as title,
		deleted as deleted,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified
	</sql>
</mapper>
