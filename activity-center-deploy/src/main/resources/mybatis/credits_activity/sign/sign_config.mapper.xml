<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.sign.activity.impl.SignConfigDaoImpl">
	
	<resultMap type="SignConfigEntity" id="signConfigMap">
		<id column="id" property="id"/>
		<result column="app_id" property="appId"/>
		<result column="title" property="title"/>
		<result column="brick_id" property="brickId"/>
		<result column="rule_description" property="ruleDescription"/>
		<result column="rate_description" property="rateDescription"/>
		<result column="trigger_type" property="triggerType"/>
		<result column="sign_type" property="signType"/>
		<result column="sign_credits" property="signCredits"/>
		<result column="image_url" property="imageUrl"/>
		<result column="status" property="status"/>
		<result column="deleted" property="deleted"/>
		<result column="switches" property="switches"/>
		<result column="gmt_create" property="gmtCreate"/>
		<result column="gmt_modified" property="gmtModified"/>
	</resultMap>
	
	<sql id="fields">
		id,
		app_id,
		title,
		brick_id,
		rule_description,
		rate_description,
		trigger_type,
		sign_type,
		sign_credits,
		image_url,
		status,
		deleted,
		switches,
		gmt_create,
		gmt_modified
	</sql>

	<select id="findAllByIds" parameterType="map" resultMap="signConfigMap">
		select
		<include refid="fields" />
		from tb_sign_config
		where id in
		<foreach collection="ids" index="index" item="it" open="(" separator="," close=")">
			#{it}
		</foreach>
	</select>

	<select id="findByPage" parameterType="map" resultMap="signConfigMap">
		select 
		<include refid="fields" />
		from tb_sign_config
		where app_id = #{appId}
		and deleted = 0
		<include refid="condition" />
		order by gmt_create desc
		limit #{offset},#{max}
	</select>
	
	<select id="findByPageCount" parameterType="map" resultType="Long">
		select 
		count(1)
		from tb_sign_config
		where app_id = #{appId}
		and deleted = 0
		<include refid="condition" />	
	</select>
	
	<sql id="condition">
		<if test="title != null and title != ''"> and title like CONCAT('%',#{title},'%') </if>
		<if test="id != null"> and id = #{id} </if>
	</sql>

	<select id="find" parameterType="long" resultMap="signConfigMap">
		select 
		<include refid="fields" />
		from tb_sign_config
		where id = #{id} limit 0,1
	</select>
	
	<select id="findByAppId" parameterType="long" resultMap="signConfigMap">
		select 
		<include refid="fields" />
		from tb_sign_config
		where app_id = #{appId} and deleted=0 limit 0,1
	</select>
	
	<insert id="add" parameterType="SignConfigEntity">
		<selectKey resultType="long" order="AFTER" keyProperty="id"> 
	        select @@IDENTITY as id
	    </selectKey>
		insert into tb_sign_config(
			title,
			app_id,
			brick_id,
			rule_description,
			rate_description,
			trigger_type,
			sign_type,
			sign_credits,
			image_url,
			deleted,
			switches,
			gmt_create,
			gmt_modified
		) values(
			#{title},
			#{appId},
			#{brickId},
			#{ruleDescription},
			#{rateDescription},
			#{triggerType},
			#{signType},
			#{signCredits},
			#{imageUrl},
			0,
			#{switches},
			now(),
			now()
		)
	</insert>
	
	<update id="delete" parameterType="long">
		update tb_sign_config set deleted = 1,gmt_modified=now() where id=#{id}
	</update>
	
	<update id="update" parameterType="SignConfigEntity">
		update tb_sign_config set gmt_modified=now()
		<if test="title != null">
			,title=#{title}
		</if>
		<if test="appId != null ">
			,app_id=#{appId}
		</if>
		<if test="brickId != null ">
			,brick_id=#{brickId}
		</if>
		<if test="ruleDescription != null ">
			,rule_description=#{ruleDescription}
		</if>
		<if test="rateDescription != null ">
			,rate_description=#{rateDescription}
		</if>
		<if test="triggerType != null ">
			,trigger_type=#{triggerType}
		</if>
		<if test="signType != null ">
			,sign_type=#{signType}
		</if>
		<if test="signCredits != null ">
			,sign_credits=#{signCredits}
		</if>
		<if test="imageUrl != null ">
			,image_url=#{imageUrl}
		</if>
		<if test="switches != null ">
			,switches=#{switches}
		</if>
		where id=#{id}
	</update>

	<update id="updateStatus" parameterType="java.util.Map">
		update tb_sign_config set status = #{status} where id = #{id}
	</update>

	<select id="findAllByAppIds" parameterType="Map" resultMap="signConfigMap">
		select
		<include refid="fields" />
		from tb_sign_config
		where app_id in
		<foreach collection="appIds" index="index" item="it" open="(" separator="," close=")">
			#{it}
		</foreach>
	</select>

</mapper>
