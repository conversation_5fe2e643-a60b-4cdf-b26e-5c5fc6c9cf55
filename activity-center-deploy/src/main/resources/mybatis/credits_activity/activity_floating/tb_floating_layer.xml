<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.activity_floating.impl.FloatingLayerDaoImpl">

	<resultMap id="floatingLayerEntity" type="cn.com.duiba.activity.center.biz.entity.activity_floating.FloatingLayerEntity" autoMapping="true">
		<id column="id" property="id"/>
		<id column="title" property="title"/>
		<id column="js_text" property="jsText"/>
		<id column="css_text" property="cssText"/>
		<id column="image" property="image"/>
		<id column="admin_id" property="adminId"/>
		<id column="gmt_create" property="gmtCreate"/>
		<id column="gmt_modified" property="gmtModified"/>
	</resultMap>

	<sql id="all4Query">
		id, title, js_text, css_text, image, admin_id, gmt_create, gmt_modified
	</sql>

	<select id="findByPage" resultType="cn.com.duiba.activity.center.biz.entity.activity_floating.FloatingLayerEntity" parameterType="Map">
		select id,
			title,
			js_text as jsText,
			css_text as cssText,
			image 
		from tb_floating_layer
		where deleted = 0 
			<if test="title!= null">and title like CONCAT('%',#{title},'%')</if>
			order by id desc
			<if test="offset != null">limit #{offset},#{max} </if>
	</select>
	
	<insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.activity_floating.FloatingLayerEntity" useGeneratedKeys="true" keyProperty="id">
		insert into tb_floating_layer 
			<trim prefix="(" suffix=")" suffixOverrides=",">
				<if test="title != null">title,</if>
				<if test="jsText != null">js_text,</if>
				<if test="cssText != null">css_text,</if>
				<if test="image != null">image,</if>
				<if test="adminId != null">admin_id,</if>
				<if test="gmtCreate != null">gmt_create,</if>
				<if test="gmtModified != null">gmt_modified,</if>
			</trim>
			<trim prefix="values (" suffix=")" suffixOverrides=",">
				<if test="title != null">#{title},</if>
				<if test="jsText != null">#{jsText},</if>
				<if test="cssText != null">#{cssText},</if>
				<if test="image != null">#{image},</if>
				<if test="adminId != null">#{adminId},</if>
				<if test="gmtCreate != null">#{gmtCreate},</if>
				<if test="gmtModified != null">#{gmtModified},</if>
			</trim>
	</insert>
	
	<update id="update" parameterType="cn.com.duiba.activity.center.biz.entity.activity_floating.FloatingLayerEntity">
		update tb_floating_layer
		set gmt_modified = #{gmtModified}
			<if test="title != null">,title = #{title}</if>
			<if test="jsText != null">,js_text = #{jsText}</if>
			<if test="cssText != null">,css_text = #{cssText}</if>
			<if test="image != null">,image = #{image}</if>
			<if test="adminId != null">,admin_id = #{adminId}</if>
		where id = #{id}
	</update>

	<select id="findById" resultType="cn.com.duiba.activity.center.biz.entity.activity_floating.FloatingLayerEntity" parameterType="Long">
		select id,
			title,
			js_text as jsText,
			css_text as cssText,
			image 
		from tb_floating_layer where  id= #{id}
	</select>

	<select id="findFloatingLayerList" parameterType="map" resultMap="floatingLayerEntity">
		SELECT <include refid="all4Query"/>
		FROM tb_floating_layer
		<if test="max != null">
			limit #{offset}, #{max}
		</if>
	</select>

	<select id="findFloatingLayerCount" resultType="int">
		SELECT count(*)
		FROM tb_floating_layer
	</select>

</mapper>
