<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.activity_floating.impl.FloatingLayerRelationDaoImpl">

	<select id="findCountById" resultType="int">
		select count(id) from tb_floating_layer_relation  where deleted = 0 and floating_layer_id = #{id}
	</select>
	
	<delete id="delet" parameterType="java.lang.Long">
		delete from tb_floating_layer_relation where floating_layer_id = #{id}
	</delete>
	
	<insert id="insertList" keyColumn="id" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
		insert into tb_floating_layer_relation (floating_layer_id, relation_id,relation_type,admin_id,deleted) 
		values
			<foreach collection="list" item="item" index="index" separator=",">
				(#{item.floatingLayerId}, #{item.relationId}, #{item.relationType}, #{item.adminId}, #{item.deleted} )
			</foreach>
	</insert>
	
	<select id="findByRelationIdAndType" resultType="cn.com.duiba.activity.center.biz.entity.activity_floating.FloatingLayerRelationEntity" parameterType="Map">
		select id,floating_layer_id as relationId from tb_floating_layer_relation  where deleted = 0 and relation_type = #{relationType} and relation_id = #{relationId} ORDER BY id DESC LIMIT 1
	</select>
	
	<select id="findByRelationType" resultType="cn.com.duiba.activity.center.biz.entity.activity_floating.FloatingLayerRelationEntity" parameterType="java.lang.String">
		select id,floating_layer_id as relationId from tb_floating_layer_relation  where deleted = 0 and relation_type = #{relationType} ORDER BY floating_layer_id DESC LIMIT 1
	</select>
	
	<select id="findById" resultType="cn.com.duiba.activity.center.biz.entity.activity_floating.FloatingLayerRelationEntity" parameterType="java.lang.Long">
		select id,floating_layer_id as relationId from tb_floating_layer_relation where floating_layer_id = #{id}
	</select>

	<select id="findByFloatingId" resultType="cn.com.duiba.activity.center.biz.entity.activity_floating.FloatingLayerRelationEntity" parameterType="long">
		SELECT id, floating_layer_id floatingLayerId, relation_id relationId, relation_type relationType
		FROM tb_floating_layer_relation
		WHERE deleted = 0
		AND floating_layer_id = #{id}
	</select>
</mapper>
