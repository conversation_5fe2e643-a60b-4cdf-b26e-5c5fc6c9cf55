<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.iqiyi.impl.HappyCodeDaoImpl">

    <resultMap id="entity" type="cn.com.duiba.activity.center.biz.entity.iqiyi.HappyCodeEntity">
        <result column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="operating_activity_id" property="operatingActivityId"/>
        <result column="partner_user_id" property="partnerUserId"/>
        <result column="consumer_id" property="consumerId"/>
        <result column="happy_code" property="happyCode"/>
        <result column="phase_id" property="phaseId"/>
        <result column="phase_number" property="phaseNumber"/>
        <result column="item_id" property="itemId"/>
        <result column="status" property="status"/>
        <result column="consumer_exchange_id" property="consumerExchangeId"/>
        <result column="order_num" property="orderNum"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <sql id="AllColumn">
        id,
        app_id,
        operating_activity_id,
        partner_user_id,
        consumer_id,
        happy_code,
        phase_id,
        phase_number,
        item_id,
        status,
        consumer_exchange_id,
        order_num,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.iqiyi.HappyCodeEntity" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO tb_iqiyi_happy_code
        (app_id,
        operating_activity_id,
        partner_user_id,
        consumer_id,
        happy_code,
        phase_id,
        phase_number,
        item_id,
        status
        )
        VALUES
        (#{appId},
        #{operatingActivityId},
        #{partnerUserId},
        #{consumerId},
        #{happyCode},
        #{phaseId},
        #{phaseNumber},
        #{itemId},
        #{status}
        )
    </insert>

    <select id="getById" resultMap="entity">
        SELECT <include refid="AllColumn"/>
        FROM tb_iqiyi_happy_code
        WHERE id = #{id}
    </select>

    <select id="listByConsumerId" parameterType="map" resultMap="entity">
        SELECT <include refid="AllColumn"/>
        FROM tb_iqiyi_happy_code
        WHERE consumer_id = #{consumerId}
    </select>

    <select id="getByHappyCode" parameterType="map" resultMap="entity">
        SELECT <include refid="AllColumn"/>
        FROM tb_iqiyi_happy_code
        WHERE happy_code = #{happyCode}
    </select>

    <select id="getCount" parameterType="map" resultType="Long">
        SELECT count(*)
        FROM tb_iqiyi_happy_code
        WHERE phase_id = #{phaseId}
        AND item_id = #{itemId}
    </select>

    <select id="getRandomByPhaseId" parameterType="map" resultMap="entity">
        SELECT <include refid="AllColumn"/>
        FROM tb_iqiyi_happy_code
        WHERE phase_id = #{phaseId}
        AND item_id = #{itemId}
        limit #{start},1
    </select>

    <update id="openPrize">
        UPDATE tb_iqiyi_happy_code
        SET status = 3, consumer_exchange_id = #{consumerExchangeId}, order_num = #{orderNum}
        WHERE id = #{id} AND status in (0,1)
    </update>

</mapper>