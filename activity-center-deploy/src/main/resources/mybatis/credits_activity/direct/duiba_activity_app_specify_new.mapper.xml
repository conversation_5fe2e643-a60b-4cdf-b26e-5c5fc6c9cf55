<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.direct.impl.DuibaActivityAppSpecifyNewDaoImpl">

	<insert id="insertAppSpecify" parameterType="cn.com.duiba.activity.center.api.dto.direct.DuibaActivityAppSpecifyNewDto" useGeneratedKeys="true" keyProperty="id">
		insert into tb_duiba_activity_app_specify (app_id,relation_id,relation_type,hd_type,gmt_create,gmt_modified)
		VALUES (#{appId},#{relationId},#{relationType},#{hdType},now(),now())
		<selectKey keyProperty="id" resultType="long" order="AFTER">
			select LAST_INSERT_ID()
		</selectKey>
	</insert>

	<select id="findAppSpecifyByActivityIdAndAppIdAndActivityType" parameterType="Map" resultType="cn.com.duiba.activity.center.api.dto.direct.DuibaActivityAppSpecifyNewDto">
		select <include refid="fields" /> from tb_duiba_activity_app_specify
		where app_id = #{appId} and relation_id = #{relationId} and relation_type=#{relationType} limit 1
	</select>

	<select id="batchFindIsSpecifyActivityIds" parameterType="Map" resultType="cn.com.duiba.activity.center.api.dto.direct.DuibaActivityAppSpecifyNewDto">
		select <include refid="fields" />  from tb_duiba_activity_app_specify
		where app_id = #{appId} and relation_type=#{relationType} and relation_id IN
		<foreach collection="relationIds" item="relationId" open="(" separator="," close=")">
			#{relationId}
		</foreach>
	</select>

	<select id="findByAppIdAndType" parameterType="Map" resultType="java.lang.Long">
		select relation_id
		from tb_duiba_activity_app_specify
		where app_id = #{appId} and relation_type=#{relationType}
	</select>

	<select id="findAppSpecifyByActivityIdAndActivityType" parameterType="Map" resultType="cn.com.duiba.activity.center.api.dto.direct.DuibaActivityAppSpecifyNewDto">
		
		select <include refid="fields"></include> from tb_duiba_activity_app_specify
		    where  relation_id = #{relationId} and relation_type=#{relationType}
	</select>
	
	<delete id="deleteAppSpecifyById" parameterType="Long" >
		DELETE  from tb_duiba_activity_app_specify where id=#{id}
	</delete>

	<select id="findAppSpecifyById" parameterType="Long" resultType="cn.com.duiba.activity.center.api.dto.direct.DuibaActivityAppSpecifyNewDto">

		SELECT <include refid="fields"></include> from tb_duiba_activity_app_specify where id=#{id}
	 </select>

	<select id="findAllAppIdByActivityIdsAndRelationType" parameterType="Map" resultType="long">
		SELECT app_id
		FROM tb_duiba_activity_app_specify
		WHERE relation_id IN
		<foreach collection="relationIds" open="(" item="relationId" separator="," close=")">
			#{relationId}
		</foreach>
		AND relation_type = #{relationType}
	</select>

	<select id="countByRelationIds" parameterType="Map" resultType="cn.com.duiba.activity.center.api.dto.direct.AppQuantityDto">
		SELECT relation_id as relationId,count(*) as joinedAppQuantity
		FROM tb_duiba_activity_app_specify
		WHERE relation_id IN
		<foreach collection="relationIds" open="(" item="relationId" separator="," close=")">
			#{relationId}
		</foreach>
		AND relation_type = #{relationType}
		group by relation_id
	</select>

	<sql id="column">
		id,
		app_id,
		relation_id,
		relation_type,
		hd_type,
		gmt_create,
		gmt_modified
	</sql>
	<sql id="fields">
		id as id,
		app_id as appId,
		relation_id as relationId,
		relation_type as relationType,
		hd_type as hdType,
		gmt_create as `gmtCreate`,
		gmt_modified as gmtModified
	</sql>
</mapper>
