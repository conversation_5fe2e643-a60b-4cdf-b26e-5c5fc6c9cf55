<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.activity.center.biz.dao.hdtool.impl.DuibaHdtoolSelectionsDaoImpl">

	<resultMap id="duibaHdtoolSelectionsEntity" type="cn.com.duiba.activity.center.biz.entity.hdtool.DuibaHdtoolSelectionsEntity" autoMapping="true">
		<id column="id" property="id"/>
		<id column="hdtool_id" property="duibaHdtoolId"/>
		<id column="position" property="position"/>
		<id column="content" property="content"/>
	</resultMap>

	<sql id="all_column">
		id,
		hdtool_id,
		position,
		content 
	</sql>

	<select id="find" parameterType="Long" resultMap="duibaHdtoolSelectionsEntity">
		SELECT <include refid="all_column"/>
		FROM tb_activity_guess_selections
		WHERE id = #{id}
	</select>
	
	<select id="findAllByHdtoolId" parameterType="Long" resultMap="duibaHdtoolSelectionsEntity">
		SELECT <include refid="all_column"/>
		FROM tb_activity_guess_selections
		WHERE hdtool_id = #{htdoolId}
	</select>

	<delete id="deleteByduibaHtdoolId" parameterType="long">
		delete from tb_activity_guess_selections  where hdtool_id = #{htdoolId}
	</delete>

	<insert id="insertSelections" keyColumn="id" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
		insert into tb_activity_guess_selections (hdtool_id,position,content ) 
		values
			<foreach collection="list" item="item" index="index" separator=",">
				(#{item.duibaHdtoolId},#{item.position},#{item.content})
			</foreach>
	</insert>

</mapper>