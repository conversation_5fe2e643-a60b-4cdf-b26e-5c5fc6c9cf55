<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.creditsfarm.impl.CreditsFarmUserRecordDaoImpl">

    <resultMap type="cn.com.duiba.activity.center.biz.entity.creditsfarm.CreditsFarmUserRecordEntity" id="creditsFarmUserRecordMap">
        <result property="id" column="id"/>
        <result property="actId" column="act_id"/>
        <result property="appId" column="app_id"/>
        <result property="consumerId" column="consumer_id"/>
        <result property="partnerUserId" column="partner_user_id"/>
        <result property="lastSignTime" column="last_sign_time"/>
        <result property="lastSpeedTime" column="last_speed_time"/>
        <result property="speedFrozeTime" column="speed_froze_time"/>
        <result property="cropCountTotal" column="crop_count_total"/>
        <result property="cropCountToday" column="crop_count_today"/>
        <result property="lastCropTime" column="last_crop_time"/>
        <result property="nutritionCount" column="nutrition_count"/>
        <result property="nutritionCountToday" column="nutrition_count_today"/>
        <result property="lastNutritionAddTime" column="last_nutrition_add_time"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="columns">
        id,
        act_id,
        app_id,
        consumer_id,
        partner_user_id,
        last_sign_time,
        last_speed_time,
        speed_froze_time,
        crop_count_total,
        crop_count_today,
        last_crop_time,
        nutrition_count,
        nutrition_count_today,
        last_nutrition_add_time,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.biz.entity.creditsfarm.CreditsFarmUserRecordEntity">
        INSERT INTO tb_credits_farm_user_record
        (act_id,
         app_id,
         consumer_id,
         partner_user_id,
         last_sign_time,
         last_speed_time,
         speed_froze_time,
         <if test="cropCountTotal != null">crop_count_total,</if>
         <if test="cropCountToday != null">crop_count_today,</if>
         last_crop_time,
         <if test="nutritionCount != null"> nutrition_count,</if>
         <if test="nutritionCountToday != null"> nutrition_count_today,</if>
         last_nutrition_add_time)
        VALUES(
        #{actId},
        #{appId},
        #{consumerId},
        #{partnerUserId},
        #{lastSignTime},
        #{lastSpeedTime},
        #{speedFrozeTime},
        <if test="cropCountTotal != null">#{cropCountTotal},</if>
        <if test="cropCountToday != null">#{cropCountToday},</if>
        #{lastCropTime},
        <if test="nutritionCount != null">#{nutritionCount},</if>
        <if test="nutritionCountToday != null">#{nutritionCountToday},</if>
        #{lastNutritionAddTime})
    </insert>

    <update id="updateById" parameterType="cn.com.duiba.activity.center.biz.entity.creditsfarm.CreditsFarmUserRecordEntity">
        UPDATE tb_credits_farm_user_record
        <set>
            <if test="actId != null">
                act_id = #{actId},
            </if>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="consumerId != null">
                consumer_id = #{consumerId},
            </if>
            <if test="partnerUserId != null">
                partner_user_id = #{partnerUserId},
            </if>
            <if test="lastSignTime != null">
                last_sign_time = #{lastSignTime},
            </if>
            <if test="lastSpeedTime != null">
                last_speed_time = #{lastSpeedTime},
            </if>
            <if test="speedFrozeTime != null">
                speed_froze_time = #{speedFrozeTime},
            </if>
            <if test="cropCountTotal != null">
                crop_count_total = #{cropCountTotal},
            </if>
            <if test="cropCountToday != null">
                crop_count_today = #{cropCountToday},
            </if>
            <if test="lastCropTime != null">
                last_crop_time = #{lastCropTime},
            </if>
            <if test="nutritionCount != null">
                nutrition_count = #{nutritionCount},
            </if>
            <if test="nutritionCountToday != null">
                nutrition_count_today = #{nutritionCountToday},
            </if>
            <if test="lastNutritionAddTime != null">
                last_nutrition_add_time = #{lastNutritionAddTime},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="findById" resultMap="creditsFarmUserRecordMap">
        SELECT <include refid="columns"/>
        FROM tb_credits_farm_user_record
        WHERE id = #{id}
    </select>

    <select id="findByUnikey" resultMap="creditsFarmUserRecordMap">
        SELECT <include refid="columns"/>
        FROM tb_credits_farm_user_record
        WHERE
        consumer_id = #{consumerId}
        AND
        act_id = #{actId}
    </select>



</mapper>