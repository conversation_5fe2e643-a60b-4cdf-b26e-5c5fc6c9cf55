<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.creditsfarm.impl.CreditsFarmSeedDaoImpl">

    <resultMap type="cn.com.duiba.activity.center.biz.entity.creditsfarm.CreditsFarmSeedEntity" id="creditsFarmSeedMap">
        <result property="id" column="id"/>
        <result property="actId" column="act_id"/>
        <result property="seedName" column="seed_name"/>
        <result property="seedType" column="seed_type"/>
        <result property="seedTypeName" column="seed_type_name"/>
        <result property="payload" column="payload"/>
        <result property="cropCredits" column="crop_credits"/>
        <result property="normalSeedPic" column="normal_seed_pic"/>
        <result property="immatureSeedPic" column="immature_seed_pic"/>
        <result property="matureSeedPic" column="mature_seed_pic"/>
        <result property="resultSeedPic" column="result_seed_pic"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="columns">
        id,
        act_id,
        seed_name,
        seed_type,
        seed_type_name,
        payload,
        crop_credits,
        normal_seed_pic,
        immature_seed_pic,
        mature_seed_pic,
        result_seed_pic,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="save" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.biz.entity.creditsfarm.CreditsFarmSeedEntity">
        INSERT INTO tb_credits_farm_seed(act_id,seed_name,seed_type,seed_type_name,payload,crop_credits,normal_seed_pic,immature_seed_pic,mature_seed_pic,result_seed_pic)
        VALUES(#{actId},#{seedName},#{seedType},#{seedTypeName},#{payload},#{cropCredits},#{normalSeedPic},#{immatureSeedPic},#{matureSeedPic},#{resultSeedPic})
    </insert>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO tb_credits_farm_seed
        (act_id,
        seed_name,
        seed_type,
        seed_type_name,
        payload,
        crop_credits,
        normal_seed_pic,
        immature_seed_pic,
        mature_seed_pic,
        result_seed_pic
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.actId},
            #{item.seedName},
            #{item.seedType},
            #{item.seedTypeName},
            #{item.payload},
            #{item.cropCredits},
            #{item.normalSeedPic},
            #{item.immatureSeedPic},
            #{item.matureSeedPic},
            #{item.resultSeedPic}
            )
        </foreach>
    </insert>

    <update id="updateById" parameterType="cn.com.duiba.activity.center.biz.entity.creditsfarm.CreditsFarmSeedEntity">
        UPDATE tb_credits_farm_seed
        <set>
            <if test="actId != null">
                act_id = #{actId},
            </if>
            <if test="seedName != null">
                seed_name = #{seedName},
            </if>
            <if test="seedType != null">
                seed_type = #{seedType},
            </if>
            <if test="seedTypeName != null">
                seed_type_name = #{seedTypeName},
            </if>
            <if test="payload != null">
                payload = #{payload},
            </if>
            <if test="cropCredits != null">
                crop_credits = #{cropCredits},
            </if>
            <if test="normalSeedPic != null">
                normal_seed_pic = #{normalSeedPic},
            </if>
            <if test="immatureSeedPic != null">
                immature_seed_pic = #{immatureSeedPic},
            </if>
            <if test="matureSeedPic != null">
                mature_seed_pic = #{matureSeedPic},
            </if>
            <if test="resultSeedPic != null">
                result_seed_pic = #{resultSeedPic},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <update id="updatePicById" parameterType="cn.com.duiba.activity.center.biz.entity.creditsfarm.CreditsFarmSeedEntity">
        UPDATE tb_credits_farm_seed
        <set>
            <if test="normalSeedPic != null">
                normal_seed_pic = #{normalSeedPic},
            </if>
            <if test="immatureSeedPic != null">
                immature_seed_pic = #{immatureSeedPic},
            </if>
            <if test="matureSeedPic != null">
                mature_seed_pic = #{matureSeedPic},
            </if>
            <if test="resultSeedPic != null">
                result_seed_pic = #{resultSeedPic},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="getById" resultMap="creditsFarmSeedMap">
        SELECT <include refid="columns"/>
        FROM tb_credits_farm_seed
        WHERE id = #{id}
    </select>

    <select id="listByActId" resultMap="creditsFarmSeedMap">
        SELECT <include refid="columns"/>
        FROM tb_credits_farm_seed
        WHERE act_id = #{actId}
        order by payload
    </select>

</mapper>