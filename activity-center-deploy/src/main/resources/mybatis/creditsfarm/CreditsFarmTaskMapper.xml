<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.creditsfarm.impl.CreditsFarmTaskDaoImpl">

    <resultMap type="cn.com.duiba.activity.center.biz.entity.creditsfarm.CreditsFarmTaskEntity" id="creditsFarmTaskMap">
        <result property="id" column="id"/>
        <result property="actId" column="act_id"/>
        <result property="taskName" column="task_name"/>
        <result property="taskType" column="task_type"/>
        <result property="taskDesc" column="task_desc"/>
        <result property="taskPic" column="task_pic"/>
        <result property="dayLimit" column="day_limit"/>
        <result property="nutritionCount" column="nutrition_count"/>
        <result property="subTaskCount" column="sub_task_count"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="columns">
        id,
        act_id,
        task_name,
        task_type,
        task_desc,
        task_pic,
        day_limit,
        nutrition_count,
        sub_task_count,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO tb_credits_farm_task
        (act_id,
        task_name,
        task_type,
        task_desc,
        task_pic,
        day_limit,
        nutrition_count,
        sub_task_count
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.actId},
            #{item.taskName},
            #{item.taskType},
            #{item.taskDesc},
            #{item.taskPic},
            #{item.dayLimit},
            #{item.nutritionCount},
            #{item.subTaskCount}
            )
        </foreach>
    </insert>

    <delete id="batchDeleteByIds">
        DELETE FROM tb_credits_farm_task WHERE id IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <update id="updateById" parameterType="cn.com.duiba.activity.center.biz.entity.creditsfarm.CreditsFarmTaskEntity">
        UPDATE tb_credits_farm_task
        <set>
            <if test="actId != null">
                act_id = #{actId},
            </if>
            <if test="taskName != null">
                task_name = #{taskName},
            </if>
            <if test="taskType != null">
                task_type = #{taskType},
            </if>
            <if test="taskDesc != null">
                task_desc = #{taskDesc},
            </if>
            <if test="taskPic != null">
                task_pic = #{taskPic},
            </if>
            <if test="dayLimit != null">
                day_limit = #{dayLimit},
            </if>
            <if test="nutritionCount != null">
                nutrition_count = #{nutritionCount},
            </if>
            <if test="subTaskCount != null">
                sub_task_count = #{subTaskCount},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="listByActId" resultMap="creditsFarmTaskMap">
        SELECT <include refid="columns"/>
        FROM tb_credits_farm_task
        WHERE act_id = #{actId}
    </select>

    <select id="selectByActIdAndType" resultMap="creditsFarmTaskMap">
        SELECT <include refid="columns"/>
        FROM tb_credits_farm_task
        WHERE act_id = #{actId}
        AND task_type = #{taskType}
    </select>


</mapper>