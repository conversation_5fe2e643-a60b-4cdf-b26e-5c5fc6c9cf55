<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.creditsfarm.impl.CreditsFarmOptionDaoImpl">

    <resultMap type="cn.com.duiba.activity.center.biz.entity.creditsfarm.CreditsFarmOptionEntity" id="creditsFarmOptionMap">
        <result property="id" column="id"/>
        <result property="actId" column="act_id"/>
        <result property="seedId" column="seed_id"/>
        <result property="appId" column="app_id"/>
        <result property="optionName" column="option_name"/>
        <result property="optionPrice" column="option_price"/>
        <result property="optionCount" column="option_count"/>
        <result property="optionPic" column="option_pic"/>
        <result property="itemId" column="item_id"/>
        <result property="stockId" column="stock_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="appItemId" column="app_item_id"/>
        <result property="merchantCoding" column="merchant_coding"/>
    </resultMap>

    <sql id="columns">
        id,
        act_id,
        seed_id,
        app_id,
        option_name,
        option_price,
        option_count,
        option_pic,
        item_id,
        stock_id,
        gmt_create,
        gmt_modified,
        app_item_id,
        merchant_coding
    </sql>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO tb_credits_farm_option
        (act_id,
        seed_id,
        app_id,
        option_name,
        option_price,
        option_count,
        option_pic,
        item_id,
        stock_id,
        app_item_id,
        merchant_coding)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.actId},
            #{item.seedId},
            #{item.appId},
            #{item.optionName},
            #{item.optionPrice},
            #{item.optionCount},
            #{item.optionPic},
            #{item.itemId},
            #{item.stockId},
            #{item.appItemId},
            #{item.merchantCoding})
        </foreach>
    </insert>

    <update id="updateById" parameterType="cn.com.duiba.activity.center.biz.entity.creditsfarm.CreditsFarmOptionEntity">
        UPDATE tb_credits_farm_option
        <set>
            <if test="actId != null">
                act_id = #{actId},
            </if>
            <if test="seedId != null">
                seed_id = #{seedId},
            </if>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="optionName != null">
                option_name = #{optionName},
            </if>
            <if test="optionPrice != null">
                option_price = #{optionPrice},
            </if>
            <if test="optionCount != null">
                option_count = #{optionCount},
            </if>
            <if test="optionPic != null">
                option_pic = #{optionPic},
            </if>
            <if test="itemId != null">
                item_id = #{itemId},
            </if>
            <if test="stockId != null">
                stock_id = #{stockId},
            </if>
            <if test="appItemId != null">
                app_item_id = #{appItemId},
            </if>
            <if test="merchantCoding != null">
                merchant_coding = #{merchantCoding},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="getById" resultMap="creditsFarmOptionMap">
        SELECT
        <include refid="columns"/>
        FROM tb_credits_farm_option
        WHERE id = #{id}
    </select>

    <select id="selectByCondition" resultMap="creditsFarmOptionMap">
        SELECT
        <include refid="columns"/>
        FROM tb_credits_farm_option
        WHERE act_id = #{actId}
        <if test="seedId != null">
            AND seed_id = #{seedId}
        </if>
    </select>

    <select id="sumOptionCountBySeed" resultType="java.lang.Integer">
        SELECT SUM(option_count)
        FROM tb_credits_farm_option
        WHERE act_id = #{actId}
        AND seed_id = #{seedId}
    </select>

    <delete id="batchDeleteByIds">
        DELETE FROM tb_credits_farm_option WHERE id IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>


</mapper>