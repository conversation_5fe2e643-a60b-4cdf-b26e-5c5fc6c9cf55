<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.creditsfarm.impl.CreditsFarmNutritionRecordDaoImpl">

    <resultMap type="cn.com.duiba.activity.center.biz.entity.creditsfarm.CreditsFarmNutritionRecordEntity" id="creditsFarmNutritionRecordMap">
        <result property="id" column="id"/>
        <result property="actId" column="act_id"/>
        <result property="appId" column="app_id"/>
        <result property="consumerId" column="consumer_id"/>
        <result property="partnerUserId" column="partner_user_id"/>
        <result property="actionType" column="action_type"/>
        <result property="taskType" column="task_type"/>
        <result property="actionDesc" column="action_desc"/>
        <result property="changeAmount" column="change_amount"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="columns">
        id,
        act_id,
        app_id,
        consumer_id,
        partner_user_id,
        action_type,
        task_type,
        action_desc,
        change_amount,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.biz.entity.creditsfarm.CreditsFarmNutritionRecordEntity">
        INSERT INTO tb_credits_farm_nutrition_record(act_id,app_id,consumer_id,partner_user_id,action_type,task_type,action_desc,change_amount)
        VALUES(#{actId},#{appId},#{consumerId},#{partnerUserId},#{actionType},#{taskType},#{actionDesc},#{changeAmount})
    </insert>


    <select id="pageQuery" resultMap="creditsFarmNutritionRecordMap">
        SELECT <include refid="columns"/>
        FROM tb_credits_farm_nutrition_record
        WHERE consumer_id = #{consumerId}
        AND act_id = #{actId}
        ORDER BY gmt_create DESC
        limit #{start},#{limit}
    </select>

    <select id="countByConsumerAndAct" resultType="java.lang.Integer">
        SELECT count(1)
        FROM tb_credits_farm_nutrition_record
        WHERE consumer_id = #{consumerId}
        AND act_id = #{actId}
    </select>

    <select id="countTodayReward" resultType="java.lang.Integer">
        SELECT count(1)
        FROM tb_credits_farm_nutrition_record
        WHERE consumer_id = #{consumerId}
        AND act_id = #{actId}
        <if test="taskType != null">
            AND task_type = #{taskType}
        </if>
        AND gmt_create &gt; #{startDate}
    </select>

</mapper>