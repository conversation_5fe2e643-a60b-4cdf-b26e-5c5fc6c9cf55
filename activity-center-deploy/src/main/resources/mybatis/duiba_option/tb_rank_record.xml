<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.optionrank.impl.OptionRankRecordDaoImpl">

	<resultMap type="OptionRankRecordEntity" id="optionRankRecord">
		<id column="id" property="id"/>
		<result column="app_id" property="appId"/>
		<result column="base_config_id" property="baseConfigId"/>
		<result column="consumer_id" property="consumerId"/>
		<result column="total_num" property="totalNum"/>
	</resultMap>

	<sql id="all_column">
		id, app_id, base_config_id, consumer_id, total_num
	</sql>

	<insert id="insert" parameterType="OptionRankRecordEntity" useGeneratedKeys="true" keyProperty="id">
		insert into tb_rank_record
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="appId != null">app_id,</if>
			<if test="baseConfigId != null">base_config_id,</if>
			<if test="consumerId != null">consumer_id,</if>
			<if test="totalNum != null">total_num</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="appId != null">#{appId},</if>
			<if test="baseConfigId != null">#{baseConfigId},</if>
			<if test="consumerId != null">#{consumerId},</if>
			<if test="totalNum != null">#{totalNum}</if>
		</trim>
	</insert>

	<select id="findByCidAndBaseConfigIdAndAppId" resultType="OptionRankRecordEntity" parameterType="Map">
		select
		<include refid="all_column"/>
		from tb_rank_record
		where base_config_id = #{baseConfigId}
		and app_id = #{appId}
		and consumer_id = #{consumerId}
		limit 1
	</select>

	<update id="updateTotalNum" parameterType="java.util.Map">
		update tb_rank_record set total_num = #{totalNum}
		where base_config_id = #{baseConfigId}
		and app_id = #{appId}
		and consumer_id = #{consumerId}
	</update>


</mapper>
