<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.seconds_kill_app.impl.SeckillAppOrderDaoImpl">

	<select id="selectcountFailByOperatingActivityIds" parameterType="Map" resultType="cn.com.duiba.activity.center.biz.entity.DeveloperActivityStatisticsEntity">
		select 
		 operating_activity_id as operatingActivityId, 
		 count(id) as failCount
		 from seckill_orders_${tb_suffix}
		where operating_activity_id in 
		<foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
		and app_id = #{appId}
		group by operating_activity_id
	</select>

	<select id="selectByLimit" resultType="cn.com.duiba.activity.center.biz.entity.seconds_kill.SeckillOrdersEntity" parameterType="Map">
		select 
		<include refid="fields" />
		from seckill_orders_${tb_suffix}
		where app_id = #{appId}
		<include refid="condition"/>
		order by gmt_create desc limit #{start},#{pageSize} 
	</select>
	
	<select id="selectTotalCount" resultType="Long" parameterType="Map">
		select 
		count(id) 
		from seckill_orders_${tb_suffix}
		where app_id = #{appId}
		<include refid="condition"/>
	</select>
	
	<sql id="condition">
		<if test="operatingActivityId != null">
			and operating_activity_id = #{operatingActivityId}
		</if>
		<if test="appId != null">
			and app_id = #{appId}
		</if>
		<if test="startTime != null and endTime != null">
			and gmt_create between #{startTime} and #{endTime}
		</if>
		<if test="status != null">
			and status = #{status}
		</if>
		<if test="consumerId != null">
			and consumer_id = #{consumerId}
		</if>
	</sql>
	
	<sql id="fields">
	    id,
	  	app_id as appId, 
	  	consumer_id as consumerId, 
	  	partner_user_id as partnerUserId, 
	  	status, 
	  	operating_activity_id as operatingActivityId, 
	  	duiba_seckill_id as duibaSeckillId, 
	    item_id as itemId, 
	    app_item_id as appItemId, 
	    item_title as itemTitle, 
	    credits, 
	    order_id as orderId, 
	    error_msg as errorMsg, 
	    url, 
	    gmt_create as gmtCreate,
		gmt_modified as gmtModified
	</sql>
</mapper>
