<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.pyramidspread.impl.PyramidAppletCodeDaoImpl">


    <resultMap type="PyramidAppletConfigEntity" id="propertyMap">
        <result property="id" column="id"/>
        <result property="actCode" column="act_code"/>
        <result property="codeId" column="code_id"/>
        <result property="codeImg" column="code_img"/>
        <result property="userId" column="user_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <select id="queryUserCodeImg" parameterType="PyramidAppletConfigEntity" resultType="String">
        select code_img
        from tb_huifeng_applet_record
        where
        act_code = #{actCode} and user_id = #{userId}
    </select>


    <insert id="bindUserCode" useGeneratedKeys="true" keyProperty="id" parameterType="PyramidAppletConfigEntity">
        insert into tb_huifeng_applet_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="actCode != null">act_code,</if>
            <if test="codeId != null">code_id,</if>
            <if test="codeImg != null">code_img,</if>
            <if test="userId != null">user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="actCode != null">#{actCode},</if>
            <if test="codeId != null">#{codeId},</if>
            <if test="codeImg != null">#{codeImg},</if>
            <if test="userId != null">#{userId}</if>
        </trim>
    </insert>

    <select id="queryUserBySunCode"  resultMap="propertyMap">
        select
        <include refid="pyramidAppletAllParam"/>
        from tb_huifeng_applet_record
        where
        act_code = #{actCode} and code_id = #{codeId}
    </select>



    <sql id="pyramidAppletAllParam">
        id,
        act_code,
        code_id,
        code_img,
        user_id,
        gmt_create,
        gmt_modified
    </sql>


</mapper>