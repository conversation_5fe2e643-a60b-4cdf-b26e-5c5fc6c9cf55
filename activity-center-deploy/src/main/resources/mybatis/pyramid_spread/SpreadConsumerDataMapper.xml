<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.pyramidspread.impl.SpreadConsumerDataDaoImpl">
    <resultMap type="SpreadConsumerDataEntity" id="propertyMap">
        <result property="id" column="id"/>
        <result property="actId" column="act_id"/>
        <result property="consumerId" column="consumer_id"/>
        <result property="appId" column="app_id"/>
        <result property="directInvitorId" column="direct_invitor_id"/>
        <result property="indirectInvitorId" column="indirect_invitor_id"/>
        <result property="inviteCnt" column="invite_cnt"/>
        <result property="directInvitorCut" column="direct_invitor_cut"/>
        <result property="indirectInvitorCut" column="indirect_invitor_cut"/>
        <result property="totalIncome" column="total_income"/>
        <result property="gmtInvited" column="gmt_invited"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="columns">
        id,
        act_id,
        consumer_id,
        app_id,
        direct_invitor_id,
        indirect_invitor_id,
        invite_cnt,
        direct_invitor_cut,
        indirect_invitor_cut,
        total_income,
        gmt_invited,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="add" useGeneratedKeys="true" keyProperty="id" parameterType="SpreadConsumerDataEntity">
        insert into tb_spread_consumer_data (
            act_id,
            consumer_id,
            app_id,
            direct_invitor_id,
            indirect_invitor_id,
            invite_cnt,
            direct_invitor_cut,
            indirect_invitor_cut,
            total_income,
            gmt_invited
        ) values (
            #{actId},
            #{consumerId},
            #{appId},
            #{directInvitorId},
            #{indirectInvitorId},
            #{inviteCnt},
            #{directInvitorCut},
            #{indirectInvitorCut},
            #{totalIncome},
            #{gmtInvited}
        ) on duplicate key update
        direct_invitor_id = case values(direct_invitor_id) when 0 then direct_invitor_id else values(direct_invitor_id) end,
        indirect_invitor_id = case values(indirect_invitor_id) when 0 then indirect_invitor_id else values(indirect_invitor_id) end,
        invite_cnt = invite_cnt + values(invite_cnt),
        direct_invitor_cut = direct_invitor_cut + values(direct_invitor_cut),
        indirect_invitor_cut = indirect_invitor_cut + values(indirect_invitor_cut),
        total_income = total_income + values(total_income),
        gmt_invited = case values(gmt_invited) when null then gmt_invited else values(gmt_invited) end
    </insert>

    <insert id="batchAdd" useGeneratedKeys="true" keyProperty="id" parameterType="SpreadConsumerDataEntity">
        insert into tb_spread_consumer_data (
        act_id,
        consumer_id,
        app_id,
        direct_invitor_id,
        indirect_invitor_id,
        invite_cnt,
        direct_invitor_cut,
        indirect_invitor_cut,
        total_income,
        gmt_invited
        ) values
        <foreach collection="list" item="it" open="" separator="," close="">
            (
            #{it.actId},
            #{it.consumerId},
            #{it.appId},
            #{it.directInvitorId},
            #{it.indirectInvitorId},
            #{it.inviteCnt},
            #{it.directInvitorCut},
            #{it.indirectInvitorCut},
            #{it.totalIncome},
            #{it.gmtInvited}
            )
        </foreach>
        on duplicate key update
        direct_invitor_id = case values(direct_invitor_id) when 0 then direct_invitor_id else values(direct_invitor_id) end,
        indirect_invitor_id = case values(indirect_invitor_id) when 0 then indirect_invitor_id else values(indirect_invitor_id) end,
        invite_cnt = invite_cnt + values(invite_cnt),
        direct_invitor_cut = direct_invitor_cut + values(direct_invitor_cut),
        indirect_invitor_cut = indirect_invitor_cut + values(indirect_invitor_cut),
        total_income = total_income + values(total_income),
        gmt_invited = case values(gmt_invited) when null then gmt_invited else values(gmt_invited) end
    </insert>

    <select id="getListByActIdAndDirectInvitorId" resultMap="propertyMap">
        select <include refid="columns"/>
        from tb_spread_consumer_data
        where act_id = #{actId}
        and direct_invitor_id = #{directInvitorId}
        order by gmt_invited desc limit #{limitCount}
    </select>

    <select id="getCntByActIdAndDirectInvitorId" resultType="int">
        select count(*)
        from tb_spread_consumer_data
        where act_id = #{actId}
        and direct_invitor_id = #{directInvitorId}
    </select>

    <select id="getCntByActIdAndIndirectInvitorId" resultType="int">
        select count(*)
        from tb_spread_consumer_data
        where act_id = #{actId}
        and indirect_invitor_id = #{indirectInvitorId}
    </select>

    <select id="getHasInvitorCntByConsumerId" resultType="int">
        select count(*)
        from tb_spread_consumer_data
        where consumer_id = #{consumerId}
        and direct_invitor_id &gt; 0
    </select>

    <select id="getListByActIdAndIndirectInvitorId" resultMap="propertyMap">
        select <include refid="columns"/>
        from tb_spread_consumer_data
        where act_id = #{actId}
        and indirect_invitor_id = #{indirectInvitorId}
        order by gmt_invited desc limit #{offset}, #{pageSize}
    </select>

    <update id="updateByActIdAndConsumerId" parameterType="SpreadConsumerDataEntity">
        update tb_spread_consumer_data
        <set>
            <if test="directInvitorId != null and directInvitorId > 0">
                direct_invitor_id = #{directInvitorId},
            </if>
            <if test="indirectInvitorId != null and indirectInvitorId > 0">
                indirect_invitor_id = #{indirectInvitorId},
            </if>
            <if test="inviteCnt != null and inviteCnt > 0">
                invite_cnt = invite_cnt + #{inviteCnt},
            </if>
            <if test="directInvitorCut != null and directInvitorCut > 0">
                direct_invitor_cut = direct_invitor_cut + #{directInvitorCut},
            </if>
            <if test="indirectInvitorCut != null and indirectInvitorCut > 0">
                indirect_invitor_cut = indirect_invitor_cut + #{indirectInvitorCut},
            </if>
            <if test="totalIncome != null and totalIncome > 0">
                total_income = total_income + #{totalIncome},
            </if>
            <if test="gmtInvited != null">
                gmt_invited = #{gmtInvited},
            </if>
            id = id
        </set>
        where act_id = #{actId}
        and consumer_id = #{consumerId}
    </update>

    <select id="getByActIdAndConsumerId" resultMap="propertyMap">
        select <include refid="columns"/>
        from tb_spread_consumer_data
        where act_id = #{actId}
        and consumer_id = #{consumerId}
        limit 1
    </select>

    <select id="listByActIdAndConsumerIds" resultMap="propertyMap">
        select <include refid="columns"/>
        from tb_spread_consumer_data
        where act_id = #{actId}
        and consumer_id in
        <foreach collection="consumerIds" item="consumerId" open="(" separator="," close=")">
            #{consumerId}
        </foreach>
    </select>
</mapper>