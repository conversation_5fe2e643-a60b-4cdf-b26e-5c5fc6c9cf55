<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.hdtool.impl.CreditsHdtoolOrdersDaoImpl">

	<!-- 扣积分失败的情况下，不扣除用户参与限制 -->
	<select id="countByConsumerIdAndOperatingActivityIdAndDate" resultType="Integer">
		select count(id) from hdtool_orders_${tb_suffix} where consumer_id = #{consumerId} and operating_activity_id = #{operatingActivityId} and gmt_create between #{start} and #{end} and status &lt; 2
	</select>
	<!-- 扣积分失败的情况下，不扣除用户参与限制 -->
	<select id="countByConsumerIdAndOperatingActivityId" resultType="Integer">
		select count(id) from hdtool_orders_${tb_suffix} where consumer_id = #{consumerId} and operating_activity_id = #{operatingActivityId} and status &lt; 2
	</select>
	
	<select id="countFreeByConsumerIdAndOperatingActivityIdAndDate" resultType="Integer">
		select count(id) from hdtool_orders_${tb_suffix} where consumer_id = #{consumerId} and operating_activity_id = #{operatingActivityId} and gmt_create between #{start} and #{end} and credits = 0
	</select>

	<!-- 统计用户在一定时间内参与某个活动次数，不限定订单状态 -->
	<select id="countByCidOaIdCreate" parameterType="Map" resultType="Integer">
		select count(id) from hdtool_orders_${tb_suffix} where consumer_id = #{consumerId} and operating_activity_id = #{operatingActivityId} and gmt_create between #{start} and #{end}
	</select>
	
	<select id="countFreeByConsumerIdAndOperatingActivityId" resultType="Integer">
		select count(id) from hdtool_orders_${tb_suffix} where consumer_id = #{consumerId} and operating_activity_id = #{operatingActivityId} and credits = 0
	</select>
	
	<select id="countByConsumerIdAndPrizeId" resultType="Integer">
		select count(id) from hdtool_orders_${tb_suffix} where consumer_id = #{consumerId} and operating_activity_id = #{operatingActivityId} and prize_id = #{prizeId}
	</select>
	
	
	
	<select id="findByIds" resultType="HdtoolOrdersEntity">
		select
		id,
		status,
		app_id,
		consumer_id,
		partner_user_id,
		operating_activity_id,
		duiba_hdtool_id,
		hdtool_type,
		prize_id,
		prize_name,
		app_item_id,
		item_id,
		coupon_id,
		face_price,
		credits,
		prize_type,
		exchange_status,
		main_order_id,
		main_order_num,
		developer_biz_id,
		ip,
		error4admin,
		error4developer,
		error4consumer,
		gmt_create,
		gmt_modified
		from hdtool_orders_${tb_suffix} where id in 
		<foreach collection="ids" index="index" item="it" open="(" separator="," close=")">
			#{it}
		</foreach>
	</select>
	

	
	<select id="findByAppAndDeveloperBizId" resultType="HdtoolOrdersEntity">
		select 
		id,
		status,
		app_id,
		consumer_id,
		partner_user_id,
		operating_activity_id,
		duiba_hdtool_id,
		hdtool_type,
		prize_id,
		prize_name,
		app_item_id,
		item_id,
		coupon_id,
		face_price,
		credits,
		prize_type,
		exchange_status,
		main_order_id,
		main_order_num,
		developer_biz_id,
		ip,
		error4admin,
		error4developer,
		error4consumer,
		gmt_create,
		gmt_modified
		from hdtool_orders_${tb_suffix} where app_id = #{appId} and developer_biz_id = #{bizId} limit 1
	</select>
	
	<select id="findExpireOrder" resultType="Long">
   		select id from hdtool_orders where gmt_create between #{startTime} and #{endTime} and exchange_status = #{exchangeStatus} and
   		prize_type in ('phonebill','alipay','qb','object','virtual')
    </select>
	<select id="find" resultType="HdtoolOrdersEntity" parameterType="Map">
		select
		id,
		status,
		app_id,
		consumer_id,
		partner_user_id,
		operating_activity_id,
		duiba_hdtool_id,
		hdtool_type,
		prize_id,
		prize_name,
		app_item_id,
		item_id,
		coupon_id,
		face_price,
		credits,
		prize_type,
		exchange_status,
		main_order_id,
		main_order_num,
		developer_biz_id,
		ip,
		error4admin,
		error4developer,
		error4consumer,
		gmt_create,
		gmt_modified
		from hdtool_orders_${tb_suffix}
		where id = #{id}
	</select>


	<update id="updateExchangeStatusToFail">
		update hdtool_orders_${tb_suffix} set
		exchange_status = #{exchangeStatus},
		error4admin=#{error4admin},
		error4developer=#{error4developer},
		error4consumer=#{error4consumer},
		gmt_modified=now()
		where id = #{id} and exchange_status != #{exchangeStatus} and status != 1
	</update>

	<update id="updateExchangeStatusToOverdue">
		update hdtool_orders_${tb_suffix} set
		exchange_status = #{exchangeStatus},
		error4admin=#{error4admin},
		error4developer=#{error4developer},
		error4consumer=#{error4consumer},
		gmt_modified=now()
		where id = #{id} and exchange_status != #{exchangeStatus} and exchange_status = 1
	</update>
	<update id="updateStatusToFail">
		update hdtool_orders_${tb_suffix} set
		status = #{status},
		error4admin=#{error4admin},
		error4developer=#{error4developer},
		error4consumer=#{error4consumer},
		gmt_modified=now()
		where id = #{id} and status != #{status}
	</update>

	<update id="doTakePrize">
		update hdtool_orders_${tb_suffix} set exchange_status = 2, gmt_modified=now() where id = #{id} and exchange_status = 1 and main_order_id is null
	</update>

	<update id="rollbackTakePrize">
		update hdtool_orders_${tb_suffix} set exchange_status = 1, gmt_modified=now() where id = #{id} and exchange_status = 2 and main_order_id is null
	</update>

	<update id="updateLotteryResult">
		update hdtool_orders_${tb_suffix} set
		app_item_id=#{appItemId},
		item_id=#{itemId},
		prize_id=#{prizeId},
		prize_name=#{prizeName},
		prize_type=#{prizeType},
		face_price=#{prizeFacePrice},
		coupon_id=#{couponId},
		status = #{status},
		exchange_status = #{exchangeStatus},
		gmt_modified=now()
		where id = #{id}
	</update>

	<update id="updateLotteryLuckyResult">
		update hdtool_orders_${tb_suffix} set
		app_item_id=#{appItemId},
		item_id=#{itemId},
		prize_id=#{prizeId},
		prize_name=#{prizeName},
		prize_type=#{prizeType},
		face_price=#{prizeFacePrice},
		coupon_id=#{couponId},
		status = #{status},
		exchange_status = #{exchangeStatus},
		gmt_modified=now()
		where id = #{id}
	</update>


	<insert id="generateHdtoolOrderSequence" useGeneratedKeys="true" keyProperty="id" parameterType="HdtoolOrdersEntity">
		insert into seq values()
	</insert>

	<update id="updateDeveloperBizId">
		update hdtool_orders_${tb_suffix} set developer_biz_id = #{bizId}, gmt_modified=now() where id = #{id}
	</update>

	<update id="updateMainOrderId">
		update hdtool_orders_${tb_suffix} set main_order_id=#{mainOrderId}, main_order_num=#{mainOrderNum}, gmt_modified=now() where id = #{id}
	</update>

	<insert id="insert" parameterType="java.util.Map" useGeneratedKeys="true" keyProperty="id">
		insert into hdtool_orders_${tb_suffix}
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="mirror.id != null">id,</if>
			<if test="mirror.status != null">status,</if>
			<if test="mirror.appId != null">app_id,</if>
			<if test="mirror.consumerId != null">consumer_id,</if>
			<if test="mirror.partnerUserId != null">partner_user_id,</if>
			<if test="mirror.operatingActivityId != null">operating_activity_id,</if>
			<if test="mirror.duibaHdtoolId != null">duiba_hdtool_id,</if>
			<if test="mirror.hdtoolType != null">hdtool_type,</if>
			<if test="mirror.prizeId != null">prize_id,</if>
			<if test="mirror.prizeName != null">prize_name,</if>
			<if test="mirror.appItemId != null">app_item_id,</if>
			<if test="mirror.itemId != null">item_id,</if>
			<if test="mirror.couponId != null">coupon_id,</if>
			<if test="mirror.facePrice != null">face_price,</if>
			<if test="mirror.credits != null">credits,</if>
			<if test="mirror.prizeType != null">prize_type,</if>
			<if test="mirror.exchangeStatus != null">exchange_status,</if>
			<if test="mirror.sourceOrderId != null">source_order_id,</if>
			<if test="mirror.mainOrderId != null">main_order_id,</if>
			<if test="mirror.mainOrderNum != null">main_order_num,</if>
			<if test="mirror.developerBizId != null">developer_biz_id,</if>
			<if test="mirror.ip != null">ip,</if>
			<if test="mirror.error4Admin != null">error4admin,</if>
			<if test="mirror.error4Developer != null">error4developer,</if>
			<if test="mirror.error4Consumer != null">error4consumer,</if>
			<if test="mirror.gmtCreate != null">gmt_create,</if>
			<if test="mirror.gmtModified != null">gmt_modified</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="mirror.id != null">#{mirror.id},</if>
			<if test="mirror.status != null">#{mirror.status},</if>
			<if test="mirror.appId != null">#{mirror.appId},</if>
			<if test="mirror.consumerId != null">#{mirror.consumerId},</if>
			<if test="mirror.partnerUserId != null">#{mirror.partnerUserId},</if>
			<if test="mirror.operatingActivityId != null">#{mirror.operatingActivityId},</if>
			<if test="mirror.duibaHdtoolId != null">#{mirror.duibaHdtoolId},</if>
			<if test="mirror.hdtoolType != null">#{mirror.hdtoolType},</if>
			<if test="mirror.prizeId != null">#{mirror.prizeId},</if>
			<if test="mirror.prizeName != null">#{mirror.prizeName},</if>
			<if test="mirror.appItemId != null">#{mirror.appItemId},</if>
			<if test="mirror.itemId != null">#{mirror.itemId},</if>
			<if test="mirror.couponId != null">#{mirror.couponId},</if>
			<if test="mirror.facePrice != null">#{mirror.facePrice},</if>
			<if test="mirror.credits != null">#{mirror.credits},</if>
			<if test="mirror.prizeType != null">#{mirror.prizeType},</if>
			<if test="mirror.exchangeStatus != null">#{mirror.exchangeStatus},</if>
			<if test="mirror.sourceOrderId != null">#{mirror.sourceOrderId},</if>
			<if test="mirror.mainOrderId != null">#{mirror.mainOrderId},</if>
			<if test="mirror.mainOrderNum != null">#{mirror.mainOrderNum},</if>
			<if test="mirror.developerBizId != null">#{mirror.developerBizId},</if>
			<if test="mirror.ip != null">#{mirror.ip},</if>
			<if test="mirror.error4Admin != null">#{mirror.error4Admin},</if>
			<if test="mirror.error4Developer != null">#{mirror.error4Developer},</if>
			<if test="mirror.error4Consumer != null">#{mirror.error4Consumer},</if>
			<if test="mirror.gmtCreate != null">#{mirror.gmtCreate},</if>
			<if test="mirror.gmtModified != null">#{mirror.gmtModified}</if>
		</trim>
	</insert>
</mapper>
