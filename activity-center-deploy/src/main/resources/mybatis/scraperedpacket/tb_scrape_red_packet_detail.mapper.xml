<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.scraperedpacket.impl.ScrapeRedPacketDetailDaoImpl">

    <resultMap id="detail" type="cn.com.duiba.activity.center.biz.entity.scraperedpacket.ScrapeRedPacketDetailEntity"/>

    <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.scraperedpacket.ScrapeRedPacketDetailEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_scrape_red_packet_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityId != null">activity_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="consumerId != null">consumer_id,</if>
            <if test="fromType != null">from_type,</if>
            <if test="redPacketLoc != null">red_packet_loc,</if>
            <if test="redPacketCode != null">red_packet_code,</if>
            <if test="inviteFriends != null">invite_friends,</if>
            <if test="allFriends != null">all_friends,</if>
            <if test="amountReceived != null">amount_received,</if>
            <if test="detailStatus != null">detail_status,</if>
            <if test="multipleCard != null">multiple_card,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityId != null">#{activityId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="consumerId != null">#{consumerId},</if>
            <if test="fromType != null">#{fromType},</if>
            <if test="redPacketLoc != null">#{redPacketLoc},</if>
            <if test="redPacketCode != null">#{redPacketCode},</if>
            <if test="inviteFriends != null">#{inviteFriends},</if>
            <if test="allFriends != null">#{allFriends},</if>
            <if test="amountReceived != null">#{amountReceived},</if>
            <if test="detailStatus != null">#{detailStatus},</if>
            <if test="multipleCard != null">#{multipleCard},</if>
        </trim>
    </insert>

    <select id="findById" parameterType="long" resultMap="detail">
        select <include refid="all_column"/>
        from tb_scrape_red_packet_detail
        where id = #{id}
    </select>

    <select id="selectByActIdAndCId" parameterType="java.util.Map" resultMap="detail">
        select <include refid="all_column"/>
        from tb_scrape_red_packet_detail
        where activity_id = #{activityId} and consumer_id = #{consumerId}
        order by gmt_create
    </select>

    <update id="updateProgressById" parameterType="java.util.Map">
		update tb_scrape_red_packet_detail set invite_friends = #{inviteFriendsAgo} + 1
		where id = #{id}
		and invite_friends = #{inviteFriendsAgo} and all_friends >= invite_friends
	</update>

    <update id="updateCollectCompleteBackById" parameterType="long">
		update tb_scrape_red_packet_detail set invite_friends = all_friends - 1
		where id = #{id}
		and invite_friends = all_friends and detail_status = 1
	</update>

    <update id="updateRedPacketCodeById" parameterType="java.util.Map">
		update tb_scrape_red_packet_detail set red_packet_code = #{redPacketCode}
		where id = #{id} and red_packet_code is null and detail_status=1
	</update>

    <update id="updateMultipleCardById" parameterType="java.util.Map">
		update tb_scrape_red_packet_detail set multiple_card = #{multipleCard},detail_status=2
		where id = #{id} and detail_status = 1
	</update>

    <update id="updateCompleteById" parameterType="java.util.Map">
		update tb_scrape_red_packet_detail set detail_status=3
		where id = #{id} and detail_status = 2
	</update>

    <select id="findLatelyList" parameterType="long" resultMap="detail">
        select <include refid="all_column"/>
        from tb_scrape_red_packet_detail
        where activity_id = #{activityId}
        order by gmt_create
        limit 50
    </select>

    <update id="updatePendReceiveById" parameterType="java.util.Map">
		update tb_scrape_red_packet_detail set detail_status=2
		where id = #{id} and detail_status = 3
	</update>

    <sql id="all_column">
        id, activity_id, app_id, consumer_id, from_type, red_packet_loc, red_packet_code, invite_friends,
        all_friends, amount_received, detail_status, multiple_card, gmt_create, gmt_modified
    </sql>


</mapper>