<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.scraperedpacket.impl.ScrapeRedPacketRecordDaoImpl">

    <resultMap id="record" type="cn.com.duiba.activity.center.biz.entity.scraperedpacket.ScrapeRedPacketRecordEntity"/>

    <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.scraperedpacket.ScrapeRedPacketRecordEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_scrape_red_packet_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="consumerId != null">consumer_id,</if>
            <if test="redPacketId != null">red_packet_id,</if>
            <if test="activityId != null">activity_id,</if>
            <if test="fromType != null">from_type,</if>
            <if test="scrapeNode != null">scrape_node,</if>
            <if test="avatar != null">avatar,</if>
            <if test="nickname != null">nickname,</if>
            <if test="scrapeRate != null">scrape_rate,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="consumerId != null">#{consumerId},</if>
            <if test="redPacketId != null">#{redPacketId},</if>
            <if test="activityId != null">#{activityId},</if>
            <if test="fromType != null">#{fromType},</if>
            <if test="scrapeNode != null">#{scrapeNode},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="nickname != null">#{nickname},</if>
            <if test="scrapeRate != null">#{scrapeRate},</if>
        </trim>
    </insert>

    <select id="findById" parameterType="long" resultMap="record">
        select <include refid="all_column"/>
        from tb_scrape_red_packet_record
        where id = #{id}
    </select>

    <select id="findByRedPacketId" parameterType="long" resultMap="record">
        select <include refid="all_column"/>
        from tb_scrape_red_packet_record
        where red_packet_id = #{redPacketId}
    </select>

    <select id="findByRedPacketIds" parameterType="list" resultMap="record">
        select <include refid="all_column"/>
        from tb_scrape_red_packet_record
        where red_packet_id in
        <foreach collection="list" separator="," open="(" item="id" close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectExistByActIdAndCid" parameterType="java.util.Map" resultType="long">
        select id
        from tb_scrape_red_packet_record
        where activity_id = #{activityId} and consumer_id = #{consumerId} and scrape_node in (2,3) limit 1
    </select>

    <sql id="all_column">
        id, consumer_id, red_packet_id, activity_id, from_type, scrape_node, avatar, nickname, scrape_rate, gmt_create, gmt_modified
    </sql>

</mapper>