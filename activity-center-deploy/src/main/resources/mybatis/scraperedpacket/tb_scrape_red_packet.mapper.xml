<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.scraperedpacket.impl.ScrapeRedPacketDaoImpl">

    <resultMap id="config" type="cn.com.duiba.activity.center.biz.entity.scraperedpacket.ScrapeRedPacketEntity"/>

    <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.scraperedpacket.ScrapeRedPacketEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_scrape_red_packet
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="rule != null">rule,</if>
            <if test="officialAccountConfig != null">official_account_config,</if>
            <if test="miniProgramConfig != null">mini_program_config,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="bannerImage != null">banner_image,</if>
            <if test="smallImage != null">small_image,</if>
            <if test="openWheel != null">open_wheel,</if>
            <if test="bonusLeft != null">bonus_left,</if>
            <if test="bonusRight != null">bonus_right,</if>
            <if test="targetNumber != null">target_number,</if>
            <if test="bonusNumber != null">bonus_number,</if>
            <if test="highestRate != null">highest_rate,</if>
            <if test="bonusLimit != null">bonus_limit,</if>
            <if test="totalBudget != null">total_budget,</if>
            <if test="warnBudget != null">warn_budget,</if>
            <if test="phoneNumber != null">phone_number,</if>
            <if test="operatingActivityId != null">operating_activity_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="customAccountId != null">custom_account_id,</if>
            <if test="dataJson != null">data_json,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="rule != null">#{rule},</if>
            <if test="officialAccountConfig != null">#{officialAccountConfig},</if>
            <if test="miniProgramConfig != null">#{miniProgramConfig},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="bannerImage != null">#{bannerImage},</if>
            <if test="smallImage != null">#{smallImage},</if>
            <if test="openWheel != null">#{openWheel},</if>
            <if test="bonusLeft != null">#{bonusLeft},</if>
            <if test="bonusRight != null">#{bonusRight},</if>
            <if test="targetNumber != null">#{targetNumber},</if>
            <if test="bonusNumber != null">#{bonusNumber},</if>
            <if test="highestRate != null">#{highestRate},</if>
            <if test="bonusLimit != null">#{bonusLimit},</if>
            <if test="totalBudget != null">#{totalBudget},</if>
            <if test="warnBudget != null">#{warnBudget},</if>
            <if test="phoneNumber != null">#{phoneNumber},</if>
            <if test="operatingActivityId != null">#{operatingActivityId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="customAccountId != null">#{customAccountId},</if>
            <if test="dataJson != null">#{dataJson},</if>
        </trim>
    </insert>

    <select id="findById" parameterType="long" resultMap="config">
        select *
        from tb_scrape_red_packet
        where id = #{id}
        and deleted = 0
    </select>

    <select id="findByOperatingActivityId" parameterType="long" resultMap="config">
        select *
        from tb_scrape_red_packet
        where operating_activity_id = #{operatingActivityId}
        and deleted = 0
    </select>

    <update id="update" parameterType="cn.com.duiba.activity.center.biz.entity.scraperedpacket.ScrapeRedPacketEntity">
        update tb_scrape_red_packet
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="rule != null">rule = #{rule},</if>
            <if test="officialAccountConfig != null">official_account_config = #{officialAccountConfig},</if>
            <if test="miniProgramConfig != null">mini_program_config = #{miniProgramConfig},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="bannerImage != null">banner_image = #{bannerImage},</if>
            <if test="smallImage != null">small_image = #{smallImage},</if>
            <if test="openWheel != null">open_wheel = #{openWheel},</if>
            <if test="bonusLeft != null">bonus_left = #{bonusLeft},</if>
            <if test="bonusRight != null">bonus_right = #{bonusRight},</if>
            <if test="targetNumber != null">target_number = #{targetNumber},</if>
            <if test="bonusNumber != null">bonus_number = #{bonusNumber},</if>
            <if test="highestRate != null">highest_rate = #{highestRate},</if>
            <if test="bonusLimit != null">bonus_limit = #{bonusLimit},</if>
            <if test="totalBudget != null">total_budget = #{totalBudget},</if>
            <if test="warnBudget != null">warn_budget = #{warnBudget},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="operatingActivityId != null">operating_activity_id = #{operatingActivityId},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="customAccountId != null">custom_account_id = #{customAccountId},</if>
            <if test="dataJson != null">data_json = #{dataJson},</if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="deleteById" parameterType="long">
        update tb_scrape_red_packet
        set deleted = 1
        where id = #{id}
    </update>

    <update id="deleteByIds" parameterType="list">
        update tb_scrape_red_packet
        set deleted = 1
        where id in
        <foreach collection="ids" close=")" item="id" open="(" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="deleteByOperatingActivityId" parameterType="long">
        update tb_scrape_red_packet
        set deleted = 1
        where operating_activity_id = #{operatingActivityId}
    </update>

    <update id="deleteByOperatingActivityIds" parameterType="list">
        update tb_scrape_red_packet
        set deleted = 1
        where operating_activity_id in
        <foreach collection="operatingActivityIds" close=")" item="id" open="(" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="cleanOfficialAccountConfig" parameterType="long">
        update tb_scrape_red_packet
        set official_account_config = null
        where id = #{id}
    </update>

    <update id="cleanMiniProgramConfig" parameterType="long">
        update tb_scrape_red_packet
        set mini_program_config = null
        where id = #{id}
    </update>

    <update id="cleanWarnBudgetConfig" parameterType="long">
        update tb_scrape_red_packet
        set warn_budget = null, phone_number = null
        where id = #{id}
    </update>

</mapper>