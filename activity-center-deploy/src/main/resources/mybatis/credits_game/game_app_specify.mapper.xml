<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.game.impl.GameAppSpecifyDaoImpl">

	<select id="findByGameId" parameterType="long" resultType="GameAppSpecifyEntity">
		select * from game_app_specify where game_config_duiba_id=#{gameConfigDuibaId}
	</select>

	<delete id="delete" parameterType="long">
		delete from game_app_specify where id=#{id}
	</delete>

	<insert id="addBatch">
		insert into game_app_specify(
		app_id,
		game_config_duiba_id,
		type,
		gmt_create,
		gmt_modified
		) values
		<foreach collection="list" separator="," item="item">
			(
			#{item.appId},
			#{item.gameConfigDuibaId},
			#{item.type},
			now(),
			now()
			)
		</foreach>
	</insert>

	<insert id="add" parameterType="GameAppSpecifyEntity">
		insert into game_app_specify(
		app_id,
		game_config_duiba_id,
		type,
		gmt_create,
		gmt_modified
		) values
		(
		#{appId},
		#{gameConfigDuibaId},
		#{type},
		now(),
		now()
		)
	</insert>

	<select id="findByGameConfigAndAppId" resultType="GameAppSpecifyEntity" parameterType="java.util.Map">
		select * from game_app_specify where game_config_duiba_id=#{gameConfigDuibaId} and app_id = #{appId}
	</select>

	<select id="findByGameConfigsAndAppId" resultType="GameAppSpecifyEntity" parameterType="java.util.Map">
		select * from game_app_specify where game_config_duiba_id IN
		<foreach collection="gameConfigDuibaIds" item="gameConfigDuibaId" open="(" close=")" separator=",">
			#{gameConfigDuibaId}
		</foreach>
		and app_id = #{appId}
	</select>

</mapper>
