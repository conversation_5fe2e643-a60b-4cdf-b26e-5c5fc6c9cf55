<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.game.impl.DuibaQuestionAnswerBrickDaoImpl">

	<select id="find" resultType="DuibaQuestionAnswerBrickEntity" parameterType="Map">
		select
		<include refid="fields" />
		from duiba_question_answer_brick
		where id = #{id}
	</select>

	<select id="findAll" resultType="DuibaQuestionAnswerBrickEntity">
		select
		<include refid="fields" />
		from duiba_question_answer_brick where status=1
	</select>

	<select id="findPageCount" resultType="Long">
		select
		count(id)
		from duiba_question_answer_brick
		where deleted = 0
	</select>

	<select id="findPage" resultType="DuibaQuestionAnswerBrickEntity" parameterType="Map">
		select
		<include refid="fields" />
		from duiba_question_answer_brick
		where
		deleted = 0
		order by gmt_create desc
		limit #{offset}, #{max}
	</select>

	<insert id="insert" parameterType="DuibaQuestionAnswerBrickEntity" useGeneratedKeys="true" keyProperty="id">
		insert into duiba_question_answer_brick
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null"> id, </if>
			<if test="title != null"> title, </if>
			<if test="status != null"> status, </if>
			<if test="md5 != null"> md5, </if>
			<if test="content != null"> content, </if>
			<if test="deleted != null"> deleted, </if>
			<if test="gmtCreate != null"> gmt_create, </if>
			<if test="gmtModified != null"> gmt_modified, </if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null"> #{id}, </if>
			<if test="title != null"> #{title}, </if>
			<if test="status != null"> #{status}, </if>
			<if test="md5 != null"> #{md5}, </if>
			<if test="content != null"> #{content}, </if>
			<if test="deleted != null"> #{deleted}, </if>
			<if test="gmtCreate != null"> #{gmtCreate}, </if>
			<if test="gmtModified != null"> #{gmtModified}, </if>
		</trim>
	</insert>

	<update id="update4Admin" parameterType="Map">
		update duiba_question_answer_brick
		set title = #{title}, content = #{content}, md5 = #{md5}, gmt_modified = now()
		where id = #{id}
	</update>

	<select id="findByTitle" parameterType="Map" resultType="DuibaQuestionAnswerBrickEntity">
		select
		<include refid="fields" />
		from duiba_question_answer_brick
		where title = #{title}
		limit 1
	</select>

	<update id="open" parameterType="Map">
		update duiba_question_answer_brick
		set status = 1, gmt_modified = now()
		where id = #{id}
	</update>

	<update id="disable" parameterType="Map">
		update duiba_question_answer_brick
		set status = 0, gmt_modified = now()
		where id = #{id}
	</update>

	<select id="getBrickContentById" resultType="string" parameterType="Map">
		select content
		from duiba_question_answer_brick
		where id = #{id}
	</select>
	
	<select id="findNoContent" resultType="DuibaQuestionAnswerBrickEntity" parameterType="Map">
		select id,
		title,
		status,
		md5,
		deleted,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified
		from duiba_question_answer_brick
		where id = #{id}
	</select>

	<sql id="fields">
		id,
		title,
		status,
		md5,
		content,
		deleted,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified
	</sql>
</mapper>
