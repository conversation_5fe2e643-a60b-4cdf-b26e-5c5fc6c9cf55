<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.game.impl.GameOptionsDuibaDaoImpl">

	<select id="find" resultType="GameOptionsDuibaEntity" parameterType="java.util.Map">
		select * from game_options_duiba where id = #{id}
	</select>
	
	<select id="findByAutoOpen" resultType="GameOptionsDuibaEntity" parameterType="java.util.Map">
		select * from game_options_duiba where game_config_duiba_id = #{gameConfigDuibaId} and auto_open = #{autoOpen} and deleted = false order by payload asc 
	</select>
	
	<select id="findByGameId" resultType="GameOptionsDuibaEntity" parameterType="java.util.Map">
		select * from game_options_duiba where game_config_duiba_id = #{gameConfigDuibaId} and deleted = false order by payload asc
	</select>
	
	<select id="findByGameIds" resultType="GameOptionsDuibaEntity" parameterType="java.util.Map">
		select * from game_options_duiba where game_config_duiba_id IN
		<foreach collection="gameConfigDuibaIds" item="gameConfigDuibaId" open="(" close=")" separator=",">
		 #{gameConfigDuibaId}
		</foreach>
		 and deleted = false order by payload asc
	</select>

</mapper>
