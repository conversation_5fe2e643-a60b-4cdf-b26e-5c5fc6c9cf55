<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.activity.center.biz.dao.gamecommon.impl.CustomerGameAppDaoImpl" >
    
    <resultMap id="BaseResultMap" type="cn.com.duiba.activity.center.biz.entity.common.CustomerGameAppEntity" >
        <id column="id" property="id" />
        <result column="customer_id" property="customerId" />
        <result column="game_id" property="gameId" />
        <result column="game_type" property="gameType" />
        <result column="app_id" property="appId" />
        <result column="is_buy" property="isBuy" />
        <result column="best_record" property="bestRecord" />
        <result column="rank" property="rank" />
        <result column="max_score_date" property="maxScoreDate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="share_num" property="shareNum" />
    </resultMap>
    
    <sql id="Base_Column_List" >
        id, customer_id, game_id, game_type, app_id,
        is_buy, best_record, rank, max_score_date, gmt_modified,gmt_create ,share_num
    </sql>

    <!--根据ID查询-->
    <select id="selectOneById" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select
        <include refid="Base_Column_List" />
        from tb_customer_game_app
        where id = #{id}
    </select>
    
     <!--根据用户ID，appID，游戏ID查询-->
    <select id="selectByGameIdAndCustomerIdAndappId" resultMap="BaseResultMap" parameterType="java.util.Map" >
        select
        <include refid="Base_Column_List" />
        from tb_customer_game_app
        where game_id = #{gameId} and  customer_id = #{customerId} and app_id = #{appId}
    </select>

    
    <!--增加-->
    <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.common.CustomerGameAppEntity" useGeneratedKeys="true" keyProperty="id">
        insert IGNORE into tb_customer_game_app 
        (customer_id, game_id, game_type, app_id,is_buy, best_record, rank, max_score_date, gmt_modified,gmt_create ,share_num)
        values 
        (#{customerId}, #{gameId}, #{gameType},#{appId},#{isBuy},#{bestRecord},#{rank}, now(),now(), now(),#{shareNum})
    </insert>

    <!--更新购买信息和游戏记录-->
    <update id="updateRecod" parameterType="java.util.Map" >
        update tb_customer_game_app
        <set >
            gmt_modified = now()
            <if test="bestRecord != null" >
                ,best_record = #{bestRecord}
            </if>

            <if test="rank != null" >
                ,rank = #{rank}
            </if>
        </set>
       where game_id = #{gameId} and  customer_id = #{customerId} and app_id = #{appId}
    </update>
    
     <!--更新购买信息和游戏记录-->
    <update id="updateIsBuy" parameterType="java.util.Map" >
        update tb_customer_game_app
        <set >
            gmt_modified = now()
            <if test="isBuy != null" >
                ,is_buy = #{isBuy}
            </if>
        </set>
       where game_id = #{gameId} and  customer_id = #{customerId} and app_id = #{appId}
    </update>
    
</mapper>