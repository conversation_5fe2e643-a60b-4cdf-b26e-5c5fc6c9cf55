<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.game.impl.DuibaQuestionAnswerDaoImpl">

	<select id="findAllQuestion" resultType="cn.com.duiba.activity.center.biz.entity.AddActivityEntity" parameterType="Map">
		select
		a.id as id,
		a.title as title,
		a.small_image as smallImage,
		a.white_image as whiteImage,
		a.banner as image,
		a.image_json as imageJson,
		40 as type,
		a.status as duibaStatus,
		a.gmt_create as gmtCreate
		from duiba_question_answer a
		where a.deleted = false and <![CDATA[ a.status in (1,2) ]]>
	</select>

	<select id="findAllByIds" resultType="DuibaQuestionAnswerEntity" parameterType="Map">
		select
		<include refid="fields"/>
		from duiba_question_answer
		where id in
		<foreach collection="ids" index="index" item="it" open="(" separator="," close=")">
			#{it}
		</foreach>
	</select>

	<select id="find" parameterType="java.util.Map" resultType="DuibaQuestionAnswerEntity">
		select
		<include refid="fields"/>
		from duiba_question_answer
		where id=#{id}
	</select>
	
	<select id="findTagById" parameterType="Long" resultType="String">
		select
		tag
		from duiba_question_answer
		where id=#{id}
	</select>

	<select id="findAutoOff" resultType="DuibaQuestionAnswerEntity" parameterType="java.util.Map">
		select <include refid="fields"/> from duiba_question_answer where deleted = false and status = 1 and <![CDATA[ auto_off_date < #{nowTime} ]]>
	</select>

	<select id="findByIds" parameterType="java.util.Map" resultType="DuibaQuestionAnswerEntity">
		select <include refid="fields"/> from duiba_question_answer where id in
		<foreach collection="ids" item="it" index="index" open="(" separator="," close=")" >  
			#{it}  
		</foreach>
	</select>
	
	<sql id="fields">
		id,
		title,
		status,
		credits_price creditsPrice,
		limit_count limitCount,
		limit_scope limitScope,
		free_limit freeLimit,
		free_scope freeScope,
		duiba_question_answer_brick_id duibaQuestionAnswerBrickId,
		rule,
		banner,
		small_image as smallImage,
		white_image as whiteImage,
		logo,
		recommend_image recommendImage,
		select_question_bank selectQuestionBank,
		question_num questionNum,
		switches,
		auto_off_date autoOffDate,
		deleted,
		gmt_create gmtCreate,
		gmt_modified gmtModified,
		free_rule as freeRule,
		activity_category_id as activityCategoryId,
		image_json as imageJson
	</sql>
	
	<select id="findExtraInfoById" resultType="cn.com.duiba.activity.center.biz.entity.ActivityExtraInfoEntity" parameterType="java.util.Map">
		select id,free_rule as freeRule from duiba_question_answer where id = #{id}
	</select>



	<select id="findByPage" parameterType="java.util.Map" resultType="DuibaQuestionAnswerEntity">
		select
		<include refid="fields"/>
		from duiba_question_answer
		where deleted = 0
		<!--Start chaijiangang,管理员后台活动管理页面增加筛选条件,2016/03/30  -->
		<include refid="condition" />
		<!--End chaijiangang,管理员后台活动管理页面增加筛选条件,2016/03/30  -->
		order by gmt_create desc
		limit #{offset},#{max}
	</select>

	<!--Start chaijiangang,管理员后台活动管理页面增加筛选条件,2016/03/30  -->
	<sql id="condition">
		<if test="title!= null"> and title like CONCAT('%',#{title},'%') </if>
		<if test="id!= null"> and id = #{id} </if>
	</sql>
	<!--End chaijiangang,管理员后台活动管理页面增加筛选条件,2016/03/30  -->

	<update id="updateSwitches" parameterType="Map">
		update duiba_question_answer
		set switches = #{switches}
		where id = #{id}
	</update>

	<select id="findPageCount" resultType="Long">
		select
		count(id)
		from duiba_question_answer
		where deleted = 0
		<!--Start chaijiangang,管理员后台活动管理页面增加筛选条件,2016/03/30  -->
		<include refid="condition" />
		<!--End chaijiangang,管理员后台活动管理页面增加筛选条件,2016/03/30  -->
	</select>

	<insert id="insert" parameterType="DuibaQuestionAnswerEntity">
		<selectKey resultType="long" order="AFTER" keyProperty="id">
			select @@IDENTITY as id
		</selectKey>
		insert into duiba_question_answer
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null"> id, </if>
			<if test="title != null"> title, </if>
			<if test="status != null"> status, </if>
			<if test="creditsPrice != null"> credits_price, </if>
			<if test="limitCount != null"> limit_count, </if>
			<if test="limitScope != null"> limit_scope, </if>
			<if test="freeLimit != null"> free_limit, </if>
			<if test="freeScope != null"> free_scope, </if>
			<if test="duibaQuestionAnswerBrickId != null"> duiba_question_answer_brick_id, </if>
			<if test="rule != null"> rule, </if>
			<if test="banner != null"> banner, </if>
			<if test="smallImage != null"> small_image, </if>
			<if test="whiteImage != null"> white_image, </if>
			<if test="logo != null"> logo, </if>
			<if test="recommendImage != null"> recommend_image, </if>
			<if test="selectQuestionBank != null"> select_question_bank, </if>
			<if test="questionNum != null"> question_num, </if>
			<if test="switches != null"> switches, </if>
			<if test="autoOffDate != null"> auto_off_date, </if>
			<if test="deleted != null"> deleted, </if>
			<if test="freeRule != null"> free_rule, </if>
			<if test="activityCategoryId != null">activity_category_id,</if>
			<if test="tag != null">tag,</if>
			<if test="imageJson != null">image_json,</if>
			gmt_create,
			gmt_modified,
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null"> #{id}, </if>
			<if test="title != null"> #{title}, </if>
			<if test="status != null"> #{status}, </if>
			<if test="creditsPrice != null"> #{creditsPrice}, </if>
			<if test="limitCount != null"> #{limitCount}, </if>
			<if test="limitScope != null"> #{limitScope}, </if>
			<if test="freeLimit != null"> #{freeLimit}, </if>
			<if test="freeScope != null"> #{freeScope}, </if>
			<if test="duibaQuestionAnswerBrickId != null"> #{duibaQuestionAnswerBrickId}, </if>
			<if test="rule != null"> #{rule}, </if>
			<if test="banner != null"> #{banner}, </if>
			<if test="smallImage != null"> #{smallImage}, </if>
			<if test="whiteImage != null"> #{whiteImage}, </if>
			<if test="logo != null"> #{logo}, </if>
			<if test="recommendImage != null"> #{recommendImage}, </if>
			<if test="selectQuestionBank != null"> #{selectQuestionBank}, </if>
			<if test="questionNum != null"> #{questionNum}, </if>
			<if test="switches != null"> #{switches}, </if>
			<if test="autoOffDate != null"> #{autoOffDate}, </if>
			<if test="deleted != null"> #{deleted}, </if>
			<if test="freeRule != null"> #{freeRule}, </if>
			<if test="activityCategoryId != null">#{activityCategoryId},</if>
			<if test="tag != null">#{tag},</if>
			<if test="imageJson != null">#{imageJson},</if>
			now(),
			now(),
		</trim>
	</insert>

	<update id="updateAutoOffDateNull">
		update duiba_question_answer set auto_off_date = null where id = #{id}
	</update>

	<update id="update" parameterType="DuibaQuestionAnswerEntity">
		update duiba_question_answer set gmt_modified=now()
		<if test="title != null">
			,title=#{title}
		</if>
		<if test="status != null">
			,status=#{status}
		</if>
		<if test="creditsPrice != null">
			,credits_price=#{creditsPrice}
		</if>
		<if test="freeLimit != null">
			,free_limit=#{freeLimit}
		</if>
		<if test="freeScope != null">
			,free_scope=#{freeScope}
		</if>
		<if test="limitCount != null">
			,limit_count=#{limitCount}
		</if>
		<if test="limitScope != null">
			,limit_scope=#{limitScope}
		</if>
		<if test="duibaQuestionAnswerBrickId != null">
			,duiba_question_answer_brick_id=#{duibaQuestionAnswerBrickId}
		</if>
		<if test="rule != null">
			,rule=#{rule}
		</if>
		<if test="banner != null">
			,banner=#{banner}
		</if>
		<if test="smallImage != null">
			,small_image=#{smallImage}
		</if>
		<if test="logo != null">
			,logo=#{logo}
		</if>
		<if test="whiteImage != null">
			,white_image=#{whiteImage}
		</if>
		<if test="recommendImage != null">
			,recommend_image=#{recommendImage}
		</if>
		<if test="autoOffDate != null">
			,auto_off_date=#{autoOffDate}
		</if>
		<if test="selectQuestionBank != null">
			,select_question_bank=#{selectQuestionBank}
		</if>
		<if test="questionNum != null">
			,question_num=#{questionNum}
		</if>
		<if test="switches != null">
			,switches=#{switches}
		</if>
		<if test="freeRule != null">
			,free_rule=#{freeRule}
		</if>
		<if test="activityCategoryId != null">
			,activity_category_id=#{activityCategoryId}
		</if>
		<if test="tag != null">
			,tag=#{tag}
		</if>
		<if test="imageJson != null">,image_json = #{imageJson}</if>
		where id=#{id}
	</update>

	<update id="updateTagById" parameterType="java.util.Map">
		update duiba_question_answer set gmt_modified=now(),tag=#{tag}
		where id=#{id}
	</update>
	
	<update id="updateStatus" parameterType="java.util.Map">
		update duiba_question_answer set status=#{status} ,gmt_modified=now()
		where id=#{id}
	</update>

	<update id="delete" parameterType="Long">
		update duiba_question_answer set deleted=1 ,gmt_modified=now()
		where id=#{id}
	</update>

	<update id="updateForEditActivityCategoryId">
		update duiba_question_answer set activity_category_id=#{activityCategoryId} where id=#{id}
	</update>
</mapper>
