<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.activity.center.biz.dao.creditgame.impl.CreditGameDaoImpl" >
    
    <resultMap id="BaseResultMap" type="cn.com.duiba.activity.center.biz.entity.creditgame.CreditGameEntity" >
        <id column="id" property="id" />
        <result column="credit_game_title" property="creditGameTitle" />
        <result column="credit_game_type" property="creditGameType" />
        <result column="credit_game_status" property="creditGameStatus" />
        <result column="credit_game_duiba_price" property="creditGameDuibaPrice" />
        <result column="credit_game_credits_price" property="creditGameCreditsPrice" />
        <result column="credit_game_app_count" property="creditGameAppCount" />
        <result column="credit_game_draw_scope" property="creditGameDrawScope" />
        <result column="credit_game_draw_limit" property="creditGameDrawLimit" />
        <result column="credit_game_free_scope" property="creditGameFreeScope" />
        <result column="credit_game_free_limit" property="creditGameFreeLimit" />
        <result column="credit_game_small_image" property="creditGameSmallImage" />
        <result column="credit_game_white_image" property="creditGameWhiteImage" />
        <result column="credit_game_banner_image" property="creditGameBannerImage" />
        <result column="credit_game_recommend_image" property="creditGameRecommendImage" />
        <result column="credit_game_logo" property="creditGameLogo" />
        <result column="credit_game_lottery_count" property="creditGameLotteryCount" />
        <result column="credit_game_custom_tag" property="creditGameCustomTag" />
        <result column="credit_game_activity_category_id" property="creditGameActivityCategoryId" />
        <result column="credit_game_auto_off_date" property="creditGameAutoOffDate" />
        <result column="credit_game_switches" property="creditGameSwitches" />
        <result column="credit_game_rule" property="creditGameRule" />
        <result column="credit_game_free_rule" property="creditGameFreeRule" />
        <result column="credit_game_desc" property="creditGameDesc" />
        <result column="credit_game_ext_desc" property="creditGameExtDesc" />
        <result column="credit_game_award_position" property="creditGameAwardPosition" />
        <result column="credit_game_award_config" property="creditGameAwardConfig" />
        <result column="credit_game_valve_config" property="creditGameValveConfig" />
        <result column="credit_game_bet_config" property="creditGameBetConfig" />
        <result column="credit_game_rule_script" property="creditGameRuleScript" />
        <result column="deleted" property="deleted" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="create_person" property="createPerson" />
        <result column="remarks" property="remarks" />
        <result column="activity_action_type" property="activityActionType" />
        <result column="image_json" property="imageJson" />
    </resultMap>
    
    <sql id="Base_Column_List" >
        id, credit_game_title, credit_game_type, credit_game_status, credit_game_duiba_price,
        credit_game_credits_price, credit_game_app_count, credit_game_draw_scope, credit_game_draw_limit,
        credit_game_free_scope, credit_game_free_limit, credit_game_small_image, credit_game_white_image,
        credit_game_banner_image, credit_game_recommend_image,credit_game_logo, credit_game_lottery_count,
        credit_game_custom_tag, credit_game_activity_category_id, credit_game_auto_off_date,
        credit_game_switches, credit_game_rule, credit_game_free_rule, credit_game_desc,
        credit_game_ext_desc, credit_game_award_position, credit_game_award_config, credit_game_valve_config,
        credit_game_bet_config, credit_game_rule_script, deleted, gmt_create, gmt_modified,
        create_person, remarks, activity_action_type,image_json
    </sql>
    
    <!-- list查询时用，大字段用不到 -->
	<sql id="List_Column_list">
		id, credit_game_title, credit_game_type,
		credit_game_draw_scope, credit_game_draw_limit,credit_game_free_scope, credit_game_free_limit,
		credit_game_status,credit_game_small_image,credit_game_white_image,credit_game_banner_image, credit_game_recommend_image,credit_game_logo,
		credit_game_activity_category_id, credit_game_auto_off_date,credit_game_switches,
		credit_game_bet_config,deleted,gmt_create, gmt_modified,activity_action_type
	</sql>
    
    <select id="selectAllCreditGame" resultType="cn.com.duiba.activity.center.biz.entity.AddActivityEntity" parameterType="java.util.Map">
		select
		a.id as id,
		a.credit_game_title as title,
		a.credit_game_small_image as smallImage,
		a.credit_game_white_image as whiteImage,
		a.credit_game_banner_image as image,
		a.image_json as imageJson,
		46 as type,
		a.credit_game_status as duibaStatus,
		a.gmt_create as gmtCreate
		from tb_credit_game a
		where a.deleted = false and <![CDATA[ a.credit_game_status in (1,2) ]]>
	</select>
	
	<select id="selectByStatus" resultMap="BaseResultMap" parameterType="java.util.Map">
		select
        <include refid="Base_Column_List" />
        from tb_credit_game
        where deleted = 0
        <if test="status!=null">
	        and credit_game_status in
	        <foreach collection="status" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
	</select>

    <!--根据ID查询-->
    <select id="queryById" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select
        <include refid="Base_Column_List" />
        from tb_credit_game
        where id = #{id}
    </select>
	<!-- id 批量查 -->
	<select id ="selectByIds" resultMap="BaseResultMap" parameterType="java.util.Map">
       select <include refid="List_Column_list" /> from tb_credit_game
       where id in
       <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</select>

	<!-- 列表页查询 -->
	<select id="selectList" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        <include refid="List_Column_list" />
        from tb_credit_game
        where 
        deleted = 0
        <if test="title!= null"> and credit_game_title like CONCAT('%',#{title},'%') </if>
        order by id desc
        <if test="offset!=null and max!=null">
        	limit #{offset},#{max}
        </if>
	</select>

	<!-- 统计总数 -->
	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(*) from tb_credit_game
		where deleted = 0
		<if test="title!= null"> and credit_game_title like CONCAT('%',#{title},'%')</if>
	</select>
	
	
    <!--删除-->
    <delete id="delete" parameterType="java.lang.Long" >
        update tb_credit_game
        set deleted = 1
        where id = #{id}
    </delete>
    
    <!--增加-->
    <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.creditgame.CreditGameEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_credit_game ( credit_game_title, credit_game_type, credit_game_status, credit_game_duiba_price,
        credit_game_credits_price, credit_game_app_count, credit_game_draw_scope, credit_game_draw_limit,
        credit_game_free_scope, credit_game_free_limit, credit_game_small_image, credit_game_white_image,
        credit_game_banner_image, credit_game_recommend_image, credit_game_logo,credit_game_lottery_count,
        credit_game_custom_tag, credit_game_activity_category_id, credit_game_auto_off_date,
        credit_game_switches, credit_game_rule, credit_game_free_rule, credit_game_desc,
        credit_game_ext_desc, credit_game_award_position, credit_game_award_config, credit_game_valve_config,
        credit_game_bet_config, credit_game_rule_script, deleted, gmt_create, gmt_modified,
        create_person, remarks, activity_action_type, image_json)
        values ( #{creditGameTitle}, #{creditGameType}, #{creditGameStatus}, #{creditGameDuibaPrice},
        #{creditGameCreditsPrice}, #{creditGameAppCount}, #{creditGameDrawScope}, #{creditGameDrawLimit},
        #{creditGameFreeScope}, #{creditGameFreeLimit}, #{creditGameSmallImage}, #{creditGameWhiteImage},
        #{creditGameBannerImage}, #{creditGameRecommendImage},#{creditGameLogo}, #{creditGameLotteryCount},
        #{creditGameCustomTag}, #{creditGameActivityCategoryId}, #{creditGameAutoOffDate},
        #{creditGameSwitches}, #{creditGameRule}, #{creditGameFreeRule}, #{creditGameDesc},
        #{creditGameExtDesc}, #{creditGameAwardPosition}, #{creditGameAwardConfig}, #{creditGameValveConfig},
        #{creditGameBetConfig}, #{creditGameRuleScript}, #{deleted}, now(), now(),
        #{createPerson}, #{remarks}, #{activityActionType}, #{imageJson})
    </insert>
    <!--更新-->
    <update id="update" parameterType="cn.com.duiba.activity.center.biz.entity.creditgame.CreditGameEntity" >
        update tb_credit_game
        <set >
            gmt_modified = now()

            <if test="creditGameTitle != null" >
                ,credit_game_title = #{creditGameTitle}
            </if>

            <if test="creditGameType != null" >
                ,credit_game_type = #{creditGameType}
            </if>

            <if test="creditGameStatus != null" >
                ,credit_game_status = #{creditGameStatus}
            </if>

            <if test="creditGameDuibaPrice != null" >
                ,credit_game_duiba_price = #{creditGameDuibaPrice}
            </if>

            <if test="creditGameCreditsPrice != null" >
                ,credit_game_credits_price = #{creditGameCreditsPrice}
            </if>

            <if test="creditGameAppCount != null" >
                ,credit_game_app_count = #{creditGameAppCount}
            </if>

            <if test="creditGameDrawScope != null" >
                ,credit_game_draw_scope = #{creditGameDrawScope}
            </if>

            <if test="creditGameDrawLimit != null" >
                ,credit_game_draw_limit = #{creditGameDrawLimit}
            </if>

            <if test="creditGameFreeScope != null" >
                ,credit_game_free_scope = #{creditGameFreeScope}
            </if>

            <if test="creditGameFreeLimit != null" >
                ,credit_game_free_limit = #{creditGameFreeLimit}
            </if>

            <if test="creditGameSmallImage != null" >
                ,credit_game_small_image = #{creditGameSmallImage}
            </if>

            <if test="creditGameWhiteImage != null" >
                ,credit_game_white_image = #{creditGameWhiteImage}
            </if>

            <if test="creditGameBannerImage != null" >
                ,credit_game_banner_image = #{creditGameBannerImage}
            </if>

            <if test="creditGameRecommendImage != null" >
                ,credit_game_recommend_image = #{creditGameRecommendImage}
            </if>
            
            <if test="creditGameLogo != null">
            	,credit_game_logo = #{creditGameLogo}
            </if>

            <if test="creditGameLotteryCount != null" >
                ,credit_game_lottery_count = #{creditGameLotteryCount}
            </if>

            <if test="creditGameCustomTag != null" >
                ,credit_game_custom_tag = #{creditGameCustomTag}
            </if>

            <if test="creditGameActivityCategoryId != null" >
                ,credit_game_activity_category_id = #{creditGameActivityCategoryId}
            </if>

            <if test="creditGameAutoOffDate != null" >
                ,credit_game_auto_off_date = #{creditGameAutoOffDate}
            </if>

            <if test="creditGameSwitches != null" >
                ,credit_game_switches = #{creditGameSwitches}
            </if>

            <if test="creditGameRule != null" >
                ,credit_game_rule = #{creditGameRule}
            </if>

            <if test="creditGameFreeRule != null" >
                ,credit_game_free_rule = #{creditGameFreeRule}
            </if>

            <if test="creditGameDesc != null" >
                ,credit_game_desc = #{creditGameDesc}
            </if>

            <if test="creditGameExtDesc != null" >
                ,credit_game_ext_desc = #{creditGameExtDesc}
            </if>

            <if test="creditGameAwardPosition != null" >
                ,credit_game_award_position = #{creditGameAwardPosition}
            </if>

            <if test="creditGameAwardConfig != null" >
                ,credit_game_award_config = #{creditGameAwardConfig}
            </if>

            <if test="creditGameValveConfig != null" >
                ,credit_game_valve_config = #{creditGameValveConfig}
            </if>

            <if test="creditGameBetConfig != null" >
                ,credit_game_bet_config = #{creditGameBetConfig}
            </if>

            <if test="creditGameRuleScript != null" >
                ,credit_game_rule_script = #{creditGameRuleScript}
            </if>

            <if test="deleted != null" >
                ,deleted = #{deleted}
            </if>

            <if test="createPerson != null" >
                ,create_person = #{createPerson}
            </if>

            <if test="remarks != null" >
                ,remarks = #{remarks}
            </if>

            <if test="activityActionType != null" >
                ,activity_action_type = #{activityActionType}
            </if>

            <if test="imageJson != null" >
                ,image_json = #{imageJson}
            </if>

        </set>
        where id = #{id}
    </update>
    <!-- 某些可空字段设为null -->
    <update id="updateNull" parameterType="java.util.Map" >
        update tb_credit_game
        <set >
            gmt_modified = now()

            <if test="creditGameDuibaPrice != null" >
                ,credit_game_duiba_price = null
            </if>

            <if test="creditGameCreditsPrice != null">
                ,credit_game_credits_price = null
            </if>

            <if test="creditGameAppCount != null" >
                ,credit_game_app_count = null
            </if>

            <if test="creditGameDrawScope != null" >
                ,credit_game_draw_scope = null
            </if>

            <if test="creditGameDrawLimit != null" >
                ,credit_game_draw_limit = null
            </if>

            <if test="creditGameFreeScope != null" >
                ,credit_game_free_scope = null
            </if>

            <if test="creditGameFreeLimit != null" >
                ,credit_game_free_limit = null
            </if>

            <if test="creditGameLotteryCount != null" >
                ,credit_game_lottery_count = null
            </if>

            <if test="creditGameCustomTag != null" >
                ,credit_game_custom_tag = null
            </if>

            <if test="creditGameActivityCategoryId != null" >
                ,credit_game_activity_category_id = null
            </if>

            <if test="creditGameAutoOffDate != null" >
                ,credit_game_auto_off_date = null
            </if>

            <if test="creditGameRule != null" >
                ,credit_game_rule = null
            </if>

            <if test="creditGameFreeRule != null" >
                ,credit_game_free_rule = null
            </if>

            <if test="creditGameDesc != null" >
                ,credit_game_desc = null
            </if>

            <if test="creditGameExtDesc != null" >
                ,credit_game_ext_desc = null
            </if>

            <if test="creditGameAwardPosition != null" >
                ,credit_game_award_position = null
            </if>

            <if test="creditGameAwardConfig != null" >
                ,credit_game_award_config = null
            </if>

            <if test="creditGameValveConfig != null" >
                ,credit_game_valve_config = null
            </if>

            <if test="creditGameBetConfig != null" >
                ,credit_game_bet_config = null
            </if>

            <if test="creditGameRuleScript != null" >
                ,credit_game_rule_script = null
            </if>

        </set>
        where id = #{id}
    </update>
    <select id ="selectAutoOff" resultType="Long">
    	select id from tb_credit_game where credit_game_status = 1 and deleted = 0 and credit_game_auto_off_date = substr(now(),1,13)
    </select>
</mapper>