<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.game.impl.DuibaQuestionStockManualChangeDaoImpl">
	
	<resultMap type="DuibaQuestionStockManualChangeEntity" id="questionStockChangeMap">
		<id column="id" property="id"/>
		<result column="question_stock_id" property="questionStockId"/>
		<result column="change_kind" property="changeKind"/>
		<result column="change_quantity" property="changeQuantity"/>
		<result column="before_stock" property="beforeStock"/>
		<result column="after_stock" property="afterStock"/>
		<result column="gmt_create" property="gmtCreate"/>
		<result column="gmt_modified" property="gmtModified"/>
	</resultMap>
	
	<select id="findByStockId" parameterType="long" resultMap="questionStockChangeMap">
		select * from duiba_question_stock_manual_change where question_stock_id = #{questionStockId}
	</select>
	
	<insert id="addBatch">
		insert into duiba_question_stock_manual_change(
			question_stock_id,
			change_kind,
			change_quantity,
			before_stock,
			after_stock,
			gmt_create,
			gmt_modified
		) values
		<foreach collection="list" item="item" separator=",">
		(
			#{item.questionStockId},
			#{item.changeKind},
			#{item.changeQuantity},
			#{item.beforeStock},
			#{item.afterStock},
			now(),
			now()
		)
		</foreach>
	</insert>
	
	<insert id="add" parameterType="DuibaQuestionStockManualChangeEntity">
		insert into duiba_question_stock_manual_change(
			question_stock_id,
			change_kind,
			change_quantity,
			before_stock,
			after_stock,
			gmt_create,
			gmt_modified
		) values
		(
			#{questionStockId},
			#{changeKind},
			#{changeQuantity},
			#{beforeStock},
			#{afterStock},
			now(),
			now()
		)
	</insert>
</mapper>
