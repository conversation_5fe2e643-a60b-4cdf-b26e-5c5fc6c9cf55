<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper
	namespace="cn.com.duiba.activity.center.biz.dao.gamecenter.impl.GameCenterResourceLocationDaoImpl">

	<resultMap id="GameCenterResourceLocationResultMap"
		type="cn.com.duiba.activity.center.biz.entity.gamecenter.GameCenterResourceLocationEntity">
		<result column="id" property="id" />
		<result column="loc_name" property="name" />
		<result column="title" property="title" />
		<result column="description" property="description" />
		<result column="max_limit" property="maxLimit" />
		<result column="min_limit" property="minLimit" />
		<result column="loc_status" property="status" />
		<result column="payload" property="payload" />
		<result column="gmt_create" property="gmtCreate" />
		<result column="gmt_modified" property="gmtModified" />
	</resultMap>

	<sql id="All_Column">
		id, loc_name, title, description, max_limit, min_limit,
		loc_status,
		payload,
		gmt_create, gmt_modified
	</sql>

	<select id="find" resultMap="GameCenterResourceLocationResultMap"
		parameterType="java.lang.Long">
		select
		<include refid="All_Column" />
		from tb_game_center_resource_location where id=#{id}
	</select>

	<select id="findResourceLocationByName" resultMap="GameCenterResourceLocationResultMap"
		parameterType="java.lang.String">
		select
		<include refid="All_Column" />
		from tb_game_center_resource_location where loc_name=#{name}
	</select>

</mapper>