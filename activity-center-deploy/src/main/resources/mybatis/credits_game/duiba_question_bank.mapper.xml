<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.activity.center.biz.dao.game.impl.DuibaQuestionBankDaoImpl">

	<select id="findByPage" parameterType="java.util.Map" resultType="DuibaQuestionBankEntity">
		select 
		<include refid="BaseColumn"/>
		from duiba_question_bank
		where deleted = 0
		order by gmt_create desc
		limit #{offset},#{max}
	</select>
	
	<select id="findAll" resultType="DuibaQuestionBankEntity">
		select 
		<include refid="BaseColumn"/>
		from duiba_question_bank
		where deleted = 0
		order by gmt_create desc
	</select>
	
	<select id="findTotalCount" resultType="Long">
		select 
		count(id)
		from duiba_question_bank
		where deleted = 0
	</select>

	<insert id="insert" parameterType="DuibaQuestionBankEntity">
		<selectKey resultType="long" order="AFTER" keyProperty="id"> 
	        select @@IDENTITY as id
	    </selectKey>
		insert into duiba_question_bank(
			name,
			number,
			deleted,
			gmt_create,
			gmt_modified
		) values
		(
			#{name},
			0,
			0,
			now(),
			now()
		)
	</insert>
	
	<update id="updateName" parameterType="java.util.Map">
		update duiba_question_bank set name=#{name} ,gmt_modified=now()
		where id=#{id}
	</update>
	
	<update id="updateNumberAdd" parameterType="Long">
		update duiba_question_bank set number= number+1 ,gmt_modified=now()
		where id=#{id}
	</update>
	
	<update id="updateNumberSub" parameterType="Long">
		update duiba_question_bank set number= number-1 ,gmt_modified=now()
		where id=#{id} and number &gt; 0
	</update>
	
	<update id="delete" parameterType="Long">
		update duiba_question_bank set deleted=1 ,gmt_modified=now()
		where id=#{id}
	</update>
	
	<select id="find" resultType="DuibaQuestionBankEntity">
		select 
		<include refid="BaseColumn"/>
		from duiba_question_bank
		where id = #{id}
	</select>
	
	<sql id="BaseColumn">
		id as id,
		name,
		number,
		deleted,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified
	</sql>
</mapper>
