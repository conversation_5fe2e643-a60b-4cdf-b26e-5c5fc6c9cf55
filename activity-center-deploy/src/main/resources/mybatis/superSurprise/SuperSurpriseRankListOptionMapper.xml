<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.superSurprise.impl.SuperSurpriseRankListOptionDaoImpl">

    <resultMap type="cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseRankListOptionEntity" id="superSurpriseRankListOptionMap">
        <result property="activityId" column="activity_id"/>
        <result property="appId" column="app_id"/>
        <result property="endIndex" column="end_index"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="id" column="id"/>
        <result property="optionId" column="option_id"/>
        <result property="startIndex" column="start_index"/>
    </resultMap>

    <sql id="columns">
        activity_id,
        app_id,
        end_index,
        gmt_create,
        gmt_modified,
        id,
        option_id,
        start_index
    </sql>

    <insert id="save" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseRankListOptionEntity">
        INSERT INTO tb_super_surprise_rank_list_option(activity_id,app_id,end_index,option_id,start_index)
        VALUES(#{activityId},#{appId},#{endIndex},#{optionId},#{startIndex})
    </insert>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseRankListOptionEntity">
        INSERT INTO tb_super_surprise_rank_list_option(activity_id,app_id,end_index,option_id,start_index)
        VALUES
        <foreach collection="list" item="item" separator="," >
            (#{item.activityId},#{item.appId},#{item.endIndex},#{item.optionId},#{item.startIndex})
        </foreach>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_super_surprise_rank_list_option WHERE id=#{id}
    </delete>

    <delete id="deleteBatchByIds">
        DELETE FROM tb_super_surprise_rank_list_option WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteByActivityId">
        DELETE FROM tb_super_surprise_rank_list_option WHERE activity_id=#{activityId}
    </delete>

    <update id="updateById" parameterType="cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseRankListOptionEntity">
        UPDATE tb_super_surprise_rank_list_option
        <set>
            <if test="activityId != null">
                activity_id = #{activityId},
            </if>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="endIndex != null">
                end_index = #{endIndex},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified},
            </if>
            <if test="optionId != null">
                option_id = #{optionId},
            </if>
            <if test="startIndex != null">
                start_index = #{startIndex},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="getById" resultMap="superSurpriseRankListOptionMap">
        SELECT <include refid="columns"/>
        FROM tb_super_surprise_rank_list_option
        WHERE id = #{id}
    </select>

    <select id="listByIds" resultMap="superSurpriseRankListOptionMap">
        SELECT <include refid="columns"/>
        FROM tb_super_surprise_rank_list_option
        WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryByActivityId" resultMap="superSurpriseRankListOptionMap">
        SELECT <include refid="columns"/>
        FROM tb_super_surprise_rank_list_option
        where activity_id=#{activityId}
    </select>

    <select id="findByActivityIdAndRankRange" resultMap="superSurpriseRankListOptionMap">
        SELECT <include refid="columns"/>
        FROM tb_super_surprise_rank_list_option
        where activity_id=#{activityId}
        and start_index  <![CDATA[ <= ]]> #{rank}
        and end_index <![CDATA[ >= ]]> #{rank}
    </select>


</mapper>