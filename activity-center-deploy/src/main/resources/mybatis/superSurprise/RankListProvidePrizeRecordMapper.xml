<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.superSurprise.impl.RankListProvidePrizeRecordDaoImpl">

    <resultMap type="cn.com.duiba.activity.center.biz.entity.superSurprise.RankListProvidePrizeRecordEntity" id="rankListProvidePrizeRecordMap">
        <result property="activityId" column="activity_id"/>
        <result property="activityType" column="activity_type"/>
        <result property="appId" column="app_id"/>
        <result property="consumerId" column="consumer_id"/>
        <result property="consumerRank" column="consumer_rank"/>
        <result property="consumerScore" column="consumer_score"/>
        <result property="exchangeStatus" column="exchange_status"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="id" column="id"/>
        <result property="optionId" column="option_id"/>
        <result property="orderNum" column="order_num"/>
    </resultMap>

    <sql id="columns">
        activity_id,
        activity_type,
        app_id,
        consumer_id,
        consumer_rank,
        consumer_score,
        exchange_status,
        gmt_create,
        gmt_modified,
        id,
        option_id,
        order_num
    </sql>

    <insert id="save" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.biz.entity.superSurprise.RankListProvidePrizeRecordEntity">
        INSERT INTO tb_rank_list_provide_prize_record(activity_id,activity_type,app_id,consumer_id,consumer_rank,consumer_score,exchange_status,option_id,order_num)
        VALUES(#{activityId},#{activityType},#{appId},#{consumerId},#{consumerRank},#{consumerScore},#{exchangeStatus},#{optionId},#{orderNum})
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_rank_list_provide_prize_record WHERE id=#{id}
    </delete>

    <delete id="deleteBatchByIds">
        DELETE FROM tb_rank_list_provide_prize_record WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <update id="updateById" parameterType="cn.com.duiba.activity.center.biz.entity.superSurprise.RankListProvidePrizeRecordEntity">
        UPDATE tb_rank_list_provide_prize_record
        <set>
            <if test="activityId != null">
                activity_id = #{activityId},
            </if>
            <if test="activityType != null">
                activity_type = #{activityType},
            </if>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="consumerId != null">
                consumer_id = #{consumerId},
            </if>
            <if test="consumerRank != null">
                consumer_rank = #{consumerRank},
            </if>
            <if test="consumerScore != null">
                consumer_score = #{consumerScore},
            </if>
            <if test="exchangeStatus != null">
                exchange_status = #{exchangeStatus},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified},
            </if>
            <if test="optionId != null">
                option_id = #{optionId},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
        </set>
        WHERE id=#{id}
    </update>
    
    <update id="updateStatus">
        update tb_rank_list_provide_prize_record
        set exchange_status = #{newStatus}
        where id=#{id} and exchange_status=#{preStatus}
    </update>

    <select id="getById" resultMap="rankListProvidePrizeRecordMap">
        SELECT <include refid="columns"/>
        FROM tb_rank_list_provide_prize_record
        WHERE id = #{id}
    </select>

    <select id="findByOrderNum" resultMap="rankListProvidePrizeRecordMap">
        SELECT <include refid="columns"/>
        FROM tb_rank_list_provide_prize_record
        WHERE order_num = #{orderNum}
    </select>

    <select id="listByIds" resultMap="rankListProvidePrizeRecordMap">
        SELECT <include refid="columns"/>
        FROM tb_rank_list_provide_prize_record
        WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="getByAcidTypeCid" resultMap="rankListProvidePrizeRecordMap">
        SELECT <include refid="columns"/>
        FROM tb_rank_list_provide_prize_record
        WHERE activity_id=#{activityId} and activity_type=#{activityType} and consumer_id=#{consumerId}
    </select>

    <select id="listByAcidTypeCids" resultMap="rankListProvidePrizeRecordMap">
        SELECT <include refid="columns"/>
        FROM tb_rank_list_provide_prize_record
        WHERE
        activity_id IN
        <foreach item="item" index="index" collection="activityIds" open="(" separator="," close=")">
            #{item}
        </foreach>
         and 
        activity_type IN
        <foreach item="item" index="index" collection="activityTypes" open="(" separator="," close=")">
            #{item}
        </foreach>
         and 
        consumer_id IN
        <foreach item="item" index="index" collection="consumerIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        
    </select>

</mapper>