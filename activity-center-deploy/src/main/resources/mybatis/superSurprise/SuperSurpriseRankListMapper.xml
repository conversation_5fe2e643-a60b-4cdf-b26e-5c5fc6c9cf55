<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.superSurprise.impl.SuperSurpriseRankListDaoImpl">

    <resultMap type="cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseRankListEntity" id="superSurpriseRankListMap">
        <result property="activityId" column="activity_id"/>
        <result property="appId" column="app_id"/>
        <result property="consumerId" column="consumer_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="id" column="id"/>
        <result property="partnerUserId" column="partner_user_id"/>
        <result property="score" column="score"/>
    </resultMap>

    <sql id="columns">
        activity_id,
        app_id,
        consumer_id,
        gmt_create,
        gmt_modified,
        id,
        partner_user_id,
        score
    </sql>

    <insert id="save" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseRankListEntity">
        INSERT INTO tb_super_surprise_rank_list(activity_id,app_id,consumer_id,partner_user_id,score)
        VALUES(#{activityId},#{appId},#{consumerId},#{partnerUserId},#{score})
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_super_surprise_rank_list WHERE id=#{id}
    </delete>

    <delete id="deleteBatchByIds">
        DELETE FROM tb_super_surprise_rank_list WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <update id="updateById" parameterType="cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseRankListEntity">
        UPDATE tb_super_surprise_rank_list
        <set>
            <if test="activityId != null">
                activity_id = #{activityId},
            </if>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="consumerId != null">
                consumer_id = #{consumerId},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified},
            </if>
            <if test="partnerUserId != null">
                partner_user_id = #{partnerUserId},
            </if>
            <if test="score != null">
                score = #{score},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <update id="increaseScoreById" parameterType="cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseRankListEntity">
        UPDATE tb_super_surprise_rank_list
        <set>
            <if test="activityId != null">
                activity_id = #{activityId},
            </if>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="consumerId != null">
                consumer_id = #{consumerId},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified},
            </if>
            <if test="partnerUserId != null">
                partner_user_id = #{partnerUserId},
            </if>
            <if test="score != null">
                score = score+ #{score},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="getById" resultMap="superSurpriseRankListMap">
        SELECT <include refid="columns"/>
        FROM tb_super_surprise_rank_list
        WHERE id = #{id}
    </select>

    <select id="listByIds" resultMap="superSurpriseRankListMap">
        SELECT <include refid="columns"/>
        FROM tb_super_surprise_rank_list
        WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="getByAcidCid" resultMap="superSurpriseRankListMap">
        SELECT <include refid="columns"/>
        FROM tb_super_surprise_rank_list
        WHERE activity_id=#{activityId} and consumer_id=#{consumerId}
    </select>

    <select id="getRank" parameterType="java.lang.Long" resultType="cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseRankListDto">
        SELECT
        id as id,
        app_id as appId,
        activity_id as activityId,
        consumer_id as consumerId,
        partner_user_id as partnerUserId,
        score as score,
        gmt_create as gmtCreate,
        gmt_modified  as gmtModified,
        @curRank := @curRank + 1 AS rank
        from tb_super_surprise_rank_list t,(SELECT @curRank := 0) q
        where activity_id=#{activityId}
        order by score desc,gmt_create asc
        limit 99
    </select>

    <select id="getRankTwoThousand" parameterType="java.lang.Long" resultType="cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseRankListDto">
        SELECT
            id as id,
            app_id as appId,
            activity_id as activityId,
            consumer_id as consumerId,
            partner_user_id as partnerUserId,
            score as score,
            gmt_create as gmtCreate,
            gmt_modified  as gmtModified,
            @curRank := @curRank + 1 AS rank
        from tb_super_surprise_rank_list t,(SELECT @curRank := 0) q
        where activity_id=#{activityId}
        order by score desc,gmt_create asc
            limit 1200
    </select>


    <select id="getSelfRank" resultType="java.lang.Integer">
        select count(*)
        from tb_super_surprise_rank_list t,(select score,gmt_create
                                             from tb_super_surprise_rank_list
                                             where activity_id=#{activityId} and consumer_id=#{consumerId}) p
        where activity_id=#{activityId}
        and (t.score > p.score or
             (t.score = p.score and <![CDATA[ t.gmt_create <  p.gmt_create]]>)  )

    </select>

    <select id="getSelfRankRecord" resultMap="superSurpriseRankListMap">
        SELECT <include refid="columns"/>
        FROM tb_super_surprise_rank_list
        WHERE activity_id = #{activityId}
        and consumer_id = #{consumerId}
    </select>
</mapper>