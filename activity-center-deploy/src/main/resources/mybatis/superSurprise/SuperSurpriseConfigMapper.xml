<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.activity.center.biz.dao.superSurprise.impl.SuperSurpriseConfigDaoImpl">

    <resultMap type="cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseConfigEntity" id="superSurpriseConfigMap">
        <result property="activityRule" column="activity_rule"/>
        <result property="activityStatus" column="activity_status"/>
        <result property="activityTitle" column="activity_title"/>
        <result property="appId" column="app_id"/>
        <result property="bannerImage" column="banner_image"/>
        <result property="consumeCredits" column="consume_credits"/>
        <result property="enableCertainPrize" column="enable_certain_prize"/>
        <result property="enableCredits" column="enable_credits"/>
        <result property="enableCreditsRebirth" column="enable_credits_rebirth"/>
        <result property="enableRankListPrize" column="enable_rank_list_prize"/>
        <result property="enableRatePrize" column="enable_rate_prize"/>
        <result property="enableRateRebirth" column="enable_rate_rebirth"/>
        <result property="endTime" column="end_time"/>
        <result property="extraInfo" column="extra_info"/>
        <result property="freeLimitTimes" column="free_limit_times"/>
        <result property="freeLimitTimesScope" column="free_limit_times_scope"/>
        <result property="gameLevel" column="game_level"/>
        <result property="gamePeriod" column="game_period"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="id" column="id"/>
        <result property="limitTimes" column="limit_times"/>
        <result property="limitTimesScope" column="limit_times_scope"/>
        <result property="rankEndTime" column="rank_end_time"/>
        <result property="rankStartTime" column="rank_start_time"/>
        <result property="rebirthCredits" column="rebirth_credits"/>
        <result property="rebirthCreditsLimitTimes" column="rebirth_credits_limit_times"/>
        <result property="rebirthRate" column="rebirth_rate"/>
        <result property="rebirthRateLimitTimes" column="rebirth_rate_limit_times"/>
        <result property="smallImage" column="small_image"/>
        <result property="startTime" column="start_time"/>
        <result property="styleConfig" column="style_config"/>
        <result property="enableRebirth" column="enable_rebirth"/>
    </resultMap>

    <sql id="columns">
        activity_rule,
        activity_status,
        activity_title,
        app_id,
        banner_image,
        consume_credits,
        enable_rebirth,
        enable_certain_prize,
        enable_credits,
        enable_credits_rebirth,
        enable_rank_list_prize,
        enable_rate_prize,
        enable_rate_rebirth,
        end_time,
        extra_info,
        free_limit_times,
        free_limit_times_scope,
        game_level,
        game_period,
        gmt_create,
        gmt_modified,
        id,
        limit_times,
        limit_times_scope,
        rank_end_time,
        rank_start_time,
        rebirth_credits,
        rebirth_credits_limit_times,
        rebirth_rate,
        rebirth_rate_limit_times,
        small_image,
        start_time,
        style_config
    </sql>

    <insert id="save" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseConfigEntity">
        INSERT INTO tb_super_surprise_config(activity_rule,activity_status,activity_title,app_id,banner_image,consume_credits,enable_certain_prize,enable_credits,enable_credits_rebirth,enable_rank_list_prize,enable_rate_prize,enable_rate_rebirth,end_time,extra_info,free_limit_times,free_limit_times_scope,game_level,game_period,limit_times,limit_times_scope,rank_end_time,rank_start_time,rebirth_credits,rebirth_credits_limit_times,rebirth_rate,rebirth_rate_limit_times,small_image,start_time,style_config,enable_rebirth)
        VALUES(#{activityRule},#{activityStatus},#{activityTitle},#{appId},#{bannerImage},#{consumeCredits},#{enableCertainPrize},#{enableCredits},#{enableCreditsRebirth},#{enableRankListPrize},#{enableRatePrize},#{enableRateRebirth},#{endTime},#{extraInfo},#{freeLimitTimes},#{freeLimitTimesScope},#{gameLevel},#{gamePeriod},#{limitTimes},#{limitTimesScope},#{rankEndTime},#{rankStartTime},#{rebirthCredits},#{rebirthCreditsLimitTimes},#{rebirthRate},#{rebirthRateLimitTimes},#{smallImage},#{startTime},#{styleConfig},#{enableRebirth})
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_super_surprise_config WHERE id=#{id}
    </delete>

    <delete id="deleteBatchByIds">
        DELETE FROM tb_super_surprise_config WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <update id="updateById" parameterType="cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseConfigEntity">
        UPDATE tb_super_surprise_config
        <set>
            <if test="activityRule != null">
                activity_rule = #{activityRule},
            </if>
            <if test="activityStatus != null">
                activity_status = #{activityStatus},
            </if>
            <if test="activityTitle != null">
                activity_title = #{activityTitle},
            </if>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="bannerImage != null">
                banner_image = #{bannerImage},
            </if>
            <if test="consumeCredits != null">
                consume_credits = #{consumeCredits},
            </if>
            <if test="enableCertainPrize != null">
                enable_certain_prize = #{enableCertainPrize},
            </if>
            <if test="enableCredits != null">
                enable_credits = #{enableCredits},
            </if>
            <if test="enableCreditsRebirth != null">
                enable_credits_rebirth = #{enableCreditsRebirth},
            </if>
            <if test="enableRankListPrize != null">
                enable_rank_list_prize = #{enableRankListPrize},
            </if>
            <if test="enableRatePrize != null">
                enable_rate_prize = #{enableRatePrize},
            </if>
            <if test="enableRateRebirth != null">
                enable_rate_rebirth = #{enableRateRebirth},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="extraInfo != null">
                extra_info = #{extraInfo},
            </if>
            free_limit_times = #{freeLimitTimes},

            <if test="freeLimitTimesScope != null">
                free_limit_times_scope = #{freeLimitTimesScope},
            </if>
            <if test="gameLevel != null">
                game_level = #{gameLevel},
            </if>
            <if test="gamePeriod != null">
                game_period = #{gamePeriod},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified},
            </if>

            limit_times = #{limitTimes},

            <if test="limitTimesScope != null">
                limit_times_scope = #{limitTimesScope},
            </if>
            <if test="rankEndTime != null">
                rank_end_time = #{rankEndTime},
            </if>
            <if test="rankStartTime != null">
                rank_start_time = #{rankStartTime},
            </if>
            <if test="rebirthCredits != null">
                rebirth_credits = #{rebirthCredits},
            </if>
            <if test="rebirthCreditsLimitTimes != null">
                rebirth_credits_limit_times = #{rebirthCreditsLimitTimes},
            </if>
            <if test="rebirthRate != null">
                rebirth_rate = #{rebirthRate},
            </if>
            <if test="rebirthRateLimitTimes != null">
                rebirth_rate_limit_times = #{rebirthRateLimitTimes},
            </if>
            <if test="smallImage != null">
                small_image = #{smallImage},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="styleConfig != null">
                style_config = #{styleConfig},
            </if>
            <if test="enableRebirth != null">
                enable_rebirth = #{enableRebirth},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="getById" resultMap="superSurpriseConfigMap">
        SELECT <include refid="columns"/>
        FROM tb_super_surprise_config
        WHERE id = #{id}
    </select>

    <select id="listByIds" resultMap="superSurpriseConfigMap">
        SELECT <include refid="columns"/>
        FROM tb_super_surprise_config
        WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>



</mapper>