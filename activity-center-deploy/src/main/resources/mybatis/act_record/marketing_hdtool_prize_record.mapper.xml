<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.duiba.activity.center.biz.dao.activity.impl.MarketingHdtoolPrizeRecordDaoImpl">

  <insert id="insert" keyProperty="id" parameterType="cn.com.duiba.activity.center.api.dto.activity.MarketingHdtoolPrizeRecordDto" useGeneratedKeys="true">
    insert into tb_marketing_hdtool_prize_record (
      activity_id,
      activity_type,
      consumer_id,
      app_item_id,
      prize_id,
      prize_type,
      prize_name,
      prize_value,
      prize_status,
      hdtool_order_id
    )values (
      #{activityId},
      #{activityType},
      #{consumerId},
      #{appItemId},
      #{prizeId},
      #{prizeType},
      #{prizeName},
      #{prizeValue},
      #{prizeStatus},
      #{hdtoolOrderId}
    )
  </insert>

  <update id="updateStatusByHdtoolOrderId">
    update tb_marketing_hdtool_prize_record
    set prize_status = #{status}
    where hdtool_order_id = #{hdtoolOrderId}
  </update>

  <update id="updateById" parameterType="cn.com.duiba.activity.center.api.dto.activity.MarketingHdtoolPrizeRecordDto">
    update tb_marketing_hdtool_prize_record
    <set>
      app_item_id = #{appItemId},
      prize_id = #{prizeId},
      prize_type = #{prizeType},
      prize_name = #{prizeName},
      prize_value = #{prizeValue},
      <if test="prizeStatus != null">
          prize_status = #{prizeStatus},
      </if>
    </set>
    where id = #{id}
  </update>

</mapper>