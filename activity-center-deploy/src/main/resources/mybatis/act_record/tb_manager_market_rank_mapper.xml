<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.activity.center.biz.dao.managermarket.impl.ManagerMarketRankDaoImpl">
    <resultMap id="BaseResultMap" type="cn.com.duiba.activity.center.biz.entity.managermarket.ManagerMarketRankEntity">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="config_id" property="configId"/>
        <result column="rank_time" property="rankTime"/>
        <result column="rank_period_type" property="rankPeriodType"/>
        <result column="rank_latitude_type" property="rankLatitudeType"/>
        <result column="staff_number" property="staffNumber"/>
        <result column="staff_name" property="staffName"/>
        <result column="organ_number" property="organNumber"/>
        <result column="organ_name" property="organName"/>
        <result column="wx_friend_count" property="wxFriendCount"/>
        <result column="wx_circle_count" property="wxCircleCount"/>
        <result column="url_opened_count" property="urlOpenedCount"/>
        <result column="binded_count" property="bindedCount"/>
        <result column="total_score" property="totalScore"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <sql id="AllColumn">
        id, app_id, config_id, rank_time, rank_period_type, rank_latitude_type, staff_number,
        staff_name, organ_number, organ_name, wx_friend_count, wx_circle_count, url_opened_count,
        binded_count, total_score, gmt_modified
    </sql>

    <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.managermarket.ManagerMarketStaffEntity"
        useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_manager_market_rank
        (
        app_id,
        config_id,
        rank_time,
        rank_period_type,
        rank_latitude_type,
        staff_number,
        staff_name,
        organ_number,
        organ_name,
        wx_friend_count,
        wx_circle_count,
        url_opened_count,
        binded_count,
        total_score
        )
        values
        (
            #{appId},
            #{configId},
            #{rankTime},
            #{rankPeriodType},
            #{rankLatitudeType},
            #{staffNumber},
            #{staffName},
            #{organNumber},
            #{organName},
            #{wxFriendCount},
            #{wxCircleCount},
            #{urlOpenedCount},
            #{bindedCount},
            #{totalScore}
            )
    </insert>

    <select id="selectByConfigIdAndStaffNumber" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="AllColumn"/>
        FROM tb_manager_market_rank
        WHERE config_id = #{configId}
        and staff_number = #{staffNumber}
    </select>

    <select id="selectByConfigIdAndOrganNumber" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="AllColumn"/>
        FROM tb_manager_market_rank
        WHERE config_id = #{configId}
        and organ_number = #{organNumber}
    </select>

    <update id="incrTotalScoreByWxFriend">
        update tb_manager_market_rank set total_score = total_score + #{incrScore}, wx_friend_count = wx_friend_count + 1
        where id = #{id}
    </update>

    <update id="incrTotalScoreByWxCircle">
        update tb_manager_market_rank set total_score = total_score + #{incrScore}, wx_circle_count = wx_circle_count + 1
        where id = #{id}
    </update>

    <update id="incrTotalScoreByUrlOpened">
        update tb_manager_market_rank set total_score = total_score + #{incrScore}, url_opened_count = url_opened_count + 1
        where id = #{id}
    </update>

    <update id="incrTotalScoreByBinded">
        update tb_manager_market_rank set total_score = total_score + #{incrScore}, binded_count = binded_count + 1
        where id = #{id}
    </update>

    <select id="listRankByLatitudeType" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="AllColumn"/>
        FROM tb_manager_market_rank
        WHERE config_id = #{configId}
        and rank_time = #{rankTime}
        and rank_latitude_type = #{rankLatitudeType}
        order by total_score desc, gmt_modified asc
        <if test="pageNo != null and pageSize != null">
            limit #{pageNo}, #{pageSize}
        </if>
    </select>

    <select id="getCountByTotalScoreAndDate" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM tb_manager_market_rank
        WHERE config_id = #{configId}
        and rank_latitude_type = #{rankLatitudeType}
        and rank_time = #{rankTime}
        and (total_score > #{totalScore} or (total_score = #{totalScore} and gmt_modified <![CDATA[<]]> #{updateTime}))
    </select>


    <select id="staffForwardRankList" parameterType="map" resultMap="BaseResultMap">
        SELECT  <include refid="AllColumn"/>
        FROM tb_manager_market_rank
        WHERE config_id = #{configId}
        AND rank_time=#{rankTime}
        and rank_latitude_type=#{rankType}
        and rank_period_type=#{sortNum}
        order by total_score desc, gmt_modified asc
        <if test="offset!=null and max!=null">
            limit #{offset},#{max}
        </if>
    </select>

    <select id="staffForwardRankListCount" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM tb_manager_market_rank
        WHERE config_id = #{configId}
        AND rank_time=#{rankTime}
        and rank_latitude_type=#{rankType}
        and rank_period_type=#{sortNum}
    </select>


</mapper>