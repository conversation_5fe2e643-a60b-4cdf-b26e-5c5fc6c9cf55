<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.duiba.activity.center.biz.dao.carousel.impl.CarouselActivityRecordDaoImpl">
    <resultMap id="baseResultMap" type="cn.com.duiba.activity.center.api.dto.carousel.CarouselActivityRecordDto">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="activity_id" property="activityId"/>
        <result column="activity_type" property="activityType"/>
        <result column="prize_name" property="prizeName"/>
        <result column="consumer_name" property="consumerName"/>
        <result column="gmt_create" property="gmtCreate"/>
    </resultMap>
    <sql id="fields">
    id,
    app_id,
    activity_id,
    activity_type,
    prize_name,
    consumer_name,
    gmt_create
  </sql>

    <insert id="insert" keyProperty="id"
            parameterType="cn.com.duiba.activity.center.api.dto.carousel.CarouselActivityRecordDto"
            useGeneratedKeys="true">
    insert into tb_carousel_activity_record (
      app_id,
      activity_id,
      activity_type,
      prize_name,
      consumer_name
    )values (
      #{appId},
      #{activityId},
      #{activityType},
      #{prizeName},
      #{consumerName}
    )
  </insert>

    <select id="selectTop4EachActivityType" parameterType="cn.com.duiba.activity.center.api.params.CarouselQueryParam"
            resultMap="baseResultMap">
        <foreach collection="list" separator="union all" item="it">
            (select
            <include refid="fields"/>
            from tb_carousel_activity_record
            where activity_id in
            <foreach collection="it.ids" open="(" separator="," close=")" item="activityId">#{activityId}</foreach>
            and activity_type = #{it.type}
            order by gmt_create desc
            limit #{limit})
        </foreach>
    </select>
</mapper>