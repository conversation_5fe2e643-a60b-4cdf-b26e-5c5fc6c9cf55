<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.activity.center.biz.dao.activity.impl.DyHdtoolTaskFreeTimesDaoImpl">
    <resultMap id="AllColumnMap" type="cn.com.duiba.activity.center.biz.entity.diyang.DyHdtoolTaskFreeTimesEntity">
        <result column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="operating_activity_id" property="operatingActivityId"/>
        <result column="consumer_id" property="consumerId"/>
        <result column="free_times" property="freeTimes"/>
        <result column="consume_times" property="consumeTimes"/>
        <result column="extra_json" property="extraJson"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <sql id="all_column">
        id,
        app_id,
        operating_activity_id,
        consumer_id,
        free_times,
        consume_times,
        extra_json,
        gmt_create,
        gmt_modified
    </sql>


    <!-- 插入 -->
    <insert id="insert">
        insert into tb_dy_hdtool_task_free_times (
            app_id,
            operating_activity_id,
            consumer_id,
            free_times, consume_times,
            extra_json) values
            (#{appId},
             #{operatingActivityId},
             #{consumerId},
             #{freeTimes}, #{consumeTimes},
             #{extraJson})
    </insert>


    <update id="increaseConsumerTimes">

        UPDATE tb_dy_hdtool_task_free_times set
            consume_times = consume_times +1 where consumer_id = #{cid}
                                               and operating_activity_id = #{opId}
                                               and  free_times> consume_times

    </update>

    <!-- 存在则更新，不存在则插入 -->
    <insert id="insertOrUpdate" keyProperty="id" useGeneratedKeys="true"
            parameterType="cn.com.duiba.activity.center.biz.entity.diyang.DyHdtoolTaskFreeTimesEntity">
        insert into tb_dy_hdtool_task_free_times (
            app_id,
            operating_activity_id,
            consumer_id,
            free_times, consume_times,
            extra_json) values
            (#{appId},
             #{operatingActivityId},
             #{consumerId},
             #{freeTimes}, #{consumeTimes},
             #{extraJson}) ON DUPLICATE KEY
        UPDATE
            id = #{id},
            app_id = #{appId},
            operating_activity_id = #{operatingActivityId},
            consumer_id = #{consumerId},
            free_times = #{freeTimes},
            consume_times = #{consumeTimes},
            extra_json = #{extraJson}
    </insert>

    <select id="findByCidAndOpId" resultMap="AllColumnMap">
        select *
        from tb_dy_hdtool_task_free_times
        where consumer_id = #{cid}
          and operating_activity_id = #{opId}
    </select>
</mapper>