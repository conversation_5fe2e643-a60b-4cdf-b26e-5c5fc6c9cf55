<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.duiba.activity.center.biz.dao.luckycode.impl.LuckyCodeUserAwardRecordDaoImpl">

    <sql id="BaseColumn">
          id,
          app_id as appId,
          consumer_id as consumerId,
          activity_id as activityId,
          operating_activity_id as operatingActivityId,
          award_config_id as awardConfigId,
          app_item_id as appItemId,
          prize_type as prizeType,
          state,
          consumer_name as consumerName,
          item_value as itemValue
    </sql>

    <select id="findUserActAward" parameterType="map" resultType="long">
        select award_config_id from tb_lucky_code_user_award_record
        where consumer_id = #{consumerId} and activity_id = #{activityId}
        <if test="state != null">
            and state = #{state}
        </if>
    </select>

    <select id="findUserAwardRecords" parameterType="map" resultType="cn.com.duiba.activity.center.biz.entity.luckycode.LuckyCodeUserAwardRecordEntity">
        select <include refid="BaseColumn" />
        from tb_lucky_code_user_award_record
        where consumer_id = #{consumerId} and activity_id = #{activityId}
        <if test="state != null">
            and state = #{state}
        </if>
        order by id desc
    </select>

    <select id="findAwardTopLimitWiningRecords" parameterType="map" resultType="cn.com.duiba.activity.center.biz.entity.luckycode.LuckyCodeUserAwardRecordEntity">
        select <include refid="BaseColumn" />
        from tb_lucky_code_user_award_record
        where award_config_id = #{awardConfigId}
        <if test="state != null">
            and state = #{state}
        </if>
        limit 0, #{limit}
    </select>

    <select id="selectById" parameterType="long"
            resultType="cn.com.duiba.activity.center.biz.entity.luckycode.LuckyCodeUserAwardRecordEntity">
        select
        <include refid="BaseColumn"/>
        from tb_lucky_code_user_award_record
        where id = #{id}
    </select>

    <update id="updateStateByIds" parameterType="map">
        update tb_lucky_code_user_award_record
        set state = #{state}
        where id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
    </update>

    <insert id="insert" keyProperty="id" parameterType="LuckyCodeUserAwardRecordEntity" useGeneratedKeys="true">
        insert into tb_lucky_code_user_award_record (
          app_id,
          consumer_id,
          activity_id,
          operating_activity_id,
          award_config_id,
          app_item_id,
          prize_type,
          state,
          consumer_name,
          item_value
        )values (
          #{appId},
          #{consumerId},
          #{activityId},
          #{operatingActivityId},
          #{awardConfigId},
          #{appItemId},
          #{prizeType},
          #{state},
          #{consumerName},
          #{itemValue}
        )
      </insert>

    <update id="updateItemValueById" parameterType="map">
        update tb_lucky_code_user_award_record
        set item_value = #{itemValue}
        where id = #{id}
    </update>

    <select id="selectByIds" parameterType="list"
            resultType="cn.com.duiba.activity.center.biz.entity.luckycode.LuckyCodeUserAwardRecordEntity">
        select
        <include refid="BaseColumn"/>
        from tb_lucky_code_user_award_record
        where id in
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>
</mapper>