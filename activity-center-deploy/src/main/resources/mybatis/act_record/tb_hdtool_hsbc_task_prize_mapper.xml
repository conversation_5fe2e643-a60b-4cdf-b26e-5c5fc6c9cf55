<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.activity.center.biz.dao.activity.impl.HsbcHdtoolTaskPrizeDaoImpl">
    <resultMap id="BaseColumnMap" type="cn.com.duiba.activity.center.biz.entity.hsbc.HsbcHdtoolTaskPrizeEntity">
        <result column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="operating_activity_id" property="operatingActivityId"/>
        <result column="task_id" property="taskId"/>
        <result column="biz_id" property="bizId"/>
        <result column="app_item_id" property="appItemId"/>
        <result column="prize_type" property="prizeType"/>
        <result column="name" property="name"/>
        <result column="logo" property="logo"/>
        <result column="rate" property="rate"/>
        <result column="remaining" property="remaining"/>
        <result column="face_price" property="facePrice"/>
        <result column="limit_count" property="limitCount"/>
        <result column="receive_times" property="receiveTimes"/>
        <result column="min_comein" property="minComein"/>
        <result column="deleted" property="deleted"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <sql id="Base_column">
        id,
        app_id,
        operating_activity_id,
        task_id,
        biz_id,
        app_item_id,
        prize_type,
        name,
        logo,
        rate,
        remaining,
        face_price,
        limit_count,
        receive_times,
        min_comein,
        deleted,
        gmt_create,
        gmt_modified
    </sql>



    <!-- 查询活动下所有绑定的任务关系 -->
    <select id="findByActAndTaskId" resultMap="BaseColumnMap">
        select *
        from tb_hdtool_hsbc_task_prize
        where operating_activity_id = #{operatingActivityId}
          and task_id = #{taskId}
          and deleted = 0
    </select>


    <!-- 查询活动下所有绑定的任务关系 -->
    <select id="findByActId" resultMap="BaseColumnMap">
        select *
        from tb_hdtool_hsbc_task_prize
        where operating_activity_id = #{operatingActivityId}
          and deleted = 0
    </select>


    <!-- 批量删除某活动下的奖品 -->
    <update id="deleteByActId">
        update tb_hdtool_hsbc_task_prize
        set deleted = 1
        where operating_activity_id = #{operatingActivityId}
        and deleted = 0
    </update>



    <!-- 批量插入 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_hdtool_hsbc_task_prize
        (app_id,operating_activity_id,task_id,biz_id,app_item_id,prize_type,name,logo,rate,remaining,face_price,limit_count,receive_times, min_comein)
        VALUES
        <foreach collection="list" item="rel" index="index" separator=",">
            (
            #{rel.appId},
            #{rel.operatingActivityId},
            #{rel.taskId},
            #{rel.bizId},
            #{rel.appItemId},
            #{rel.prizeType},
            #{rel.name},
            #{rel.logo},
            #{rel.rate},
            #{rel.remaining},
            #{rel.facePrice},
            #{rel.limitCount},
            #{rel.receiveTimes},
            #{rel.minComein}
            )
        </foreach>
    </insert>


    <update id="decrPrizeStock">
        UPDATE tb_hdtool_hsbc_task_prize
        SET remaining = (remaining - 1)
        WHERE id = #{id} and remaining > 0
    </update>

    <update id="incrPrizeStock">
        UPDATE tb_hdtool_hsbc_task_prize
        SET remaining = (remaining + 1)
        WHERE id = #{id}
    </update>

    <select id="findOnePrize" resultMap="BaseColumnMap">
        SELECT * FROM tb_hdtool_hsbc_task_prize
        where operating_activity_id = #{operatingActivityId}
          and task_id = #{taskId}
          and biz_id = #{bizId}
          and app_item_id = #{appItemId}
          and deleted = 0
    </select>

</mapper>