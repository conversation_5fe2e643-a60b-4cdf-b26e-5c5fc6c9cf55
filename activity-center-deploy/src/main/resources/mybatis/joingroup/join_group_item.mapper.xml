<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.duiba.activity.center.biz.dao.joingroup.impl.JoinGroupItemDaoImpl">
  <resultMap id="BaseResultMap" type="cn.com.duiba.activity.center.biz.entity.joingroup.JoinGroupItemEntity">
    <id column="id" property="id"/>
    <result column="item_name" property="itemName"/>
    <result column="join_group_config_id" property="joinGroupConfigId"/>
    <result column="item_id" property="itemId"/>
    <result column="prize_type" property="prizeType"/>
    <result column="expired_time" property="expiredTime"/>
    <result column="group_number" property="groupNumber"/>
    <result column="remaining" property="remaining"/>
    <result column="face_price" property="facePrice"/>
    <result column="cheat_rule_type" property="cheatRuleType"/>
    <result column="cheat_percent" property="cheatPercent"/>
    <result column="open_limit_type" property="openLimitType"/>
    <result column="open_limit" property="openLimit"/>
    <result column="join_limit_type" property="joinLimitType"/>
    <result column="join_limit" property="joinLimit"/>
    <result column="item_status" property="itemStatus"/>
  </resultMap>

  <sql id="BaseColumn">
    id, item_name, join_group_config_id, item_id, prize_type, expired_time, group_number, remaining, face_price,
    cheat_rule_type, cheat_percent, open_limit_type, open_limit, join_limit_type, join_limit,
    item_status
  </sql>

  <select id="selectById" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="BaseColumn"/>
    from tb_join_group_item
    where id = #{id} and deleted = 0
  </select>

  <select id="selectByIds" parameterType="map" resultMap="BaseResultMap">
    SELECT <include refid="BaseColumn"/>
    FROM tb_join_group_item
    WHERE id in
    <foreach collection="idList" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
    AND deleted = 0
  </select>

  <insert id="insert" parameterType="cn.com.duiba.activity.center.biz.entity.joingroup.JoinGroupItemEntity"
          useGeneratedKeys="true" keyProperty="id">
    insert into tb_join_group_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="itemName != null">item_name,</if>
      <if test="joinGroupConfigId != null">join_group_config_id,</if>
      <if test="itemId != null">item_id,</if>
      <if test="prizeType != null">prize_type,</if>
      <if test="expiredTime != null">expired_time,</if>
      <if test="groupNumber != null">group_number,</if>
      <if test="remaining != null">remaining,</if>
      <if test="facePrice != null">face_price,</if>
      <if test="cheatRuleType != null">cheat_rule_type,</if>
      <if test="cheatPercent != null">cheat_percent,</if>
      <if test="openLimitType != null">open_limit_type,</if>
      <if test="openLimit != null">open_limit,</if>
      <if test="joinLimitType != null">join_limit_type,</if>
      <if test="joinLimit != null">join_limit,</if>
      <if test="itemStatus != null">item_status,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="itemName != null">#{itemName},</if>
      <if test="joinGroupConfigId != null">#{joinGroupConfigId},</if>
      <if test="itemId != null">#{itemId},</if>
      <if test="prizeType != null">#{prizeType},</if>
      <if test="expiredTime != null">#{expiredTime},</if>
      <if test="groupNumber != null">#{groupNumber},</if>
      <if test="remaining != null">#{remaining},</if>
      <if test="facePrice != null">#{facePrice},</if>
      <if test="cheatRuleType != null">#{cheatRuleType},</if>
      <if test="cheatPercent != null">#{cheatPercent},</if>
      <if test="openLimitType != null">#{openLimitType},</if>
      <if test="openLimit != null">#{openLimit},</if>
      <if test="joinLimitType != null">#{joinLimitType},</if>
      <if test="joinLimit != null">#{joinLimit},</if>
      <if test="itemStatus != null">#{itemStatus},</if>
    </trim>
  </insert>

  <update id="update" parameterType="cn.com.duiba.activity.center.biz.entity.joingroup.JoinGroupItemEntity">
    update tb_join_group_item
    <set>
      <if test="itemName != null">item_name = #{itemName},</if>
      <if test="joinGroupConfigId != null">join_group_config_id = #{joinGroupConfigId},</if>
      <if test="itemId != null">item_id = #{itemId},</if>
      <if test="prizeType != null">prize_type = #{prizeType},</if>
      <if test="expiredTime != null">expired_time = #{expiredTime},</if>
      <if test="groupNumber != null">group_number = #{groupNumber},</if>
      <if test="remaining != null">remaining = #{remaining},</if>
      <if test="facePrice != null">face_price = #{facePrice},</if>
      <if test="cheatRuleType != null">cheat_rule_type = #{cheatRuleType},</if>
      <if test="cheatPercent != null">cheat_percent = #{cheatPercent},</if>
      <if test="openLimitType != null">open_limit_type = #{openLimitType},</if>
      <if test="openLimit != null">open_limit = #{openLimit},</if>
      <if test="joinLimitType != null">join_limit_type = #{joinLimitType},</if>
      <if test="joinLimit != null">join_limit = #{joinLimit},</if>
      <if test="itemStatus != null">item_status = #{itemStatus},</if>
    </set>
    where id = #{id}
  </update>

  <update id="deleteById" parameterType="map">
    update tb_join_group_item
    set deleted = 1
    where id = #{id}
  </update>

  <update id="deleteByIds" parameterType="map">
    update tb_join_group_item
    set deleted = 1
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </update>

  <select id="selectByConfigId" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="BaseColumn"/>
    from tb_join_group_item
    where join_group_config_id = #{configId} and deleted = 0
  </select>

  <update id="updateToSubRemaining" parameterType="map">
    update tb_join_group_item
    set remaining = remaining - #{number}
    where id = #{joinGroupItemId}
    AND remaining > #{number} - 1
    AND deleted = 0
  </update>

  <update id="updateToAddRemaining" parameterType="long">
    update tb_join_group_item
    set remaining = remaining + #{number}
    where id = #{joinGroupItemId}
  </update>
  
  <update id="batchAddRemaining" parameterType="map">
    <foreach collection="itemList" item="item" index="index" separator=";">
      update tb_join_group_item
      set remaining = remaining + #{item.remaining}
      where id = #{item.id}
    </foreach>
  </update>
</mapper>