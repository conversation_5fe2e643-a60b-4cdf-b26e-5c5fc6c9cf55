package cn.com.duiba.activity.center;

import cn.com.duiba.Application;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * 所有数据库相关测试类(dao/service层)都应该继承此类, 每个测试方法执行后所有数据库操作都会自动回滚 可以在子类上加上注解@TransactionConfiguration(defaultRollback =
 * false)禁止自动回滚 或者在子类的某个测试方法上加上注解@Rollback(false)来禁止该方法的自动回滚 Created by wenqi.huang on 16/4/11.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(properties = {}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, classes = Application.class)
@ActiveProfiles({"dev","noRegister"})
// @Transactional
public abstract class TransactionalTestCaseBase { //extends AbstractTransactionalJUnit4SpringContextTests

}
