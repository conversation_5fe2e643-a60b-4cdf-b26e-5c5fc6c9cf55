package cn.com.duiba.activity.center.biz.dao.quizz;

import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.quizz.QuizzStockConsumeEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ)
public class DuibaQuizzStockConsumeDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaQuizzStockConsumeDao duibaQuizzStockConsumeDao;
	
	private QuizzStockConsumeEntity info;
	
	private String[] exceptFields = new String[] { "gmtCreate", "gmtModified" };
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(QuizzStockConsumeEntity.class);
		duibaQuizzStockConsumeDao.insert(info);
	}
	
	@Test
	public void findByBizIdTest(){
		QuizzStockConsumeEntity infoTest = duibaQuizzStockConsumeDao.findByBizId(info.getBizId(), info.getAction());
		TestUtils.assertEqualsReflect(infoTest, info, false, exceptFields);
	}
	
}
