package cn.com.duiba.activity.center.biz.service.creditgame;

import java.util.Date;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import cn.com.duiba.activity.center.biz.dao.creditgame.CreditGameDaoTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.creditgame.CreditGameSkinTemplateDao;
import cn.com.duiba.activity.center.biz.entity.creditgame.CreditGameSkinTemplateEntity;

/**
 * 积分游戏DAO单元测试
 * <AUTHOR>
 * @since 2016-09-05
 */
public class CreditGameSkinTemplateServiceTest extends CreditGameDaoTestCaseBase<CreditGameSkinTemplateEntity> {


    @Autowired
    private CreditGameSkinTemplateDao creditGameSkinTemplateDao;

    @Override
    protected CreditGameSkinTemplateEntity genEntity(){
        CreditGameSkinTemplateEntity entity=new CreditGameSkinTemplateEntity();
        entity.setCreditGameSkinImgUri("img uri");
        entity.setGmtModified(new Date());
        entity.setCreditGameType((byte)1);
        entity.setCreditGameDom("dom");
        entity.setCreditGameSkinTemplateName("皮肤模板名称");
        entity.setGmtCreate(new Date());
        entity.setIsShow(true);
        return entity;
    }
    @Test
    public void testInsert(){
       doTestInsert(creditGameSkinTemplateDao);
    }

    @Test
    public void testQueryById(){
        doTestQueryById(creditGameSkinTemplateDao);
    }

    /*
    @Test
    public void testQuery(){
        doTestQuery(creditGameSkinTemplateDao);
    }
*/
   /* @Test
    public void testUpdate(){
        doTestUpdate(creditGameSkinTemplateDao, new PreUpdateHandler<CreditGameSkinTemplateEntity>() {
            @Override
            public void preHandle(CreditGameSkinTemplateEntity creditGameSkinTemplateEntity) {
                creditGameSkinTemplateEntity.setGmtModified(new Date());
            }
        });
    }*/


    @Test
    public void testDelete(){
       doTestDelete(creditGameSkinTemplateDao);
    }
}
