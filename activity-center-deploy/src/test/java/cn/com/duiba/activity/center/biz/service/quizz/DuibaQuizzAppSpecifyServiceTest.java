package cn.com.duiba.activity.center.biz.service.quizz;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.api.dto.quizz.DuibaQuizzAppSpecifyDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.quizz.QuizzBaseTest;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/26.
 */
@Transactional(DsConstants.DATABASE_QUIZZ)
public class DuibaQuizzAppSpecifyServiceTest extends QuizzBaseTest {

    @Resource
    private DuibaQuizzAppSpecifyService duibaQuizzAppSpecifyService;

    private DuibaQuizzAppSpecifyDto     DuibaQuizzAppSpecifyDto;

    @Before
    public void testInsert() {
        DuibaQuizzAppSpecifyDto e = new DuibaQuizzAppSpecifyDto();
        TestUtils.setRandomAttributesForBean(e, false);
        duibaQuizzAppSpecifyService.insert(e);
        DuibaQuizzAppSpecifyDto = e;
    }

    @Test
    public void testFind() {
        DuibaQuizzAppSpecifyDto e = DuibaQuizzAppSpecifyDto;
        DuibaQuizzAppSpecifyDto e1 = duibaQuizzAppSpecifyService.find(e.getId());
        assertDO(e, e1);
    }

    @Test
    public void testDelete() {
        DuibaQuizzAppSpecifyDto e = DuibaQuizzAppSpecifyDto;
        duibaQuizzAppSpecifyService.delete(e.getId());
        Assert.assertNull(duibaQuizzAppSpecifyService.find(e.getId()));
    }

    @Test
    public void testFindByDuibaQuizzId() {
        DuibaQuizzAppSpecifyDto e = DuibaQuizzAppSpecifyDto;
        Assert.assertTrue(duibaQuizzAppSpecifyService.findByDuibaQuizzId(e.getDuibaQuizzId()).size() > 0);
    }

    @Test
    public void testFindByDuibaQuizzAndApp() {
        DuibaQuizzAppSpecifyDto e = DuibaQuizzAppSpecifyDto;
        DuibaQuizzAppSpecifyDto e1 = duibaQuizzAppSpecifyService.findByDuibaQuizzAndApp(e.getDuibaQuizzId(),
                                                                                        e.getAppId());
        assertDO(e, e1);
        duibaQuizzAppSpecifyService.delete(e.getId());
        e1 = duibaQuizzAppSpecifyService.findByDuibaQuizzAndApp(e.getDuibaQuizzId(),
                e.getAppId());
        Assert.assertNull(e1);
    }

    @Test
    public void testFindByDuibaQuizzsAndApp() {
        DuibaQuizzAppSpecifyDto e = DuibaQuizzAppSpecifyDto;
        Map<Long,DuibaQuizzAppSpecifyDto> map = duibaQuizzAppSpecifyService.findByDuibaQuizzsAndApp(Collections.singletonList(e.getDuibaQuizzId()),
                                                                                        e.getAppId());
        Assert.assertTrue(!map.isEmpty());
        duibaQuizzAppSpecifyService.delete(e.getId());
        map = duibaQuizzAppSpecifyService.findByDuibaQuizzsAndApp(Collections.singletonList(e.getDuibaQuizzId()),
                e.getAppId());
        Assert.assertTrue(map.isEmpty());
    }

    private void assertDO(DuibaQuizzAppSpecifyDto e, DuibaQuizzAppSpecifyDto e1) {
        assertDO(e, e1, null);
    }

    private void assertDO(DuibaQuizzAppSpecifyDto e, DuibaQuizzAppSpecifyDto e1, String[] exculdeFields) {
        List<String> exculdeFieldsList = new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[] { "gmtCreate", "gmtModified", "autoOffDate" }));
        if (exculdeFields != null && exculdeFields.length > 0) {
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1, false, exculdeFieldsList.toArray(new String[exculdeFieldsList.size() + 1]));// 时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr(e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr(e1.getGmtModified()));
    }

}
