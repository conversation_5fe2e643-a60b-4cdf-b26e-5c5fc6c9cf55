package cn.com.duiba.activity.center.api.remoteservice.freegroup;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.freegroup.FreeGroupItemDto;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.List;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/3/25 10:02
 * @description:
 */
@Rollback(value = false)
public class RemoteFreeGroupItemServiceTest extends TransactionalTestCaseBase {

    @Autowired
    RemoteFreeGroupItemService remoteFreeGroupItemService;

    @Test
    public void testGetByConfigId(){
	    List<FreeGroupItemDto> freeGroupItemDtoList = remoteFreeGroupItemService.getByConfigId(2L);
        System.out.println(JSON.toJSON(freeGroupItemDtoList));
        Assert.assertNotNull(freeGroupItemDtoList);
    }

    @Test
    public void testGetById(){
	    FreeGroupItemDto freeGroupItemDto = remoteFreeGroupItemService.getById(2L);
	    System.out.println(JSON.toJSON(freeGroupItemDto));
	    Assert.assertNotNull(freeGroupItemDto);
    }
}
