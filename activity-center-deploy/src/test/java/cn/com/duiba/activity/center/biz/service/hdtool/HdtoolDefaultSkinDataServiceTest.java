package cn.com.duiba.activity.center.biz.service.hdtool;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolSkinDefaultDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/7/6.
 */
@Transactional(DsConstants.DATABASE_CREDITS)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class HdtoolDefaultSkinDataServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private HdtoolSkinDefaultDataSerivce hdtoolDefaultSkinDataService;

    @Test
    public void testcreateHdtoolDefaultSkin() throws Exception {
        HdtoolSkinDefaultDto entity=new HdtoolSkinDefaultDto();
        TestUtils.setRandomAttributesForBean(entity,false);
        hdtoolDefaultSkinDataService.createHdtoolDefaultSkin(entity);
    }

    @Test
    public void testUpdateData() throws Exception {
        HdtoolSkinDefaultDto entity=new HdtoolSkinDefaultDto();
        TestUtils.setRandomAttributesForBean(entity,false);
        hdtoolDefaultSkinDataService.createHdtoolDefaultSkin(entity);
        entity.setHdtoolType("test");
        Assert.assertTrue(hdtoolDefaultSkinDataService.updateHdtoolDefaultSkin(entity)>0);
    }

    @Test
    public void testSelectBaseHdtoolData() throws Exception {
        HdtoolSkinDefaultDto entity=new HdtoolSkinDefaultDto();
        TestUtils.setRandomAttributesForBean(entity,false);
        hdtoolDefaultSkinDataService.createHdtoolDefaultSkin(entity);
        Assert.assertNotNull(hdtoolDefaultSkinDataService.queryHdtoolBaseSkin(entity.getTemplateType(), entity.getType()));
    }

    @Test
    public void testSelectConfigAndStyleData() throws Exception {
        HdtoolSkinDefaultDto entity=new HdtoolSkinDefaultDto();
        TestUtils.setRandomAttributesForBean(entity,false);
        hdtoolDefaultSkinDataService.createHdtoolDefaultSkin(entity);
        Assert.assertNotNull(hdtoolDefaultSkinDataService.queryHdtoolDefaultConfig(entity.getTemplateType(),entity.getType()));
    }

    @Test
    public void testSelectAllByPagination() throws Exception {
        HdtoolSkinDefaultDto entity=new HdtoolSkinDefaultDto();
        TestUtils.setRandomAttributesForBean(entity,false);
        hdtoolDefaultSkinDataService.createHdtoolDefaultSkin(entity);
        Assert.assertTrue(hdtoolDefaultSkinDataService.queryListPagnation(0,10).size()>0);
    }

    @Test
    public void testSelectAllByPaginationCount() throws Exception {
        HdtoolSkinDefaultDto entity=new HdtoolSkinDefaultDto();
        TestUtils.setRandomAttributesForBean(entity,false);
        hdtoolDefaultSkinDataService.createHdtoolDefaultSkin(entity);
        Assert.assertNotNull(hdtoolDefaultSkinDataService.queryListCount()>0);
    }
}
