package cn.com.duiba.activity.center.biz.dao.guess;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.guess.DuibaGuessStockManualChangeEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class DuibaGuessStockManualChangeDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaGuessStockManualChangeDao duibaGuessStockManualChangeDao;
	
	private DuibaGuessStockManualChangeEntity info;
	
	@Before
	public void addTest(){
		info = TestUtils.createRandomBean(DuibaGuessStockManualChangeEntity.class);
		info.setChangeKind(10);
		duibaGuessStockManualChangeDao.add(info);
	}
	
	@Test
	public void findByStockIdTest(){
		List<DuibaGuessStockManualChangeEntity> list = duibaGuessStockManualChangeDao.findByStockId(info.getGuessStockId());
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void addBatchTest(){
		DuibaGuessStockManualChangeEntity e1 = TestUtils.createRandomBean(DuibaGuessStockManualChangeEntity.class);
		e1.setChangeKind(10);
		DuibaGuessStockManualChangeEntity e2 = TestUtils.createRandomBean(DuibaGuessStockManualChangeEntity.class);
		e2.setChangeKind(10);
		List<DuibaGuessStockManualChangeEntity> list = new ArrayList<DuibaGuessStockManualChangeEntity>();
		list.add(e1);
		list.add(e2);
		duibaGuessStockManualChangeDao.addBatch(list);
		Assert.assertNotNull(e1.getId());
		Assert.assertNotNull(e2.getId());
	}
	
}
