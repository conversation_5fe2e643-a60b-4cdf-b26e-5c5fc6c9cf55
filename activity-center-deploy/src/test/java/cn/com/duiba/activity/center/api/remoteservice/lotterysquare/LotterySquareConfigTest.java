package cn.com.duiba.activity.center.api.remoteservice.lotterysquare;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.lotterysquare.LotterySquareBonusConfigDao;
import cn.com.duiba.activity.center.biz.dao.lotterysquare.LotterySquareConfigDao;
import cn.com.duiba.activity.center.biz.dao.lotterysquare.LotterySquareGradientRewardDao;
import cn.com.duiba.activity.center.biz.entity.lotterysquare.LotterySquareBonusConfigEntity;
import cn.com.duiba.activity.center.biz.entity.lotterysquare.LotterySquareConfigEntity;
import cn.com.duiba.activity.center.biz.entity.lotterysquare.LotterySquareGradientRewardEntity;
import cn.com.duiba.wolf.utils.TestUtils;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/12/5
 */
public class LotterySquareConfigTest extends TransactionalTestCaseBase {
    private static final Logger logger = LoggerFactory.getLogger(LotterySquareConfigTest.class);

    @Autowired
    private LotterySquareBonusConfigDao lotterySquareBonusConfigDao;
    @Autowired
    private LotterySquareConfigDao lotterySquareConfigDao;
    @Autowired
    private LotterySquareGradientRewardDao gradientRewardDao;
    @Autowired
    private RemoteLotterySquareConfigService remoteLotterySquareConfigService;
    @Autowired
    private RemoteLotterySquareRecordService remoteLotterySquareRecordService;

    @Test
    public void testConfigInsert(){
        LotterySquareConfigEntity lotterySquareConfigEntity =  TestUtils.createRandomBean(LotterySquareConfigEntity.class);
        lotterySquareConfigEntity.setActivityAppType(1);
        lotterySquareConfigEntity.setBonusType(1);
        lotterySquareConfigEntity.setShareLimitScope(1);
        lotterySquareConfigEntity.setShareBonusRequire(null);
        lotterySquareConfigDao.insert(lotterySquareConfigEntity);
    }

    @Test
    public void testBonusInsert(){
        LotterySquareBonusConfigEntity lotterySquareBonusConfigEntity = TestUtils.createRandomBean(LotterySquareBonusConfigEntity.class);
        lotterySquareBonusConfigEntity.setConsumerType(1);
        lotterySquareBonusConfigEntity.setBonusStyle(1);

        lotterySquareBonusConfigDao.batchInsert(Collections.singletonList(lotterySquareBonusConfigEntity));
    }

    @Test
    public void testRewardInsert(){
        LotterySquareGradientRewardEntity lotterySquareBonusConfigEntity = TestUtils.createRandomBean(LotterySquareGradientRewardEntity.class);
        lotterySquareBonusConfigEntity.setPrizeLevel(1);
        gradientRewardDao.batchInsert(Collections.singletonList(lotterySquareBonusConfigEntity));
    }

    @Test
    public void findTest() {
        LotterySquareConfigEntity configEntity = lotterySquareConfigDao.findById(1L);
        logger.info("configEntity:{}", JSON.toJSONString(configEntity));
        List<LotterySquareBonusConfigEntity> configEntities = lotterySquareBonusConfigDao.findByActivityId(1L);
        logger.info("configEntities:{}", JSON.toJSONString(configEntities));
        List<LotterySquareGradientRewardEntity> entities = gradientRewardDao.findByActivityId(1L);
        logger.info("LotterySquareGradientRewardEntity:{}", JSON.toJSONString(entities));
    }

    @Test
    public void updateTest() {
        LotterySquareBonusConfigEntity lotterySquareBonusConfigEntity = TestUtils.createRandomBean(LotterySquareBonusConfigEntity.class);
        lotterySquareBonusConfigEntity.setConsumerType(2);
        lotterySquareBonusConfigEntity.setBonusStyle(2);
        lotterySquareBonusConfigEntity.setId(1L);
        logger.info("bonusConfigUpdateResult:{}", lotterySquareBonusConfigDao.update(lotterySquareBonusConfigEntity));

        LotterySquareConfigEntity lotterySquareConfigEntity =  TestUtils.createRandomBean(LotterySquareConfigEntity.class);
        lotterySquareConfigEntity.setActivityAppType(2);
        lotterySquareConfigEntity.setId(1L);
        lotterySquareConfigEntity.setBonusType(1);
        lotterySquareConfigEntity.setShareLimitScope(1);
        lotterySquareConfigEntity.setShareBonusRequire(null);
        logger.info("configEntityUpdateResult:{}", lotterySquareConfigDao.update(lotterySquareConfigEntity));
    }

    @Test
    public void deleteTest() {
        logger.info("configEntities:{}", lotterySquareBonusConfigDao.delete(1L));
    }

    @Test
    public void findAutoOff() {
        logger.info("autoOffIds:{}", remoteLotterySquareConfigService.findAutoOff());
    }

    @Test
    public void batchUpdateRead() {
        List<Long> id = new ArrayList<>();
        id.add(2L);
        id.add(3L);
        logger.info("autoOffIds:{}",remoteLotterySquareRecordService.batchUpdateRead(id));
    }



}
