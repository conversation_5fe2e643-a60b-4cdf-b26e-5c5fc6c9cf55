package cn.com.duiba.activity.center.biz.dao.seconds_kill;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.activity.OperatingActivityDao;
import cn.com.duiba.activity.center.biz.entity.AddActivityEntity;
import cn.com.duiba.activity.center.biz.entity.activity.OperatingActivityEntity;
import cn.com.duiba.activity.center.biz.entity.seconds_kill.DuibaSecondsKillActivityEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * 
 * ClassName: DuibaSecondsKillActivityDaoTest <br/>
 * date: 2016年12月1日 下午8:04:31 <br/>
 *
 * @version 
 * @since JDK 1.6
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class DuibaSecondsKillActivityDaoTest extends TransactionalTestCaseBase {
	@Autowired
	private DuibaSecondsKillActivityDao duibaSecondsKillActivityDao;
	
	@Autowired
	private OperatingActivityDao operatingActivityDao;
	
	private DuibaSecondsKillActivityEntity info;
	
	private String[] exceptFields = new String[] { "gmtCreate", "gmtModified", "autoOffDate", "autoOnDate" ,"autoOnDate","tag"};
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaSecondsKillActivityEntity.class);
		duibaSecondsKillActivityDao.insert(info);
	}
	
	@Test
	public void findTest(){
		DuibaSecondsKillActivityEntity test = duibaSecondsKillActivityDao.find(info.getId());
		TestUtils.assertEqualsReflect(info, test, false, exceptFields);
	}

	@Test
	public void findAllDuibaSecondKillByAppIdTest(){
		DuibaSecondsKillActivityEntity t1 = TestUtils.createRandomBean(DuibaSecondsKillActivityEntity.class);
		t1.setDeleted(false);
		t1.setStatus(1);
		duibaSecondsKillActivityDao.insert(t1);
		OperatingActivityEntity t2 = TestUtils.createRandomBean(OperatingActivityEntity.class);
		t2.setActivityId(t1.getId());
		t2.setDeleted(false);
		t2.setType(30);
		operatingActivityDao.insert(t2);
		List<AddActivityEntity> list = duibaSecondsKillActivityDao.findAllDuibaSecondKillByAppId(t2.getAppId());
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void findAllByIdsTest(){
		List<Long> ids = new ArrayList<Long>();
		ids.add(info.getId());
		List<DuibaSecondsKillActivityEntity> list = duibaSecondsKillActivityDao.findAllByIds(ids);
		TestUtils.assertEqualsReflect(info, list.get(0), false, exceptFields);
	}

	@Test
	public void findAutoOffTest(){
		DuibaSecondsKillActivityEntity e = TestUtils.createRandomBean(DuibaSecondsKillActivityEntity.class);
		e.setDeleted(false);
		e.setStatus(1);
		Calendar calendar = Calendar.getInstance();
    	calendar.setTime(new Date());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	e.setAutoOffDate(calendar.getTime());
		duibaSecondsKillActivityDao.insert(e);
		List<DuibaSecondsKillActivityEntity> list = duibaSecondsKillActivityDao.findAutoOff();
    	Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void changeStatusTest(){
		DuibaSecondsKillActivityEntity e = TestUtils.createRandomBean(DuibaSecondsKillActivityEntity.class);
		duibaSecondsKillActivityDao.changeStatus(info.getId(), e.getStatus());
		int status = duibaSecondsKillActivityDao.find(info.getId()).getStatus();
		Assert.assertTrue(status == e.getStatus());
	}

	@Test
	public void updateAutoOffDateNullTest(){
		duibaSecondsKillActivityDao.updateAutoOffDateNull(info.getId());
		DuibaSecondsKillActivityEntity e = duibaSecondsKillActivityDao.find(info.getId());
		Assert.assertNull(e.getAutoOffDate());
		Assert.assertNull(e.getAutoOnDate());
	}

	@Test
	public void findByPageTest(){
		DuibaSecondsKillActivityEntity e = TestUtils.createRandomBean(DuibaSecondsKillActivityEntity.class);
		e.setDeleted(false);
		duibaSecondsKillActivityDao.insert(e);
		Map<String, Object> map = new HashMap<>();
		map.put("title", e.getTitle());
		map.put("id", e.getId());
		map.put("offset", 0);
		map.put("max", 10);
		List<DuibaSecondsKillActivityEntity> list = duibaSecondsKillActivityDao.findByPage(map);
		TestUtils.assertEqualsReflect(e, list.get(0), false, exceptFields);
	}

	@Test
	public void countTest(){
		DuibaSecondsKillActivityEntity e = TestUtils.createRandomBean(DuibaSecondsKillActivityEntity.class);
		e.setDeleted(false);
		duibaSecondsKillActivityDao.insert(e);
		Map<String, Object> map = new HashMap<>();
		map.put("title", e.getTitle());
		map.put("id", e.getId());
		int count = duibaSecondsKillActivityDao.count(map);
		Assert.assertTrue(count > 0);
	}

	@Test
	public void deleteByIdTest(){
		duibaSecondsKillActivityDao.deleteById(info.getId());
		DuibaSecondsKillActivityEntity e = duibaSecondsKillActivityDao.find(info.getId());
		Assert.assertTrue(e.getDeleted());
	}

	@Test
	public void updateSwitchesTest(){
		DuibaSecondsKillActivityEntity e = TestUtils.createRandomBean(DuibaSecondsKillActivityEntity.class);
		duibaSecondsKillActivityDao.updateSwitches(info.getId(), e.getSwitches());
		long switches = duibaSecondsKillActivityDao.find(info.getId()).getSwitches();
		Assert.assertTrue(switches == e.getSwitches());
	}

	@Test
	public void updateTest(){
		DuibaSecondsKillActivityEntity e = TestUtils.createRandomBean(DuibaSecondsKillActivityEntity.class);
		e.setId(info.getId());
		duibaSecondsKillActivityDao.update(e);
		DuibaSecondsKillActivityEntity test = duibaSecondsKillActivityDao.find(info.getId());
		TestUtils.assertEqualsReflect(e, test, false, exceptFields);
	}
}
