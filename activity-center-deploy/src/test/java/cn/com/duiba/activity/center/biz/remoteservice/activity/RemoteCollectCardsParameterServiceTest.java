package cn.com.duiba.activity.center.biz.remoteservice.activity;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteCollectCardsParameterService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by zzy on 2017/2/16.
 */
public class RemoteCollectCardsParameterServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteCollectCardsParameterService remoteCollectCardsParameterService;

    private long activityId = 1L;

    @Test
    public void testGetNeedNumber(){
        System.out.println(remoteCollectCardsParameterService.getNeedNumber(activityId));
    }

    @Test
    public void testSaveNeedNumber(){
        remoteCollectCardsParameterService.saveNeedNumber(activityId, 4);
        System.out.println("SAVE OK");
    }

    @Test
    public void testGetPercent(){
        System.out.println(remoteCollectCardsParameterService.getPercent(activityId));
    }

    @Test
    public void testSavePercent(){
        remoteCollectCardsParameterService.savePercent(activityId, 0.5);
        System.out.println("SAVE OK");
    }

    @Test
    public void testGetLimitMax(){
        System.out.println(remoteCollectCardsParameterService.getLimitMax(activityId));
    }

    @Test
    public void testSaveLimitMax(){
        remoteCollectCardsParameterService.saveLimitMax(activityId, 5);
        System.out.println("SAVE OK");
    }
}
