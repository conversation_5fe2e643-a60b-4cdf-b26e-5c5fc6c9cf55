package cn.com.duiba.activity.center.biz.dao.quizz;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.quizz.QuizzOrdersEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ_CONSUMER)
public class QuizzOrdersSimpleDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private QuizzOrdersSimpleDao quizzOrdersSimpleDao;
	
	@Autowired
	private QuizzOrdersSequenceDao quizzOrdersSequenceDao;
	
	private QuizzOrdersEntity info;
	
	@Before
	public void insertTest(){
		QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		quizzOrdersSimpleDao.insert(e, quizzOrdersSequenceDao.getId());
		info = e;
	}
	
	@Test
	public void countByConsumerIdAndOperatingActivityIdTest(){
		QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setStatus(1);
		quizzOrdersSimpleDao.insert(e, quizzOrdersSequenceDao.getId());
		int count = quizzOrdersSimpleDao.countByConsumerIdAndOperatingActivityId(e.getConsumerId(), e.getOperatingActivityId());
		Assert.assertTrue(count > 0);
	}

	@Test
    public void countByConsumerIdAndOperatingActivityIdAndDateTest() throws Exception{
		QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setStatus(1);
		quizzOrdersSimpleDao.insert(e, quizzOrdersSequenceDao.getId());
		Calendar calendar = Calendar.getInstance();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	Date start = calendar.getTime();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, 1);
    	Date end = calendar.getTime();
    	int count = quizzOrdersSimpleDao.countByConsumerIdAndOperatingActivityIdAndDate(e.getConsumerId(), e.getOperatingActivityId(), start, end);
    	Assert.assertTrue(count > 0);
    }
	
	@Test
    public void countFreeByConsumerIdAndOperatingActivityIdAndDateTest() throws Exception{
		QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setCredits(0L);
		quizzOrdersSimpleDao.insert(e, quizzOrdersSequenceDao.getId());
		Calendar calendar = Calendar.getInstance();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	Date start = calendar.getTime();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, 1);
    	Date end = calendar.getTime();
    	int count = quizzOrdersSimpleDao.countFreeByConsumerIdAndOperatingActivityIdAndDate(e.getConsumerId(), e.getOperatingActivityId(), start, end);
    	Assert.assertTrue(count > 0);
    }

	@Test
    public void countFreeByConsumerIdAndOperatingActivityIdTest(){
		QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setCredits(0L);
		quizzOrdersSimpleDao.insert(e, quizzOrdersSequenceDao.getId());
    	int count = quizzOrdersSimpleDao.countFreeByConsumerIdAndOperatingActivityId(e.getConsumerId(), e.getOperatingActivityId());
    	Assert.assertTrue(count > 0);
    }

	@Test
    public void findTest(){
		QuizzOrdersEntity test = quizzOrdersSimpleDao.find(info.getConsumerId(), info.getId());
		Assert.assertNotNull(test);
    }

	@Test
    public void countByConsumerIdAndPrizeIdTest(){
		int count = quizzOrdersSimpleDao.countByConsumerIdAndPrizeId(info.getConsumerId(), info.getOperatingActivityId(), info.getPrizeId());
		Assert.assertTrue(count > 0);
    }

	@Test
    public void findByIdsTest(){
    	List<Long> ids = new ArrayList<Long>();
    	ids.add(info.getId());
    	List<QuizzOrdersEntity> list = quizzOrdersSimpleDao.findByIds(info.getConsumerId(), ids);
    	Assert.assertTrue(list.size() > 0);
    }
}
