package cn.com.duiba.activity.center.biz.dao.game;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.game.DuibaQuestionStockManualChangeEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/2.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaQuestionStockManualChangeDaoTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaQuestionStockManualChangeDao duibaQuestionStockManualChangeDao;

    @Test
    public void testAddBatch() {
        DuibaQuestionStockManualChangeEntity duibaQuestionStockManualChangeDto =new DuibaQuestionStockManualChangeEntity();
        TestUtils.setRandomAttributesForBean(duibaQuestionStockManualChangeDto,false);
        List<DuibaQuestionStockManualChangeEntity> list=new ArrayList<>();
        list.add(duibaQuestionStockManualChangeDto);
        duibaQuestionStockManualChangeDao.addBatch(list);
    }

    @Test
    public void testAdd() {
        DuibaQuestionStockManualChangeEntity duibaQuestionStockManualChangeDto =new DuibaQuestionStockManualChangeEntity();
        TestUtils.setRandomAttributesForBean(duibaQuestionStockManualChangeDto,false);
        duibaQuestionStockManualChangeDao.add(duibaQuestionStockManualChangeDto);
    }

    @Test
    public void testFindByStockId() {
        DuibaQuestionStockManualChangeEntity duibaQuestionStockManualChangeDto =new DuibaQuestionStockManualChangeEntity();
        TestUtils.setRandomAttributesForBean(duibaQuestionStockManualChangeDto,false);
        duibaQuestionStockManualChangeDao.add(duibaQuestionStockManualChangeDto);
        Assert.assertNotNull(duibaQuestionStockManualChangeDao.findByStockId(duibaQuestionStockManualChangeDto.getQuestionStockId()));
    }

}
