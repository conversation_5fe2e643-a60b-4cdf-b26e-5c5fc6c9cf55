package cn.com.duiba.activity.center.api.remoteservice.stock;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.dcommons.domain.Tuple;
import cn.com.duiba.service.exception.BusinessException;
import com.alibaba.fastjson.JSON;
import org.javatuples.Pair;
import org.junit.Test;
import org.springframework.test.annotation.Rollback;

import javax.annotation.Resource;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/2/20 19:42
 * @description:
 */
@Rollback(value = false)
public class RemoteActStockChangeServiceTest extends TransactionalTestCaseBase {

	@Resource
	private RemoteActStockChangeService remoteActStockChangeService;

	@Test
	public void testAddActStockQuantity() throws BusinessException{
		Tuple.Tuple2<Integer, Integer> result = remoteActStockChangeService.addActStockQuantity(1157L, "plugin", 0L, 1L);
		System.out.println(JSON.toJSON(result));
		System.out.println(result._1());
		System.out.println(result._2());
	}

	@Test
	public void testAddActStockQuantityApi() throws BizException{
		Pair<Integer, Integer> result = remoteActStockChangeService.addActStockQuantityApi(1157L, "plugin", 0L, 1L);
		System.out.println(JSON.toJSON(result));
		System.out.println(result.getValue0());
		System.out.println(result.getValue1());
	}

	@Test
	public void testReduceActStockQuantity() throws BusinessException{
		Tuple.Tuple2<Integer, Integer> result = remoteActStockChangeService.reduceActStockQuantity(1157L, "plugin", 0L, 1L);
		System.out.println(JSON.toJSON(result));
		System.out.println(result._1());
		System.out.println(result._2());
	}

	@Test
	public void testReduceActStockQuantityApi() throws BizException{
		Pair<Integer, Integer> result = remoteActStockChangeService.reduceActStockQuantityApi(1157L, "plugin", 0L, 1L);
		System.out.println(JSON.toJSON(result));
		System.out.println(result.getValue0());
		System.out.println(result.getValue1());
	}
}
