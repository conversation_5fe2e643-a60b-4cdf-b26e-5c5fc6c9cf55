package cn.com.duiba.activity.center.biz.remoteservice.direct;

import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.direct.ActivityBlackList4DeveloperDto;
import cn.com.duiba.activity.center.api.remoteservice.direct.RemoteActivityBlackList4DeveloperService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.dubbo.DubboResult;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;

/**
 * Created by suyuanlong on 16/7/19.
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class RemoteActivityBlackList4DeveloperServiceImplTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteActivityBlackList4DeveloperService remoteActivityBlackList4DeveloperService;

    @Test
    public void testIsExistBlackByActivityIdAndActivityTypeAndDeveloperId() throws Exception {

    	DubboResult<Boolean> result = remoteActivityBlackList4DeveloperService.isExistBlackByActivityIdAndActivityTypeAndDeveloperId(57l, 41, 1l);

        Preconditions.checkArgument(true == result.isSuccess());


    }

    @Test
    public void testInsertBlack4Developer() throws Exception {

        ActivityBlackList4DeveloperDto dto = new ActivityBlackList4DeveloperDto();
        dto.setActivityType(41);
        dto.setDeveloperId(2l);
        dto.setActivityId(57l);
        DubboResult result = remoteActivityBlackList4DeveloperService.insertBlack4Developer(dto);
        Preconditions.checkArgument(true == result.isSuccess());


    }

    @Test
    public void testInsertBlackList4Developer() throws Exception {


        ActivityBlackList4DeveloperDto dto = new ActivityBlackList4DeveloperDto();
        dto.setActivityType(41);
        dto.setDeveloperId(2l);
        dto.setActivityId(57l);

        ActivityBlackList4DeveloperDto dto1 = new ActivityBlackList4DeveloperDto();
        dto.setActivityType(41);
        dto.setDeveloperId(3l);
        dto.setActivityId(57l);

        ActivityBlackList4DeveloperDto dto2 = new ActivityBlackList4DeveloperDto();
        dto.setActivityType(41);
        dto.setDeveloperId(4l);
        dto.setActivityId(57l);

        List list = Lists.newArrayList();
        list.add(dto);
        list.add(dto1);
        list.add(dto2);

        DubboResult result = remoteActivityBlackList4DeveloperService.insertBlackList4Developer(list);

        Preconditions.checkArgument(true == result.isSuccess());

        List<ActivityBlackList4DeveloperDto> ls = (List<ActivityBlackList4DeveloperDto>) result.getResult();
        Preconditions.checkArgument(ls.size() == 3);
    }

    @Test
    public void testDeleteBlackById() throws Exception {
        DubboResult result = remoteActivityBlackList4DeveloperService.deleteBlackById(100l);
        Preconditions.checkArgument(true == result.isSuccess());

    }

    @Test
    public void testFindByActivityIdAndActivityType() throws Exception {

        DubboResult result = remoteActivityBlackList4DeveloperService.findByActivityIdAndActivityType(57l, 41, 0, 100);
        Preconditions.checkArgument(true == result.isSuccess());

    }

    @Test
    public void testFindCountByActivityIdAndActivityType() throws Exception {

        DubboResult result = remoteActivityBlackList4DeveloperService.findCountByActivityIdAndActivityType(121l, 7);
        Preconditions.checkArgument(true == result.isSuccess());

        System.out.println("======"+result.getResult()+"======");

    }
    
    
    @Test
    public void testGetActivityBlackList() throws Exception {


        ActivityBlackList4DeveloperDto dto = new ActivityBlackList4DeveloperDto();
        dto.setActivityType(41);
        dto.setDeveloperId(2l);
        dto.setActivityId(57l);

        ActivityBlackList4DeveloperDto dto1 = new ActivityBlackList4DeveloperDto();
        dto1.setActivityType(41);
        dto1.setDeveloperId(3l);
        dto1.setActivityId(57l);

        ActivityBlackList4DeveloperDto dto2 = new ActivityBlackList4DeveloperDto();
        dto2.setActivityType(41);
        dto2.setDeveloperId(4l);
        dto2.setActivityId(57l);

        List<ActivityBlackList4DeveloperDto> list = Lists.newArrayList();
        list.add(dto);
        list.add(dto1);
        list.add(dto2);

        remoteActivityBlackList4DeveloperService.insertBlackList4Developer(list);
        
        
        DubboResult<List<Long>> result = remoteActivityBlackList4DeveloperService.getActivityBlackList(list);

        Preconditions.checkArgument(true == result.isSuccess());

        List<Long> ls = (List<Long>) result.getResult();
        Preconditions.checkArgument(ls.size() == 1);
    }
}