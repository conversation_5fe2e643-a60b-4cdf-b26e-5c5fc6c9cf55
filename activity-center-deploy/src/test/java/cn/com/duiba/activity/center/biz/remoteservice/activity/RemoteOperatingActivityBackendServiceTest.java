package cn.com.duiba.activity.center.biz.remoteservice.activity;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteOperatingActivityBackendService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by zzy on 2018/3/11.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class RemoteOperatingActivityBackendServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteOperatingActivityBackendService remoteOperatingActivityBackendService;

    @Test
    public void testFindByIds() {
        List<OperatingActivityDto> list = remoteOperatingActivityBackendService.findAllByIds(Lists.asList(100L, new Long[]{200L, 1000L, 1003L, 10001L}), false);
        Assert.assertNotNull(list);
    }
}
