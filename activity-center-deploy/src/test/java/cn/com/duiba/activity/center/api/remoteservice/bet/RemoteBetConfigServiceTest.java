package cn.com.duiba.activity.center.api.remoteservice.bet;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.bet.BetConfigDto;
import cn.com.duiba.activity.center.api.enums.BetBonusTypeEnum;
import cn.com.duiba.activity.center.api.enums.ConfigStatusEnum;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.common.util.DateUtil;
import cn.com.duiba.boot.exception.BizException;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @author: zheng<PERSON><PERSON><PERSON>
 * @date: 18/7/18 17:28
 * @description:
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class RemoteBetConfigServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteBetConfigService remoteBetConfigService;

    @Test
    public void testGetListByStatusAndEndTime(){
	    Date startTime = DateUtil.getDayDate(new Date());
	    Date endTime = DateUtil.getDayDate(DateUtil.daysAddOrSub(new Date(), 1));
	    List<BetConfigDto> betConfigDtoList = remoteBetConfigService.getListByStatusAndEndTime(
			    ConfigStatusEnum.OPEN.getCode(),
			    BetBonusTypeEnum.POCKET.getCode(),
			    startTime,
			    endTime);
	    System.out.println(JSON.toJSON(betConfigDtoList));
    }

	@Test
	public void testGetListByAppAndStatusAndEndTime(){
		Date startTime = DateUtil.getDayDate(new Date());
		Date endTime = DateUtil.getDayDate(DateUtil.daysAddOrSub(new Date(), 1));
		List<BetConfigDto> betConfigDtoList = remoteBetConfigService.getListByAppAndStatusAndEndTime(
				1L,
				ConfigStatusEnum.OPEN.getCode(),
				BetBonusTypeEnum.POCKET.getCode(),
				startTime,
				endTime);
		System.out.println(JSON.toJSON(betConfigDtoList));
	}


	@Test
	public void updateEndTime(){
		BetConfigDto betConfigDto = new BetConfigDto();
		betConfigDto.setId(413L);
		betConfigDto.setEndTime(new Date());
		try {
			remoteBetConfigService.updateEndTime(betConfigDto);
		} catch (BizException e) {
			e.printStackTrace();
		}
	}

}
