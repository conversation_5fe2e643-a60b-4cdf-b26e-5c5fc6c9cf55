package cn.com.duiba.activity.center.api.remoteservice.seedredpacket;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.seedredpacket.SeedRedPacketDto;
import cn.com.duiba.activity.center.api.dto.seedredpacket.SeedRedPacketGrownTimeDto;
import cn.com.duiba.activity.center.api.params.SeedRedPacketTaskParam;
import cn.com.duiba.activity.center.api.request.SeedRedPacketSaveRequest;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.api.bo.page.Page;
import cn.com.duiba.api.bo.page.PageQuery;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.wolf.utils.DateUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Created by hww on 2018/7/8 下午1:21.
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class RemoteBackendSeedRedPacketServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteBackendSeedRedPacketService remoteBackendSeedRedPacketService;

    /**
     * 新增
     */
    @Test
    @Rollback(false)
    public void testInsert() {
        long id = insert();
        Assert.assertTrue(id > 0);
    }

    private long insert() {
        SeedRedPacketDto config = new SeedRedPacketDto();
        long price = (long) (Math.random() * 100);
        config.setSeedCreditsPrice(price);
        config.setTitle("测试创建" + System.currentTimeMillis());
        config.setUnlockLandCreditsPrice(price);
        config.setStatus(SeedRedPacketDto.STATUS_CLOSE);
        List<SeedRedPacketGrownTimeDto> list = Lists.newArrayList();
        for (int i = 1; i < 9; i++) {
            list.add(generateGrownTime(i, i));
        }
        config.setGrownTimeList(list);
        try {
            return remoteBackendSeedRedPacketService.insert(config);
        } catch (BizException e) {
            System.out.println(e.getMessage());
            return 0;
        }
    }

    private SeedRedPacketGrownTimeDto generateGrownTime(long time, int id) {
        SeedRedPacketGrownTimeDto s = new SeedRedPacketGrownTimeDto();
        s.setGrownTime(time);
        s.setRedPacketId(id);
        return s;
    }

    /**
     * 更新
     */
    @Test
    @Rollback(false)
    public void testUpdateGeneral() {
        SeedRedPacketDto config = remoteBackendSeedRedPacketService.findById(insert());
        config.setTitle(config.getTitle() + 1);

        List<SeedRedPacketGrownTimeDto> list = config.getGrownTimeList();
        list.forEach(g -> g.setGrownTime(g.getGrownTime() + 1));
        remoteBackendSeedRedPacketService.updateGeneral(config);

        SeedRedPacketDto configAfterUpdate = remoteBackendSeedRedPacketService.findById(1L);
        Assert.assertTrue(Objects.equals(configAfterUpdate.getTitle(), config.getTitle()));
    }

    /**
     * 删除
     */
    @Test
    @Rollback(false)
    public void testDeleted() {
        long id = insert();
        SeedRedPacketDto config = remoteBackendSeedRedPacketService.findById(id);
        Assert.assertTrue(config != null);
        remoteBackendSeedRedPacketService.deleted(id);
        SeedRedPacketDto configAfterDelete = remoteBackendSeedRedPacketService.findById(id);
        Assert.assertTrue(configAfterDelete == null);
    }

    /**
     * 状态变更
     */
    @Test
    @Rollback(false)
    public void testUpdateStatus() {
        long id = insert();
        SeedRedPacketDto config = remoteBackendSeedRedPacketService.findById(id);
        Assert.assertTrue(config != null);
        int oldStatus = config.getStatus();
        int skip = remoteBackendSeedRedPacketService.updateStatus(id, 1 - config.getStatus());
        Assert.assertTrue(skip == 1);
        SeedRedPacketDto configAfterUpdate = remoteBackendSeedRedPacketService.findById(id);
        Assert.assertTrue(configAfterUpdate.getStatus() != oldStatus);
    }

    /**
     * 分页查询
     */
    @Test
    @Rollback(false)
    public void testFindByPage() {
        long id = insert();
        PageQuery query = new PageQuery();
        Page<SeedRedPacketDto> page = remoteBackendSeedRedPacketService.findByPageApi(query, null);
        Assert.assertTrue(page != null);
        List<SeedRedPacketDto> list = page.getList();
        Assert.assertTrue(CollectionUtils.isNotEmpty(list));
        SeedRedPacketDto first = list.get(0);
        Assert.assertTrue(Objects.equals(first.getId(), id));
    }

    @Test
    @Rollback(false)
    public void testSave() {
        SeedRedPacketSaveRequest request = new SeedRedPacketSaveRequest();
        request.setForInsert(true);
        request.setTitle("活动标题");
        request.setRule("活动规则");
        request.setAccountType(0);
        request.setPeriodId(1L);

        List<SeedRedPacketTaskParam> tasks = Lists.newArrayList();

        for (long i = 1; i < 9; i++) {
            SeedRedPacketTaskParam task = new SeedRedPacketTaskParam();
            task.setLandId(i);
            task.setTaskType(1);
            task.setTaskTarget(1);
            tasks.add(task);
        }
        request.setTasks(tasks);
        request.setCredits(1L);
        request.setPlantNumDaily(1L);
        request.setDrawNumDaily(1L);
        request.setBonusNumber(1);
        request.setBonusLimit(100);
        request.setUnBonusLimit(99);
        request.setAppId(1L);
        request.setPeriodId(1L);
        request.setStartTime(new Date());
        request.setEndTime(DateUtils.daysAddOrSub(new Date(), 1));
        List<SeedRedPacketGrownTimeDto> grownTimes = Lists.newArrayList();
        request.setGrownTimes(grownTimes);
        for (long i = 1; i < 9; i++) {
            SeedRedPacketGrownTimeDto temp = new SeedRedPacketGrownTimeDto();
            temp.setGrownTime(i * 10);
            temp.setRedPacketId((int)i);
            grownTimes.add(temp);
        }

        grownTimes.remove(1);
        grownTimes.remove(2);
        long id = remoteBackendSeedRedPacketService.save(request);
        request.setId(id);
        request.setForInsert(false);
        request.setTitle("活动标题2");
        long updateResult = remoteBackendSeedRedPacketService.save(request);
        SeedRedPacketDto s = remoteBackendSeedRedPacketService.findById(updateResult);
        Assert.assertTrue(Objects.equals(request.getTitle(), s.getTitle()));
    }

}
