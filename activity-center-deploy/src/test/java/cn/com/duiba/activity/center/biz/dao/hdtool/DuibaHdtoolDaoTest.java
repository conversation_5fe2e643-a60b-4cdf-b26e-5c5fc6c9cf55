package cn.com.duiba.activity.center.biz.dao.hdtool;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.dbroute.SwitchDBService;
import cn.com.duiba.activity.center.biz.entity.hdtool.DuibaHdtoolEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/24.
 */
@Transactional(DsConstants.DATABASE_HDTOOL_CONF)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaHdtoolDaoTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaHdtoolDao duibaHdtoolDao;
    @Resource
    private SwitchDBService switchDBService;

    private DuibaHdtoolEntity duibaHdtoolDto;

    @Before
    public void testInsert(){
        DuibaHdtoolEntity e=new DuibaHdtoolEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        duibaHdtoolDao.insert(e);
        duibaHdtoolDto =e;
        System.out.println("test");
    }

    @Test
    public void testFind() {
        DuibaHdtoolEntity e= duibaHdtoolDto;
        DuibaHdtoolEntity e1=duibaHdtoolDao.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindAutoOff() {
        DuibaHdtoolEntity e=new DuibaHdtoolEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-10000000l));
        duibaHdtoolDao.insert(e);
        Assert.assertTrue(duibaHdtoolDao.findAutoOff().size()>0);
    }

    @Test
    public void testFindDuibaHdToolsList() {
        DuibaHdtoolEntity e= duibaHdtoolDto;
        Map<String,Object> params=new HashMap<>();
        params.put("max",10);
        params.put("offset",0);
        Assert.assertTrue(duibaHdtoolDao.findDuibaHdToolsList(params).size()>0);
    }

    @Test
    public void testCountDuibaHdToolsList() {
        DuibaHdtoolEntity e= duibaHdtoolDto;
        Map<String,Object> params=new HashMap<>();
        Assert.assertTrue(duibaHdtoolDao.countDuibaHdToolsList(params)>0);
    }

    @Test
    public void testFindAllDuibaHdTools() {
        DuibaHdtoolEntity e=new DuibaHdtoolEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-10000000l));
        duibaHdtoolDao.insert(e);
        Assert.assertTrue(duibaHdtoolDao.findAllDuibaHdTools(1l).size()>0);
    }

    @Test
    public void testFindAllByIds() {
        DuibaHdtoolEntity e= duibaHdtoolDto;
        assertDO(e,duibaHdtoolDao.findAllByIds(Arrays.asList(e.getId())).get(0));
    }

    @Test
    public void testFindExtraInfoById() {
        DuibaHdtoolEntity e= duibaHdtoolDto;
        Assert.assertEquals(e.getFreeRule(),duibaHdtoolDao.findExtraInfoById(e.getId()).getFreeRule());
    }

    @Test
    public void testGetCountDuibaHdTool() {
        Map<String,Object> params=new HashMap<>();
        params.put("activityName", duibaHdtoolDto.getTitle());
        Assert.assertTrue(duibaHdtoolDao.getCountDuibaHdTool(params)>0);
    }

    @Test
    public void testFindDuibaToolList() {
        Map<String,Object> params=new HashMap<>();
        params.put("activityName", duibaHdtoolDto.getTitle());
        params.put("max",10);
        params.put("offset",0);
        Assert.assertTrue(duibaHdtoolDao.findDuibaToolList(params).size()>0);
    }

    @Test
    public void testUpdateAutoOffDateNull() {
        duibaHdtoolDao.updateAutoOffDateNull(duibaHdtoolDto.getId());
        Assert.assertNull(duibaHdtoolDao.find(duibaHdtoolDto.getId()).getAutoOffDate());
    }

    @Test
    public void testDeleteById() {
        duibaHdtoolDao.deleteById(duibaHdtoolDto.getId());
        Assert.assertTrue(duibaHdtoolDao.find(duibaHdtoolDto.getId()).getDeleted());
    }

    @Test
    public void testUpdate() {
        DuibaHdtoolEntity e=new DuibaHdtoolEntity(duibaHdtoolDto.getId());
        e.setTitle("haha");
        duibaHdtoolDao.update(e);
        Assert.assertTrue(duibaHdtoolDao.find(duibaHdtoolDto.getId()).getTitle().equals("haha"));
    }

    @Test
    public void testUpdateStatus() {
        duibaHdtoolDao.updateStatus(duibaHdtoolDto.getId(),42);
        Assert.assertEquals(duibaHdtoolDao.find(duibaHdtoolDto.getId()).getStatus(),(Integer) 42);
    }

    private void assertDO(DuibaHdtoolEntity e, DuibaHdtoolEntity e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaHdtoolEntity e, DuibaHdtoolEntity e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","tag","activityActionType"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

}
