package cn.com.duiba.activity.center.biz.service.cctv;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.cctv.TaskUnitDto;
import cn.com.duiba.activity.center.api.params.cctv.TaskQueryParam;
import cn.com.duiba.activity.center.api.remoteservice.cctv.RemoteTaskUnitService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

public class TaskUnitTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteTaskUnitService remoteTaskUnitService;

    @Test
    public void save() {
        TaskUnitDto dto = new TaskUnitDto();
        dto.setAppId(19441L);
        dto.setConsumerId(500458107L);
        dto.setTaskType("YSYY_TASK_1");
        dto.setTaskScope(1);
        dto.setScopeTag(20210520);
        JSONObject extra = new JSONObject();
        extra.put("min", 500);
        dto.setExtra(JSON.toJSONString(extra));
        remoteTaskUnitService.save(dto);
    }

    @Test
    public void findByTypes() {
        TaskQueryParam queryParam = new TaskQueryParam();
        queryParam.setAppId(47805L);
        queryParam.setConsumerId(500472080L);
        queryParam.setTypes(Arrays.asList("YSYY_TASK_1","YSYY_TASK_2"));
        queryParam.setTaskScope(1);
        queryParam.setScopeTag(20210520);
        List<TaskUnitDto> taskUnitDtos = remoteTaskUnitService.queryByTypes(queryParam);
        System.out.println(JSON.toJSONString(taskUnitDtos));
    }

    @Test
    public void del() {
        remoteTaskUnitService.batchDelete(Arrays.asList(1L,2L));
    }
}
