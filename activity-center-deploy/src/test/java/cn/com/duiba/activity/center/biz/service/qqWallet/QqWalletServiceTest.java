package cn.com.duiba.activity.center.biz.service.qqWallet;

import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Maps;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.qqwallet.QqWalletDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.service.qqwallet.QqWalletService;
import cn.com.duiba.wolf.utils.TestUtils;

/** 
 * ClassName:QqWalletServiceTest.java <br/>
 * <AUTHOR> 
 * @date 创建时间：2017年11月15日 下午12:07:50 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
@Transactional(DsConstants.DATABASE_DUIBA_CUSTOM)
public class QqWalletServiceTest extends TransactionalTestCaseBase {
	/**
	 * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
	 */
	protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_DUIBA_CUSTOM;
	@Autowired
	private QqWalletService qqWalletService;
	
	@Before
	public void testInsert() {
		QqWalletDto dto=new QqWalletDto();
		TestUtils.setRandomAttributesForBean(dto,false);
		dto.setItemId(101L);
		dto.setGoodsName("goodsName");
		dto.setOrderNum("orderNum1343");
		dto.setStatus(0);
		qqWalletService.insertQqWallet(dto);
	}

	@Test
	public void testfindByMap() {
		Map<String, Object> paramMap = Maps.newHashMap();
		paramMap.put("startDay", "2017-11-13 14:54:02");
		paramMap.put("endDay", "2017-11-15 14:54:02");
		List<QqWalletDto> list = qqWalletService.findByMap(paramMap);
		Assert.assertNotNull(list);
	}

	@Test
	public void testUpdateQqWallet() {
		QqWalletDto dto = new QqWalletDto();
		dto.setOrderNum("orderNum1343");
		dto.setStatus(1);
		dto.setBizData("顺风：123456789");
		qqWalletService.updateQqWallet(dto);
		QqWalletDto dot1 = qqWalletService.findByOrderNum("orderNum1343");
		Assert.assertNotNull(dot1);
	}
}
