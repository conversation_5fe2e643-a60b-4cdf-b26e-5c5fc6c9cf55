package cn.com.duiba.activity.center.biz.service.group;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.group.GroupActivityRecordDto;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 组团活动测试
 * @author: zhengwei
 * @date: 2018-08-11 21:05
 */
public class GroupActivityServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private GroupActivityRecordService groupActivityRecordService;

    @Test
    public void insert() {
        GroupActivityRecordDto dto = new GroupActivityRecordDto();
        dto.setAppId(1L);
        dto.setActivityId(0L);
        dto.setTeamId("souhu_1");
        dto.setPartnerId("souhu_1");
        dto.setParentId("souhu_0");
        dto.setConsumerId(1539361L);
        dto.setTag(1);
        groupActivityRecordService.insert(dto);
    }

    @Test
    public void select() {
        GroupActivityRecordDto dto = groupActivityRecordService.findByPartnerId(1L, 0L, "souhu_1");
    }

    @Test
    public void update() {
        GroupActivityRecordDto dto = new GroupActivityRecordDto();
        dto.setAppId(1L);
        dto.setActivityId(0L);
        dto.setTeamId("souhu_1");
        dto.setPartnerId("souhu_1");
        dto.setParentId("souhu_3");
        dto.setConsumerId(1539361L);
        dto.setTag(1);
    }

    @Test
    public void batchInsert() {
        List<GroupActivityRecordDto> data = getList(15);
        List<String> partnerIds = data.stream().map(e -> e.getPartnerId()).collect(Collectors.toList());
        List<String> existIds = groupActivityRecordService
                .findByPartnerIds(data.get(0).getAppId(), data.get(0).getActivityId(), partnerIds);
        groupActivityRecordService.batchInsert(data.stream().filter(e -> !existIds.contains(e.getPartnerId())).collect(Collectors.toList()));
    }

    List<GroupActivityRecordDto> getList(int size) {
        List<GroupActivityRecordDto> list = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            GroupActivityRecordDto groupActivityRecordDto = new GroupActivityRecordDto();
            groupActivityRecordDto.setAppId(2L);
            groupActivityRecordDto.setActivityId(1L);
            groupActivityRecordDto.setTeamId("souhu_0");
            groupActivityRecordDto.setConsumerId(123L);
            groupActivityRecordDto.setPartnerId("souhu_" + i);
            groupActivityRecordDto.setApprenticeNum(5L);
            groupActivityRecordDto.setValidApprenticeNum(3L);
            groupActivityRecordDto.setTag(0);
            list.add(groupActivityRecordDto);
        }
        return list;
    }
}
