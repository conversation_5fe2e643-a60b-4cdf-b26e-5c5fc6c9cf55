package cn.com.duiba.activity.center.biz.service.hdtool;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolStockConsumeDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/25.
 */
@Transactional(DsConstants.DATABASE_CREDITS)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class HdtoolStockConsumeServiceTest extends TransactionalTestCaseBase {

    @Resource
    private HdtoolStockConsumeService hdtoolStockConsumeService;

    @Test
    public void testInsert() {
        HdtoolStockConsumeDto hdtoolStockConsumeDto =new HdtoolStockConsumeDto(true);
        TestUtils.setRandomAttributesForBean(hdtoolStockConsumeDto,false);
        hdtoolStockConsumeService.insert(hdtoolStockConsumeDto);
    }

    @Test
    public void testfindByBizIdAndSourcePay() {
        HdtoolStockConsumeDto hdtoolStockConsumeDto =new HdtoolStockConsumeDto(true);
        TestUtils.setRandomAttributesForBean(hdtoolStockConsumeDto,false);
        hdtoolStockConsumeDto.setAction("pay");
        hdtoolStockConsumeService.insert(hdtoolStockConsumeDto);
        Assert.assertNotNull(hdtoolStockConsumeService.findByBizIdAndSourcePay(hdtoolStockConsumeDto.getBizId(), hdtoolStockConsumeDto.getBizSource()));
    }

}
