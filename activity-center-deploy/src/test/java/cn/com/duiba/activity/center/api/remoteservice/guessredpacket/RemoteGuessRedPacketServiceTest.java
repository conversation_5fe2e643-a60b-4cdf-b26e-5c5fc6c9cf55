package cn.com.duiba.activity.center.api.remoteservice.guessredpacket;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.guessredpacket.GuessRedPacketDto;
import cn.com.duiba.activity.center.api.params.GuessRedPacketParams;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @description: 猜红包测试类
 * @author: Zhengwei
 * @date: 2018-09-11 13:48
 */
public class RemoteGuessRedPacketServiceTest extends TransactionalTestCaseBase {
    @Autowired
    RemoteGuessRedPacketService remoteGuessRedPacketService;

    @Test
    public void select() {
        GuessRedPacketParams params = new GuessRedPacketParams();
        params.setAppId(1L);
        params.setActivityId(81784L);
        params.setConsumerId(1539361L);
        List<GuessRedPacketDto> result = remoteGuessRedPacketService.findRedPackets(params);
        System.out.print(JSON.toJSONString(result));
    }

    @Test
    @Rollback(false)
    public void insert() {
        GuessRedPacketDto dto = new GuessRedPacketDto();
        dto.setAppId(1L);
        dto.setActivityId(81784L);
        dto.setConsumerId(1539361L);
        dto.setPartnerUserId("doumi01");
        dto.setOwnerConsumerId(1539361L);
        dto.setAmount(360L);
        dto.setRedPacketId(710802427060289L);
        remoteGuessRedPacketService.addRedPacket(dto);
    }
}
