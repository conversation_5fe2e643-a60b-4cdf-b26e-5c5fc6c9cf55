package cn.com.duiba.activity.center.biz.dao.guess;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.guess.GuessStockConsumeEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class GuessStockConsumeDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private GuessStockConsumeDao guessStockConsumeDao;
	
	private GuessStockConsumeEntity info;
	
	@Before
    public void insertTest(){
		info = TestUtils.createRandomBean(GuessStockConsumeEntity.class);
		info.setBizId("10");
		guessStockConsumeDao.insert(info);
    }
    
	@Test
    public void findByBizIdTest(){
		GuessStockConsumeEntity e = guessStockConsumeDao.findByBizId(info.getGuessStockId(), info.getBizId(), info.getAction());
		Assert.assertNotNull(e);
    }

}
