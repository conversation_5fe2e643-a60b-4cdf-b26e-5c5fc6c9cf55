package cn.com.duiba.activity.center.biz.dao.shuqi;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.shuqipk.PkTeamMemberRecordDao;
import cn.com.duiba.activity.center.biz.entity.shuqipk.PkTeamMemberRecordEntity;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.wolf.utils.DateUtils;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/1/6 23:21
 * @description:
 */
@Transactional(DsConstants.DATABASE_CUSTOM_RECORD)
@Rollback(value = false)
public class PkTeamMemberRecordDaoTest extends TransactionalTestCaseBase {

    @Autowired
    private PkTeamMemberRecordDao pkTeamMemberRecordDao;

    @Test
    public void testInsert() throws BizException{
        PkTeamMemberRecordEntity teamMemberRecord = new PkTeamMemberRecordEntity();
        teamMemberRecord.setAppId(1L);
        teamMemberRecord.setActivityId(12431L);
        teamMemberRecord.setConsumerId(3L);
        teamMemberRecord.setTeamRecordId(1000130000003L);
        teamMemberRecord.setRecordDate(getRecordDate(DateUtils.daysAddOrSub(new Date(), -2)));

        Long result = pkTeamMemberRecordDao.insert(teamMemberRecord);
        System.out.println(result);
        Assert.assertTrue(ObjectUtils.compare(result, 0L) > 0);
    }

    private Long getRecordDate(Date date){
        return DateUtils.getDayStartTime(date).getTime()/1000;
    }

    @Test
    public void testInsertBatch() throws BizException{
        List<PkTeamMemberRecordEntity> entityList = new ArrayList<>();
        PkTeamMemberRecordEntity teamMemberRecord1 = new PkTeamMemberRecordEntity();
        teamMemberRecord1.setAppId(1L);
        teamMemberRecord1.setActivityId(12431L);
        teamMemberRecord1.setConsumerId(3L);
        teamMemberRecord1.setTeamRecordId(1000130000003L);
        teamMemberRecord1.setRecordDate(getRecordDate(DateUtils.daysAddOrSub(new Date(), -3)));
        entityList.add(teamMemberRecord1);

        PkTeamMemberRecordEntity teamMemberRecord2 = new PkTeamMemberRecordEntity();
        teamMemberRecord2.setAppId(1L);
        teamMemberRecord2.setActivityId(12431L);
        teamMemberRecord2.setConsumerId(4L);
        teamMemberRecord2.setTeamRecordId(1000130010004L);
        teamMemberRecord2.setRecordDate(getRecordDate(DateUtils.daysAddOrSub(new Date(), -3)));
        entityList.add(teamMemberRecord2);

        int result = pkTeamMemberRecordDao.insertBatch(entityList);
        System.out.println(result);
        Assert.assertTrue(result > 0);
    }

    @Test
    public void testUpdatePraiseCount(){
        int result = pkTeamMemberRecordDao.updatePraiseCount(1000000000003L,1L);
        System.out.println(result);
        Assert.assertTrue(result > 0);
    }

    @Test
    public void testUpdateOrderNum(){
        PkTeamMemberRecordEntity teamMemberRecord = new PkTeamMemberRecordEntity();
        teamMemberRecord.setId(1000000000003L);
        teamMemberRecord.setOrderNum("45435345353");

        int result = pkTeamMemberRecordDao.updateOrderNum(teamMemberRecord);
        System.out.println(result);
        Assert.assertTrue(result > 0);
    }

    @Test
    public void testUpdateBatchReadTime(){
        List<PkTeamMemberRecordEntity> entityList = new ArrayList<>();
        PkTeamMemberRecordEntity teamMemberRecord1 = new PkTeamMemberRecordEntity();
        teamMemberRecord1.setId(1000010000003L);
        teamMemberRecord1.setReadTime(345L);
        entityList.add(teamMemberRecord1);

        PkTeamMemberRecordEntity teamMemberRecord2 = new PkTeamMemberRecordEntity();
        teamMemberRecord2.setId(1000010010004L);
        teamMemberRecord2.setReadTime(654L);
        entityList.add(teamMemberRecord2);

        int result = pkTeamMemberRecordDao.updateBatchReadTime(entityList);
        System.out.println(result);
        Assert.assertTrue(result > 0);
    }

    @Test
    public void testSelectListByteamMemberRecordIds(){
        List<PkTeamMemberRecordEntity> PkTeamMemberRecordEntityList = pkTeamMemberRecordDao.selectListByTeamRecordIds(Arrays.asList(1000130000003L, 1000130010004L), null);
        System.out.println(JSON.toJSON(PkTeamMemberRecordEntityList));
        Assert.assertNotNull(PkTeamMemberRecordEntityList);
    }
}
