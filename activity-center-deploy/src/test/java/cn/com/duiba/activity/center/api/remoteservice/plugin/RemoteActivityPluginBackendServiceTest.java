package cn.com.duiba.activity.center.api.remoteservice.plugin;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.plugin.ActivityPluginDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.utils.TestUtils;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by yansen on 17/4/25.
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class RemoteActivityPluginBackendServiceTest extends TransactionalTestCaseBase {

    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS_ACTIVITY;
    @Autowired
    private RemoteActivityPluginBackendService remoteActivityPluginBackendService;

    @Test
    public void testFindPluginsByIds() throws Exception {
        ActivityPluginDto activityPluginDto1= TestUtils.createRandomBean(ActivityPluginDto.class);
        activityPluginDto1.setStatus(0);
        activityPluginDto1.setSignType(1);
        activityPluginDto1.setSubType("a");
        activityPluginDto1.setTriggerTpye(1);
        activityPluginDto1.setLimitScope(1);
        ActivityPluginDto activityPluginDto2=TestUtils.createRandomBean(ActivityPluginDto.class);
        activityPluginDto2.setStatus(0);
        activityPluginDto2.setSignType(1);
        activityPluginDto2.setSubType("a");
        activityPluginDto2.setTriggerTpye(1);
        activityPluginDto2.setLimitScope(1);
        activityPluginDto1.setExitDays(1);
        activityPluginDto2.setExitDays(1);

        activityPluginDto2=remoteActivityPluginBackendService.createActivityPluginInfo(activityPluginDto2).getResult();
        activityPluginDto1=remoteActivityPluginBackendService.createActivityPluginInfo(activityPluginDto1).getResult();

        List<Long> ids=new ArrayList<>();
        ids.add(activityPluginDto1.getId());
        ids.add(activityPluginDto2.getId());
        DubboResult result=remoteActivityPluginBackendService.findAndSortByIds(ids,null);
        System.out.println(result.getResult());
    }

    @Test
    public void testAutoOnPlugin() {
        DubboResult<?> dubboResult = remoteActivityPluginBackendService.autoOnPlugin();
        Assert.assertTrue(dubboResult.isSuccess());
    }


    @Test
    public void testFindAllOpenPlugin() {
        ActivityPluginDto activityPluginDto1= TestUtils.createRandomBean(ActivityPluginDto.class);
        activityPluginDto1.setStatus(1);
        activityPluginDto1.setSignType(1);
        activityPluginDto1.setSubType(ActivityPluginDto.PLUGIN);
        activityPluginDto1.setTriggerTpye(1);
        activityPluginDto1.setLimitScope(1);
        activityPluginDto1.setExitDays(1);
        activityPluginDto1.setDeleted(0);

        ActivityPluginDto plugin = remoteActivityPluginBackendService.createActivityPluginInfo(activityPluginDto1).getResult();
        List<ActivityPluginDto> list = remoteActivityPluginBackendService.findAllOpenPlugin();

        Assert.assertTrue(list.size() > 0);
        Assert.assertTrue(Lists.transform(list, ActivityPluginDto::getId).contains(plugin.getId()));
    }

}

