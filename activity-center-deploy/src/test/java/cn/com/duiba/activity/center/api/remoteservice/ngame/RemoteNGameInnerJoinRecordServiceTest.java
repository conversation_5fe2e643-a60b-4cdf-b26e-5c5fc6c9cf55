package cn.com.duiba.activity.center.api.remoteservice.ngame;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.ngame.NGameInnerJoinRecordDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * Created by hww on 2018/1/24 下午4:17.
 */
@Transactional(DsConstants.DATABASE_NGAME)
public class RemoteNGameInnerJoinRecordServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteNGameInnerJoinRecordService remoteNGameInnerJoinRecordService;

    @Test
    public void testRemoteNGameInnerJoinRecordService() {
        NGameInnerJoinRecordDto record = TestUtils.createRandomBean(NGameInnerJoinRecordDto.class);
        record.setId(null);
        //数据库字段类型为tinyint，防止随机字段过大
        record.setRecordStatus(1);
        //数据库字段类型为tinyint，防止随机字段过大
        record.setDeleted(1);
        Long id = remoteNGameInnerJoinRecordService.insert(record);
        Assert.assertTrue(id != null && id > 0);
    }

    @Test
    public void testFindByOrderIdAndConsumerId() {
        NGameInnerJoinRecordDto record = TestUtils.createRandomBean(NGameInnerJoinRecordDto.class);
        record.setId(null);
        //数据库字段类型为tinyint，防止随机字段过大
        record.setRecordStatus(1);
        //数据库字段类型为tinyint，防止随机字段过大
        record.setDeleted(1);
        Long id = remoteNGameInnerJoinRecordService.insert(record);
        NGameInnerJoinRecordDto recordFromDB = remoteNGameInnerJoinRecordService.findByOrderIdAndConsumerId(record.getOrderId(), record.getConsumerId());
        Assert.assertTrue(Objects.equals(id, recordFromDB.getId()));
    }

    @Test
    public void testGeneralUpdate() {
        NGameInnerJoinRecordDto record = TestUtils.createRandomBean(NGameInnerJoinRecordDto.class);
        record.setId(null);
        //数据库字段类型为tinyint，防止随机字段过大
        record.setRecordStatus(1);
        //数据库字段类型为tinyint，防止随机字段过大
        record.setDeleted(1);
        //初始化修改次数
        record.setTimes(1);
        Long id = remoteNGameInnerJoinRecordService.insert(record);

        NGameInnerJoinRecordDto dto4Update = new NGameInnerJoinRecordDto();
        dto4Update.setId(id);
        dto4Update.setTimes(record.getTimes());

        dto4Update.setScore(record.getScore() + 1);
        dto4Update.setDynamicData(record.getDynamicData() + "a");
        int skip = remoteNGameInnerJoinRecordService.generalUpdate(dto4Update);

        NGameInnerJoinRecordDto recordFromDB = remoteNGameInnerJoinRecordService.findByOrderIdAndConsumerId(record.getOrderId(), record.getConsumerId());
        Assert.assertTrue(Objects.equals(skip, 1));
        Assert.assertTrue(Objects.equals(dto4Update.getScore(), recordFromDB.getScore()));
        Assert.assertTrue(Objects.equals(dto4Update.getDynamicData(), recordFromDB.getDynamicData()));
        Assert.assertTrue(Objects.equals(dto4Update.getTimes() + 1, recordFromDB.getTimes()));
    }

}
