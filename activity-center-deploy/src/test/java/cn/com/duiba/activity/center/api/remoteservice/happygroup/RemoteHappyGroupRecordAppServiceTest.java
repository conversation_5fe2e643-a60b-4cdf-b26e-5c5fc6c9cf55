package cn.com.duiba.activity.center.api.remoteservice.happygroup;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupBaseConfigDto;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupConfigDto;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupRecordDto;
import cn.com.duiba.activity.center.api.enums.HappyGroupRecordStatusEnum;
import cn.com.duiba.activity.center.api.enums.JoinGroupStatusEnum;
import cn.com.duiba.activity.center.api.enums.YesOrNoEnum;
import cn.com.duiba.activity.center.api.params.HappyGroupRecordPageParam;
import cn.com.duiba.boot.exception.BizException;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @author: zhengjianhao
 * @date: 2019/3/5 11:41
 * @description:
 */
@Rollback(value = false)
public class RemoteHappyGroupRecordAppServiceTest extends TransactionalTestCaseBase {

    @Autowired
    RemoteHappyGroupRecordAppService remoteHappyGroupRecordAppService;

    @Test
    public void testAdd() {
	    HappyGroupRecordDto happyGroupRecordDto = new HappyGroupRecordDto();
	    happyGroupRecordDto.setAppId(1L);
	    happyGroupRecordDto.setGroupInfoId(1L);
	    happyGroupRecordDto.setGroupItemId(1L);
	    happyGroupRecordDto.setActivityConfigId(1L);
	    happyGroupRecordDto.setConsumerId(5L);
	    happyGroupRecordDto.setEndTime(new Date());
	    happyGroupRecordDto.setOwnerType(YesOrNoEnum.NO.getCode());
		int result = remoteHappyGroupRecordAppService.add(happyGroupRecordDto);
	    System.out.println(result);
	    Assert.assertTrue(result > 0);
    }

    @Test
    public void testModifyStatusById() {
    	int result = remoteHappyGroupRecordAppService.modifyStatusById(1L, HappyGroupRecordStatusEnum.EXCHANGE_SUCCESS.getCode(), "111");
	    System.out.println(result);
	    Assert.assertTrue(result > 0);
    }

	@Test
	public void testGetOwnerByGroupId() {
		Long ownerId = remoteHappyGroupRecordAppService.getOwnerByGroupId(1L);
		System.out.println(ownerId);
		Assert.assertNotNull(ownerId);
	}

	@Test
	public void testGetMembersByGroupId() {
		List<Long> memberIds = remoteHappyGroupRecordAppService.getMembersByGroupId(1L, 3);
		System.out.println(memberIds);
		Assert.assertNotNull(memberIds);
	}

	@Test
	public void testGetExchangeWaitingList() {
		List<HappyGroupRecordDto> happyGroupRecordDtoList = remoteHappyGroupRecordAppService.getExchangeWaitingList(1L);
		System.out.println(JSON.toJSON(happyGroupRecordDtoList));
		Assert.assertTrue(happyGroupRecordDtoList.size() > 0);
	}

	@Test
	public void testGetCountByConfigAndCIdAndType() throws BizException {
		Integer result = remoteHappyGroupRecordAppService.getCountByConfigAndCIdAndType(1L, 1L, YesOrNoEnum.YES.getCode(), null);
		System.out.println(result);
		Assert.assertTrue(result > 0);
	}

	@Test
	public void testGetCountByItemAndCIdAndType() throws BizException {
		Integer result = remoteHappyGroupRecordAppService.getCountByItemAndCIdAndType(1L, 1L, YesOrNoEnum.YES.getCode(), null);
		System.out.println(result);
		Assert.assertTrue(result > 0);
	}

	@Test
	public void testCheckUnderWayGroupByItem() throws BizException {
		Boolean result = remoteHappyGroupRecordAppService.checkUnderWayGroupByItem(1L, 1L);
		System.out.println(result);
		Assert.assertTrue(result);
	}

	@Test
	public void testGetUnderWayGroupByItems() throws BizException {
		List<HappyGroupRecordDto> happyGroupRecordDtoList = remoteHappyGroupRecordAppService.getUnderWayGroupByItems(Arrays.asList(1L, 2L), 1L);
		System.out.println(JSON.toJSON(happyGroupRecordDtoList));
		Assert.assertTrue(happyGroupRecordDtoList.size()>0);
	}

	@Test
	public void testGetByGroupIdAndConsumerId()  throws BizException{
		HappyGroupRecordDto happyGroupRecordDto = remoteHappyGroupRecordAppService.getByGroupIdAndConsumerId(1L, 1L);
		System.out.println(JSON.toJSON(happyGroupRecordDto));
		Assert.assertNotNull(happyGroupRecordDto);
	}

	@Test
	public void testGetPage() {
		HappyGroupRecordPageParam happyGroupRecordPageParam = new HappyGroupRecordPageParam();
		happyGroupRecordPageParam.setConsumerId(1L);
		happyGroupRecordPageParam.setGroupConfigId(1L);
		happyGroupRecordPageParam.setRecordStatus(JoinGroupStatusEnum.FAILURE.getCode());
		List<HappyGroupRecordDto> happyGroupRecordDtoList = remoteHappyGroupRecordAppService.getPage(happyGroupRecordPageParam);
		System.out.println(JSON.toJSON(happyGroupRecordDtoList));
		Assert.assertTrue(happyGroupRecordDtoList.size() == 2);
	}

	@Test
	public void testGetCountByGroupId(){
    	Integer count = remoteHappyGroupRecordAppService.getCountByGroupId(18L);
		System.out.println(count);
		Assert.assertTrue(count > 0);
	}
}
