package cn.com.duiba.activity.center.biz.service.activity_brick;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity_brick.PopupActivityBrickDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

/** 
 * ClassName:PopupActivityBrickServiceTest.java <br/>
 * <AUTHOR> 
 * @date 创建时间：2016年10月12日 下午5:10:40 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class PopupActivityBrickServiceTest extends TransactionalTestCaseBase {

	/**
	 * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
	 */
	protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_ACT_COM_CONF;
	@Resource
	private PopupActivityBrickService popupActivityBrickService;
	
	private PopupActivityBrickDto dto;
	
	@Before
	public void testInsert(){
		PopupActivityBrickDto popupActivityBrickDto =new PopupActivityBrickDto();
		TestUtils.setRandomAttributesForBean(popupActivityBrickDto, false);
		popupActivityBrickDto.setStatus(1);
		popupActivityBrickDto.setType(1);
		dto =popupActivityBrickService.insert(popupActivityBrickDto);
	}
	
	@Test
	public void testdeleteById(){
		popupActivityBrickService.deleteById(dto.getId());
		PopupActivityBrickDto ent=popupActivityBrickService.findById(dto.getId());
		Assert.assertNotNull(ent);
	}
	
	@Test
	public void testshowBrick(){
		popupActivityBrickService.showBrick(dto.getId());
		PopupActivityBrickDto ent=popupActivityBrickService.findById(dto.getId());
		Assert.assertEquals(new Integer(1),ent.getStatus());
	}
	
	@Test
	public void testhiddenBrick(){
		popupActivityBrickService.hiddenBrick(dto.getId());
		PopupActivityBrickDto ent=popupActivityBrickService.findById(dto.getId());
		Assert.assertEquals(new Integer(0),ent.getStatus());
	}
	
	@Test
	public void testfindPage(){
		Map<String, Object> paramMap =new HashMap<>();
		paramMap.put("offset", 0);
		paramMap.put("max", 10);
		List<PopupActivityBrickDto> list=popupActivityBrickService.findPage(paramMap);
		Assert.assertNotNull(list);
	}
	
	@Test
	public void testfindPageCount(){
		Map<String, Object> paramMap =new HashMap<>();
		paramMap.put("type", 1);
		Long count =popupActivityBrickService.findPageCount(paramMap);
		Assert.assertTrue(count > 0L);
	}
	
	@Test
	public void testUpdate(){
		PopupActivityBrickDto popupActivityBrickDto =new PopupActivityBrickDto();
		popupActivityBrickDto.setName("name");
		popupActivityBrickDto.setId(dto.getId());
		popupActivityBrickService.update(popupActivityBrickDto);
		PopupActivityBrickDto ent=popupActivityBrickService.findById(dto.getId());
		Assert.assertEquals("name", ent.getName());
	}
}
