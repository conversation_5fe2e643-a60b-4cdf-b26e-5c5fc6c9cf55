package cn.com.duiba.activity.center.api.remoteservice.guessredpacket;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.guessredpacket.RedPacketGuessRecordDto;
import cn.com.duiba.activity.center.api.enums.GuessRedPackAwardStatusEnum;
import com.alibaba.fastjson.JSONObject;
import org.junit.Assert;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/3/27 11:30
 */
public class RemoteRedPacketGuessRecordServiceTest extends TransactionalTestCaseBase {
    private static final Logger logger = LoggerFactory.getLogger(RemoteRedPacketGuessRecordServiceTest.class);
    @Autowired
    RemoteRedPacketGuessRecordService remoteRedPacketGuessRecordService;

    @Test
    @Rollback(false)
    public void insert() {
        RedPacketGuessRecordDto dto = new RedPacketGuessRecordDto();
        dto.setAppId(1L);
        dto.setActivityId(81784L);
        dto.setConsumerId(1539361L);
        dto.setOwnerConsumerId(1539361L);
        dto.setAmount(360L);
        dto.setRedPacketId(710802427060289L);
        dto.setGuessResult(0);
        Long id = remoteRedPacketGuessRecordService.insert(dto);
        System.out.println(id);
    }

    @Test
    public void getConsumerPacket() {
        Long redPacketId = 950158247746960994L;
        Long consumerId = 500311013L;
        Long addMoney = 400L;
        List<RedPacketGuessRecordDto> guessRecordDtos = remoteRedPacketGuessRecordService.selectByPacketId(redPacketId);
        logger.info("guess record before update,{}", JSONObject.toJSONString(guessRecordDtos));
        List<RedPacketGuessRecordDto> consumerGuessRecord = remoteRedPacketGuessRecordService.getConsumerGuessRecord(consumerId);
        logger.info("consumer guess record before update,{}", JSONObject.toJSONString(consumerGuessRecord));
        Assert.assertTrue(guessRecordDtos.size()>0);
        guessRecordDtos.forEach(g -> {
            // 奖励记录
            g.setAwardAmount(addMoney);
            g.setAwardStatus(GuessRedPackAwardStatusEnum.NOT_RECEIVE.getStatus());
            if (g.getConsumerId().equals(consumerId)) {
                g.setGuessResult(1);
            }
        });
        remoteRedPacketGuessRecordService.batchUpdateRecord(guessRecordDtos);
        List<RedPacketGuessRecordDto> newGuessRecordDtos = remoteRedPacketGuessRecordService.selectByPacketId(redPacketId);
        logger.info("guess record after update,{}", JSONObject.toJSONString(newGuessRecordDtos));
        List<RedPacketGuessRecordDto> newConsumerGuessRecord = remoteRedPacketGuessRecordService.getConsumerGuessRecord(consumerId);
        logger.info("consumer guess record after update,{}", JSONObject.toJSONString(newConsumerGuessRecord));
    }
}
