package cn.com.duiba.activity.center.biz.dao.quizz;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.quizz.QuizzOrdersEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ_CONSUMER)
public class QuizzOrderTextChangeDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private QuizzOrderTextChangeDao quizzOrderTextChangeDao;
	
	@Autowired
	private QuizzOrdersSequenceDao quizzOrdersSequenceDao;
	
	private QuizzOrdersEntity info;
	
	@Before
	public void insertTest(){
		QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		quizzOrderTextChangeDao.insert(e, quizzOrdersSequenceDao.getId());
		info = e;
	}

	@Test
    public void updateDeveloperBizIdTest(){
    	QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		
		quizzOrderTextChangeDao.updateDeveloperBizId(info.getConsumerId(), info.getId(), e.getDeveloperBizId());
		
		String developerBizId = quizzOrderTextChangeDao.find(info.getConsumerId(), info.getId()).getDeveloperBizId();
		Assert.assertTrue(developerBizId.equals(e.getDeveloperBizId()));
    }

	@Test
    public void updateMainOrderIdTest(){
    	QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		
		quizzOrderTextChangeDao.updateMainOrderId(info.getConsumerId(), info.getId(), e.getMainOrderId(), e.getMainOrderNum());
		
		long mainOrderId = quizzOrderTextChangeDao.find(info.getConsumerId(), info.getId()).getMainOrderId();
		Assert.assertTrue(mainOrderId == e.getMainOrderId());
    }
}
