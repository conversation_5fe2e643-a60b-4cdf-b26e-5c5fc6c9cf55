package cn.com.duiba.activity.center.biz.service.permission;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.permission.AppPermissionDAO;
import cn.com.duiba.activity.center.biz.entity.permission.AppPermissionEntity;
import cn.com.duiba.activity.center.biz.entity.permission.PermissionEntity;
import cn.com.duiba.wolf.utils.TestUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by zhengjy on 2016/12/8.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class AppPermissionServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private AppPermissionService appPermissionService;
    @Autowired
    private AppPermissionDAO appPermissionDAO;
    @Autowired
    private PermissionService permissionService;

    private AppPermissionEntity appPermissionDO;

    @Before
    public void testInsert() {
        AppPermissionEntity e=new AppPermissionEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        appPermissionService.insert(e);
        appPermissionDO=e;
    }

    @Test
    public  void  testHasItem(){
        Assert.assertTrue(appPermissionService.hasItem(appPermissionDO.getAppId(),appPermissionDO.getPermissionId()) >= 1);
    }

    @Test
    public void testUpdateByAppIdAndPermissionId() {
        AppPermissionEntity e=appPermissionDO;
        boolean newEnableStatus=!e.getEnable();
        appPermissionService.updateByAppIdAndPermissionId(e.getAppId(),e.getPermissionId(),newEnableStatus);
        AppPermissionEntity e1=appPermissionDAO.find(e.getId());
        Assert.assertEquals(e1.getEnable(),newEnableStatus);

    }

    @Test
    public void testUpdateEnableByAppIdAndActivityIdAndType(){
        PermissionEntity permissionDO=new PermissionEntity();
        TestUtils.setRandomAttributesForBean(permissionDO,false);
        permissionService.insert(permissionDO);
        AppPermissionEntity e=new AppPermissionEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setEnable(false);
        e.setPermissionId(permissionDO.getId());
        appPermissionService.insert(e);
        Integer count= appPermissionService.updateEnableByAppIdAndActivityIdAndType(permissionDO.getSourceId(),permissionDO.getSourceType(),true);
        Assert.assertTrue(count>0);
    }
}
