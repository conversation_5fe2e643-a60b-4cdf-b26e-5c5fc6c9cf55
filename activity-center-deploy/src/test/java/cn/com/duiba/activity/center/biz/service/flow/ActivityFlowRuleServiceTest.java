package cn.com.duiba.activity.center.biz.service.flow;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.flow.ActivityFlowRuleDto;
import cn.com.duiba.activity.center.api.dto.flow.FlowFeatureDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.flow.ActivityFlowRuleDao;
import cn.com.duiba.activity.center.biz.entity.flow.ActivityFlowRuleEntity;

/**
 * Created by zzy on 2017/4/22.
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class ActivityFlowRuleServiceTest extends TransactionalTestCaseBase {

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private ActivityFlowRuleService activityFlowRuleService;
    @Autowired
    private ActivityFlowRuleDao activityFlowRuleDao;

    private static final String PERIOD_DEMO = "{\n" +
            "    \"6\":[\n" +
            "        {\n" +
            "            \"s\":\"00\",\n" +
            "            \"e\":\"02\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"s\":\"20\",\n" +
            "            \"s\":\"22\"\n" +
            "        }\n" +
            "    ],\n" +
            "    \"7\":[\n" +
            "        {\n" +
            "            \"s\":\"02\",\n" +
            "            \"e\":\"04\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"s\":\"22\",\n" +
            "            \"s\":\"24\"\n" +
            "        }\n" +
            "    ]\n" +
            "}";

    private static final String REGION_DEMO = "[\"1100\",\"3100\",\"4401\", \"4403\", “3301”]";

    @Test
    public void testInsert() {
        doInsert();
    }


    private Long doInsert() {
        ActivityFlowRuleDto activityFlowRuleDto = new ActivityFlowRuleDto();
        activityFlowRuleDto.setAppId(1L);
        activityFlowRuleDto.setActivityIds("[1,2,3]");
        activityFlowRuleDto.setActivityType(ActivityFlowRuleDto.ACTIVITY_TYPE_PLUGIN);
        activityFlowRuleDto.setRuleType(ActivityFlowRuleDto.RULE_TYPE_SPECIAL);
        activityFlowRuleDto.setProxyFlag(ActivityFlowRuleDto.PROXY_FLAG_OPEN);
        long now = System.currentTimeMillis();
        activityFlowRuleDto.setInvalidSdate(new Date(now));
        activityFlowRuleDto.setInvalidEdate(new Date(now + 3 * 24 * 3600 * 1000));
        activityFlowRuleDto.setValidPeriod(PERIOD_DEMO);
        activityFlowRuleDto.setValidRegions(REGION_DEMO);
        Long id = activityFlowRuleService.insert(activityFlowRuleDto);
        Assert.assertNotNull(id);
        return id;
    }

    @Test
    public void testUpdate() {
        Long id = doInsert();
        ActivityFlowRuleEntity entity = activityFlowRuleDao.find(id);
        Assert.assertNotNull(entity);
        ActivityFlowRuleDto activityFlowRuleDto = new ActivityFlowRuleDto();
        activityFlowRuleDto.setAppId(1L);
        activityFlowRuleDto.setActivityType(ActivityFlowRuleDto.ACTIVITY_TYPE_PLUGIN);
        activityFlowRuleDto.setRuleType(ActivityFlowRuleDto.RULE_TYPE_SPECIAL);
        boolean ret = activityFlowRuleService.update(activityFlowRuleDto);
        Assert.assertTrue(ret);
    }

    @Test
    public void testFindByKey() {
        Long id = doInsert();
        ActivityFlowRuleDto dto = activityFlowRuleService.findByKey(1L, ActivityFlowRuleDto.RULE_TYPE_SPECIAL, ActivityFlowRuleDto.ACTIVITY_TYPE_PLUGIN);
        Assert.assertNotNull(dto);
        Assert.assertEquals(id, dto.getId());
    }

    @Test
    public void testFindListByRule() throws ParseException {
        doInsert();
        FlowFeatureDto flowFeatureDto = new FlowFeatureDto();
        flowFeatureDto.setActivityType(ActivityFlowRuleDto.ACTIVITY_TYPE_PLUGIN);
        flowFeatureDto.setAppId(1L);
        flowFeatureDto.setCityCode("3301");
        flowFeatureDto.setTime(dateFormat.parse("2017-04-22 01:00:00"));
        List<Long> ids = activityFlowRuleService.findListByRule(flowFeatureDto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(ids));
    }


    @Test
    public void testDelete() {
        Long id = doInsert();
        int ret = activityFlowRuleDao.delete(id);
        Assert.assertEquals(1, ret);
    }
}
