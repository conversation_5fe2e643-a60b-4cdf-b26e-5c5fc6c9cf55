package cn.com.duiba.activity.center.api.remoteservice.happygroup;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupBaseConfigDto;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupConfigDto;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupItemDto;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.Arrays;
import java.util.List;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/3/5 11:41
 * @description:
 */
@Rollback(value = false)
public class RemoteHappyGroupItemAppServiceTest extends TransactionalTestCaseBase {

    @Autowired
    RemoteHappyGroupItemAppService remoteHappyGroupItemAppService;

    @Test
    public void testGetByActivityConfigId() {
        List<HappyGroupItemDto> happyGroupItemDtoList = remoteHappyGroupItemAppService.getByActivityConfigId(1L);
        System.out.println(JSON.toJSON(happyGroupItemDtoList));
        Assert.assertNotNull(happyGroupItemDtoList);
    }

    @Test
    public void testGetById() {
        HappyGroupItemDto happyGroupItemDto = remoteHappyGroupItemAppService.getById(1L);
        System.out.println(JSON.toJSON(happyGroupItemDto));
        Assert.assertNotNull(happyGroupItemDto);
    }

    @Test
    public void testGetByIds() {
        List<HappyGroupItemDto> happyGroupItemDtoList = remoteHappyGroupItemAppService.getByIds(Arrays.asList(1L, 2L));
        System.out.println(JSON.toJSON(happyGroupItemDtoList));
        Assert.assertNotNull(happyGroupItemDtoList);
    }
}
