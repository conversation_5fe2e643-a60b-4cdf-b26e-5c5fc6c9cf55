package cn.com.duiba.activity.center.biz.dao.chaos;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.chaos.ActPreChangeStockEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/22.
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class ActPreChangeStockDaoTest extends TransactionalTestCaseBase{

    @Resource
    private ActPreChangeStockDao actPreChangeStockDao;

    @Test
    public void testInsert() {
        ActPreChangeStockEntity e=new ActPreChangeStockEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        actPreChangeStockDao.insert(e);
    }

}
