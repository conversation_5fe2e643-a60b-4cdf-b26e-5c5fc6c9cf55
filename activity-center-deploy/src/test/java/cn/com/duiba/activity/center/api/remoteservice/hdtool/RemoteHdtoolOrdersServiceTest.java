package cn.com.duiba.activity.center.api.remoteservice.hdtool;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolOrdersDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.hdtool.HdtoolOrdersDao;
import cn.com.duiba.activity.center.biz.entity.hdtool.HdtoolOrdersEntity;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.wolf.utils.DateUtils;
import com.google.common.collect.Sets;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * Created by pc on 2017/9/19
 */
@Transactional(DsConstants.DATABASE_MNG_HDTOOL)
public class RemoteHdtoolOrdersServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private  RemoteHdtoolOrdersService remoteHdtoolOrdersService;
    @Autowired
    private HdtoolOrdersDao hdtoolOrdersDao;

    @Test
    public void testFindOrdersByPrizeTypeAndOperatingActivityId() {
        Long id = (long) (Math.random() * 100000);
        hdtoolOrdersDao.insert(getOrder(id, ItemDto.TypeObject));
        hdtoolOrdersDao.insert(getOrder(id, ItemDto.TypeAlipay));
        List<HdtoolOrdersDto> objectOrders = remoteHdtoolOrdersService.findOrdersByPrizeTypeAndOperatingActivityId(id, DateUtils.daysAddOrSub(new Date(), -1), Sets.newHashSet(ItemDto.TypeObject), 10, null);
        Assert.assertEquals(objectOrders.size(), 1);
        List<HdtoolOrdersDto> objectAndAlipayOrders = remoteHdtoolOrdersService.findOrdersByPrizeTypeAndOperatingActivityId(id, DateUtils.daysAddOrSub(new Date(), -1), Sets.newHashSet(ItemDto.TypeObject, ItemDto.TypeAlipay), 10, null);
        Assert.assertEquals(objectAndAlipayOrders.size(), 2);
        List<HdtoolOrdersDto> findByCount = remoteHdtoolOrdersService.findOrdersByPrizeTypeAndOperatingActivityId(id, DateUtils.daysAddOrSub(new Date(), -1), Sets.newHashSet(ItemDto.TypeObject, ItemDto.TypeAlipay), 1, null);
        Assert.assertEquals(findByCount.size(), 1);

    }

    private HdtoolOrdersEntity getOrder(Long operatingActivityId, String prizeType) {
        HdtoolOrdersEntity hdtoolOrdersEntity = new HdtoolOrdersEntity();
        hdtoolOrdersEntity.setId((long) (Math.random() * 100000));
        hdtoolOrdersEntity.setConsumerId(1L);
        hdtoolOrdersEntity.setStatus(HdtoolOrdersDto.StatusSuccess);
        hdtoolOrdersEntity.setAppId(1L);
        hdtoolOrdersEntity.setOperatingActivityId(operatingActivityId);
        hdtoolOrdersEntity.setHdtoolType(OperatingActivityDto.TypeScratchCard);
        //查询只会查出已经领奖的订单
        hdtoolOrdersEntity.setExchangeStatus(HdtoolOrdersDto.ExchangeStatusSuccess);
        hdtoolOrdersEntity.setGmtCreate(new Date());
        hdtoolOrdersEntity.setGmtModified(new Date());
        hdtoolOrdersEntity.setPrizeType(prizeType);
        return hdtoolOrdersEntity;
    }






}
