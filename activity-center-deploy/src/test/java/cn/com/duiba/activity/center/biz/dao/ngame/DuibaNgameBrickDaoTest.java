package cn.com.duiba.activity.center.biz.dao.ngame;

import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.ngame.DuibaNgameBrickDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.ngame.DuibaNgameBrickEntity;

@Transactional(DsConstants.DATABASE_NGAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaNgameBrickDaoTest extends TransactionalTestCaseBase {

	@Resource
	private DuibaNgameBrickDao duibaNgameBrickDao;

	@Test
	public void findTest() {
		DuibaNgameBrickEntity b = duibaNgameBrickDao.find(2l);
		System.out.println(b.getTitle());
	}
	
	@Test
	public void getBrickContentByIdTest() {
		System.out.println(duibaNgameBrickDao.getBrickContentById(2l));
	}
	
	@Test
	public void findNoContentTest() {
		System.out.println(duibaNgameBrickDao.findNoContent(2l).getTitle());
	}

	@Test
	public void insertTest() {
		DuibaNgameBrickEntity brick = new DuibaNgameBrickEntity();
		brick.setTitle("aaa");
		brick.setBrickStatus(0);
		brick.setContent("dskfajs");
		brick.setRank(DuibaNgameBrickDto.RANK_DESC);
		brick.setShareCopy("dsfa");
		brick.setTimeLimit(3L);
		brick.setMd5("dsalfl");
		duibaNgameBrickDao.insert(brick);
	}

	@Test
	public void update4AdminTest() {
		DuibaNgameBrickEntity brick = duibaNgameBrickDao.find(5l);
		brick.setTitle("aaa111");
		duibaNgameBrickDao.update4Admin(brick);
	}

	@Test
	public void findByTitleTest() {
		System.out.println(duibaNgameBrickDao.findByTitle("aaa111").getId());
	}

	@Test
	public void openTest() {
		duibaNgameBrickDao.open(5l);
	}

	@Test
	public void disableTest() {
		duibaNgameBrickDao.disable(5l);
	}

	@Test
	public void findPageTest() {
		List<DuibaNgameBrickEntity> list = duibaNgameBrickDao.findPage(0, 10);
		System.out.println(list.size());
	}

	@Test
	public void findPageCountTest() {
		System.out.println(duibaNgameBrickDao.findPageCount());
	}

	@Test
	public void findAllTest() {
		List<DuibaNgameBrickEntity> list = duibaNgameBrickDao.findAll();
		System.out.println(list.size());
	}
}
