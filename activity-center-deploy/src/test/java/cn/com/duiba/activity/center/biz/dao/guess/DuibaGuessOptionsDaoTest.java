package cn.com.duiba.activity.center.biz.dao.guess;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.guess.DuibaGuessOptionsEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class DuibaGuessOptionsDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaGuessOptionsDao duibaGuessOptionsDao;
	
	private DuibaGuessOptionsEntity info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaGuessOptionsEntity.class);
		duibaGuessOptionsDao.insert(info);
	}
	
	@Test
	public void findByIdTest(){
		DuibaGuessOptionsEntity e = duibaGuessOptionsDao.findById(info.getId());
		Assert.assertNotNull(e);
	}
	
	@Test
	public void findByGuessIdTest(){
		DuibaGuessOptionsEntity e = TestUtils.createRandomBean(DuibaGuessOptionsEntity.class);
		e.setDeleted(false);
		duibaGuessOptionsDao.insert(e);
		List<DuibaGuessOptionsEntity> list = duibaGuessOptionsDao.findByGuessId(e.getDuibaGuessId());
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void testFindByGuessIds(){
		DuibaGuessOptionsEntity e = TestUtils.createRandomBean(DuibaGuessOptionsEntity.class);
		e.setDeleted(false);
		duibaGuessOptionsDao.insert(e);
		List<Long> ids = new ArrayList<>();
		ids.add(e.getDuibaGuessId());
		List<DuibaGuessOptionsEntity> list = duibaGuessOptionsDao.findByGuessIds(ids);
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void findByAutoOpenTest(){
		DuibaGuessOptionsEntity e = TestUtils.createRandomBean(DuibaGuessOptionsEntity.class);
		e.setDeleted(false);
		e.setAutoOpen(1);
		duibaGuessOptionsDao.insert(e);
		List<DuibaGuessOptionsEntity> list = duibaGuessOptionsDao.findByAutoOpen(e.getDuibaGuessId(), true);
		Assert.assertTrue(list.size() > 0);
	}
	
	@Test
	public void findByAutoOpenAscTest(){
		DuibaGuessOptionsEntity e = TestUtils.createRandomBean(DuibaGuessOptionsEntity.class);
		e.setDeleted(false);
		e.setAutoOpen(1);
		duibaGuessOptionsDao.insert(e);
		List<DuibaGuessOptionsEntity> list = duibaGuessOptionsDao.findByAutoOpen(e.getDuibaGuessId(), true);
		Assert.assertTrue(list.size() > 0);
	}

	//from manager
	@Test
	public void deleteTest(){
		List<Long> ids = new ArrayList<Long>();
		ids.add(info.getId());
		duibaGuessOptionsDao.delete(ids);
		boolean deleted = duibaGuessOptionsDao.findById(info.getId()).getDeleted();
		Assert.assertTrue(deleted);
	}
	
	@Test
	public void updateInfoFormTest(){
		DuibaGuessOptionsEntity e = TestUtils.createRandomBean(DuibaGuessOptionsEntity.class);
		e.setId(info.getId());
		duibaGuessOptionsDao.updateInfoForm(e);
		String description = duibaGuessOptionsDao.findById(info.getId()).getDescription();
		Assert.assertTrue(description.equals(e.getDescription()));
	}

	/**
	 * 更新库存
	 */
	@Test
	public void updateRemainingByIdTest(){
		DuibaGuessOptionsEntity e = TestUtils.createRandomBean(DuibaGuessOptionsEntity.class);
		duibaGuessOptionsDao.updateRemainingById(info.getId(), e.getRemaining());
		int remaining = duibaGuessOptionsDao.findById(info.getId()).getRemaining();
		Assert.assertTrue(remaining == e.getRemaining());
	}
}
