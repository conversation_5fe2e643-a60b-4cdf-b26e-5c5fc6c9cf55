package cn.com.duiba.activity.center.biz.dao.guess;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.guess.DuibaGuessStockEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class DuibaGuessStockDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaGuessStockDao duibaGuessStockDao;
	
	private DuibaGuessStockEntity info;
	
	@Before
	public void addTest(){
		info = TestUtils.createRandomBean(DuibaGuessStockEntity.class);
		duibaGuessStockDao.add(info);
	}
	
	@Test
	public void findByIdTest(){
		DuibaGuessStockEntity e = duibaGuessStockDao.findById(info.getId());
		Assert.assertNotNull(e);
	}
	
	@Test
	public void findRemainingTest(){
		DuibaGuessStockEntity e = duibaGuessStockDao.findRemaining(info.getGuessOptionId());
		Assert.assertNotNull(e);
	}
	
	@Test
	public void subStockTest(){
		int subNumber = info.getStock() - 1;
		duibaGuessStockDao.subStock(info.getId(), subNumber);
		int stock = duibaGuessStockDao.findById(info.getId()).getStock();
		Assert.assertTrue(stock == (info.getStock() - subNumber));
	}
	
	@Test
	public void addStockTest(){
		DuibaGuessStockEntity e = TestUtils.createRandomBean(DuibaGuessStockEntity.class);
		duibaGuessStockDao.addStock(info.getId(), e.getStock());
		int stock = duibaGuessStockDao.findById(info.getId()).getStock();
		Assert.assertTrue(stock == (info.getStock() + e.getStock()));
	}
	
	//from manager
	@Test
	public void findByGuessOptionIdTest(){
		DuibaGuessStockEntity e = duibaGuessStockDao.findByGuessOptionId(info.getGuessOptionId());
		Assert.assertNotNull(e);
	}

	@Test
	public void findByGuessOptionIdsTest(){
		List<Long> list = new ArrayList<Long>();
		list.add(info.getGuessOptionId());
		List<DuibaGuessStockEntity> listEntity = duibaGuessStockDao.findByGuessOptionIds(list);
		Assert.assertTrue(listEntity.size() > 0);
	}

	/**
	 * 增加库存
	 * @param id
	 * @param stockAdd
	 * @return
	 */
	@Test
	public void updateStockAddTest(){
		DuibaGuessStockEntity e = TestUtils.createRandomBean(DuibaGuessStockEntity.class);
		duibaGuessStockDao.addStock(info.getId(), e.getStock());
		int stock = duibaGuessStockDao.findById(info.getId()).getStock();
		Assert.assertTrue(stock == (info.getStock() + e.getStock()));
	}
	/**
	 * 减少库存
	 * @param id
	 * @param stockAdd
	 * @return
	 */
	@Test
	public void updateStockSubTest(){
		int subNumber = info.getStock() - 1;
		duibaGuessStockDao.subStock(info.getId(), subNumber);
		int stock = duibaGuessStockDao.findById(info.getId()).getStock();
		Assert.assertTrue(stock == (info.getStock() - subNumber));
	}

	@Test
	public void addBatchTest(){
		DuibaGuessStockEntity e1 = TestUtils.createRandomBean(DuibaGuessStockEntity.class);
		DuibaGuessStockEntity e2 = TestUtils.createRandomBean(DuibaGuessStockEntity.class);
		List<DuibaGuessStockEntity> list = new ArrayList<DuibaGuessStockEntity>();
		list.add(e1);
		list.add(e2);
		duibaGuessStockDao.addBatch(list);
		Assert.assertNotNull(e1.getId());
		Assert.assertNotNull(e2.getId());
	}

}
