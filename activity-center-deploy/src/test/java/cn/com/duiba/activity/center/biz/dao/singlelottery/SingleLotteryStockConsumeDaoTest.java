package cn.com.duiba.activity.center.biz.dao.singlelottery;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.singlelottery.SingleLotteryStockConsumeEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/21.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class SingleLotteryStockConsumeDaoTest extends TransactionalTestCaseBase{
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS;

    @Resource
    private SingleLotteryStockConsumeDao singleLotteryStockConsumeDao;


    @Test
    public void testInsert() {
        SingleLotteryStockConsumeEntity e=new SingleLotteryStockConsumeEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        singleLotteryStockConsumeDao.insert(e);
    }

    @Test
    public void testFindByBizIdAndSource() {
        SingleLotteryStockConsumeEntity e=new SingleLotteryStockConsumeEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setAction("pay");
        singleLotteryStockConsumeDao.insert(e);
        Assert.assertNotNull(singleLotteryStockConsumeDao.findByBizIdAndSource(e.getBizId(),e.getBizSource()));
    }

}
