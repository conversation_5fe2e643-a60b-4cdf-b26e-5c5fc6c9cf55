package cn.com.duiba.activity.center.biz.service.chaos;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.chaos.ActPreStockDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/22.
 */
@Transactional(ActPreStockServiceTest.THIS_DATABASE_SCHEMA)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class ActPreStockServiceTest extends TransactionalTestCaseBase{
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS_ACTIVITY;

    @Resource
    private ActPreStockSerivce actPreStockService;

    private ActPreStockDto actPreStockDO;

    @Before
    public void testInsert() {
        ActPreStockDto e=new ActPreStockDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(0);
        actPreStockService.insert(e);
        actPreStockDO=e;
    }

    @Test
    public void testFindByLock() {
        ActPreStockDto e=actPreStockDO;
        ActPreStockDto e1=actPreStockService.findByLock(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindPreStockByApp() {
        ActPreStockDto e=actPreStockDO;
        ActPreStockDto e1=actPreStockService.findPreStockByApp(e.getRelationPrizeId(),e.getRelationType(),e.getAppId());
        assertDO(e,e1);
    }

    @Test
    public void testFindPreStockByShare() {
        ActPreStockDto e=new ActPreStockDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(0);
        e.setAppId(0l);
        actPreStockService.insert(e);
        ActPreStockDto e1=actPreStockService.findPreStockByShare(e.getRelationPrizeId(),e.getRelationType());
        assertDO(e,e1);
    }

    @Test
    public void testDecrementRemaining() {
        actPreStockService.decrementRemaining(actPreStockDO.getId());
        Assert.assertTrue(actPreStockService.findByLock(actPreStockDO.getId()).getPrizeQuantity().equals(actPreStockDO.getPrizeQuantity()-1));
    }

    @Test
    public void testIncrementRemaining() {
        actPreStockService.incrementRemaining(actPreStockDO.getId());
        Assert.assertTrue(actPreStockService.findByLock(actPreStockDO.getId()).getPrizeQuantity().equals(actPreStockDO.getPrizeQuantity()+1));
    }

    @Test
    public void testAddRemainingById() {
        actPreStockService.addRemainingById(actPreStockDO.getId(),1l);
        Assert.assertTrue(actPreStockService.findByLock(actPreStockDO.getId()).getPrizeQuantity().equals(actPreStockDO.getPrizeQuantity()+1));

    }

    @Test
    public void testSubRemainingById() {
        actPreStockService.subRemainingById(actPreStockDO.getId(),1l);
        Assert.assertTrue(actPreStockService.findByLock(actPreStockDO.getId()).getPrizeQuantity().equals(actPreStockDO.getPrizeQuantity()-1));
    }

    @Test
    public void testUpdate() {
        ActPreStockDto e=new ActPreStockDto(actPreStockDO.getId());
        TestUtils.setRandomAttributesForBean(e,false);
        actPreStockService.update(e);
        ActPreStockDto e1=actPreStockService.findByLock(e.getId());
        assertDO(e,e1);

    }

    @Test
    public void testDeleteActStock() {
        ActPreStockDto e=actPreStockDO;
        actPreStockService.deleteActStock(e.getRelationConfigId(),e.getRelationType());
        Assert.assertNull(actPreStockService.findByLock(e.getId()));
    }

    @Test
    public void testDeleteActPrizeStock() {
        ActPreStockDto e=actPreStockDO;
        actPreStockService.deleteActPrizeStock(e.getRelationConfigId(),e.getRelationPrizeId(),e.getRelationType());
        Assert.assertNull(actPreStockService.findByLock(e.getId()));
    }

    @Test
    public void testDeleteActStockAppId() {
        ActPreStockDto e=actPreStockDO;
        actPreStockService.deleteActStockAppId(e.getRelationPrizeId(),e.getRelationType(),e.getAppId());
    }

    @Test
    public void testFindActStockByConfigId() {
        ActPreStockDto e=actPreStockDO;
        Assert.assertTrue(actPreStockService.findActStockByConfigId(e.getRelationConfigId(),e.getRelationType()).size()>0);
    }

    @Test
    public void testAddAppActPreStock() {
        actPreStockService.addAppActPreStock(1l,1l,1l,"test","test",1l);
    }

    @Test
    public void testAddShareActPreStock() {
        actPreStockService.addShareActPreStock(1l,1l,1l,"test","test");
    }

    @Test
    public void testRefreshActPreStock() {
        ActPreStockDto e=actPreStockDO;
        actPreStockService.refreshActPreStock(e.getRelationPrizeId(),e.getRelationType(),e.getAppId(),20l,"test");
    }


    @Test
    public void testFindPreStock() {
        ActPreStockDto e=actPreStockDO;
        Assert.assertTrue(actPreStockService.findPreStock(e.getRelationConfigId(),e.getRelationType()).size()>0);
        Assert.assertNotNull(actPreStockService.findPreStock(e.getRelationPrizeId(),e.getRelationType(),e.getAppId()));
    }

    private void assertDO(ActPreStockDto e, ActPreStockDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(ActPreStockDto e, ActPreStockDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }


}
