package cn.com.duiba.activity.center.biz.service.singlelottery;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.singlelottery.SingleLotteryStockConsumeDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/21.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class SingleLotteryStockConsumeServiceTest extends TransactionalTestCaseBase{
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS;

    @Resource
    private SingleLotteryStockConsumeService singleLotteryStockConsumeService;

    @Test
    public void testInsert() {
        SingleLotteryStockConsumeDto e=new SingleLotteryStockConsumeDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        singleLotteryStockConsumeService.insert(e);
    }

    @Test
    public void testFindByBizIdAndSource() {
        SingleLotteryStockConsumeDto e=new SingleLotteryStockConsumeDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setAction("pay");
        singleLotteryStockConsumeService.insert(e);
        Assert.assertNotNull(singleLotteryStockConsumeService.findByBizIdAndSource(e.getBizId(),e.getBizSource()));
    }

}
