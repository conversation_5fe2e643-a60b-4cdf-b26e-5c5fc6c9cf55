package cn.com.duiba.activity.center.biz.remoteservice.flow;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.flow.ActivityFlowRuleDto;
import cn.com.duiba.activity.center.api.remoteservice.flow.RemoteActivityFlowRuleBackendService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.dubbo.DubboResult;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by zzy on 2017/7/25.
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class RemoteActivityFlowRuleBackendServiceTest extends TransactionalTestCaseBase {
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static final String PERIOD_DEMO = "{\n" +
            "    \"6\":[\n" +
            "        {\n" +
            "            \"s\":\"00\",\n" +
            "            \"e\":\"02\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"s\":\"20\",\n" +
            "            \"s\":\"22\"\n" +
            "        }\n" +
            "    ],\n" +
            "    \"7\":[\n" +
            "        {\n" +
            "            \"s\":\"02\",\n" +
            "            \"e\":\"04\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"s\":\"22\",\n" +
            "            \"s\":\"24\"\n" +
            "        }\n" +
            "    ]\n" +
            "}";

    private static final String REGION_DEMO = "[\"1100\",\"3100\",\"4401\", \"4403\", “3301”]";
    @Autowired
    private RemoteActivityFlowRuleBackendService remoteActivityFlowRuleBackendService;

    @Test
    public void testUpdateList() {
        ActivityFlowRuleDto activityFlowRuleDto = new ActivityFlowRuleDto();
        activityFlowRuleDto.setAppId(101L);
        activityFlowRuleDto.setActivityIds("[1,2,3]");
        activityFlowRuleDto.setActivityType(ActivityFlowRuleDto.ACTIVITY_TYPE_PLUGIN);
        activityFlowRuleDto.setRuleType(ActivityFlowRuleDto.RULE_TYPE_SPECIAL);
        activityFlowRuleDto.setProxyFlag(ActivityFlowRuleDto.PROXY_FLAG_OPEN);
        long now = System.currentTimeMillis();
        activityFlowRuleDto.setInvalidSdate(new Date(now));
        activityFlowRuleDto.setInvalidEdate(new Date(now + 3 * 24 * 3600 * 1000));
        activityFlowRuleDto.setValidPeriod(PERIOD_DEMO);
        activityFlowRuleDto.setValidRegions(REGION_DEMO);
        DubboResult<Long> dubboResult = remoteActivityFlowRuleBackendService.insert(activityFlowRuleDto);
        Assert.assertTrue(dubboResult.isSuccess());
        Assert.assertNotNull(dubboResult.getResult());

        ActivityFlowRuleDto activityFlowRuleDto2 = new ActivityFlowRuleDto();
        activityFlowRuleDto2.setAppId(201L);
        activityFlowRuleDto2.setActivityIds("[1,2,3]");
        activityFlowRuleDto2.setActivityType(ActivityFlowRuleDto.ACTIVITY_TYPE_PLUGIN);
        activityFlowRuleDto2.setRuleType(ActivityFlowRuleDto.RULE_TYPE_SPECIAL);
        activityFlowRuleDto2.setProxyFlag(ActivityFlowRuleDto.PROXY_FLAG_OPEN);
        long now2 = System.currentTimeMillis();
        activityFlowRuleDto2.setInvalidSdate(new Date(now2));
        activityFlowRuleDto2.setInvalidEdate(new Date(now2 + 3 * 24 * 3600 * 1000));
        activityFlowRuleDto2.setValidPeriod(PERIOD_DEMO);
        activityFlowRuleDto2.setValidRegions(REGION_DEMO);
        DubboResult<Long> dubboResult2 = remoteActivityFlowRuleBackendService.insert(activityFlowRuleDto2);
        Assert.assertTrue(dubboResult2.isSuccess());
        Assert.assertNotNull(dubboResult2.getResult());

        activityFlowRuleDto.setActivityIds("[4,5,6]");
        activityFlowRuleDto2.setActivityIds("[7,8,9]");

        DubboResult<List<Boolean>> updateDubboResult = remoteActivityFlowRuleBackendService.updateList(ImmutableList.<ActivityFlowRuleDto>of(activityFlowRuleDto, activityFlowRuleDto2));
        Assert.assertTrue(updateDubboResult.isSuccess());
        Assert.assertEquals(2, updateDubboResult.getResult().size());
        DubboResult<ActivityFlowRuleDto> d4 = remoteActivityFlowRuleBackendService.findByKey(101L, ActivityFlowRuleDto.RULE_TYPE_SPECIAL, ActivityFlowRuleDto.ACTIVITY_TYPE_PLUGIN);
        Assert.assertTrue(d4.isSuccess());
        Assert.assertNotNull("[4,5,6]", d4.getResult().getActivityIds());

        DubboResult<ActivityFlowRuleDto> d5 = remoteActivityFlowRuleBackendService.findByKey(201L, ActivityFlowRuleDto.RULE_TYPE_SPECIAL, ActivityFlowRuleDto.ACTIVITY_TYPE_PLUGIN);
        Assert.assertTrue(d5.isSuccess());
        Assert.assertNotNull("[7,8,9]", d5.getResult().getActivityIds());

        DubboResult<Map<Long, ActivityFlowRuleDto>> d6 = remoteActivityFlowRuleBackendService.findListByKey(ImmutableSet.<Long>of(101L, 201L), ActivityFlowRuleDto.RULE_TYPE_SPECIAL, ActivityFlowRuleDto.ACTIVITY_TYPE_PLUGIN);
        Assert.assertTrue(d6.isSuccess());
        Assert.assertEquals(2, d6.getResult().size());

    }

}
