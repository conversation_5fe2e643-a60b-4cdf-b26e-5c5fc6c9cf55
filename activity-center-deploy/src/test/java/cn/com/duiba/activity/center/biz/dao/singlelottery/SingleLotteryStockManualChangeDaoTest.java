package cn.com.duiba.activity.center.biz.dao.singlelottery;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.singlelottery.SingleLotteryStockManualChangeEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/21.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class SingleLotteryStockManualChangeDaoTest extends TransactionalTestCaseBase {
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS;

    @Resource
    private SingleLotteryStockManualChangeDao singleLotteryStockManualChangeDao;

    @Test
    public void testInsert() {
        SingleLotteryStockManualChangeEntity e=new SingleLotteryStockManualChangeEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        singleLotteryStockManualChangeDao.insert(e);
    }
}
