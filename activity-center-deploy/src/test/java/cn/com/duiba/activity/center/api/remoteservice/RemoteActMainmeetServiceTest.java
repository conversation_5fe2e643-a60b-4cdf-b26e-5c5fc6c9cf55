package cn.com.duiba.activity.center.api.remoteservice;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.MainMeetActivityDto;
import cn.com.duiba.activity.center.api.enums.ActivityTypeEnum;
import cn.com.duiba.activity.center.api.params.MainmeetActivityParams;
import cn.com.duiba.activity.center.api.params.MainmeetParams;
import cn.com.duiba.wolf.dubbo.DubboResult;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created by liuyao on 2017/2/8.
 */
public class RemoteActMainmeetServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteActMainmeetService remoteMainmeetService;
    @Test
    public void testLoadMainMeetData(){
        MainmeetParams params = new MainmeetParams();
        params.setAppId(1L);
        params.setDeveloperId(1L);
        MainmeetActivityParams item5 = new MainmeetActivityParams();
        item5.setType(ActivityTypeEnum.ACTIVITY_TOOL);
        item5.setActivityIds(Lists.newArrayList(8996L));
        params.addActivitys(item5);
        DubboResult<List<MainMeetActivityDto>> result = remoteMainmeetService.getMainmeetActivityList(params);
        Assert.assertTrue(result.isSuccess());
        List<MainMeetActivityDto> list = result.getResult();
        Assert.assertTrue(list.size()<=1);
        if(!list.isEmpty()){
            MainMeetActivityDto dto = list.get(0);
            Assert.assertTrue(Objects.equal(ActivityTypeEnum.ACTIVITY_TOOL,dto.getType()));
        }


    }

}
