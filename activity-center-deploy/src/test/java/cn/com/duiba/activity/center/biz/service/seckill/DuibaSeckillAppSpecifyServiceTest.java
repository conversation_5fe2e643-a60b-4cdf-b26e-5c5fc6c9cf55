package cn.com.duiba.activity.center.biz.service.seckill;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.seckill.DuibaSeckillAppSpecifyDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Created by yansen on 16/6/7.
 */
@Transactional(DsConstants.DATABASE_SECKILL)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaSeckillAppSpecifyServiceTest extends TransactionalTestCaseBase {
    @Resource
    private DuibaSeckillAppSpecifyService duibaSeckillAppSpecifyService;

    private ThreadLocal<DuibaSeckillAppSpecifyDto> duibaSeckillAppSpecifyDO=new ThreadLocal<>();

    @Before
    public void testInsert() {
        DuibaSeckillAppSpecifyDto e=new DuibaSeckillAppSpecifyDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaSeckillAppSpecifyService.insert(e);
        duibaSeckillAppSpecifyDO.set(e);
    }

    @Test
    public void testFind() {
        DuibaSeckillAppSpecifyDto e=duibaSeckillAppSpecifyDO.get();
        DuibaSeckillAppSpecifyDto e1=duibaSeckillAppSpecifyService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindActivityIdsByAppId() {
        DuibaSeckillAppSpecifyDto e = duibaSeckillAppSpecifyDO.get();
        List<Long> list = duibaSeckillAppSpecifyService.findActivityIdsByAppId(e.getAppId());
        Assert.assertTrue(list.size() > 0);
    }

    @Test
    public void testDelete() {
        DuibaSeckillAppSpecifyDto e=duibaSeckillAppSpecifyDO.get();
        duibaSeckillAppSpecifyService.delete(e.getId());
        Assert.assertNull(duibaSeckillAppSpecifyService.find(e.getId()));
    }

    @Test
    public void testFindByDuibaSeckillId() {
        DuibaSeckillAppSpecifyDto e=duibaSeckillAppSpecifyDO.get();
        Assert.assertTrue(duibaSeckillAppSpecifyService.findByDuibaSeckillId(e.getDuibaSeckillId()).size()>0);
    }

    @Test
    public void testFindByDuibaSeckillAndApp() {
        DuibaSeckillAppSpecifyDto e=duibaSeckillAppSpecifyDO.get();
        assertDO(e,duibaSeckillAppSpecifyService.findByDuibaSeckillAndApp(e.getDuibaSeckillId(),e.getAppId()));
        duibaSeckillAppSpecifyService.delete(e.getId());
        Assert.assertNull(duibaSeckillAppSpecifyService.findByDuibaSeckillAndApp(e.getDuibaSeckillId(),e.getAppId()));
    }

    @Test
    public void testFindByDuibaSeckillsAndApp() {
        DuibaSeckillAppSpecifyDto e=duibaSeckillAppSpecifyDO.get();
        Assert.assertTrue(!duibaSeckillAppSpecifyService.findByDuibaSeckillsAndApp(Collections.singletonList(e.getDuibaSeckillId()),e.getAppId()).isEmpty());
        duibaSeckillAppSpecifyService.delete(e.getId());
        Assert.assertTrue(duibaSeckillAppSpecifyService.findByDuibaSeckillsAndApp(Collections.singletonList(e.getDuibaSeckillId()),e.getAppId()).isEmpty());
    }

    private void assertDO(DuibaSeckillAppSpecifyDto e, DuibaSeckillAppSpecifyDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaSeckillAppSpecifyDto e, DuibaSeckillAppSpecifyDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","dateStart","dateEnd","timeStart","timeEnd","deleted"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

}
