/**
 * Project Name:activity-center-deploy
 * File Name:RemoteActivityPrizeOptionServiceImplTest.java
 * Package Name:cn.com.duiba.activity.center.biz.remoteservice.rob
 * Date:2016年7月26日下午4:07:30
 * Copyright (c) 2016, duiba.com.cn All Rights Reserved.
 *
*/

package cn.com.duiba.activity.center.biz.remoteservice.rob;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.prize.ActivityPrizeOptionDto;
import cn.com.duiba.activity.center.api.remoteservice.prize.RemoteActivityPrizeOptionService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

import java.util.List;

/**
 * ClassName:RemoteActivityPrizeOptionServiceImplTest <br/>
 * Date:     2016年7月26日 下午4:07:30 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
@Ignore
public class RemoteActivityPrizeOptionServiceImplTest extends TransactionalTestCaseBase{

    @Autowired
    private RemoteActivityPrizeOptionService remoteActivityPrizeOptionService;
    
    ActivityPrizeOptionDto dto = null;
    @Before
    public void insertTest(){
        dto = new ActivityPrizeOptionDto();
        TestUtils.setRandomAttributesForBean(dto,false);
        remoteActivityPrizeOptionService.saveOrUpdateOption(dto).getResult();
    }
    
    @Test
    public void del(){
        Boolean bo = remoteActivityPrizeOptionService.delOption(dto.getId()).getResult();
        Assert.assertTrue(bo);
    }

    @Test
    public void testFindWarningOption() {
        List<ActivityPrizeOptionDto> oldOptions = remoteActivityPrizeOptionService.findWarningOption(ActivityPrizeOptionDto.Activity_Type_Plugin);
        dto = new ActivityPrizeOptionDto();
        TestUtils.setRandomAttributesForBean(dto,false);
        dto.setStockWarning(1);
        remoteActivityPrizeOptionService.saveOrUpdateOption(dto).getResult();
        List<ActivityPrizeOptionDto> newOptions = remoteActivityPrizeOptionService.findWarningOption(ActivityPrizeOptionDto.Activity_Type_Plugin);
        Assert.assertTrue(newOptions.size() - oldOptions.size() == 1);
    }
}

