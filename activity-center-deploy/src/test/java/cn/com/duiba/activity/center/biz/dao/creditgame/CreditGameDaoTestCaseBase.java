package cn.com.duiba.activity.center.biz.dao.creditgame;

import org.junit.Assert;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.GenericCURDDao;
import cn.com.duiba.activity.center.biz.entity.creditgame.IdTimeable;

/**
 * 积分游戏 DAO 测试基类
 */
public abstract class CreditGameDaoTestCaseBase<E extends IdTimeable> extends TransactionalTestCaseBase{
    protected abstract E genEntity();


    protected void doTestInsert(GenericCURDDao dao){
        E entity=genEntity();
        Assert.assertNotNull(entity);

        int insertNum=dao.insert(entity);
        Assert.assertTrue(insertNum==1);
        Assert.assertTrue(null!=entity.getId());

        E queryEntity= (E)dao.queryById(entity.getId());
        Assert.assertNotNull(queryEntity);
        int deleteNum=dao.delete(queryEntity.getId());
        Assert.assertTrue(deleteNum==1);
    }


    protected void doTestQueryById(GenericCURDDao dao){
        E entity=genEntity();
        Assert.assertNotNull(entity);

        int insertNum=dao.insert(entity);
        Assert.assertTrue(insertNum==1);
        Assert.assertTrue(null!=entity.getId());

        E queryEntity= (E)dao.queryById(entity.getId());
        Assert.assertNotNull(queryEntity);

        int deleteNum=dao.delete(queryEntity.getId());
        Assert.assertTrue(deleteNum==1);
    }


    /*
    protected void doTestQuery(GenericCURDDao dao){
        E entity=genEntity();
        Assert.assertNotNull(entity);
        int insertNum=dao.insert(entity);
        Assert.assertTrue(insertNum==1);
        Assert.assertTrue(null!=entity.getId());
        entity.setGmtCreate(null);
        entity.setGmtModified(null);

        List<E> entities=dao.query(entity);
        Assert.assertTrue(!CollectionUtils.isEmpty(entities));
        E queryEntity= entities.get(0);
        Assert.assertNotNull(queryEntity);

        int deleteNum=dao.delete(queryEntity.getId());
        Assert.assertTrue(deleteNum==1);
    }
    */


    protected void doTestUpdate(GenericCURDDao dao,PreUpdateHandler<E> preUpdateHandler){
        E entity=genEntity();
        Assert.assertNotNull(entity);
        int insertNum=dao.insert(entity);
        Assert.assertTrue(insertNum==1);
        Assert.assertTrue(null!=entity.getId());

        if(null!=preUpdateHandler){
            preUpdateHandler.preHandle(entity);
        }
        int updateNum=dao.update(entity);
        Assert.assertTrue(updateNum==1);

        int deleteNum=dao.delete(entity.getId());
        Assert.assertTrue(deleteNum==1);
    }



    protected void doTestDelete(GenericCURDDao dao){
        E entity=genEntity();
        Assert.assertNotNull(entity);
        int insertNum=dao.insert(entity);
        Assert.assertTrue(insertNum==1);
        Assert.assertTrue(null!=entity.getId());

        int deleteNum=dao.delete(entity.getId());
        Assert.assertTrue(deleteNum==1);
    }
}
