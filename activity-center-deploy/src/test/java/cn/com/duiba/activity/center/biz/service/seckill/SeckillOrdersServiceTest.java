/**
 * Project Name:duiba-service-chaos
 * File Name:SeckillOrdersServiceTest.java
 * Package Name:cn.com.duiba.service.service
 * Date:2016年7月18日上午11:22:13
 * Copyright (c) 2016, duiba.com.cn All Rights Reserved.
 *
*/

package cn.com.duiba.activity.center.biz.service.seckill;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * ClassName:SeckillOrdersServiceTest <br/>
 * Date:     2016年7月18日 上午11:22:13 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
@Transactional(DsConstants.DATABASE_SECKILL)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class SeckillOrdersServiceTest extends TransactionalTestCaseBase {
    @Resource
    private SeckillOrdersService seckillOrdersService;

    @Test
    public void testfind(){
        System.out.println(seckillOrdersService.find(1l).getStatus());
    }

    @Test
    public void testtotalCount(){
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("appId", 1l);
        System.out.println(seckillOrdersService.totalCount(queryMap));
    }

    @Test
    public void testfindByLimit(){
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("appId", 1l);
        queryMap.put("activityId", 22181l);
        queryMap.put("start", 0);
        queryMap.put("pageSize", 10);
        System.out.println(seckillOrdersService.findByLimit(queryMap));
    }
}

