package cn.com.duiba.activity.center.biz.dao.singlelottery;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.singlelottery.SingleLotteryOrderDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.singlelottery.SingleLotteryOrderEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/24.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class SingleLotteryOrderDaoTest extends TransactionalTestCaseBase {

    @Resource
    private SingleLotteryOrderDao singleLotteryOrderDao;

    ThreadLocal<SingleLotteryOrderEntity> threadLocal=new ThreadLocal<>();

    @Before
    public void testInsert() {
        SingleLotteryOrderEntity e=new SingleLotteryOrderEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setOptionType(1);
        singleLotteryOrderDao.insert(e);
        threadLocal.set(e);
    }

    @Test
    public void testFind() throws Exception {
        SingleLotteryOrderEntity e=threadLocal.get();
        SingleLotteryOrderEntity e1=singleLotteryOrderDao.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testCountByConsumerIdAndOptionType() throws Exception {
        SingleLotteryOrderEntity e=threadLocal.get();
        Assert.assertTrue(singleLotteryOrderDao.countByConsumerIdAndOptionType(e.getOperatingActivityId(),e.getConsumerId(),e.getOptionType())>0);
    }

    @Test
    public void testFindAllByIds() throws Exception {
        SingleLotteryOrderEntity e=threadLocal.get();
        Assert.assertTrue(singleLotteryOrderDao.findAllByIds(Arrays.asList(e.getId())).size()>0);
    }

    @Test
    public void testFindLotteryCountByComsumer() throws Exception {
        SingleLotteryOrderEntity e=threadLocal.get();
        Assert.assertTrue(singleLotteryOrderDao.findLotteryCountByComsumer(e.getOperatingActivityId(),e.getAppId(),e.getConsumerId())>0);
    }

    @Test
    public void testFindCountByComsumerTime() throws Exception {
        SingleLotteryOrderEntity e=threadLocal.get();
        Assert.assertTrue(singleLotteryOrderDao.findCountByComsumerTime(e.getOperatingActivityId(),e.getAppId(),e.getConsumerId(),new Date(System.currentTimeMillis()-10000000l),new Date(System.currentTimeMillis()+1000000l))>0);
    }

    @Test
    public void testFindFrontLotteryList() throws Exception {
        SingleLotteryOrderEntity e=new SingleLotteryOrderEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(1);
        e.setOptionType(1);
        singleLotteryOrderDao.insert(e);
        Assert.assertTrue(singleLotteryOrderDao.findFrontLotteryList(e.getOperatingActivityId(),e.getAppId()).size()>0);
    }

    @Test
    public void testFindByAppAndDeveloperBizId() throws Exception {
        SingleLotteryOrderEntity e=threadLocal.get();
        assertDO(singleLotteryOrderDao.findByAppAndDeveloperBizId(e.getAppId(),e.getDeveloperBizId()),e);
    }

    @Test
    public void testFindByIds() throws Exception {
        SingleLotteryOrderEntity e=threadLocal.get();
        Assert.assertTrue(singleLotteryOrderDao.findByIds(Arrays.asList(e.getId())).size()>0);
    }

    @Test
    public void testFindByInOrderIds() throws Exception {
        SingleLotteryOrderEntity e=threadLocal.get();
        Assert.assertTrue(singleLotteryOrderDao.findByInOrderIds(Arrays.asList(e.getOrderId())).size()>0);
    }

    @Test
    public void testFindAllByLtGmtCreateAndExchangeStatus() throws Exception {
        SingleLotteryOrderEntity e=new SingleLotteryOrderEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(1);
        e.setGmtCreate(new Date(System.currentTimeMillis()-87000*1000l));
        singleLotteryOrderDao.insert(e);
        Assert.assertTrue(singleLotteryOrderDao.findAllByLtGmtCreateAndExchangeStatus().size()>0);
    }

    @Test
    public void testFindFailCountByOperatingActivityIds() throws Exception {
        SingleLotteryOrderEntity e=new SingleLotteryOrderEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(3);
        singleLotteryOrderDao.insert(e);
        Assert.assertTrue(singleLotteryOrderDao.findFailCountByOperatingActivityIds(Arrays.asList(e.getOperatingActivityId())).size()>0);
    }

    @Test
    public void testFindByLimit() throws Exception {
        SingleLotteryOrderEntity e=threadLocal.get();
        Map<String,Object> params=new HashMap<>();
        params.put("operationActivityId",e.getOperatingActivityId());
        params.put("startTime",DateUtils.minutesAddOrSub(e.getGmtCreate(),-10));
        params.put("endTime",DateUtils.minutesAddOrSub(e.getGmtCreate(),10));
        params.put("start",0);
        params.put("pageSize",10);
        Assert.assertTrue(singleLotteryOrderDao.findByLimit(params).size()>0);
    }

    @Test
    public void testFindByCount() throws Exception {
        SingleLotteryOrderEntity e=threadLocal.get();
        Map<String,Object> params=new HashMap<>();
        params.put("operationActivityId",e.getOperatingActivityId());
        params.put("startTime",DateUtils.minutesAddOrSub(e.getGmtCreate(),-10));
        params.put("endTime",DateUtils.minutesAddOrSub(e.getGmtCreate(),10));
        Assert.assertTrue(singleLotteryOrderDao.findByCount(params)>0);
    }

    @Test
    public void testCountWintimesByOptionType() throws Exception {
        SingleLotteryOrderEntity e=new SingleLotteryOrderEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(1);
        singleLotteryOrderDao.insert(e);
        Assert.assertTrue(singleLotteryOrderDao.countWintimesByOptionType(Arrays.asList(e.getOperatingActivityId()),new Date(System.currentTimeMillis()-100000l),new Date(System.currentTimeMillis()+1000000l),e.getOptionType())>0);
    }

    @Test
    public void testGetWinningListByOperatingActivityIds() throws Exception {
        SingleLotteryOrderEntity e=new SingleLotteryOrderEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setOptionType(1);
        singleLotteryOrderDao.insert(e);
        Assert.assertTrue(singleLotteryOrderDao.getWinningListByOperatingActivityIds(Arrays.asList(e.getOperatingActivityId())).size()>0);
    }

    @Test
    public void testUpdateExchangeStatusToFail() throws Exception {
        SingleLotteryOrderEntity e=threadLocal.get();
        singleLotteryOrderDao.updateExchangeStatusToFail(e.getId(),"testadmin","testdev","testconsumer");
        SingleLotteryOrderEntity e1=singleLotteryOrderDao.find(e.getId());
        Assert.assertEquals(e1.getError4admin(),"testadmin");
    }

    @Test
    public void testUpdateExchangeStatusToOverdue() throws Exception {
        SingleLotteryOrderEntity e=new SingleLotteryOrderEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(1);
        singleLotteryOrderDao.insert(e);
        singleLotteryOrderDao.updateExchangeStatusToOverdue(e.getId(),"testadmin","testdev","testconsumer");
        SingleLotteryOrderEntity e1=singleLotteryOrderDao.find(e.getId());
        Assert.assertEquals(e1.getError4admin(),"testadmin");
    }

    @Test
    public void testUpdateStatusToSuccess() throws Exception {
        SingleLotteryOrderEntity e=threadLocal.get();
        singleLotteryOrderDao.updateStatusToSuccess(e.getId());
        SingleLotteryOrderEntity e1=singleLotteryOrderDao.find(e.getId());
        Assert.assertTrue(e1.getStatus().equals(SingleLotteryOrderDto.StatusSuccess));
    }

    @Test
    public void testUpdateStatusToFail() throws Exception {
        SingleLotteryOrderEntity e=threadLocal.get();
        singleLotteryOrderDao.updateStatusToFail(e.getId(),"testadmin","testdev","testconsumer");
        SingleLotteryOrderEntity e1=singleLotteryOrderDao.find(e.getId());
        Assert.assertTrue(e1.getStatus().equals(SingleLotteryOrderDto.StatusFail));
    }

    @Test
    public void testUpdatePrizeTypeToThanks() throws Exception {
        SingleLotteryOrderEntity e=threadLocal.get();
        singleLotteryOrderDao.updatePrizeTypeToThanks(e.getId());
        SingleLotteryOrderEntity e1=singleLotteryOrderDao.find(e.getId());
        Assert.assertTrue(e1.getStatus().equals(SingleLotteryOrderDto.StatusSuccess));
    }

    @Test
    public void testDoTakePrize() throws Exception {
        SingleLotteryOrderEntity e=new SingleLotteryOrderEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(1);
        e.setOrderId(null);
        singleLotteryOrderDao.insert(e);
        Assert.assertTrue(singleLotteryOrderDao.doTakePrize(e.getId())>0);
    }

    @Test
    public void testRollbackTakePrize() throws Exception {
        SingleLotteryOrderEntity e=new SingleLotteryOrderEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(2);
        e.setOrderId(null);
        singleLotteryOrderDao.insert(e);
        Assert.assertTrue(singleLotteryOrderDao.rollbackTakePrize(e.getId())>0);
    }

    @Test
    public void testUpdateLotteryResult() throws Exception {
        SingleLotteryOrderEntity e=threadLocal.get();
        singleLotteryOrderDao.updateLotteryResult(e.getId(),e.getAppItemId()+1,e.getItemId(),e.getPrizeName(),e.getPrizeType(),e.getPrizeDegree(),e.getPrizeFacePrice(),e.getOptionType(),e.getCouponId()+1);
        SingleLotteryOrderEntity e1=singleLotteryOrderDao.find(e.getId());
        Assert.assertTrue(e.getAppItemId().equals(e1.getAppItemId()-1));
    }

    @Test
    public void testUpdateDeveloperBizId() throws Exception {
        SingleLotteryOrderEntity e=threadLocal.get();
        singleLotteryOrderDao.updateDeveloperBizId(e.getId(),"testBiz");
        SingleLotteryOrderEntity e1=singleLotteryOrderDao.find(e.getId());
        Assert.assertEquals(e1.getDeveloperBizId(),"testBiz");
    }

    @Test
    public void testupdateMainOrderId() throws Exception {
        SingleLotteryOrderEntity e=threadLocal.get();
        singleLotteryOrderDao.updateMainOrderId(e.getId(),1l);
        SingleLotteryOrderEntity e1=singleLotteryOrderDao.find(e.getId());
        Assert.assertTrue(e1.getOrderId().equals(1l));
    }

    private void assertDO(SingleLotteryOrderEntity e, SingleLotteryOrderEntity e1){
        assertDO(e,e1,null);
    }

    private void assertDO(SingleLotteryOrderEntity e, SingleLotteryOrderEntity e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","orderNum","partnerUserId","winningNum"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }
}
