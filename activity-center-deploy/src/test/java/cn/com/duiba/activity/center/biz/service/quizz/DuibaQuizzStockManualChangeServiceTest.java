package cn.com.duiba.activity.center.biz.service.quizz;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.quizz.DuibaQuizzStockManualChangeDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ)
public class DuibaQuizzStockManualChangeServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaQuizzStockManualChangeService duibaQuizzStockManualChangeService;
	
	private DuibaQuizzStockManualChangeDto info;
	
	@Before
	public void addTest(){
		info = TestUtils.createRandomBean(DuibaQuizzStockManualChangeDto.class);
		duibaQuizzStockManualChangeService.add(info);
	}
	
	@Test
	public void findByStockIdTest(){
		List<DuibaQuizzStockManualChangeDto> list = duibaQuizzStockManualChangeService.findByStockId(info.getQuizzStockId());
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void addBatchTest(){
		List<DuibaQuizzStockManualChangeDto> list = new ArrayList<DuibaQuizzStockManualChangeDto>();
		DuibaQuizzStockManualChangeDto e1 = TestUtils.createRandomBean(DuibaQuizzStockManualChangeDto.class);
		list.add(e1);
		duibaQuizzStockManualChangeService.addBatch(list);
		List<DuibaQuizzStockManualChangeDto> entityList = duibaQuizzStockManualChangeService.findByStockId(e1.getQuizzStockId());
		Assert.assertTrue(entityList.size() > 0);
	}

    
}
