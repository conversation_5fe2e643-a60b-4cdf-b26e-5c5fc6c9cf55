package cn.com.duiba.activity.center.biz.dao.ngame;

import java.util.Date;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.ngame.NgameOrdersExtendEntity;

@Transactional(DsConstants.DATABASE_NGAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class NgameOrdersExtendDaoTest extends TransactionalTestCaseBase {

	@Resource
	private NgameOrdersExtendDao ngameOrdersExtendDao;

	@Test
	public void findGameDataTest() {
		System.out.println(ngameOrdersExtendDao.findGameData(2l));
	}

	@Test
	public void findTest() {
		System.out.println(ngameOrdersExtendDao.find(1l).getGameData());
	}

	@Test
	public void insertTest() {
		NgameOrdersExtendEntity e = new NgameOrdersExtendEntity();
		e.setGameOrderId(222l);
		e.setGmtCreate(new Date());
		e.setGmtModified(new Date());
		ngameOrdersExtendDao.insert(e);
	}

	@Test
	public void updateGameDataTest() {
		System.out.println(ngameOrdersExtendDao.updateGameData(3l,"[{\"num\":1,\"time\":2927,\"xy\":\"280,518\"}]"));
	}
}
