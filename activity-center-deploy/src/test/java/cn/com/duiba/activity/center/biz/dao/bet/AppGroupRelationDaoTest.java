package cn.com.duiba.activity.center.biz.dao.bet;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.bet.AppGroupRelationEntity;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 * @date 2018/05/03
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class AppGroupRelationDaoTest extends TransactionalTestCaseBase {
    @Autowired
    private AppGroupRelationDao appGroupRelationDao;

    private AppGroupRelationEntity appGroupRelationEntity;

    @Before
    public void setup() {
        appGroupRelationEntity = new AppGroupRelationEntity();
        appGroupRelationEntity.setAppId(1L);
        appGroupRelationEntity.setGroupId(2L);

        appGroupRelationEntity.setId(appGroupRelationDao.insert(appGroupRelationEntity));
    }

    @Test
    public void shouldInsertCorrectly() {
        assertThat(appGroupRelationEntity.getId()).isNotNull().isGreaterThan(0);
    }

    @Test
    public void shouldBatchInsertCorrectly() {
        AppGroupRelationEntity entity = new AppGroupRelationEntity();
        entity.setAppId(2L);
        entity.setGroupId(3L);
        AppGroupRelationEntity entity1 = new AppGroupRelationEntity();
        entity1.setGroupId(3L);
        entity1.setAppId(4L);
        List<AppGroupRelationEntity> appGroupRelationEntityList =
                appGroupRelationDao.batchInsert(Arrays.asList(entity1, entity));

        assertThat(appGroupRelationEntityList).isNotNull();
        assertThat(appGroupRelationEntityList.size()).isEqualTo(2);
        assertThat(appGroupRelationEntityList.get(0).getId()).isGreaterThan(0);
        assertThat(appGroupRelationEntityList.get(1).getId()).isGreaterThan(0);
    }

    @Test
    public void shouldDeleteCorrectly() {
        assertThat(appGroupRelationDao.delete(appGroupRelationEntity.getId())).isEqualTo(1);
    }

    @Test
    public void shouldListByGroupIdCorrectly() {
        List<AppGroupRelationEntity> appGroupRelationEntityList =
                appGroupRelationDao.listByGroupId(appGroupRelationEntity.getGroupId(), 1, 1);

        assertThat(appGroupRelationEntityList).isNotNull();
        assertThat(appGroupRelationEntityList.size()).isEqualTo(1);
    }

    @Test
    public void shouldDeleteByGroupIdCorrectly() {
        assertThat(appGroupRelationDao.deleteByGroupId(appGroupRelationEntity.getGroupId())).isEqualTo(1);
    }

    @Test
    public void shouldListByAppIdCorrectly() {
        List<AppGroupRelationEntity> appGroupRelationEntityList =
                appGroupRelationDao.listByAppId(appGroupRelationEntity.getAppId());

        assertThat(appGroupRelationEntityList).isNotNull();
    }
}
