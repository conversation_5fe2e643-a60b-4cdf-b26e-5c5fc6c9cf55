package cn.com.duiba.activity.center.api.remoteservice.anticheat;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by zzy on 2017/6/5.
 */
//@Transactional(DsConstants.DATABASE_CREDITS)
public class RemoteExchangeLimitServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteExchangeLimitService remoteExchangeLimitService;

//    @Before
//    public void prepareData() {
//        remoteExchangeLimitService.createExchangeLimitRecord(1L, 123L, 234L, "QB", "446205922");
//    }

    @Test
    public void testcheckExchangeLimit() {
        Long ll = remoteExchangeLimitService.checkExchangeLimit(1L, 123L, "QB", "446205922").getResult();
        System.out.println(ll);
    }

    @Test
    public void testcheckExchangeLimitType() {
        remoteExchangeLimitService.checkExchangeLimitType(1L, 123L, "QB", "446205922", "sign").getResult();
    }

    @Test
    public void testcreateExchangeLimitRecordType() {
        remoteExchangeLimitService.createExchangeLimitRecordType(1L, 123L, 1l, "QB", "446205922", "sign");
    }

    @Test
    public void testdataBackExchangeLimitType() {
        remoteExchangeLimitService.dataBackExchangeLimitType(1L, "QB", 123L, "446205922", "sign");
    }

    @Test
    public void testdataBackExchangeLimit() {

    }

    @Test
    public void testRefactor(){
        remoteExchangeLimitService.createExchangeLimitRecord(2L, 123L, 234L, "QB", "446205922");
        remoteExchangeLimitService.createExchangeLimitRecordType(2L, 123L, 1l, "QB", "446205922", "test");

    }
}
