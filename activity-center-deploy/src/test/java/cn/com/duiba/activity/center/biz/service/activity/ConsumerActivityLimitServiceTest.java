package cn.com.duiba.activity.center.biz.service.activity;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/1/9.
 */
//@Transactional(DsConstants.DATABASE_CKVTABLE)
public class ConsumerActivityLimitServiceTest  extends TransactionalTestCaseBase {
	@Autowired
	private ConsumerActivityLimitService consumerActivityLimitService;

	@Test
	public void testFindConsumerJoinNumToday(){
		consumerActivityLimitService.findConsumerJoinNumToday(1L,1L);
	}

	@Test
	public void findConsumerJoinPlugdrawNum(){
		Integer number = consumerActivityLimitService.findConsumerJoinPlugdrawNum(1L, "plugdraw",1L);
		System.out.println(number);
	}

	@Test
	public void incrConsumerJoinPlugdrawNum(){
		Boolean result = consumerActivityLimitService.incrConsumerJoinPlugdrawNum(1L, "plugdraw",1L);
		System.out.println(result);
		Assert.assertTrue(result);
	}

	@Test
	public void testFindConsumerJoinPlugdrawNumEveryDay(){
		Integer number = consumerActivityLimitService.findConsumerJoinPlugdrawNumEveryDay(1L, "plugdraw",1L);
		System.out.println(number);
	}

	@Test
	public void testIncrConsumerJoinPlugdrawNumEveryDay(){
		Boolean result = consumerActivityLimitService.incrConsumerJoinPlugdrawNumEveryDay(1L, "plugdraw",1L);
		System.out.println(result);
		Assert.assertTrue(result);
	}

	@Test
	public void testFindConsumerJoinPlugdrawNumEveryWeek(){
		Integer number = consumerActivityLimitService.findConsumerJoinPlugdrawNumEveryWeek(1L, "plugdraw",1L);
		System.out.println(number);
	}

	@Test
	public void testIncrConsumerJoinPlugdrawNumEveryWeek(){
		Boolean result = consumerActivityLimitService.incrConsumerJoinPlugdrawNumEveryWeek(1L, "plugdraw",1L);
		System.out.println(result);
		Assert.assertTrue(result);
	}

	@Test
	public void testFindConsumerJoinPlugdrawNumEveryMonth(){
		Integer number = consumerActivityLimitService.findConsumerJoinPlugdrawNumEveryMonth(1L, "plugdraw",1L);
		System.out.println(number);
	}

	@Test
	public void testIncrConsumerJoinPlugdrawNumEveryMonth(){
		Boolean result = consumerActivityLimitService.incrConsumerJoinPlugdrawNumEveryMonth(1L, "plugdraw",1L);
		System.out.println(result);
		Assert.assertTrue(result);
	}

	@Test
	public void testIncrConsumerJoinPlugdrawNumAll(){
		Boolean result = consumerActivityLimitService.incrConsumerJoinPlugdrawNumAll(1L, "plugdraw",1L);
		System.out.println(result);
		Assert.assertTrue(result);
	}
}
