package cn.com.duiba.activity.center.biz.service.singlelottery;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.singlelottery.SingleLotteryAppSpecifyDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/20.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class DuibaSingleLotteryAppSpecifyServiceTest extends TransactionalTestCaseBase{
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS;

    @Resource
    private DuibaSingleLotteryAppSpecifyService duibaSingleLotteryAppSpecifyService;

//    @Resource
//    private OperatingActivityDao operatingActivityDao;


    private SingleLotteryAppSpecifyDto e;

    @Before
    public void testInsert(){
        e=getInsertAbleAppDO();
        e.setRemaining(100);
        duibaSingleLotteryAppSpecifyService.insertAppSpecify(e);
    }

    @Test
    public void testFindAllSpecifyByDuibaSingleLottery() {
        List<SingleLotteryAppSpecifyDto> list= duibaSingleLotteryAppSpecifyService.findAllSpecifyByDuibaSingleLottery(e.getDuibaSingleLotteryId());
        boolean isfind=false;
        for(SingleLotteryAppSpecifyDto s:list){
            if(s.getId().equals(e.getId())){
                isfind=true;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindSpecifyById() {
        SingleLotteryAppSpecifyDto e1= duibaSingleLotteryAppSpecifyService.findSpecifyById(e.getId());
        assertAppDO(e,e1);
    }

    @Test
    public void testFindSpecifyForupdate() {
        SingleLotteryAppSpecifyDto e1= duibaSingleLotteryAppSpecifyService.findSpecifyForupdate(e.getId());
        assertAppDO(e,e1);
    }

    @Test
    public void testReduceSpecifyAppRemaining() {
        duibaSingleLotteryAppSpecifyService.reduceSpecifyAppRemaining(e.getDuibaSingleLotteryId(),e.getAppId());
        SingleLotteryAppSpecifyDto e1= duibaSingleLotteryAppSpecifyService.findSpecifyById(e.getId());
        Assert.assertEquals(e.getRemaining(),(Integer)(e1.getRemaining()+1));
    }

    @Test
    public void testAddSpecifyAppRemaining() {
        duibaSingleLotteryAppSpecifyService.addSpecifyAppRemaining(e.getDuibaSingleLotteryId(),e.getAppId());
        SingleLotteryAppSpecifyDto e1= duibaSingleLotteryAppSpecifyService.findSpecifyById(e.getId());
        Assert.assertEquals(e.getRemaining(),(Integer)(e1.getRemaining()-1));
    }

    @Test
    public void testAddSpecifyOrderCount() {
        duibaSingleLotteryAppSpecifyService.addSpecifyOrderCount(e.getDuibaSingleLotteryId(),e.getAppId());
        SingleLotteryAppSpecifyDto e1= duibaSingleLotteryAppSpecifyService.findSpecifyById(e.getId());
        Assert.assertEquals(e.getOrderCount(),(Integer)(e1.getOrderCount()-1));
    }

    @Test
    public void testDeleteSpecify() {
        duibaSingleLotteryAppSpecifyService.deleteSpecify(e.getId());
        Assert.assertNull(duibaSingleLotteryAppSpecifyService.findSpecifyById(e.getId()));
    }

    @Test
    public void testInsertAppSpecify() {
        SingleLotteryAppSpecifyDto e1= duibaSingleLotteryAppSpecifyService.findSpecifyById(e.getId());
        assertAppDO(e,e1);
    }

    @Test
    public void testUpdateSpecifyRemaining() {
        duibaSingleLotteryAppSpecifyService.updateSpecifyRemaining(e.getId(),42);
        SingleLotteryAppSpecifyDto e1= duibaSingleLotteryAppSpecifyService.findSpecifyById(e.getId());
        Assert.assertEquals(e1.getRemaining(),new Integer(42));
    }

    @Test
    public void testFindSpecifyByDuibaSingleLotteryAndApp() {
        SingleLotteryAppSpecifyDto dto = duibaSingleLotteryAppSpecifyService.findSpecifyByDuibaSingleLotteryAndApp(e.getDuibaSingleLotteryId(),e.getAppId());
        Assert.assertEquals(dto.getRemaining(),e.getRemaining());

        duibaSingleLotteryAppSpecifyService.deleteSpecify(e.getId());
        dto = duibaSingleLotteryAppSpecifyService.findSpecifyByDuibaSingleLotteryAndApp(e.getDuibaSingleLotteryId(),e.getAppId());
        Assert.assertNull(dto);
    }

    @Test
    public void testFindSpecifyByDuibaSingleLotterysAndApp() {
        Map<Long, SingleLotteryAppSpecifyDto> map = duibaSingleLotteryAppSpecifyService.findSpecifyByDuibaSingleLotterysAndApp(Collections.singletonList(e.getDuibaSingleLotteryId()),e.getAppId());
        Assert.assertTrue(!map.isEmpty());

        duibaSingleLotteryAppSpecifyService.deleteSpecify(e.getId());
        map = duibaSingleLotteryAppSpecifyService.findSpecifyByDuibaSingleLotterysAndApp(Collections.singletonList(e.getDuibaSingleLotteryId()),e.getAppId());
        Assert.assertTrue(map.isEmpty());
    }

    private SingleLotteryAppSpecifyDto getInsertAbleAppDO(){
        SingleLotteryAppSpecifyDto e=new SingleLotteryAppSpecifyDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        return e;
    }

    private void assertAppDO(SingleLotteryAppSpecifyDto e,SingleLotteryAppSpecifyDto e1){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified"}));

        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }

}
