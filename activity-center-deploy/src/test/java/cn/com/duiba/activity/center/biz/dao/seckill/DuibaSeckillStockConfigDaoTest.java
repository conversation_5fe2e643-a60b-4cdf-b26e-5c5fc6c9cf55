package cn.com.duiba.activity.center.biz.dao.seckill;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.seckill.DuibaSeckillStockConfigEntity;
import cn.com.duiba.wolf.utils.TestUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * Created by wenqi.huang on 2016/11/24.
 */
@Transactional(DsConstants.DATABASE_SECKILL)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaSeckillStockConfigDaoTest extends TransactionalTestCaseBase{

    @Resource
    private DuibaSeckillStockConfigDao duibaSeckillStockConfigDao;

    private ThreadLocal<DuibaSeckillStockConfigEntity> entity = new ThreadLocal<>();

    @Before
    public void testInsert() {
        DuibaSeckillStockConfigEntity config = TestUtils.createRandomBean(DuibaSeckillStockConfigEntity.class);
        config.setAppId(1002L);
        config.setDeleted(false);
        config.setStock(0);
        config.setLimitStock(0);
        duibaSeckillStockConfigDao.insert(config);
        entity.set(config);
    }

    @Test
    public void testFind() {
        DuibaSeckillStockConfigEntity e = duibaSeckillStockConfigDao.find(entity.get().getId());
        assertEntityEquals(e, entity.get());
    }

    @Test
    public void testFindForUpdate() {
        DuibaSeckillStockConfigEntity e = duibaSeckillStockConfigDao.findForUpdate(entity.get().getId());
        assertEntityEquals(e, entity.get());
    }

    @Test
    public void testFindByUnique() {
        DuibaSeckillStockConfigEntity e = entity.get();
        DuibaSeckillStockConfigEntity e1 = duibaSeckillStockConfigDao.findByUnique(e.getSeckillId(), e.getAppId());
        assertEntityEquals(e,e1);
    }

    private void assertEntityEquals(DuibaSeckillStockConfigEntity e, DuibaSeckillStockConfigEntity e1){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","day"}));
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()]));
    }

    @Test
    public void testConsumeStock() {
        DuibaSeckillStockConfigEntity e = entity.get();
        duibaSeckillStockConfigDao.setConfigStock(e.getId(), 10);
        DuibaSeckillStockConfigEntity e1 = duibaSeckillStockConfigDao.find(entity.get().getId());
        Assert.assertTrue(e.getStock().equals(e1.getStock() - 10));
        duibaSeckillStockConfigDao.setConfigStock(e.getId(), -1);
        DuibaSeckillStockConfigEntity e2 = duibaSeckillStockConfigDao.find(entity.get().getId());
        Assert.assertTrue(e1.getStock().equals(e2.getStock() + 1));
        duibaSeckillStockConfigDao.consumeStock(e.getId());
        DuibaSeckillStockConfigEntity e3 = duibaSeckillStockConfigDao.find(entity.get().getId());
        Assert.assertTrue(e2.getStock().equals(e3.getStock() + 1));
    }

    @Test
    public void testFindBySeckillId() {
        DuibaSeckillStockConfigEntity e = entity.get();
        List<DuibaSeckillStockConfigEntity> list = duibaSeckillStockConfigDao.findBySeckillId(e.getSeckillId());
        Assert.assertTrue(list.size() > 0);
    }

    @Test
    public void testGetStockConfigMaxPayload() {
        DuibaSeckillStockConfigEntity e = entity.get();
        duibaSeckillStockConfigDao.getStockConfigMaxPayload(e.getSeckillId());
    }

    @Test
    public void testFindBySeckillIdAndAppIds() {
        DuibaSeckillStockConfigEntity e = entity.get();
        List<Long> appIds = new ArrayList<>();
        appIds.add(e.getAppId());
        List<DuibaSeckillStockConfigEntity> list = duibaSeckillStockConfigDao.findBySeckillIdAndAppIds(e.getSeckillId(), appIds);
        Assert.assertTrue(list.size() > 0);
    }

    @Test
    public void testInvocationStockConfig() {
        DuibaSeckillStockConfigEntity e = entity.get();
        e.setPayload(10);
        duibaSeckillStockConfigDao.invocationStockConfig(e);
        DuibaSeckillStockConfigEntity e1 = duibaSeckillStockConfigDao.find(e.getId());
        Assert.assertEquals(e.getPayload(), e1.getPayload());
        Assert.assertTrue(e1.getDeleted() == false);
    }

    @Test
    public void testMarkDelStockConfig() {
        DuibaSeckillStockConfigEntity e = entity.get();
        e.setPayload(120);
        duibaSeckillStockConfigDao.markDelStockConfig(e);
        DuibaSeckillStockConfigEntity e1 = duibaSeckillStockConfigDao.find(e.getId());
        Assert.assertEquals(e.getPayload(), e1.getPayload());
        Assert.assertTrue(e1.getDeleted() == true);
    }

    @Test
    public void testSetDay() {
        DuibaSeckillStockConfigEntity e = entity.get();
        duibaSeckillStockConfigDao.setDay(e.getId(), new Date());
    }

    @Test
    public void testSetPayload() {
        DuibaSeckillStockConfigEntity e = entity.get();
        e.setPayload(120);
        duibaSeckillStockConfigDao.setPayload(e.getId(),e.getPayload());
        DuibaSeckillStockConfigEntity e1 = duibaSeckillStockConfigDao.find(e.getId());
        Assert.assertEquals(e.getPayload(), e1.getPayload());
    }

    @Test
    public void testSetLimitStock() {
        Integer limitStock = 100;
        duibaSeckillStockConfigDao.setLimitStock(entity.get().getId(), limitStock);
        DuibaSeckillStockConfigEntity e1 = duibaSeckillStockConfigDao.find(entity.get().getId());
        Assert.assertEquals(limitStock, e1.getLimitStock());
    }

    @Test
    public void testFindConfigBySecKillIdAndAppId() {
        DuibaSeckillStockConfigEntity e = entity.get();
        DuibaSeckillStockConfigEntity e1 = duibaSeckillStockConfigDao.findConfigBySecKillIdAndAppId(e.getSeckillId(), e.getAppId());
        Assert.assertNotNull(e1);
    }

    @Test
    public void testFindBySecKillIdForUpdate() {
        DuibaSeckillStockConfigEntity e = entity.get();
        List<DuibaSeckillStockConfigEntity> list = duibaSeckillStockConfigDao.findBySecKillIdForUpdate(e.getSeckillId());
        Assert.assertTrue(list.size()>0);
    }
}
