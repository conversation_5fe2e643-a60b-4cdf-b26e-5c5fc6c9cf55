package cn.com.duiba.activity.center.biz.service.bet;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.bet.ActivityBudgetCheckDto;
import cn.com.duiba.activity.center.api.dto.bet.BetConfigDto;
import cn.com.duiba.activity.center.api.enums.AttributionTypeEnum;
import cn.com.duiba.activity.center.api.enums.BetOpenAwardTypeEnum;
import cn.com.duiba.activity.center.api.enums.BudgetBizTypeEnum;
import cn.com.duiba.activity.center.api.params.ActivityBudgetCheckQueryParam;
import cn.com.duiba.activity.center.api.remoteservice.bet.RemoteActivityBudgetCheckService;
import cn.com.duiba.activity.center.api.remoteservice.betv2.RemoteBetV2ConfigService;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.kvtable.service.api.dto.DuibaKvtableDto;
import cn.com.duiba.kvtable.service.api.enums.ActCenterHBaseKeyEnum;
import cn.com.duiba.kvtable.service.api.enums.HbaseKeySpaceEnum;
import cn.com.duiba.kvtable.service.api.params.HbaseVKeyParam;
import cn.com.duiba.kvtable.service.api.remoteservice.hbase.RemoteDuibaKvtableService;
import cn.com.duiba.wolf.utils.DateUtils;
import com.alibaba.fastjson.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 * @date 2018/05/02
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class BetConfigServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private BetConfigService betConfigService;
    @Autowired
    private RemoteBetV2ConfigService remoteBetV2ConfigService;
    @Autowired
    private RemoteDuibaKvtableService remoteDuibaKvtableService;

    @Autowired
    private RemoteActivityBudgetCheckService remoteActivityBudgetCheckService;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String,Long> redisTemplate;

    @Before
    public void setup() {
    }



    @Test
    public void shouldRight() {
        HbaseVKeyParam param1 = new HbaseVKeyParam();
        param1.setConsumerId(1L);
        param1.setVkey(ActCenterHBaseKeyEnum.K02.toString());
        param1.setKeySpaceEnum(HbaseKeySpaceEnum.K03);

        HbaseVKeyParam param2 = new HbaseVKeyParam();
        param2.setConsumerId(2L);
        param2.setVkey(ActCenterHBaseKeyEnum.K02.toString());
        param2.setKeySpaceEnum(HbaseKeySpaceEnum.K03);

        List<DuibaKvtableDto> list = remoteDuibaKvtableService.batchFindByVkeys(Arrays.asList(param1, param2));
        assertThat(list.size()).isEqualTo(0);
    }
    @Test
    public void shouldListCorrectly() throws BizException {
        remoteBetV2ConfigService.doDraw();
    }

    @Test
    public void insert() throws BizException {
        BetConfigDto dto=new BetConfigDto();
        dto.setOpenPrizeStatus(null);
        dto.setAppId(1L);
        dto.setBonusAmount("122");
        dto.setCreditsValue("0.1");
        dto.setShareExp(3221L);
        dto.setDrawType(BetOpenAwardTypeEnum.BET_ON_TIME);
        dto.setEndTime(new Date());
        dto.setAttributionType(AttributionTypeEnum.DEVELOPER.getCode());
        dto.setRightAnswerOptionId(1L);
        dto.setConfigStatus(3);
        dto.setTitle("zeghaun");
        dto.setWinningBetTimes(99999);
        dto.setEndTime(new Date());
        dto.setBetType(11);
        dto.setBonusType(1);
        dto.setConfigStatus(4);
        System.out.println(JSONObject.toJSONString(betConfigService.insert(dto)));
    }

    @Test
    public void executePipelined() throws BizException {
        redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            RedisSerializer<Object> keyS = (RedisSerializer<Object>) redisTemplate
                    .getKeySerializer();
                connection.zIncrBy(keyS.serialize("liukai2"),-12,keyS.serialize(123));
                connection.zIncrBy(keyS.serialize("liukai2"),-12,keyS.serialize(999));
                return null;
        });
        System.out.println(JSONObject.toJSONString(redisTemplate.opsForZSet().rangeByScore("liukai2",0,10200)));
    }



    @Test
    public void ActivityBudgetCheckServiceinsert() throws BizException {
        ActivityBudgetCheckDto activityBudgetCheckDto = new ActivityBudgetCheckDto();
        activityBudgetCheckDto.setBizType(BudgetBizTypeEnum.PK);
        activityBudgetCheckDto.setStartTime(DateUtils.getSecondDate("2019-01-12 12:12:12"));
        activityBudgetCheckDto.setEndTime(DateUtils.getSecondDate("2019-02-12 12:12:12"));
        activityBudgetCheckDto.setBonusSize(100);
        activityBudgetCheckDto.setBonusAmount(10000);
        activityBudgetCheckDto.setAppId(111111L);
        activityBudgetCheckDto.setBizId(123L);
        int a = remoteActivityBudgetCheckService.insert(activityBudgetCheckDto);
        System.out.println(a);
    }


    @Test
    public void existBudgetCheck() throws BizException {
        ActivityBudgetCheckQueryParam activityBudgetCheckQueryParam = new ActivityBudgetCheckQueryParam();
        activityBudgetCheckQueryParam.setEndTime(DateUtils.getSecondDate("2019-02-12 12:12:12"));
        activityBudgetCheckQueryParam.setStartTime(DateUtils.getSecondDate("2019-01-12 12:12:12"));
        activityBudgetCheckQueryParam.setBonusSize(100);
        activityBudgetCheckQueryParam.setBonusAmount(10000);
        activityBudgetCheckQueryParam.setAppId(111111L);
        activityBudgetCheckQueryParam.setBizId(123L);
        activityBudgetCheckQueryParam.setBizType(BudgetBizTypeEnum.PK);
        Boolean a = false;
        try {
              a = remoteActivityBudgetCheckService.existBudgetCheck(activityBudgetCheckQueryParam);
        } catch (BizException e) {
            e.printStackTrace();
        }
        System.out.println(a);
    }

}
