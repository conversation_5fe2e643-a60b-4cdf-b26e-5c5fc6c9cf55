package cn.com.duiba.activity.center.biz.service.guess;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.guess.DuibaGuessStockDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.guess.DuibaGuessStockDao;
import cn.com.duiba.activity.center.biz.entity.guess.DuibaGuessStockEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class DuibaGuessStockServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaGuessStockService duibaGuessStockService;
	@Autowired
	private DuibaGuessStockDao duibaGuessStockDao;
	
	private DuibaGuessStockDto info;
	
	@Before
	public void addTest(){
		info = TestUtils.createRandomBean(DuibaGuessStockDto.class);
		duibaGuessStockService.add(info);
	}
	
	@Test
	public void findByIdTest(){
		DuibaGuessStockEntity e = duibaGuessStockDao.findById(info.getId());
		Assert.assertNotNull(e);
	}
	
	@Test
	public void findRemainingTest(){
		DuibaGuessStockDto e = duibaGuessStockService.findRemaining(info.getGuessOptionId());
		Assert.assertNotNull(e);
	}
	
	@Test
	public void subStockTest(){
		int subNumber = info.getStock() - 1;
		duibaGuessStockService.subStock(info.getId(), subNumber);
		int stock = duibaGuessStockDao.findById(info.getId()).getStock();
		Assert.assertTrue(stock == (info.getStock() - subNumber));
	}
	
	@Test
	public void addStockTest(){
		DuibaGuessStockDto e = TestUtils.createRandomBean(DuibaGuessStockDto.class);
		duibaGuessStockService.addStock(info.getId(), e.getStock());
		int stock = duibaGuessStockDao.findById(info.getId()).getStock();
		Assert.assertTrue(stock == (info.getStock() + e.getStock()));
	}
	
	//from manager
	@Test
	public void findByGuessOptionIdTest(){
		DuibaGuessStockDto e = duibaGuessStockService.findByGuessOptionId(info.getGuessOptionId());
		Assert.assertNotNull(e);
	}

	@Test
	public void findByGuessOptionIdsTest(){
		List<Long> list = new ArrayList<Long>();
		list.add(info.getGuessOptionId());
		List<DuibaGuessStockDto> listEntity = duibaGuessStockService.findByGuessOptionIds(list);
		Assert.assertTrue(listEntity.size() > 0);
	}

	/**
	 * 增加库存
	 * @param id
	 * @param stockAdd
	 * @return
	 */
	@Test
	public void updateStockAddTest(){
		DuibaGuessStockDto e = TestUtils.createRandomBean(DuibaGuessStockDto.class);
		duibaGuessStockService.addStock(info.getId(), e.getStock());
		int stock = duibaGuessStockDao.findById(info.getId()).getStock();
		Assert.assertTrue(stock == (info.getStock() + e.getStock()));
	}
	/**
	 * 减少库存
	 * @param id
	 * @param stockAdd
	 * @return
	 */
	@Test
	public void updateStockSubTest(){
		int subNumber = info.getStock() - 1;
		duibaGuessStockService.subStock(info.getId(), subNumber);
		int stock = duibaGuessStockDao.findById(info.getId()).getStock();
		Assert.assertTrue(stock == (info.getStock() - subNumber));
	}

	@Test
	public void addBatchTest(){
		DuibaGuessStockDto e1 = TestUtils.createRandomBean(DuibaGuessStockDto.class);
		DuibaGuessStockDto e2 = TestUtils.createRandomBean(DuibaGuessStockDto.class);
		List<DuibaGuessStockDto> list = new ArrayList<DuibaGuessStockDto>();
		list.add(e1);
		list.add(e2);
		list = duibaGuessStockService.addBatch(list);
		Assert.assertNotNull(list.get(0).getId());
		Assert.assertNotNull(list.get(1).getId());
	}

}
