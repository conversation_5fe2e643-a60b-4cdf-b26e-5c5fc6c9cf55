package cn.com.duiba.activity.center.api.remoteservice.singlelottery;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.service.exception.BusinessException;
import org.junit.Test;
import org.springframework.test.annotation.Rollback;

import javax.annotation.Resource;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/2/19 14:43
 * @description:
 */
@Rollback(value = false)
public class RemoteSingleLotteryStockConsumeServiceTest extends TransactionalTestCaseBase {

	@Resource
	private RemoteSingleLotteryStockConsumeService remoteSingleLotteryStockConsumeService;

	@Test
	public void testConsumeDuibaSingleLotteryStock() throws BusinessException{
		remoteSingleLotteryStockConsumeService.consumeDuibaSingleLotteryStock(10000L, 1L, 1, "100", "single");
	}

	@Test
	public void testConsumeDuibaSingleLotteryStockApi() throws BizException{
		remoteSingleLotteryStockConsumeService.consumeDuibaSingleLotteryStockApi(9290L, 1L, 1, "101", "single");
	}

	@Test
	public void testPaybackDuibaSingleLotteryStock() throws BusinessException{
		remoteSingleLotteryStockConsumeService.paybackDuibaSingleLotteryStock("101", "single");
	}

	@Test
	public void testPaybackDuibaSingleLotteryStockApi() throws BizException{
		remoteSingleLotteryStockConsumeService.paybackDuibaSingleLotteryStockApi("100", "single");
	}

	@Test
	public void testConsumeSingleLotterySpecifyStock() throws BusinessException{
		remoteSingleLotteryStockConsumeService.consumeSingleLotterySpecifyStock(9290L, 1L, "103", "single");
	}

	@Test
	public void testConsumeSingleLotterySpecifyStockApi() throws BizException{
		remoteSingleLotteryStockConsumeService.consumeSingleLotterySpecifyStockApi(9290L, 1L, "102", "single");
	}

	@Test
	public void testPaybackSingleLotterySpecifyStock() throws BusinessException{
		remoteSingleLotteryStockConsumeService.paybackSingleLotterySpecifyStock("102", "single");
	}

	@Test
	public void testPaybackSingleLotterySpecifyStockApi() throws BizException{
		remoteSingleLotteryStockConsumeService.paybackSingleLotterySpecifyStockApi("103", "single");
	}

	@Test
	public void testConsumeDevSingleLotteryStock() throws BusinessException{
		remoteSingleLotteryStockConsumeService.consumeDevSingleLotteryStock(3428L, 1L, "104", "single");
	}

	@Test
	public void testConsumeDevSingleLotteryStockApi() throws BizException{
		remoteSingleLotteryStockConsumeService.consumeDevSingleLotteryStockApi(3428L, 1L, "105", "single");
	}

	@Test
	public void testPabackDevSingleLoteryStock() throws BusinessException{
		remoteSingleLotteryStockConsumeService.pabackDevSingleLoteryStock("104", "single");
	}

	@Test
	public void testPabackDevSingleLoteryStockApi() throws BizException{
		remoteSingleLotteryStockConsumeService.paybackDevSingleLoteryStockApi("105", "single");
	}
}
