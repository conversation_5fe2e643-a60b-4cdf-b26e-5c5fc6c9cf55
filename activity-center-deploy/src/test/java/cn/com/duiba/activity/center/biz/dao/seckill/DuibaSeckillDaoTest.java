package cn.com.duiba.activity.center.biz.dao.seckill;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.seckill.DuibaSeckillEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created by yansen on 16/5/24.
 */
@Transactional(DsConstants.DATABASE_SECKILL)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaSeckillDaoTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaSeckillDao duibaSeckillDao;
    
    private DuibaSeckillEntity duibaSeckillDO;

    @Before
    public void testInsert() {
        DuibaSeckillEntity e=new DuibaSeckillEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(1);
        e.setDeleted(false);
        duibaSeckillDao.insert(e);
        duibaSeckillDO=e;
    }

    @Test
    public void testFind() {
        DuibaSeckillEntity e=duibaSeckillDO;
        DuibaSeckillEntity e1=duibaSeckillDao.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindAllOpenedByIds() {
        DuibaSeckillEntity e=duibaSeckillDO;
        List<Long> ids = new ArrayList<>();
        ids.add(e.getId());
        List<DuibaSeckillEntity> e1=duibaSeckillDao.findAllOpenedByIds(ids);
        assertDO(e,e1.get(0));
    }

    @Test
    public void testFindTagById() {
        DuibaSeckillEntity e=duibaSeckillDO;
        String tag=duibaSeckillDao.findTagById(e.getId());
        Assert.assertEquals(tag,e.getTag());
    }

    @Test
    public void testUpdateTagById() {
        DuibaSeckillEntity e=duibaSeckillDO;
        String tag = "fgla";
        duibaSeckillDao.updateTagById(e.getId(),tag);
        String tag1=duibaSeckillDao.findTagById(e.getId());
        Assert.assertEquals(tag,tag1);
    }

    @Test
    public void testUpdate() {
        DuibaSeckillEntity e=duibaSeckillDO;
        e.setTag("fad");
        duibaSeckillDao.update(e);
        String tag1=duibaSeckillDao.findTagById(e.getId());
        Assert.assertEquals(e.getTag(),tag1);
    }

    @Test
    public void testFindByPage() {
        Map<String,Object> params=new HashMap<>();
        params.put("title",duibaSeckillDO.getTitle());
        params.put("max",10);
        params.put("offset",0);
        Assert.assertTrue(duibaSeckillDao.findByPage(params).size()>0);
    }

    @Test
    public void testFindPageCount() {
        Map<String,Object> params=new HashMap<>();
        params.put("title",duibaSeckillDO.getTitle());
        params.put("max",10);
        params.put("offset",0);
        Assert.assertTrue(duibaSeckillDao.findPageCount()>0);
        Assert.assertTrue(duibaSeckillDao.findPageCount(params)>0);
    }

    @Test
    public void testUpdateStatus() {
        duibaSeckillDao.updateStatus(duibaSeckillDO.getId(),42);
        Assert.assertTrue(duibaSeckillDao.find(duibaSeckillDO.getId()).getStatus().equals(42));
    }

    @Test
    public void testDelete() {
        duibaSeckillDao.delete(duibaSeckillDO.getId());
        Assert.assertTrue(duibaSeckillDao.find(duibaSeckillDO.getId()).getDeleted());
    }

    @Test
    public void testUpdateAutoOffDateNull() {
        duibaSeckillDao.updateAutoOffDateNull(duibaSeckillDO.getId());
        Assert.assertNull(duibaSeckillDao.find(duibaSeckillDO.getId()).getAutoOffDate());
    }

    @Test
    public void testUpdateInfoForm() {
        duibaSeckillDO.setTitle("saf");
        Assert.assertTrue(duibaSeckillDao.updateInfoForm(duibaSeckillDO)>0);
    }

    @Test
    public void testUpdateSwitches() {
        duibaSeckillDao.updateSwitches(duibaSeckillDO.getId(),42l);
        Assert.assertTrue(duibaSeckillDao.find(duibaSeckillDO.getId()).getSwitches().equals(42));
    }

    @Test
    public void testFindByMap() {
        Map<String,Object> params=new HashMap<>();
        params.put("activityName",duibaSeckillDO.getTitle());
        params.put("max",10);
        params.put("offset",0);
        Assert.assertTrue(duibaSeckillDao.findByMap(params).size()>0);
    }

    @Test
    public void testFindAllByIds() {
        assertDO(duibaSeckillDO,duibaSeckillDao.findAllByIds(Arrays.asList(duibaSeckillDO.getId())).get(0));
    }

    @Test
    public void testFindAllSeckill() {
        Assert.assertTrue(duibaSeckillDao.findAllSeckill(1l).size()>0);
    }

    @Test
    public void testFindEffective() {
        DuibaSeckillEntity e=new DuibaSeckillEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(1);
        e.setDeleted(false);
        duibaSeckillDao.insert(e);
        Assert.assertTrue(duibaSeckillDao.findEffective().size()>0);
    }


    private void assertDO(DuibaSeckillEntity e, DuibaSeckillEntity e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaSeckillEntity e, DuibaSeckillEntity e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","dateStart","dateEnd","timeStart","timeEnd","deleted"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }
}
