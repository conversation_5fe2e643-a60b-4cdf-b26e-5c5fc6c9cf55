package cn.com.duiba.activity.center.biz.service.through.hk;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.hsbc.HsbcChanceOrderDao;
import cn.com.duiba.activity.center.biz.entity.hsbc.HsbcChanceOrderEntity;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
@Transactional(DsConstants.DATABASE_ACT_RECORD)
public class HsbcOrderChanceOrderTest extends TransactionalTestCaseBase {

    @Resource
    private HsbcChanceOrderDao hsbcChanceOrderDao;

    @Test
    public void test1(){
        HsbcChanceOrderEntity hsbcChanceOrderEntity = new HsbcChanceOrderEntity();
        hsbcChanceOrderDao.insert(hsbcChanceOrderEntity);
    }
}
