package cn.com.duiba.activity.center.api.remoteservice.creditsfarm;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.boot.exception.BizException;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;

import java.util.Date;
import java.util.Objects;

import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmActSaveParam;
import org.apache.commons.lang3.StringUtils;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

public class RemoteCreditsFarmBackendServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteCreditsFarmBackendService remoteCreditsFarmBackendService;
    public static final String key = "aaaa";
    public static final String value = "bbb";

    @Test
    public void saveCreditsFarm() throws BizException {

        CreditsFarmActSaveParam param = new CreditsFarmActSaveParam();
        param.setSaleTimes(0);
        param.setNonWechatShareSwitch(0);
        param.setNonRegistrationUrl("");
        param.setSeeds(Lists.newArrayList());
        param.setTasks(Lists.newArrayList());
        param.setOptions(Lists.newArrayList());
        param.setAppId(1L);
        param.setTitle("");
        param.setAppType("");
        param.setPlayType(0);
        param.setRule("");
        param.setPrizeCost(0);
        param.setDayCropLimit(0);
        param.setTakeBackRatio(0);
        param.setStartTime(new Date());
        param.setEndTime(new Date());
        param.setAuthorizeUrl("");
        param.setWechatQrCode("");
        param.setSmallImage("");
        param.setNutritionCountLimit(0);
        param.setSaleSwitch(false);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(key, value);
        param.setInterfaceConfig(jsonObject.toJSONString());

        Long id = remoteCreditsFarmBackendService.saveCreditsFarm(param, false);

        System.out.println(id);

        CreditsFarmActSaveParam creditsFarm = remoteCreditsFarmBackendService.getCreditsFarm(id);

        Assert.assertTrue(StringUtils.isNotBlank(creditsFarm.getInterfaceConfig()));

        JSONObject object = JSONObject.parseObject(creditsFarm.getInterfaceConfig());
        assertEquals(value, object.getString(key));

    }

    @Test
    public void getCreditsFarm() {
    }

    @Test
    public void findLatestCreatedAct() {

        CreditsFarmActSaveParam creditsFarm = remoteCreditsFarmBackendService.findLatestCreatedAct(1L);
        Assert.assertTrue(StringUtils.isNotBlank(creditsFarm.getInterfaceConfig()));

        JSONObject object = JSONObject.parseObject(creditsFarm.getInterfaceConfig());
        assertEquals(value, object.getString(key));
    }
}