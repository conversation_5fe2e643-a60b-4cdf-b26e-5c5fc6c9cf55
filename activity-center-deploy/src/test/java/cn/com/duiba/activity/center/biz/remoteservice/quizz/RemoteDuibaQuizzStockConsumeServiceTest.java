package cn.com.duiba.activity.center.biz.remoteservice.quizz;

import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.quizz.QuizzStockConsumeDto;
import cn.com.duiba.activity.center.api.remoteservice.quizz.RemoteDuibaQuizzStockConsumeService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ)
@Ignore
public class RemoteDuibaQuizzStockConsumeServiceTest extends TransactionalTestCaseBase{

	@Autowired
	private RemoteDuibaQuizzStockConsumeService duibaQuizzStockConsumeService;
	
	private QuizzStockConsumeDto info;
	
	private String[] exceptFields = new String[] { "gmtCreate", "gmtModified" };
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(QuizzStockConsumeDto.class);
		duibaQuizzStockConsumeService.insert(info);
	}
	
	@Test
	public void findByBizIdTest(){
		QuizzStockConsumeDto infoTest = duibaQuizzStockConsumeService.findByBizId(info.getBizId(), info.getAction());
		TestUtils.assertEqualsReflect(infoTest, info, false, exceptFields);
	}
}
