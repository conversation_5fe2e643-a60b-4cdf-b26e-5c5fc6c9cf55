package cn.com.duiba.activity.center.biz.service.creditgame;

import java.util.Date;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import cn.com.duiba.activity.center.biz.entity.creditgame.CreditGameSkinEntity;

/**
 * 积分游戏DAO单元测试
 * <AUTHOR>
 * @since 2016-09-05
 */
public class CreditGameSkinServiceTest extends CreditGameServiceTestCaseBase<CreditGameSkinEntity> {


    @Autowired
    private CreditGameSkinService creditGameSkinService;

    @Override
    protected CreditGameSkinEntity genEntity(){
        CreditGameSkinEntity entity=new CreditGameSkinEntity();
        entity.setGmtCreate(new Date());
        entity.setGmtModified(new Date());
        entity.setCreditGameSkinName("皮肤名称");
        entity.setCreditGameType((byte)1);
        entity.setGameSkinId(123l);
        entity.setCreditGameId(123l);
        entity.setCreditGameSkinImgUri("skin uri");
        return entity;
    }
    @Test
    public void testInsert(){
        doTestInsert(creditGameSkinService);
    }

    @Test
    public void testQueryById(){
       doTestQueryById(creditGameSkinService);
    }

    /*
    @Test
    public void testQuery(){
       doTestQuery(creditGameSkinService);
    }
    */

   /* @Test
    public void testUpdate(){
        doTestUpdate(creditGameSkinService, new PreUpdateHandler<CreditGameSkinEntity>() {
            @Override
            public void preHandle(CreditGameSkinEntity creditGameSkinEntity) {
                creditGameSkinEntity.setGmtModified(new Date());
            }
        });
    }*/


    @Test
    public void testDelete(){
        doTestDelete(creditGameSkinService);
    }
}
