package cn.com.duiba.activity.center.api.remoteservice.hdtool;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.hdtool.DuibaHdtoolDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;


/**
 * Created by sty on 8/17/17.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class RemoteDuibaHdtoolNewServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteDuibaHdtoolServiceNew remoteDuibaHdtoolServiceNew;

    @Test
    public void testUpdateDuiba() {
        DuibaHdtoolDto dto=new DuibaHdtoolDto();
        dto.setId(8865L);
        dto.setTitle("lalal");
        dto.setCreditsType(1);
        dto.setCreditsPrice(100L);
        remoteDuibaHdtoolServiceNew.update_duiba(dto);
    }
}
