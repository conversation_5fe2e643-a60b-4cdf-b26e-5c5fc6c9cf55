package cn.com.duiba.activity.center.biz.dao.ngame;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.ngame.DuibaNgameAppSpecifyEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/8.
 */
@Transactional(DsConstants.DATABASE_NGAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaNgameAppSpecifyDaoTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaNgameAppSpecifyDao duibaNgameAppSpecifyDao;

    private ThreadLocal<DuibaNgameAppSpecifyEntity> duibaNgameAppSpecifyDO=new ThreadLocal<>();

    @Before
    public void testAdd() {
        DuibaNgameAppSpecifyEntity e=new DuibaNgameAppSpecifyEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaNgameAppSpecifyDao.add(e);
        duibaNgameAppSpecifyDO.set(e);
    }

    @Test
    public void testFindByGameId() {
        DuibaNgameAppSpecifyEntity e=duibaNgameAppSpecifyDO.get();
        Assert.assertTrue(duibaNgameAppSpecifyDao.findByGameId(e.getDuibaGameId()).size()>0);
    }

    @Test
    public void testFindByGameConfigAndAppId() {
        DuibaNgameAppSpecifyEntity e=duibaNgameAppSpecifyDO.get();
        DuibaNgameAppSpecifyEntity e1=duibaNgameAppSpecifyDao.findByGameConfigAndAppId(e.getAppId(),e.getDuibaGameId());
        assertDO(e,e1);
    }

    @Test
    public void testAddBatch() {
        DuibaNgameAppSpecifyEntity e=new DuibaNgameAppSpecifyEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        List<DuibaNgameAppSpecifyEntity> list=new ArrayList<>();
        list.add(e);
        duibaNgameAppSpecifyDao.addBatch(list);
        Assert.assertNotNull(duibaNgameAppSpecifyDao.findByGameConfigAndAppId(e.getAppId(),e.getDuibaGameId()));
    }

    @Test
    public void testDelete() {
        DuibaNgameAppSpecifyEntity e=duibaNgameAppSpecifyDO.get();
        DuibaNgameAppSpecifyEntity e1=duibaNgameAppSpecifyDao.findByGameConfigAndAppId(e.getAppId(),e.getDuibaGameId());
        duibaNgameAppSpecifyDao.delete(e1.getId());
        Assert.assertNull(duibaNgameAppSpecifyDao.findByGameConfigAndAppId(e.getAppId(),e.getDuibaGameId()));
    }

    private void assertDO(DuibaNgameAppSpecifyEntity e, DuibaNgameAppSpecifyEntity e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaNgameAppSpecifyEntity e, DuibaNgameAppSpecifyEntity e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","id","deleted"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

//    @Override
//    public DatabaseSchema chooseSchema() {
//        return DatabaseSchema.nameOf(THIS_DATABASE_SCHEMA);
//    }
}
