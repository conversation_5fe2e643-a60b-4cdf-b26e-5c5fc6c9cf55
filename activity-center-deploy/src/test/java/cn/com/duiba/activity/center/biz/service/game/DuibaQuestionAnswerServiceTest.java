package cn.com.duiba.activity.center.biz.service.game;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionAnswerDto;
import cn.com.duiba.activity.center.api.dto.quizz.AddActivityDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/23.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaQuestionAnswerServiceTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaQuestionAnswerService duibaQuestionAnswerService;

    private DuibaQuestionAnswerDto duibaQuestionAnswerDto;

    @Before
    public void testInsert(){
        DuibaQuestionAnswerDto e=new DuibaQuestionAnswerDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        duibaQuestionAnswerService.insert(e);
        duibaQuestionAnswerDto =e;
    }

    @Test
    public void testFind() {
        DuibaQuestionAnswerDto e= duibaQuestionAnswerDto;
        DuibaQuestionAnswerDto e1=duibaQuestionAnswerService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindAutoOff() {
        DuibaQuestionAnswerDto e=new DuibaQuestionAnswerDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-1000000l));
        duibaQuestionAnswerService.insert(e);
        Assert.assertTrue(duibaQuestionAnswerService.findAutoOff().size()>0);
    }

    @Test
    public void testUpdateStatus() {
        DuibaQuestionAnswerDto e= duibaQuestionAnswerDto;
        Assert.assertTrue(duibaQuestionAnswerService.updateStatus(e.getId(),3)>0);
        Assert.assertTrue(duibaQuestionAnswerService.updateStatus(e.getId(),new Integer(3))>0);
    }

    @Test
    public void testFindByIds() {
        DuibaQuestionAnswerDto e= duibaQuestionAnswerDto;
        List<DuibaQuestionAnswerDto> list=duibaQuestionAnswerService.findByIds(Arrays.asList(e.getId()));
        assertDO(e,list.get(0));
    }

    @Test
    public void testFindExtraInfoById() {
        DuibaQuestionAnswerDto e= duibaQuestionAnswerDto;
        Assert.assertEquals(duibaQuestionAnswerService.findExtraInfoById(e.getId()).getFreeRule(),e.getFreeRule());
    }

    @Test
    public void testFindAllByIds() {
        DuibaQuestionAnswerDto e= duibaQuestionAnswerDto;
        List<DuibaQuestionAnswerDto> list=duibaQuestionAnswerService.findAllByIds(Arrays.asList(e.getId()));
        assertDO(e,list.get(0));
    }

    @Test
    public void testFindAllQuestion() {
        DuibaQuestionAnswerDto e=new DuibaQuestionAnswerDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-1000000l));
        duibaQuestionAnswerService.insert(e);
        List<AddActivityDto> list=duibaQuestionAnswerService.findAllQuestion(null);
        boolean isfind=false;
        for(AddActivityDto a:list){
            if(a.getId().equals(e.getId())) {
                isfind = true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindByPage() {
        Assert.assertTrue(duibaQuestionAnswerService.findByPage(0,10).size()>0);
        Map<String,Object> params=new HashMap<>();
        params.put("max",10);
        params.put("offset",0);
        Assert.assertTrue(duibaQuestionAnswerService.findByPage(params).size()>0);
    }

    @Test
    public void testFindPageCount() {
        Assert.assertTrue(duibaQuestionAnswerService.findPageCount()>0);
        Map<String,Object> params=new HashMap<>();
        params.put("title", duibaQuestionAnswerDto.getTitle());
        Assert.assertTrue(duibaQuestionAnswerService.findPageCount(params)>0);
    }

    @Test
    public void testDelete() {
        duibaQuestionAnswerService.delete(duibaQuestionAnswerDto.getId());
        Assert.assertTrue(duibaQuestionAnswerService.find(duibaQuestionAnswerDto.getId()).getDeleted());
    }

    @Test
    public void testUpdateAutoOffDateNull() {
        DuibaQuestionAnswerDto e= duibaQuestionAnswerDto;
        duibaQuestionAnswerService.updateAutoOffDateNull(e.getId());
        Assert.assertNull(duibaQuestionAnswerService.find(e.getId()).getAutoOffDate());
    }

    @Test
    public void testUpdateSwitches() {
        DuibaQuestionAnswerDto e= duibaQuestionAnswerDto;
        duibaQuestionAnswerService.updateSwitches(e.getId(),42l);
        Assert.assertEquals((Integer)42,duibaQuestionAnswerService.find(e.getId()).getSwitches());
    }

    @Test
    public void testUpdate() {
        duibaQuestionAnswerDto.setBanner("test");
        duibaQuestionAnswerService.update(duibaQuestionAnswerDto);
        Assert.assertEquals(duibaQuestionAnswerDto.getBanner(),duibaQuestionAnswerService.find(duibaQuestionAnswerDto.getId()).getBanner());
    }

    private void assertDO(DuibaQuestionAnswerDto e, DuibaQuestionAnswerDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaQuestionAnswerDto e, DuibaQuestionAnswerDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","tag",}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

}
