package cn.com.duiba.activity.center.biz.service.recommend;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.recommend.RecommendSkinDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by zhengjy on 2017/2/8.
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class RecommendSkinServiceTest extends TransactionalTestCaseBase {
    @Autowired
    RecommendSkinService recommendSkinService;


    private RecommendSkinDto save(){
        RecommendSkinDto recommendSkinDto =new RecommendSkinDto();
        recommendSkinDto.setHtmlContext("xxxxx2");
        recommendSkinDto.setShowStatus("OPEN");
        recommendSkinDto.setSkinName("wawawa");
        Long l = recommendSkinService.insert(recommendSkinDto);
        recommendSkinDto.setId(l);
        return  recommendSkinDto;

    }

    @Test
    public void testInsert(){
        Assert.assertTrue(save().getId()>0);
    }

    @Test
    public void testUpdate(){
        Assert.assertTrue(recommendSkinService.update(save()));
    }

    @Test
    public void testFindList(){
        save();
        List<RecommendSkinDto> ret= recommendSkinService.findList(0,20,new RecommendSkinDto());
        Assert.assertTrue(ret != null);
    }

    @Test
    public void testFindCount(){
        Assert.assertTrue(recommendSkinService.findCount(save())>0);
    }

    @Test
    public void testFind(){
        Assert.assertTrue(recommendSkinService.find(save().getId()) != null);
    }

    @Test
    public void testDelete(){
        Assert.assertTrue(recommendSkinService.delete(save().getId()));
    }


}
