package cn.com.duiba.activity.center.biz.dao.game;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionAnswerOrdersDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.game.DuibaQuestionAnswerOrdersEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/2.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaQuestionAnswerOrdersDaoTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaQuestionAnswerOrdersDao duibaQuestionAnswerOrdersDao;


    @Test
    public void testInsert() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionAnswerOrdersDao.insert(e);
    }

    @Test
    public void testFind() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionAnswerOrdersDao.insert(e);       
        DuibaQuestionAnswerOrdersEntity e1=duibaQuestionAnswerOrdersDao.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testUpdateStatusToConsumeSuccess() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(0);
        duibaQuestionAnswerOrdersDao.insert(e);      
        duibaQuestionAnswerOrdersDao.updateStatusToConsumeSuccess(e.getId());
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.find(e.getId()).getStatus().equals(1));
    }

    @Test
    public void testUpdateStatusToConsumeFail() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(0);
        duibaQuestionAnswerOrdersDao.insert(e);
        duibaQuestionAnswerOrdersDao.updateStatusToConsumeFail(e.getId(),"test","test","test");
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.find(e.getId()).getStatus().equals(2));
    }

    @Test
    public void testUpdateStatusToSuccess() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(1);
        duibaQuestionAnswerOrdersDao.insert(e);
        duibaQuestionAnswerOrdersDao.updateStatusToSuccess(e.getId());
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.find(e.getId()).getStatus().equals(3));
    }

    @Test
    public void testUpdateDeveloperBizId() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(0);
        duibaQuestionAnswerOrdersDao.insert(e);
        duibaQuestionAnswerOrdersDao.updateDeveloperBizId(e.getId(),"test");
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.find(e.getId()).getDeveloperBizId().equals("test"));
    }

    @Test
    public void testCountByConsumerIdAndOperatingActivityId() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(0);
        duibaQuestionAnswerOrdersDao.insert(e);
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.countByConsumerIdAndOperatingActivityId(e.getConsumerId(),e.getOperatingActivityId())>0);
    }

    @Test
    public void testCountByConsumerIdAndOperatingActivityIdAndDate() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(0);
        duibaQuestionAnswerOrdersDao.insert(e);
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.countByConsumerIdAndOperatingActivityIdAndDate(e.getConsumerId(),e.getOperatingActivityId(),new Date(System.currentTimeMillis()-100000l),new Date(System.currentTimeMillis()+100000l))>0);
    }

    @Test
    public void testCountFreeByConsumerIdAndOperatingActivityId() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setCredits(0l);
        duibaQuestionAnswerOrdersDao.insert(e);
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.countFreeByConsumerIdAndOperatingActivityId(e.getConsumerId(),e.getOperatingActivityId())>0);
    }

    @Test
    public void testCountFreeByConsumerIdAndOperatingActivityIdAndDate() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setCredits(0l);
        e.setStatus(1);
        duibaQuestionAnswerOrdersDao.insert(e);
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.countFreeByConsumerIdAndOperatingActivityIdAndDate(e.getConsumerId(),e.getOperatingActivityId(),new Date(System.currentTimeMillis()-100000l),new Date(System.currentTimeMillis()+1000000l))>0);
    }

    @Test
    public void testUpdateExchangeStatusToFail() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setCredits(0l);
        e.setStatus(1);
        e.setExchangeStatus(1);
        duibaQuestionAnswerOrdersDao.insert(e);
        duibaQuestionAnswerOrdersDao.updateExchangeStatusToFail(e.getId(),"test","test","test");
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.find(e.getId()).getExchangeStatus().equals(3));
    }

    @Test
    public void testUpdateMainOrderId() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setCredits(0l);
        e.setStatus(1);
        duibaQuestionAnswerOrdersDao.insert(e);
        duibaQuestionAnswerOrdersDao.updateMainOrderId(e.getId(),20l,"test");
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.find(e.getId()).getMainOrderId().equals(20l));
    }

    @Test
    public void testUpdateExchangeStatusToOverdue() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setCredits(0l);
        e.setStatus(1);
        e.setExchangeStatus(1);
        duibaQuestionAnswerOrdersDao.insert(e);
        duibaQuestionAnswerOrdersDao.updateExchangeStatusToOverdue(e.getId(),"test","test","test");
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.find(e.getId()).getExchangeStatus().equals(DuibaQuestionAnswerOrdersDto.ExchangeStatusOverdue));
    }

    @Test
    public void testUpdateExchangeStatusToSucess() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setCredits(0l);
        e.setStatus(1);
        e.setExchangeStatus(1);
        duibaQuestionAnswerOrdersDao.insert(e);
        duibaQuestionAnswerOrdersDao.updateExchangeStatusToSucess(e.getId(),"","","");
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.find(e.getId()).getExchangeStatus().equals(2));
    }

    @Test
    public void testUpdateExchangeStatusToWait() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setCredits(0l);
        e.setStatus(1);
        e.setExchangeStatus(0);
        duibaQuestionAnswerOrdersDao.insert(e);
        duibaQuestionAnswerOrdersDao.updateExchangeStatusToWait(e.getId(),"","","");
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.find(e.getId()).getExchangeStatus().equals(DuibaQuestionAnswerOrdersDto.ExchangeStatusWait));
    }

    @Test
    public void testDoTakePrize() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setMainOrderId(null);
        e.setExchangeStatus(1);
        duibaQuestionAnswerOrdersDao.insert(e);
        duibaQuestionAnswerOrdersDao.doTakePrize(e.getId());
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.find(e.getId()).getExchangeStatus().equals(2));
    }

    @Test
    public void testRollbackTakePrize() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setMainOrderId(null);
        e.setExchangeStatus(2);
        duibaQuestionAnswerOrdersDao.insert(e);
        duibaQuestionAnswerOrdersDao.rollbackTakePrize(e.getId());
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.find(e.getId()).getExchangeStatus().equals(1));
    }

    @Test
    public void testFindOverdueOrder() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setMainOrderId(null);
        e.setExchangeStatus(1);
        e.setPrizeOverdueDate(new Date(System.currentTimeMillis()-1000000l));
        e.setStatus(3);
        duibaQuestionAnswerOrdersDao.insert(e);
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.findOverdueOrder().size()>0);
    }

    @Test
    public void testFindByIds() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setMainOrderId(null);
        e.setExchangeStatus(1);
        e.setPrizeOverdueDate(new Date(System.currentTimeMillis()-1000000l));
        e.setStatus(3);
        duibaQuestionAnswerOrdersDao.insert(e);
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.findByIds(Arrays.asList(e.getId())).size()>0);
    }

    @Test
    public void testUpdatePrizeInfo() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(1);
        e.setStatus(3);
        duibaQuestionAnswerOrdersDao.insert(e);
        e.setPrizeId(20l);
        duibaQuestionAnswerOrdersDao.updatePrizeInfo(e);

        Assert.assertTrue(duibaQuestionAnswerOrdersDao.find(e.getId()).getPrizeId().equals(20l));
    }

    @Test
    public void testUpdateScore() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(1);
        e.setStatus(3);
        duibaQuestionAnswerOrdersDao.insert(e);
        duibaQuestionAnswerOrdersDao.updateScore(e.getId(),42);
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.find(e.getId()).getScore().equals(42));
    }

    @Test
    public void testFindByAppAndDeveloperBizId() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(1);
        e.setStatus(3);
        duibaQuestionAnswerOrdersDao.insert(e);
        Assert.assertNotNull(duibaQuestionAnswerOrdersDao.findByAppAndDeveloperBizId(e.getAppId(),e.getDeveloperBizId()));
    }

    @Test
    public void testFindQuestionOrderLimit50() {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(2);
        e.setStatus(3);
        duibaQuestionAnswerOrdersDao.insert(e);
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.findQuestionOrderLimit50(e.getAppId(),e.getOperatingActivityId()).size()>0);
    }

    @Test
    public void testCountFailByOperatingActivityIds() throws Exception {
        DuibaQuestionAnswerOrdersEntity e=new DuibaQuestionAnswerOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(3);
        e.setStatus(3);
        duibaQuestionAnswerOrdersDao.insert(e);
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.countFailByOperatingActivityIds(Arrays.asList(e.getOperatingActivityId())).size()>0);
    }

    @Test
    public void testFindByLimit() throws Exception {
        Map<String,Object> params=new HashMap<>();
        params.put("start",0);
        params.put("pageSize",20);
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.findByLimit(params).size()>0);
    }

    @Test
    public void testTotalCount() throws Exception {
        Map<String,Object> params=new HashMap<>();
        Assert.assertTrue(duibaQuestionAnswerOrdersDao.totalCount(params)>0);
    }

    private void assertDO(DuibaQuestionAnswerOrdersEntity e, DuibaQuestionAnswerOrdersEntity e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaQuestionAnswerOrdersEntity e, DuibaQuestionAnswerOrdersEntity e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","questionType","prizeOverdueDate"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

}
