package cn.com.duiba.activity.center.api.remoteservice.freegroup;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.freegroup.FreeGroupConfigDto;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/3/25 10:02
 * @description:
 */
@Rollback(value = false)
public class RemoteFreeGroupConfigServiceTest extends TransactionalTestCaseBase {

    @Autowired
    RemoteFreeGroupConfigService remoteFreeGroupConfigService;

    @Test
    public void testGetById(){
	    FreeGroupConfigDto freeGroupConfigDto = remoteFreeGroupConfigService.getById(2L);
	    System.out.println(JSON.toJSON(freeGroupConfigDto));
	    Assert.assertNotNull(freeGroupConfigDto);
    }
}
