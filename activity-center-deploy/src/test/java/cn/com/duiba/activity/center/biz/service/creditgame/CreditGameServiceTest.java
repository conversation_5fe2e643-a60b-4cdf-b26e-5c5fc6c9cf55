package cn.com.duiba.activity.center.biz.service.creditgame;

import java.util.Date;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import cn.com.duiba.activity.center.biz.entity.creditgame.CreditGameEntity;

/**
 * 积分游戏DAO单元测试
 * <AUTHOR>
 * @since 2016-09-05
 */
public class CreditGameServiceTest extends CreditGameServiceTestCaseBase<CreditGameEntity> {


    @Autowired
    private CreditGameService creditGameService;

    protected CreditGameEntity genEntity(){
        CreditGameEntity creditGameEntity=new CreditGameEntity();
        creditGameEntity.setActivityActionType((byte)1);
        creditGameEntity.setCreditGameType((byte)1);
        creditGameEntity.setCreditGameTitle("积分游戏Title");
        creditGameEntity.setCreatePerson("创建人");
        creditGameEntity.setCreditGameActivityCategoryId(1233444l);
        creditGameEntity.setCreditGameAppCount(100l);
        creditGameEntity.setCreditGameAutoOffDate(new Date());
        creditGameEntity.setCreditGameAwardConfig("CreditGameAwardConfig");
        creditGameEntity.setCreditGameAwardPosition("CreditGameAwardPosition");
        creditGameEntity.setCreditGameBannerImage("CreditGameBannerImage");
        creditGameEntity.setCreditGameBetConfig("CreditGameBetConfig");
        creditGameEntity.setCreditGameCreditsPrice(100l);
        creditGameEntity.setCreditGameCustomTag("CreditGameCustomTag");
        creditGameEntity.setCreditGameDrawLimit(101l);
        creditGameEntity.setCreditGameDrawScope("1111111111");
        creditGameEntity.setCreditGameDuibaPrice(10l);
        creditGameEntity.setCreditGameFreeLimit(100l);
        creditGameEntity.setCreditGameFreeScope("1111111111");
        creditGameEntity.setCreditGameLotteryCount(100l);
        creditGameEntity.setCreditGameRecommendImage("CreditGameRecommendImage");
        creditGameEntity.setCreditGameRuleScript("ruleScripts");
        creditGameEntity.setCreditGameSmallImage("CreditGameSmallImage");
        creditGameEntity.setCreditGameStatus((byte)1);
        creditGameEntity.setCreditGameSwitches(9l);
        creditGameEntity.setCreditGameValveConfig("CreditGameValveConfig");
        creditGameEntity.setCreditGameWhiteImage("CreditGameWhiteImage");
        creditGameEntity.setGmtCreate(new Date());
        creditGameEntity.setGmtModified(new Date());
        creditGameEntity.setRemarks("Remarks");

        creditGameEntity.setCreditGameRule("CreditGameRule");
        creditGameEntity.setCreditGameFreeRule("CreditGameFreeRule");
        creditGameEntity.setCreditGameDesc("CreditGameDesc");
        creditGameEntity.setCreditGameExtDesc("CreditGameExtDesc");
        Byte a=1;
        creditGameEntity.setDeleted(a);

        return creditGameEntity;
    }
    @Test
    public void testInsert(){
        doTestInsert(creditGameService);
    }

    @Test
    public void testQueryById(){
        doTestQueryById(creditGameService);
    }

    /*
    @Test
    public void testQuery(){
        doTestQuery(creditGameService);
    }

*/
/*    @Test
    public void testUpdate(){
        doTestUpdate(creditGameService, new PreUpdateHandler<CreditGameEntity>() {
            @Override
            public void preHandle(CreditGameEntity creditGameEntity) {
                creditGameEntity.setGmtModified(new Date());
            }
        });
    }
*/

    @Test
    public void testDelete(){
       doTestDelete(creditGameService);
    }
}
