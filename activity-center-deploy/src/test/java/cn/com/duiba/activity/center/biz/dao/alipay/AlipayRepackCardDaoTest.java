package cn.com.duiba.activity.center.biz.dao.alipay;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.alipayactivityredpack.AlipayRepackCardDto;
import cn.com.duiba.activity.center.biz.dao.activityredpack.AlipayRepackCardDao;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AlipayRepackCardDaoTest.java
 * @Description
 * @createTime 2022年11月17日 22:06:00
 */
public class AlipayRepackCardDaoTest  extends TransactionalTestCaseBase {
    @Autowired
    AlipayRepackCardDao alipayRepackCardDao;


    @Test
    public void test(){
        AlipayRepackCardDto alipayRepackCardEntity=new AlipayRepackCardDto();




        alipayRepackCardEntity.setId(303000L);
        alipayRepackCardEntity.setShortUrl("https://duibatest/cjw");
        alipayRepackCardDao.getByShortUrl("https://duibatest/cjw");
        alipayRepackCardDao.listByShortUrls(Arrays.asList("https://duibatest/cjw","https://duibatest/1"));
        alipayRepackCardDao.search(alipayRepackCardEntity);
        System.out.println("-----------");
    }

}
