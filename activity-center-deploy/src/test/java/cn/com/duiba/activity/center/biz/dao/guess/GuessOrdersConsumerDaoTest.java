package cn.com.duiba.activity.center.biz.dao.guess;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.guess.GuessOrdersEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS_CON)
public class GuessOrdersConsumerDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private GuessOrdersConsumerDao guessOrdersConsumerDao;
	@Autowired
	private GuessOrdersSequenceDao guessOrdersSequenceDao;
	
	private GuessOrdersEntity info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(GuessOrdersEntity.class);
		info.setCredits(0L);
		info.setOrderStatus(2);
		info.setIsGivePrize(1);
		info.setExchangeStatus(10);
		guessOrdersConsumerDao.insert(info, guessOrdersSequenceDao.getId());
	}

	@Test
    public void findTest(){
		GuessOrdersEntity e = guessOrdersConsumerDao.find(info.getConsumerId(), info.getId());
		Assert.assertNotNull(e);
    }

	@Test
    public void countByConsumerIdAndOperatingActivityIdTest(){
		int num = guessOrdersConsumerDao.countByConsumerIdAndOperatingActivityId(info.getConsumerId(), info.getOperatingActivityId());
		Assert.assertTrue(num > 0);
	}

	@Test
    public void countByConsumerIdAndOperatingActivityIdAndDateTest(){
    	Calendar calendar = Calendar.getInstance();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	Date start = calendar.getTime();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, 1);
    	Date end = calendar.getTime();
    	int num = guessOrdersConsumerDao.countByConsumerIdAndOperatingActivityIdAndDate(info.getConsumerId(), info.getOperatingActivityId(), start, end);
    	Assert.assertTrue(num > 0);
    }

	@Test
    public void countFreeByConsumerIdAndOperatingActivityIdAndDateTest(){
    	Calendar calendar = Calendar.getInstance();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	Date start = calendar.getTime();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, 1);
    	Date end = calendar.getTime();
    	int num = guessOrdersConsumerDao.countFreeByConsumerIdAndOperatingActivityIdAndDate(info.getConsumerId(), info.getOperatingActivityId(), start, end);
    	Assert.assertTrue(num > 0);
    }

	@Test
    public void countFreeByConsumerIdAndOperatingActivityIdTest(){
    	int num = guessOrdersConsumerDao.countFreeByConsumerIdAndOperatingActivityId(info.getConsumerId(), info.getOperatingActivityId());
    	Assert.assertTrue(num > 0);
    }

	@Test
    public void findByIdsTest(){
    	List<Long> ids = new ArrayList<Long>();
    	ids.add(info.getId());
    	List<GuessOrdersEntity> list = guessOrdersConsumerDao.findByIds(info.getConsumerId(), ids);
    	Assert.assertTrue(list.size() > 0);
    }

	@Test
    public void findWinOrdersTest(){
    	List<GuessOrdersEntity> list = guessOrdersConsumerDao.findWinOrders(info.getConsumerId(), info.getDuibaGuessId());
    	Assert.assertTrue(list.size() > 0);
    }

	@Test
    public void findGuessOrdersTest(){
    	List<GuessOrdersEntity> list = guessOrdersConsumerDao.findGuessOrders(info.getConsumerId(), info.getDuibaGuessId());
    	Assert.assertTrue(list.size() > 0);
    }

	@Test
	public void updateDeveloperBizIdTest(){
		GuessOrdersEntity e = TestUtils.createRandomBean(GuessOrdersEntity.class);
		guessOrdersConsumerDao.updateDeveloperBizId(info.getConsumerId(), info.getId(), e.getDeveloperBizId());
		String bizId = guessOrdersConsumerDao.find(info.getConsumerId(), info.getId()).getDeveloperBizId();
		Assert.assertTrue(bizId.equals(e.getDeveloperBizId()));
	}

	@Test
	public void updateMainOrderIdTest(){
		GuessOrdersEntity e = TestUtils.createRandomBean(GuessOrdersEntity.class);
		guessOrdersConsumerDao.updateMainOrderId(info.getConsumerId(), info.getId(), e.getMainOrderId(), e.getMainOrderNum());
		long mainOrderId = guessOrdersConsumerDao.find(info.getConsumerId(), info.getId()).getMainOrderId();
		Assert.assertTrue(mainOrderId == e.getMainOrderId());
	}
}
