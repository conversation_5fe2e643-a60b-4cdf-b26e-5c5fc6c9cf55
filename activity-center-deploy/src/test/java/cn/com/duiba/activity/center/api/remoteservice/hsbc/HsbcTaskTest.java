package cn.com.duiba.activity.center.api.remoteservice.hsbc;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.hsbc.HsbcHdtoolFreeTimesDto;
import cn.com.duiba.activity.center.api.params.hsbc.HsbcFreeTimeQueryParams;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class HsbcTaskTest extends TransactionalTestCaseBase {

    @Resource
    private RemoteHsbcHdtoolFreeTimeService remoteHsbcHdtoolFreeTimeService;

    @Test
    public void findAllByCidAndScopeTimesTest(){
        /**
         * [{"canCompleteCount":10,"completeCount":1,"taskId":"TASK2021102510001"},{"canCompleteCount":5,"completeCount":0,"taskId":"TASK2021102510002"},{"canCompleteCount":6,"completeCount":0,"taskId":"TASK2021102510003"}]
         */
        LocalDateTime now = LocalDateTime.now();
        HsbcFreeTimeQueryParams scopeTaskQuery = new HsbcFreeTimeQueryParams();
        scopeTaskQuery.setConsumerId(12L);
        scopeTaskQuery.setCurrent(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        scopeTaskQuery.setTaskIdList(Arrays.asList("TASK2021102510002", "TASK2021102510001"));

        List<HsbcHdtoolFreeTimesDto> dbScopeTaskFreeTimes = remoteHsbcHdtoolFreeTimeService.findAllByCidAndScopeTimes(scopeTaskQuery);

    }


    @Test
    public void findTaskByCidAndScopeTypesTest() {
        HsbcFreeTimeQueryParams foreverTaskQuery = new HsbcFreeTimeQueryParams();
        foreverTaskQuery.setConsumerId(12L);
        foreverTaskQuery.setScopeTypeList(Collections.singletonList(99));

        List<HsbcHdtoolFreeTimesDto> dbForeverTaskFreeTimes = remoteHsbcHdtoolFreeTimeService.findTaskByCidAndScopeTypes(foreverTaskQuery);
    }
}
