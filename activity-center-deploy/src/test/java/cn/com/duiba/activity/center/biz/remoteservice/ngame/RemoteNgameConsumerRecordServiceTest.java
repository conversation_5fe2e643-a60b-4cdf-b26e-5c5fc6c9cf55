package cn.com.duiba.activity.center.biz.remoteservice.ngame;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.ngame.NgameConsumerRecordDto;
import cn.com.duiba.activity.center.api.dto.ngame.SimpleConsumerRankDto;
import cn.com.duiba.activity.center.api.remoteservice.ngame.RemoteNgameConsumerRecordService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.service.ngame.NgameRankService;
import com.google.common.collect.Maps;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by hww on 2017/8/8.
 */
@Transactional(DsConstants.DATABASE_NGAME)
public class RemoteNgameConsumerRecordServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteNgameConsumerRecordService nGameService;
    @Autowired
    private NgameRankService ngameRankService;

    private String cid = "500098005";
    private Long gameId = 3220L;
    private Long maxScore = 102L;

    @Test
    public void testUpdateRedisRank() {
        nGameService.updateRedisRank(gameId, cid, maxScore, true);
    }

    @Test
    public void testFindScoreRank() {
        nGameService.findScoreRank(gameId, cid, true);
    }

    @Test
    public void testGetConsumerRank(){
        Long gameId =(long)(Math.random() * 10000);
        //加入25条数据
        for (int i = 0; i < 25; i++) {
            ngameRankService.add(gameId, String.valueOf(i), maxScore, true);
        }
        List<SimpleConsumerRankDto> consumerRank1 = nGameService.getConsumerRank(gameId, Boolean.TRUE, 0);
        //查询出20条纪录
        Assert.assertEquals(consumerRank1.size(), 20);
        List<SimpleConsumerRankDto> consumerRank2 = nGameService.getConsumerRank(gameId, Boolean.TRUE, 20);
        //查询出5条记录
        Assert.assertEquals(consumerRank2.size(), 5);
    }

    @Test
    public void testUpdateMaxScoreAndTotal() {
        NgameConsumerRecordDto insertRecordDto = getNewRecord();
        NgameConsumerRecordDto updateRecordDto = nGameService.insert(insertRecordDto);
        updateRecordDto.setMaxScore(999L);
        updateRecordDto.setTotalScore(9999L);
        nGameService.updateMaxScoreAndTotal(updateRecordDto);
        NgameConsumerRecordDto queryRecordDto = nGameService.findRecordByConIdAndNgameId(insertRecordDto.getConsumerId(), insertRecordDto.getDuibaNgameId());
        Assert.assertEquals(queryRecordDto.getMaxScore(), updateRecordDto.getMaxScore());
        Assert.assertEquals(queryRecordDto.getTotalScore(), updateRecordDto.getTotalScore());
    }

    @Test
    public void testFindTopWinningByMaxScore() {
        NgameConsumerRecordDto insertRecordDto = getNewRecord();
        nGameService.insert(insertRecordDto);
        Map<String, Object> params = Maps.newHashMap();
        params.put("offset", 0);
        params.put("max", 20);
        params.put("gameDuibaId", insertRecordDto.getDuibaNgameId());
        params.put("cheat", insertRecordDto.getCheat());
        params.put("sort", "ASC");
        List<NgameConsumerRecordDto> list1 = nGameService.findTopWinningByMaxScore(params);
        Assert.assertEquals(list1.size(), 1);
        List<NgameConsumerRecordDto> list2 = nGameService.findTopWinningByMaxScore(Maps.<String, Object>newHashMap());
        Assert.assertEquals(list2.size(), 0);
    }

    @Test
    public void testFindTopWinningByTotalScore() {
        NgameConsumerRecordDto insertRecordDto = getNewRecord();
        nGameService.insert(insertRecordDto);
        Map<String, Object> params = Maps.newHashMap();
        params.put("offset", 0);
        params.put("max", 20);
        params.put("gameDuibaId", insertRecordDto.getDuibaNgameId());
        params.put("cheat", insertRecordDto.getCheat());
        List<NgameConsumerRecordDto> list1 = nGameService.findTopWinningByTotalScore(params);
        Assert.assertEquals(list1.size(), 1);
        List<NgameConsumerRecordDto> list2 = nGameService.findTopWinningByTotalScore(Maps.<String, Object>newHashMap());
        Assert.assertEquals(list2.size(), 0);
    }

    @Test
    public void testUpdateGameOrdersId(){
        NgameConsumerRecordDto insertRecordDto = getNewRecord();
        NgameConsumerRecordDto updateRecordDto = nGameService.insert(insertRecordDto);
        updateRecordDto.setGameOrdersId(-1L);
        nGameService.updateGameOrdersId(updateRecordDto);
        NgameConsumerRecordDto result = nGameService.find(updateRecordDto.getId());
        Assert.assertEquals(result.getGameOrdersId(), updateRecordDto.getGameOrdersId());
    }



    private NgameConsumerRecordDto getNewRecord() {
        NgameConsumerRecordDto recordDto = new NgameConsumerRecordDto(true);
        recordDto.setAppId(1L);
        recordDto.setConsumerId(500103042L);
        recordDto.setPartnerUserId("1335");
        recordDto.setOperatingActivityId(1L);
        recordDto.setDuibaNgameId(-1L);
        recordDto.setMaxScore(100L);
        recordDto.setShareStatus(NgameConsumerRecordDto.share_status_0);
        recordDto.setTotalScore(100L);
        recordDto.setIsGivePrize(false);
        recordDto.setGameOrdersId(1L);
        recordDto.setCheat(false);
        recordDto.setMaxScoreDate(new Date());
        return recordDto;
    }

    @Test
    public void testFindByConsumerAndNgameIds(){
        nGameService.findByConsumerAndNgameIds(1L, Lists.newArrayList(1L,2L,3L));
    }


}
