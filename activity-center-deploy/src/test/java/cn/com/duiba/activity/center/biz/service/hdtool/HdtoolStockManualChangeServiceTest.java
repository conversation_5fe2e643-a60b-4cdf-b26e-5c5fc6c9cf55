package cn.com.duiba.activity.center.biz.service.hdtool;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolStockManualChangeDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/25.
 */
@Transactional(DsConstants.DATABASE_CREDITS)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class HdtoolStockManualChangeServiceTest extends TransactionalTestCaseBase {

    @Resource
    private HdtoolStockManualChangeService hdtoolStockManualChangeService;

    @Test
    public void testInsert() {
        HdtoolStockManualChangeDto e=new HdtoolStockManualChangeDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        hdtoolStockManualChangeService.insert(e);
    }

}
