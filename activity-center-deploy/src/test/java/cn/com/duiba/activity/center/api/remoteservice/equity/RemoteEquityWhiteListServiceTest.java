package cn.com.duiba.activity.center.api.remoteservice.equity;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.equity.EquityWhiteListDto;
import cn.com.duiba.activity.center.api.params.EquityWhiteListParam;
import cn.com.duiba.api.bo.page.Page;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class RemoteEquityWhiteListServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteEquityWhiteListService remoteEquityWhiteListService;

    @Test
    public void testFindByEquityIdAndBatchId() {
        EquityWhiteListParam equityWhiteListParam = new EquityWhiteListParam();
        equityWhiteListParam.setAppId(1L);
        equityWhiteListParam.setEquityId(34L);
        equityWhiteListParam.setBatchId(65L);
        Page<EquityWhiteListDto> equityWhiteListDtoList = remoteEquityWhiteListService.findByEquityIdAndBatchId(equityWhiteListParam);
        System.out.println("111**********************");
        System.out.println(JSONObject.toJSONString(equityWhiteListDtoList));

        equityWhiteListParam.setUid("158");
        equityWhiteListDtoList = remoteEquityWhiteListService.findByEquityIdAndBatchId(equityWhiteListParam);
        System.out.println(JSONObject.toJSONString(equityWhiteListDtoList));
    }

    @Test
    public void testDeleteById() {
        EquityWhiteListDto dto = remoteEquityWhiteListService.findById(64183L);
        System.out.println(JSONObject.toJSONString(dto));
        Integer ret = remoteEquityWhiteListService.deleteById(64183L);
        System.out.println("111**********************");
        System.out.println(ret);
    }
}
