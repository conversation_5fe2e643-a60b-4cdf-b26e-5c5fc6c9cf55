package cn.com.duiba.activity.center.biz.dao.scraperedpacket;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.entity.scraperedpacket.ScrapeRedPacketRecordEntity;
import com.alibaba.fastjson.JSONObject;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

public class ScrapeRedPacketRecordDaoTest extends TransactionalTestCaseBase {

    @Autowired
    private ScrapeRedPacketRecordDao scrapeRedPacketRecordDao;

    @Test
    public void testInsertAndFind() {
        Long id = scrapeRedPacketRecordDao.insert(newEntity());
        Assert.assertTrue(id > 0);
        ScrapeRedPacketRecordEntity entity = scrapeRedPacketRecordDao.findById(id);
        Assert.assertNotNull(entity);
        System.out.println(JSONObject.toJSON(entity));

    }

    @Test
    public void test() {
        List<ScrapeRedPacketRecordEntity> list = scrapeRedPacketRecordDao.findByRedPacketIds(Lists.newArrayList(1L,2L,3L));
        System.out.println(list.size());
    }

    private ScrapeRedPacketRecordEntity newEntity() {
        ScrapeRedPacketRecordEntity entity = new ScrapeRedPacketRecordEntity();
        entity.setConsumerId(0L);
        entity.setRedPacketId(0L);
        entity.setAvatar("");
        entity.setNickname("");
        entity.setScrapeRate("1.1");
        entity.setGmtCreate(new Date());
        entity.setGmtModified(new Date());
        return entity;
    }



}
