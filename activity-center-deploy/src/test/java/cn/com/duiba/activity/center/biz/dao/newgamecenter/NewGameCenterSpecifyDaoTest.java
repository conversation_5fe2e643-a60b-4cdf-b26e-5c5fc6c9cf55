package cn.com.duiba.activity.center.biz.dao.newgamecenter;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.newgamecenter.NewGameCenterSpecifyEntity;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;


/**
 * <AUTHOR>
 * @date 2018/09/03
 */
@Transactional(DsConstants.DATABASE_NGAME)
public class NewGameCenterSpecifyDaoTest extends TransactionalTestCaseBase {
    @Autowired
    private NewGameCenterSpecifyDao gameCenterSpecifyDao;
    private Boolean isInserted = Boolean.FALSE;

    @Before
    public void setup() {
        isInserted = gameCenterSpecifyDao.batchInsert(
                1L, Stream.of(1L, 2L, 3L).collect(Collectors.toList()));
    }

    @Test
    public void shouldBatchInsertCorrectly() {
        assertThat(isInserted).isTrue();
    }

    @Test
    public void shouldDeleteCorrectly() {
        List<NewGameCenterSpecifyEntity> specifyEntityList = gameCenterSpecifyDao.list(1L);

        assertThat(gameCenterSpecifyDao.delete(specifyEntityList.get(0).getId())).isEqualTo(1);
    }

    @Test
    public void shouldListCorrectly() {
        List<NewGameCenterSpecifyEntity> specifyEntityList = gameCenterSpecifyDao.list(1L);
        assertThat(specifyEntityList).isNotNull();
        assertThat(specifyEntityList.size()).isEqualTo(3);
    }

    @Test
    public void shouldFindByAppIdCorrectly() {
        NewGameCenterSpecifyEntity specifyEntity = gameCenterSpecifyDao.findByAppId(1L);

        assertThat(specifyEntity).isNotNull();
        assertThat(specifyEntity.getId()).isGreaterThan(0);
    }
}
