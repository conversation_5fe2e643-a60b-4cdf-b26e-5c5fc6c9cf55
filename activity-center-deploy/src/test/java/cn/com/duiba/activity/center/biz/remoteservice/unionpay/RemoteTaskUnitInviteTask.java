package cn.com.duiba.activity.center.biz.remoteservice.unionpay;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.taskunit.TaskUnitInviteGenDto;
import cn.com.duiba.activity.center.api.remoteservice.taskunit.RemoteTaskUnitGenInviteService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class RemoteTaskUnitInviteTask extends TransactionalTestCaseBase {

    @Autowired
    private RemoteTaskUnitGenInviteService remoteTaskUnitGenInviteService;

    @Test
    public void gen () {
        TaskUnitInviteGenDto dto = new TaskUnitInviteGenDto();
        dto.setAppId(47805L);
        dto.setConsumerId(1L);
        dto.setInviteCode("xxxxx");
        remoteTaskUnitGenInviteService.save(dto);
    }
}
