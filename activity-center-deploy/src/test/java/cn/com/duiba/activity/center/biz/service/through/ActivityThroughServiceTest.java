package cn.com.duiba.activity.center.biz.service.through;

import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.through.ActivityThroughDto;
import cn.com.duiba.activity.center.api.dto.through.ActivityThroughStepInfoDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

import com.google.common.collect.Lists;

/** 
 * ClassName:ActivityThroughServiceTest.java <br/>
 * <AUTHOR> 
 * @date 创建时间：2017年9月14日 下午4:18:09 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
@Transactional(DsConstants.DATABASE_HDTOOL_CONF)
public class ActivityThroughServiceTest extends TransactionalTestCaseBase {
	/**
	 * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
	 */
	protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS_ACTIVITY;
	@Resource
	private ActivityThroughService activityThroughService;
	private Long throughActivityId =100L;
	@Before
	public void testInsert(){
		List<ActivityThroughStepInfoDto> data = Lists.newArrayList();
		ActivityThroughStepInfoDto stepInfo = new ActivityThroughStepInfoDto();
		TestUtils.setRandomAttributesForBean(stepInfo, false);
		stepInfo.setPrizeType(1);
		ActivityThroughStepInfoDto stepInfo1 = new ActivityThroughStepInfoDto();
		TestUtils.setRandomAttributesForBean(stepInfo1, false);
		stepInfo1.setPrizeType(2);
		ActivityThroughStepInfoDto stepInfo2 = new ActivityThroughStepInfoDto();
		TestUtils.setRandomAttributesForBean(stepInfo2, false);
		stepInfo2.setPrizeType(3);
		data.add(stepInfo);
		data.add(stepInfo1);
		data.add(stepInfo2);
		
		List<ActivityThroughDto> list = Lists.newArrayList();
		ActivityThroughDto dto1 = new ActivityThroughDto();
		TestUtils.setRandomAttributesForBean(dto1, false);
		dto1.setThroughActivityId(throughActivityId);
		dto1.setThroughId(1);
		dto1.setData(data);
		list.add(dto1);
		ActivityThroughDto dto2 = new ActivityThroughDto();
		TestUtils.setRandomAttributesForBean(dto2, false);
		dto2.setThroughActivityId(throughActivityId);
		dto2.setData(data);
		list.add(dto2);
		ActivityThroughDto dto3 = new ActivityThroughDto();
		TestUtils.setRandomAttributesForBean(dto3, false);
		dto3.setThroughActivityId(throughActivityId);
		dto3.setData(data);
		list.add(dto3);

		activityThroughService.saveOrUpdateActivityThrough(list, throughActivityId);
	}
	
	@Test
	public void testfindByThroughActivityId(){
		List<ActivityThroughDto> list = activityThroughService.findByThroughActivityId(throughActivityId);
		Assert.assertNotNull(list);
	}
	
	@Test
	public void testfindActivityThroughInfo(){
		ActivityThroughDto dto = activityThroughService.findActivityThroughInfo(1L,throughActivityId);
		Assert.assertNotNull(dto);
	}

}
