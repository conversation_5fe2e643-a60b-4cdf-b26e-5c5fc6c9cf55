package cn.com.duiba.activity.center.api.remoteservice.hdtool;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.service.exception.BusinessException;
import org.junit.Test;
import org.springframework.test.annotation.Rollback;

import javax.annotation.Resource;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/2/19 14:43
 * @description:
 */
@Rollback(value = false)
public class RemoteHdtoolStockConsumerServiceTest extends TransactionalTestCaseBase {

	@Resource
	private RemoteHdtoolStockConsumeService remoteHdtoolStockConsumeService;

	@Test
	public void testConsumeDuibaHdtoolOptionStock() throws BusinessException{
		remoteHdtoolStockConsumeService.consumeDuibaHdtoolOptionStock(15637L, 1L, "101", "test");
	}

	@Test
	public void testConsumeDuibaHdtoolOptionStockApi() throws BizException{
		remoteHdtoolStockConsumeService.consumeDuibaHdtoolOptionStockApi(15637L, 1L, "102", "test");
	}

	@Test
	public void testPaybackDuibaHdtoolOptionStock() throws BusinessException{
		remoteHdtoolStockConsumeService.paybackDuibaHdtoolOptionStock("102", "test");
	}

	@Test
	public void testPaybackDuibaHdtoolOptionStockApi() throws BizException{
		remoteHdtoolStockConsumeService.paybackDuibaHdtoolOptionStockApi("101", "test");
	}

	@Test
	public void testConsumeAppHdtoolOptionStock() throws BusinessException{
		remoteHdtoolStockConsumeService.consumeAppHdtoolOptionStock(6574L, 1L, "103", "test");
	}

	@Test
	public void testConsumeAppHdtoolOptionStockApi() throws BizException{
		remoteHdtoolStockConsumeService.consumeAppHdtoolOptionStockApi(6574L, 1L, "104", "test");
	}

	@Test
	public void testPaybackAppHdtoolOptionStock() throws BusinessException{
		remoteHdtoolStockConsumeService.paybackAppHdtoolOptionStock("103", "test");
	}

	@Test
	public void testPaybackAppHdtoolOptionStockApi() throws BizException{
		remoteHdtoolStockConsumeService.paybackAppHdtoolOptionStockApi("104", "test");
	}
}
