package cn.com.duiba.activity.center.biz.remoteservice.quizz;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.quizz.QuizzOrdersDto;
import cn.com.duiba.activity.center.api.remoteservice.quizz.RemoteQuizzOrdersSequenceService;
import cn.com.duiba.activity.center.api.remoteservice.quizz.RemoteQuizzOrdersService;
import cn.com.duiba.activity.center.api.remoteservice.quizz.RemoteQuizzOrdersStatusChangeService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ_CONSUMER)
@Ignore
public class RemoteQuizzOrdersStatusChangeServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private RemoteQuizzOrdersStatusChangeService quizzOrdersStatusChangeService;
	
	@Autowired
	private RemoteQuizzOrdersSequenceService quizzOrdersSequenceService;
	
	@Autowired
	private RemoteQuizzOrdersService quizzOrdersService;
	
	private QuizzOrdersDto info;
	
	@Before
	public void insertTest() {
		QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		quizzOrdersStatusChangeService.insert(e, quizzOrdersSequenceService.getId().getId());
		info = e;
	}
	
	@Test
	public void updateExchangeStatusToFailTest(){
		QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setStatus(2);
		e.setExchangeStatus(1);
		quizzOrdersStatusChangeService.insert(e, quizzOrdersSequenceService.getId().getId());
		
		quizzOrdersStatusChangeService.updateExchangeStatusToFail(e.getConsumerId(), e.getId(), info.getError4admin(), info.getError4developer(), info.getError4consumer());
		
		String error4admin = quizzOrdersStatusChangeService.find(e.getConsumerId(), e.getId()).getError4admin();
		Assert.assertTrue(error4admin.equals(info.getError4admin()));
	}

	/**
	* 修改订单领奖状态为过期失效
	*/
	@Test
	public void updateExchangeStatusToOverdueTest(){
		QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setExchangeStatus(1);
		quizzOrdersStatusChangeService.insert(e, quizzOrdersSequenceService.getId().getId());
		
		quizzOrdersService.insert(e);
		
		quizzOrdersStatusChangeService.updateExchangeStatusToOverdue(e.getId(), info.getError4admin(), info.getError4developer(), info.getError4consumer());
		
		String error4admin = quizzOrdersStatusChangeService.find(e.getConsumerId(), e.getId()).getError4admin();
		Assert.assertTrue(error4admin.equals(info.getError4admin()));
	}
	
	/**
	* 修改订单状态失败
	*/
	@Test
	public void updateStatusToFailTest(){
		QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setStatus(1);
		quizzOrdersStatusChangeService.insert(e, quizzOrdersSequenceService.getId().getId());
		
		quizzOrdersStatusChangeService.updateStatusToFail(e.getConsumerId(), e.getId(), info.getError4admin(), info.getError4developer(), info.getError4consumer());
		
		String error4admin = quizzOrdersStatusChangeService.find(e.getConsumerId(), e.getId()).getError4admin();
		Assert.assertTrue(error4admin.equals(info.getError4admin()));
	}
	
	/**
	* 处理领奖
	*/
	@Test
	public void doTakePrizeTest(){
		QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setExchangeStatus(1);
		e.setMainOrderId(null);
		quizzOrdersStatusChangeService.insert(e, quizzOrdersSequenceService.getId().getId());
		
		quizzOrdersStatusChangeService.doTakePrize(e.getConsumerId(), e.getId());
		
		int exchangeStatus = quizzOrdersStatusChangeService.find(e.getConsumerId(), e.getId()).getExchangeStatus();
		Assert.assertTrue(exchangeStatus == 2);
	}
	
	/**
	* 领奖创建订单失败，回滚领奖
	*/
	@Test
	public void rollbackTakePrizeTest(){
		QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setExchangeStatus(2);
		e.setMainOrderId(null);
		quizzOrdersStatusChangeService.insert(e, quizzOrdersSequenceService.getId().getId());
		
		quizzOrdersStatusChangeService.rollbackTakePrize(e.getConsumerId(), e.getId());
		
		int exchangeStatus = quizzOrdersStatusChangeService.find(e.getConsumerId(), e.getId()).getExchangeStatus();
		Assert.assertTrue(exchangeStatus == 1);
	}
	
	/**
	* 处理抽奖结果
	*/
	@Test
	public void updateLotteryResultTest(){
		QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setStatus(2);
		quizzOrdersStatusChangeService.insert(e, quizzOrdersSequenceService.getId().getId());
		
		quizzOrdersStatusChangeService.updateLotteryResult(e.getConsumerId(), e.getId(), info.getAppItemId(), info.getItemId(), 
				info.getPrizeId(), info.getPrizeName(), info.getPrizeType(), info.getPrizeFacePrice(), info.getCouponId());
		
		String prizeType = quizzOrdersStatusChangeService.find(e.getConsumerId(), e.getId()).getPrizeType();
		Assert.assertTrue(prizeType.equals(info.getPrizeType()));
	}

	/**
	* 福袋请求降级处理时更新订单为谢谢参与
	*/
	@Test
	public void updateLotteryLuckResultTest(){
		QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		
		quizzOrdersStatusChangeService.updateLotteryLuckResult(info.getConsumerId(), info.getId(), e.getAppItemId(), e.getItemId(), 
				e.getPrizeId(), e.getPrizeName(), e.getPrizeType(), e.getPrizeFacePrice(), e.getCouponId());
		
		String prizeType = quizzOrdersStatusChangeService.find(info.getConsumerId(), info.getId()).getPrizeType();
		Assert.assertTrue(prizeType.equals(e.getPrizeType()));
	}
}
