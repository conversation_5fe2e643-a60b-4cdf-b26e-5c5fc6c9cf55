package cn.com.duiba.activity.center.biz.remoteservice.zz;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.zhuanzhuan.FriendPointLogDto;
import cn.com.duiba.activity.center.api.params.FriendPointBaseQueryParam;
import cn.com.duiba.activity.center.api.remoteservice.zhuanzhuan.RemoteFriendPointService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;

/**
 * Created by sty on 2018/5/2.
 */
@Transactional(DsConstants.DATABASE_DUIBA_CUSTOM)

public class remoteZzTest extends TransactionalTestCaseBase {

  @Autowired
  private RemoteFriendPointService remoteFriendPointService;

  @Test
  public void testInsert(){
    FriendPointLogDto log = new FriendPointLogDto();
    log.setActivityId(1L);
    log.setAppId(2L);
    log.setConsumerId(3L);
    remoteFriendPointService.insert(log);
  }

  @Test
  public void testUpdateFriendPoint(){
    FriendPointLogDto log = new FriendPointLogDto();
    log.setActivityId(1L);
    log.setAppId(2L);
    log.setConsumerId(3L);
    log.setFriendPoint(15);
    remoteFriendPointService.updateFriendPoint(log);
  }

  @Test
  public void testUpdateUserInfo(){
    FriendPointLogDto log = new FriendPointLogDto();
    log.setActivityId(1L);
    log.setAppId(2L);
    log.setConsumerId(3L);
    log.setHelped(true);
    remoteFriendPointService.updateUserInfo(log);
  }

  @Test
  public void testUpdateOldFriendHelp(){
    FriendPointLogDto log = new FriendPointLogDto();
    log.setActivityId(1L);
    log.setAppId(2L);
    log.setConsumerId(3L);
    log.setOldHelped(1);
    remoteFriendPointService.updateOldFriendHelp(log);
  }

  @Test
  public void testFind(){
    FriendPointBaseQueryParam log = new FriendPointBaseQueryParam();
    log.setActivityId(1L);
    log.setAppId(2L);
    log.setConsumerId(3L);
    remoteFriendPointService.findByUserAndAct(log);
  }
}
