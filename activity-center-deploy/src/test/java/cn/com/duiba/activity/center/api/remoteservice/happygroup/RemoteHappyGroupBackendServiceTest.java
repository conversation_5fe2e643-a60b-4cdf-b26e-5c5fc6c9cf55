package cn.com.duiba.activity.center.api.remoteservice.happygroup;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupBackendConfigDto;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupItemDto;
import cn.com.duiba.api.enums.LimitScopeEnum;
import cn.com.duiba.boot.exception.BizException;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: zhen<PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/3/7 16:23
 * @description:
 */
@Rollback(value = false)
public class RemoteHappyGroupBackendServiceTest extends TransactionalTestCaseBase {

    @Autowired
    RemoteHappyGroupBackendService remoteHappyGroupBackendService;

    @Test
    public void testSave() throws BizException {
        HappyGroupBackendConfigDto happyGroupBackendConfigDto = new HappyGroupBackendConfigDto();
        happyGroupBackendConfigDto.setPublish(false);
        happyGroupBackendConfigDto.setId(9L);
        happyGroupBackendConfigDto.setAppId(1L);
        happyGroupBackendConfigDto.setTitle("测试活动");
        happyGroupBackendConfigDto.setRule("<p>111</p>");
        happyGroupBackendConfigDto.setSmallImage("//yun.duiba.com.cn/duiba-h5/sign/smallimagepsd2.jpg");
        happyGroupBackendConfigDto.setBannerImage("//yun.duiba.com.cn/duiba-h5/sign/bannerpsd.jpg");
        happyGroupBackendConfigDto.setShareTitle("11");
        happyGroupBackendConfigDto.setShareSubTitle("11");
        happyGroupBackendConfigDto.setSharePic("//yun.duiba.com.cn/duiba-h5/sign/img122708.png");
        happyGroupBackendConfigDto.setIosDownloadLink("11");
        happyGroupBackendConfigDto.setAndroidDownloadLink("11");
        happyGroupBackendConfigDto.setStartTime(new Date());
        happyGroupBackendConfigDto.setEndTime(new Date());
        happyGroupBackendConfigDto.setOpenGroupLimit(0);
        happyGroupBackendConfigDto.setOpenGroupLimit(1144);
        happyGroupBackendConfigDto.setJoinGroupLimitType(0);
        happyGroupBackendConfigDto.setJoinGroupLimit(11);

        List<HappyGroupItemDto> happyGroupItemDtoList = new ArrayList<>();

        HappyGroupItemDto item1 = new HappyGroupItemDto();
        item1.setId(28L);
        item1.setItemName("111");
        item1.setAppId(1L);
        item1.setActivityConfigId(9L);
        item1.setGroupNumber(11);
        item1.setExpiredTime(990660000L);
        item1.setPrizeType("object");
        item1.setOwnerPrizeId(8834L);
        item1.setOwnerPrizeName("兑吧实物编辑");
        item1.setMemberPrizeId(9976L);
        item1.setMemberPrizeName("郭燕飞实物1号");
        item1.setOpenGroupLimitType(0);
        item1.setOpenGroupLimit(11);
        happyGroupItemDtoList.add(item1);

        HappyGroupItemDto item2 = new HappyGroupItemDto();
        item2.setId(26L);
        item2.setItemName("测试21");
        item2.setAppId(1L);
        item2.setActivityConfigId(9L);
        item2.setGroupNumber(12);
        item2.setExpiredTime(1080720000L);
        item2.setPrizeType("credits");
        item2.setOwnerPrizeId(11L);
        item2.setOwnerPrizeName("111");
        item2.setMemberPrizeId(11L);
        item2.setMemberPrizeName("111");
        item2.setOpenGroupLimitType(0);
        item2.setOpenGroupLimit(11);
        happyGroupItemDtoList.add(item2);

        HappyGroupItemDto item3 = new HappyGroupItemDto();
        item3.setId(27L);
        item3.setItemName("11");
        item3.setAppId(1L);
        item3.setActivityConfigId(9L);
        item3.setGroupNumber(11);
        item3.setExpiredTime(990660000L);
        item3.setPrizeType("object");
        item3.setOwnerPrizeId(8834L);
        item3.setOwnerPrizeName("兑吧实物编辑");
        item3.setMemberPrizeId(9976L);
        item3.setMemberPrizeName("郭燕飞实物1号");
        item3.setOpenGroupLimitType(0);
        item3.setOpenGroupLimit(11);
        happyGroupItemDtoList.add(item3);

        HappyGroupItemDto item4 = new HappyGroupItemDto();
//        item2.setId(21L);
        item4.setItemName("测试2");
        item4.setAppId(1L);
        item4.setActivityConfigId(9L);
        item4.setGroupNumber(12);
        item4.setExpiredTime(1080720000L);
        item4.setPrizeType("credits");
        item4.setOwnerPrizeId(11L);
        item4.setOwnerPrizeName("111");
        item4.setMemberPrizeId(11L);
        item4.setMemberPrizeName("111");
        item4.setOpenGroupLimitType(0);
        item4.setOpenGroupLimit(11);
        happyGroupItemDtoList.add(item4);

        HappyGroupItemDto item5 = new HappyGroupItemDto();
//        item2.setId(26L);
        item5.setItemName("测试21");
        item5.setAppId(1L);
        item5.setActivityConfigId(9L);
        item5.setGroupNumber(12);
        item5.setExpiredTime(1080720000L);
        item5.setPrizeType("credits");
        item5.setOwnerPrizeId(11L);
        item5.setOwnerPrizeName("111");
        item5.setMemberPrizeId(11L);
        item5.setMemberPrizeName("111");
        item5.setOpenGroupLimitType(0);
        item5.setOpenGroupLimit(11);
        happyGroupItemDtoList.add(item5);

        happyGroupBackendConfigDto.setGroupItemList(happyGroupItemDtoList);

        System.out.println(JSON.toJSON(happyGroupBackendConfigDto));
        Long id = remoteHappyGroupBackendService.save(happyGroupBackendConfigDto);
        System.out.println(id);
        Assert.assertNotNull(id);
    }

    @Test
    public void testFind() throws  BizException{
        HappyGroupBackendConfigDto happyGroupBackendConfigDto = remoteHappyGroupBackendService.find(1L);
        System.out.println(JSON.toJSON(happyGroupBackendConfigDto));
        Assert.assertNotNull(happyGroupBackendConfigDto);
    }
}
