/**
 * Project Name:activity-center-deploy
 * File Name:ActivityCategoryRelationDaoTest.java
 * Package Name:cn.com.duiba.activity.center.biz.dao.category.impl
 * Date:2016年6月12日上午11:52:43
 * Copyright (c) 2016, duiba.com.cn All Rights Reserved.
 *
*/

package cn.com.duiba.activity.center.biz.dao.category.impl;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.category.ActivityCategoryRelationDao;
import cn.com.duiba.activity.center.biz.entity.ActivityCategoryRelationEntity;
import cn.com.duiba.wolf.utils.TestUtils;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

/**
 * ClassName:ActivityCategoryRelationDaoTest <br/>
 * Date:     2016年6月12日 上午11:52:43 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class ActivityCategoryRelationDaoTest extends TransactionalTestCaseBase{
    
    @Autowired
    private ActivityCategoryRelationDao activityCategoryRelationDao;

    private ActivityCategoryRelationEntity activityCategoryRelationEntity;
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS_ACTIVITY;


    @Before
    public void testInsert(){
        ActivityCategoryRelationEntity entity = new ActivityCategoryRelationEntity();
        TestUtils.setRandomAttributesForBean(entity, false);
        activityCategoryRelationDao.insert(entity);
        activityCategoryRelationEntity = entity;
    }

    @Test
    public void testSelectByAppCategory(){
        Long appId = this.activityCategoryRelationEntity.getAppId();
        Long categoryId = this.activityCategoryRelationEntity.getCategoryId();
        List<ActivityCategoryRelationEntity> list = this.activityCategoryRelationDao.selectByAppCategory(appId, categoryId);
        assertTrue(list!=null&&list.size()>0);
        for(ActivityCategoryRelationEntity temp:list){
            assertEquals(temp.getAppId().longValue(),appId.longValue());
            assertEquals(temp.getCategoryId().longValue(),categoryId.longValue());
        }
    }
    @Test
    public void testSelect(){
        Long id = this.activityCategoryRelationEntity.getId();
        System.out.println(id);
        ActivityCategoryRelationEntity entity = this.activityCategoryRelationDao.select(id);
        assertEquals(entity.getId().longValue(),id.longValue());
    }
    @Test
    public void testUpdatePlayload(){
        Long id = this.activityCategoryRelationEntity.getId();
        int payload = this.activityCategoryRelationEntity.getPayload();
        int rs = this.activityCategoryRelationDao.updatePayload(id, payload);
        assertEquals(1,rs);
        assertEquals(payload,this.activityCategoryRelationDao.select(id).getPayload().intValue());
    }
    @Test
    public void testSelectWithConditions(){
        ActivityCategoryRelationEntity c1 = new ActivityCategoryRelationEntity();
        ActivityCategoryRelationEntity c2 = new ActivityCategoryRelationEntity();

        c1.setAppId(1L);
        c2.setCategoryId(1L);

        test(c1);
        test(c2);

    }
    private void test(ActivityCategoryRelationEntity condition){
        List<ActivityCategoryRelationEntity> rs =  this.activityCategoryRelationDao.selectWithConditions(condition);
        for(ActivityCategoryRelationEntity temp:rs){
            if(condition.getId()!=null){
                assertEquals(temp.getId().longValue(),condition.getId().longValue());
            }
            if(condition.getAppId()!=null){
                assertEquals(temp.getAppId().longValue(),condition.getAppId().longValue());
            }
            if(condition.getCategoryId()!=null){
                assertEquals(temp.getCategoryId().longValue(),condition.getCategoryId().longValue());
            }
            if(condition.getOperatingActivityId()!=null){
                assertEquals(temp.getOperatingActivityId().longValue(), condition.getOperatingActivityId().longValue());
            }
            if(condition.getPayload()!=null){
                assertEquals(temp.getPayload().longValue(),condition.getPayload().longValue());
            }
        }
    }

    @Test
    public void testdelete(){
        Long operatingActivityId = this.activityCategoryRelationEntity.getOperatingActivityId();
        int rs = this.activityCategoryRelationDao.deleteByOperatingActivityId(operatingActivityId);
        assertEquals(1,rs);
    }

}

