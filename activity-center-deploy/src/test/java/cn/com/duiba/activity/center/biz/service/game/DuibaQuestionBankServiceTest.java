package cn.com.duiba.activity.center.biz.service.game;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionBankDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/7.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaQuestionBankServiceTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaQuestionBankService duibaQuestionBankService;

    private ThreadLocal<DuibaQuestionBankDto> duibaQuestionBankDO=new ThreadLocal<>();

    @Before
    public void testInsert() {
        DuibaQuestionBankDto d=new DuibaQuestionBankDto();
        TestUtils.setRandomAttributesForBean(d,false);
        d.setDeleted(0);
        d.setNumber(10);
        duibaQuestionBankService.insert(d);
        duibaQuestionBankDO.set(d);
    }

    @Test
    public void testFind() {
        DuibaQuestionBankDto e=duibaQuestionBankDO.get();
        DuibaQuestionBankDto e1=duibaQuestionBankService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindByPage() {
        Assert.assertTrue(duibaQuestionBankService.findByPage(0,10).size()>0);
    }

    @Test
    public void testFindAll() {
        Assert.assertTrue(duibaQuestionBankService.findAll().size()>0);
    }

    @Test
    public void testFindTotalCount() {
        Assert.assertTrue(duibaQuestionBankService.findTotalCount()>0);
    }

    @Test
    public void testUpdateName() {
        Assert.assertTrue(duibaQuestionBankService.updateName(duibaQuestionBankDO.get().getId(),"test")>0);
    }

    @Test
    public void testUpdateNumberAddAndSub() {
        Assert.assertTrue(duibaQuestionBankService.updateNumberAdd(duibaQuestionBankDO.get().getId())>0);
        Assert.assertTrue(duibaQuestionBankService.updateNumberSub(duibaQuestionBankDO.get().getId())>0);
    }

    @Test
    public void testDelete() {
        Assert.assertTrue(duibaQuestionBankService.delete(duibaQuestionBankDO.get().getId())>0);

    }

    private void assertDO(DuibaQuestionBankDto e, DuibaQuestionBankDto e1) {
        assertDO(e, e1, null);
    }

    private void assertDO(DuibaQuestionBankDto e, DuibaQuestionBankDto e1, String[] exculdeFields) {
        List<String> exculdeFieldsList = new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate", "gmtModified","number"}));
        if (exculdeFields != null && exculdeFields.length > 0) {
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1, false, exculdeFieldsList.toArray(new String[exculdeFieldsList.size() + 1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr(e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr(e1.getGmtModified()));
    }

}
