package cn.com.duiba.activity.center.api.remoteservice;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.ActivityKeyDto;
import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.params.OptQueryByIdsParam;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteOperatingActivityService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by xiaoxuda on 2018/11/20.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class RemoteOperatingActivityServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteOperatingActivityService remoteOperatingActivityService;

    @Test
    public void test(){
        OptQueryByIdsParam param = new OptQueryByIdsParam();
        param.setOptIds(Lists.newArrayList(44104l,76699l,80445l));
        param.setAppId(22028L);
        param.setDeleted(false);
        param.setStatus(OperatingActivityDto.StatusIntOpen);
        List<ActivityKeyDto> result = remoteOperatingActivityService.findByOptIdsUnDeletedAndOpen(param);
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.get(0));
        Assert.assertNotNull(result.get(0).getOperatingActivityDto());
    }
}
