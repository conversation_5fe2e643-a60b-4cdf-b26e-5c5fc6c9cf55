package cn.com.duiba.activity.center.api.remoteservice.ngame;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.ngame.NgameConsumerRecordDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by xutao on 2018/4/19.
 */
@Transactional(DsConstants.DATABASE_NGAME)
public class RemoteNgameConsumerRecordServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteNgameConsumerRecordService remoteNgameConsumerRecordService;

    @Test
    public void findRecordByConIdAndNgameId() {
        NgameConsumerRecordDto dto = remoteNgameConsumerRecordService.findRecordByConIdAndNgameId(500098005L, 3812L);
        if (dto != null) {
            remoteNgameConsumerRecordService.updateIsGivePrize(dto.getId(), false);
            remoteNgameConsumerRecordService.updateScore(dto.getId(), 100L);
        }
    }
}
