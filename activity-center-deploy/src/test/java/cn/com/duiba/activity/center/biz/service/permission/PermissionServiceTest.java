package cn.com.duiba.activity.center.biz.service.permission;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.permission.PermissionEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by zhengjy on 2016/12/8.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class PermissionServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private PermissionService permissionService;

    private PermissionEntity permissionDO;

    @Before
    public void testInsert() {
        PermissionEntity e=new PermissionEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        permissionService.insert(e);
        permissionDO=e;
    }

    @Test
    public  void testSelectBySource(){
        PermissionEntity e=permissionDO;
        PermissionEntity e1 =permissionService.findBySource(e.getSourceId(),e.getSourceType());
        assertDO(e,e1);
    }

    @Test
    public void testUpdate(){
        PermissionEntity e=new PermissionEntity();
        e.setId(permissionDO.getId());
        TestUtils.setRandomAttributesForBean(e,false);
        permissionService.update(e);
    }

    @Test
    public void testfindAllPermission(){
        Assert.assertNotNull(permissionService.findAllPermission(1l));
    }

    private void assertDO(PermissionEntity e, PermissionEntity e1){
        String[] exceptFields=new String[]{"gmtCreate","gmtModified","overDate"};
        TestUtils.assertEqualsReflect(e, e1,false,exceptFields);//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }
}
