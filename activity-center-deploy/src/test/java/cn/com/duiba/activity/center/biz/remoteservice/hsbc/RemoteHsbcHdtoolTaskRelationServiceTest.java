package cn.com.duiba.activity.center.biz.remoteservice.hsbc;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.hsbc.HsbcHdtoolTaskRelationDto;
import cn.com.duiba.activity.center.api.params.hsbc.HsbcTaskRelationShipParams;
import cn.com.duiba.activity.center.api.remoteservice.hsbc.RemoteHsbcHdtoolTaskRelationService;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

public class RemoteHsbcHdtoolTaskRelationServiceTest extends TransactionalTestCaseBase {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RemoteHsbcHdtoolTaskRelationService remoteHsbcHdtoolTaskRelationService;

    @Test
    public void batchInsertTest() {
        HsbcHdtoolTaskRelationDto taskRelationDto = new HsbcHdtoolTaskRelationDto();

        taskRelationDto.setId(1L);
        taskRelationDto.setTaskSubtitle("yyy");
        taskRelationDto.setTaskRewardFreeTimes(5);
        taskRelationDto.setExtraJson("test");

        HsbcTaskRelationShipParams params = new HsbcTaskRelationShipParams();
        params.setUpdateRelationList(Collections.singletonList(taskRelationDto));

        remoteHsbcHdtoolTaskRelationService.bindHdtoolAndTaskRelationship(params);
    }


    @Test
    public void findRelationByTaskIdList() {
        List<HsbcHdtoolTaskRelationDto> relationshipList = remoteHsbcHdtoolTaskRelationService.findByAppIdAndTaskIdList(47805L, Collections.singletonList("TASK2021102510002"));
        log.info("relationshipList = {}", JSON.toJSONString(relationshipList));

        Assert.assertTrue(CollectionUtils.isNotEmpty(relationshipList));
    }
    
    
    @Test
    public void findByOpIdTest() {
        List<HsbcHdtoolTaskRelationDto> result = remoteHsbcHdtoolTaskRelationService.findByOpId(123L);
        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
    }
}
