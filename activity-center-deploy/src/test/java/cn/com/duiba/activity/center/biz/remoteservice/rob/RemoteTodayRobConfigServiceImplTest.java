/**
 * Project Name:activity-center-deploy
 * File Name:RemoteTodayRobConfigServiceImplTest.java
 * Package Name:cn.com.duiba.activity.center.biz.remoteservice.rob
 * Date:2016年7月26日下午2:34:27
 * Copyright (c) 2016, duiba.com.cn All Rights Reserved.
 *
*/

package cn.com.duiba.activity.center.biz.remoteservice.rob;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.rob.TodayRobConfigDto;
import cn.com.duiba.activity.center.api.remoteservice.rob.RemoteTodayRobConfigService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * ClassName:RemoteTodayRobConfigServiceImplTest <br/>
 * Date:     2016年7月26日 下午2:34:27 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
@Ignore
public class RemoteTodayRobConfigServiceImplTest extends TransactionalTestCaseBase{

    @Autowired
    private RemoteTodayRobConfigService remoteTodayRobConfigService;
   
    TodayRobConfigDto dto = null;
    
    @Before
    public void insertTest(){
        dto = new TodayRobConfigDto();
        TestUtils.setRandomAttributesForBean(dto,false);
        dto.setStatus(1);
        dto.setDeleted(false);
        dto.setSwitches(1);
        remoteTodayRobConfigService.saveOrUpdateTodayRobConfig(dto);
    }
    
    @Test
    public void insert(){
        TodayRobConfigDto dto = new TodayRobConfigDto();
        TestUtils.setRandomAttributesForBean(dto,false);
        dto.setStatus(1);
        dto.setDeleted(false);
        dto.setSwitches(1);
        remoteTodayRobConfigService.saveOrUpdateTodayRobConfig(dto);
    }
    
    @Test
    public void findPage(){
        Map<String,Object> params = new HashMap<>();
        params.put("id", 1);
        params.put("offset", 0);
        params.put("pageSize", 10);
        try{
            List<TodayRobConfigDto> list = remoteTodayRobConfigService.findTodayRobConfigPage(params).getResult();
            Assert.assertTrue(list.size() == 0);
        }catch(Exception e){
            e.printStackTrace();
        }
    }
}

