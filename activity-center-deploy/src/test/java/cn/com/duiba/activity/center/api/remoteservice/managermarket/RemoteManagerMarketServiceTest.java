package cn.com.duiba.activity.center.api.remoteservice.managermarket;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.managermarket.*;
import cn.com.duiba.activity.center.api.params.ManagerMarketInviteRecordParam;
import com.alibaba.fastjson.JSON;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.Date;
import java.util.List;

/**
 * @Author: lufeng
 * @Description:
 * @Date: Created in 2020/8/10
 */
@Rollback(value = false)
public class RemoteManagerMarketServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteManagerMarketService remoteManagerMarketService;

    @Test
    public void testInsertConfig(){
        ManagerMarketBackendConfigDto dto = new ManagerMarketBackendConfigDto();
        dto.setAppId(1L);
        dto.setOpId(1L);
        dto.setTitle("这是标题");
        dto.setUnit("单位");
        dto.setStaffExcelName("员工表格1.xls");
        dto.setStaffExcelUrl("www.baidu.com");
        dto.setOrganExcelName("机构表格1.xls");
        dto.setOrganExcelUrl("www.taobao.com");
        dto.setRankSwitch(true);
        dto.setRankPeriod("1");
        dto.setRankLatitude("1,2");
        ManagerMarketTaskDto taskDto = new ManagerMarketTaskDto();
        taskDto.setAppId(1L);
        taskDto.setType(1);
        taskDto.setName("任务名称");
        taskDto.setContext("文本内容");
        taskDto.setImage("www.baidu.com");
        taskDto.setStaffExclusiveUrl("www.baidu.com");
        taskDto.setActivityUrl("www.baidu.com");
        taskDto.setShareImage("分享图片");
        taskDto.setShareTitle("分享标题");
        taskDto.setShareSubtitle("分享副标题");
        taskDto.setWxFriendScore(1);
        taskDto.setWxCircleScore(3);
        taskDto.setUrlOpenedScore(5);
        taskDto.setBindedScore(7);
        taskDto.setLimitScore(100);
        taskDto.setEndTime(new Date());
        taskDto.setMustTaskSwitch(false);
        taskDto.setToppingSwitch(true);
        taskDto.setToppingTime(new Date());
        List<ManagerMarketTaskDto> list = Lists.newArrayList();
        list.add(taskDto);
        dto.setManagerMarketTaskDtoList(list);
        System.out.println(remoteManagerMarketService.saveConfig(dto));
    }

    @Test
    public void testSelectConfig() {
        ManagerMarketBackendConfigDto dto = remoteManagerMarketService.getBackendConfig(3L);
        System.out.println("title :" + dto.getTitle());
    }

    @Test
    public void testInsertStaff() {
        List<ManagerMarketStaffDto> list = Lists.newArrayList();
        ManagerMarketStaffDto staffDto = new ManagerMarketStaffDto();
        staffDto.setAppId(1L);
        staffDto.setConfigId(1L);
        staffDto.setName("卢锋");
        staffDto.setNumber("30902098");
        staffDto.setPhone("15068116897");
        list.add(staffDto);
        ManagerMarketStaffDto staffDto1 = new ManagerMarketStaffDto();
        staffDto1.setAppId(1L);
        staffDto1.setConfigId(1L);
        staffDto1.setName("卢锋1");
        staffDto1.setNumber("30902099");
        staffDto1.setPhone("15068116896");
        list.add(staffDto1);
        boolean result = remoteManagerMarketService.batchSaveStaff(list);
        System.out.println("保存结果：" + result);
    }

    @Test
    public void testDeleteStaff() {
        boolean result = remoteManagerMarketService.deleteStaff(1L,1L);
        System.out.println("删除结果：" + result);
    }

    @Test
    public void testSelect() {
        ManagerMarketStaffDto dto = remoteManagerMarketService.selectByConfigIdAndPhone(1L, "15068116897");
        System.out.println("查询结果：" + dto.getName());
    }

    @Test
    public void testInsertOrgan() {
        List<ManagerMarketOrganDto> list = Lists.newArrayList();
        ManagerMarketOrganDto dto = new ManagerMarketOrganDto();
        dto.setAppId(1L);
        dto.setConfigId(1L);
        dto.setName("杭州分行");
        dto.setNumber("11111");
        list.add(dto);
        ManagerMarketOrganDto dto1 = new ManagerMarketOrganDto();
        dto1.setAppId(1L);
        dto1.setConfigId(1L);
        dto1.setName("杭州分行1");
        dto1.setNumber("22222");
        list.add(dto1);
        boolean result = remoteManagerMarketService.batchSaveOrgan(list);
        System.out.println("保存结果：" + result);
    }

    @Test
    public void testSaveStaffExt() {
        ManagerMarketStaffExtDto dto = new ManagerMarketStaffExtDto();
        dto.setId(2L);
        dto.setAppId(1L);
        dto.setConfigId(1L);
        dto.setHeadImage("www.baidu.com1");
        dto.setOrganNumber("11112");
        dto.setPosition("总监2");
        dto.setQrCode("www.baidu.com1");
        dto.setStaffNumber("30902098");
        boolean result = remoteManagerMarketService.enterStaff(dto);
        System.out.println("保存结果：" + result);
    }

    @Test
    public void testSelectStaffExt() {
        ManagerMarketStaffExtDto dto = remoteManagerMarketService.getStaffExtById(1L);
        System.out.println("保存结果：" + dto.getStaffNumber());
    }

    @Test
    public void testInsertRand() {
        ManagerMarketRankDto dto = new ManagerMarketRankDto();
        dto.setAppId(1L);
        dto.setConfigId(1L);
        dto.setRankTime("2020-1-5");
        dto.setRankPeriodType(1);
        dto.setRankLatitudeType(1);
        dto.setStaffName("卢锋");
        dto.setStaffNumber("30902098");
        dto.setOrganName("杭州分行");
        dto.setTotalScore(0L);
        Long id = remoteManagerMarketService.saveRank(dto);
        System.out.println("保存结果：" + id);
    }

    @Test
    public void testIncScore() {
        Long id = 2L;
        int score = 3;
        int opType = 2;
        boolean result = remoteManagerMarketService.incrTotalScoreByOpType(id, score, opType);
        System.out.println("保存结果：" + result);
    }

    @Test
    public void insertInviteRecord() {
        ManagerMarketInviteRecordDTO dto = new ManagerMarketInviteRecordDTO();
        dto.setInviterPhone("18810146888");
        dto.setUserId(111142L);
        dto.setConfigId(3L);
        dto.setStaffNumber("22122xx");
        dto.setInviterUserId(3333L);
        dto.setInviterOpenId("xasdsaasdc");
        remoteManagerMarketService.insertInviteRecord(dto);

    }

    @Test
    public void selectInviteRecordListByParam() {
        ManagerMarketInviteRecordParam dto = new ManagerMarketInviteRecordParam();
        dto.setBegin("2020-11-23 18:00:00");
        dto.setEnd("2020-11-24 19:00:00");
        dto.setAppId(1L);
        dto.setConfigIds(com.google.common.collect.Lists.newArrayList(1L,20L));
        List<ManagerMarketInviteRecordDTO> list = remoteManagerMarketService.selectInviteRecordListByParam(dto);
        System.out.println(JSON.toJSONString(list));

    }


}
