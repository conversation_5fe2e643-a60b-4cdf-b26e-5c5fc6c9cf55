package cn.com.duiba.activity.center.biz.service.quizz;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.api.dto.quizz.DuibaQuizzDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.quizz.QuizzBaseTest;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/24.
 */
@Transactional(DsConstants.DATABASE_QUIZZ)
public class DuibaQuizzServiceTest extends QuizzBaseTest {

    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    // protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_QUIZZ;

    @Resource
    private DuibaQuizzService duibaQuizzService;

    private DuibaQuizzDto     DuibaQuizzDto;

    @Before
    public void testInsert() {
        DuibaQuizzDto e = new DuibaQuizzDto();
        TestUtils.setRandomAttributesForBean(e, false);
        e.setDeleted(false);
        duibaQuizzService.insert(e);
        DuibaQuizzDto = e;
    }

    @Test
    public void testFind() {
        DuibaQuizzDto e = DuibaQuizzDto;
        DuibaQuizzDto e1 = duibaQuizzService.find(e.getId());
        assertDO(e, e1);
    }

    @Test
    public void testFindByPage() {
        Assert.assertTrue(duibaQuizzService.findByPage(0, 10).size() > 0);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("max", 10);
        paramMap.put("offset", 0);
        Assert.assertTrue(duibaQuizzService.findByPage(paramMap).size() > 0);
    }

    @Test
    public void testFindPageCount() {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("title", DuibaQuizzDto.getTitle());
        Assert.assertTrue(duibaQuizzService.findPageCount(paramMap) > 0);
    }

    @Test
    public void testUpdateStatus() {
        duibaQuizzService.updateStatus(DuibaQuizzDto.getId(), 42);
        Assert.assertTrue(duibaQuizzService.find(DuibaQuizzDto.getId()).getStatus().equals(42));
    }

    @Test
    public void testDelete() {
        duibaQuizzService.delete(DuibaQuizzDto.getId());
        Assert.assertTrue(duibaQuizzService.find(DuibaQuizzDto.getId()).getDeleted());
    }

    @Test
    public void testUpdateInfoForm() {
        DuibaQuizzDto.setBanner("test");
        Assert.assertTrue(duibaQuizzService.updateInfoForm(DuibaQuizzDto) > 0);
    }

    @Test
    public void testUpdateAutoOffDateNull() {
        duibaQuizzService.updateAutoOffDateNull(DuibaQuizzDto.getId());
        Assert.assertNull(duibaQuizzService.find(DuibaQuizzDto.getId()).getAutoOffDate());
    }

    @Test
    public void testUpdateSwitches() {
        duibaQuizzService.updateSwitches(DuibaQuizzDto.getId(), 42l);
        Assert.assertTrue(duibaQuizzService.find(DuibaQuizzDto.getId()).getSwitches().equals(42));
    }

    @Test
    public void testFindAutoOff() {
        DuibaQuizzDto e = new DuibaQuizzDto();
        TestUtils.setRandomAttributesForBean(e, false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis() - 1000000l));
        duibaQuizzService.insert(e);
        Assert.assertTrue(duibaQuizzService.findAutoOff().size() > 0);
    }

    @Test
    public void testFindAllByIds() {
        assertDO(DuibaQuizzDto, duibaQuizzService.findAllByIds(Arrays.asList(DuibaQuizzDto.getId())).get(0));
    }

    @Test
    public void testFindAllQuizz() {
        DuibaQuizzDto e = new DuibaQuizzDto();
        TestUtils.setRandomAttributesForBean(e, false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis() - 1000000l));
        duibaQuizzService.insert(e);
        Assert.assertTrue(duibaQuizzService.findAllQuizz().size() > 0);// lappid没用到
    }

    private void assertDO(DuibaQuizzDto e, DuibaQuizzDto e1) {
        assertDO(e, e1, null);
    }

    private void assertDO(DuibaQuizzDto e, DuibaQuizzDto e1, String[] exculdeFields) {
        List<String> exculdeFieldsList = new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[] { "gmtCreate", "gmtModified", "autoOffDate","tag", }));
        if (exculdeFields != null && exculdeFields.length > 0) {
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1, false, exculdeFieldsList.toArray(new String[exculdeFieldsList.size() + 1]));// 时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr(e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr(e1.getGmtModified()));
    }

}
