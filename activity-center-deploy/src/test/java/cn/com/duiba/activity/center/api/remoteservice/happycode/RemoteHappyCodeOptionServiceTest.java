package cn.com.duiba.activity.center.api.remoteservice.happycode;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.happy_code.HappyCodeOptionDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import com.google.common.collect.Lists;
import javax.validation.constraints.AssertTrue;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Created by hww on 2017/12/11
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class RemoteHappyCodeOptionServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteHappyCodeOptionService remoteHappyCodeOptionService;


    private long insertTest(Long phaseId) {
        HappyCodeOptionDto option = new HappyCodeOptionDto();
        option.setItemId(1L);
        option.setPrizeType("prizeType");
        option.setPhaseId(phaseId);
        option.setPrizeName("prizeName");
        option.setMemo("memo");
        option.setLogo("logo");
        option.setGmtModified(new Date());
        option.setGmtCreate(new Date());
        option.setDescription("de");
        option.setDeleted(0);
        option.setItemName("itemName");
        option.setRemaining(1);
        return remoteHappyCodeOptionService.insert(option);
    }

    @Test
    public void testBatchInsert(){
        List<HappyCodeOptionDto> list = Lists.newArrayList();
        HappyCodeOptionDto option = new HappyCodeOptionDto();
        option.setItemId(1L);
        option.setPrizeType("prizeType");
        option.setPhaseId(1L);
        option.setPrizeName("prizeName");
        option.setMemo("memo");
        option.setLogo("logo");
        option.setGmtModified(new Date());
        option.setGmtCreate(new Date());
        option.setDescription("de");
        option.setDeleted(0);
        option.setItemName("itemName");
        option.setRemaining(1);
        list.add(option);
        Assert.assertTrue(remoteHappyCodeOptionService.batchInsert(list));
    }

    @Test
    public void findOneByPhaseIdTest() {

        HappyCodeOptionDto option = remoteHappyCodeOptionService.findOneByPhaseId(903L);
        Assert.assertTrue(option != null);
    }

    @Test
    public void findByPhaseIdsTest() {
        Long phaseId1 = (long) (Math.random() * 10000);
        insertTest(phaseId1);
        Long phaseId2 = (long) (Math.random() * 10000);
        insertTest(phaseId2);
        Long phaseId3 = (long) (Math.random() * 10000);
        insertTest(phaseId3);
        List<HappyCodeOptionDto> options = remoteHappyCodeOptionService.findByPhaseIds(Lists.newArrayList(phaseId1, phaseId2, phaseId3));
        Assert.assertTrue(options.size() == 3);
    }

    @Test
    public void updateOptionTest() {
        Long phaseId1 = (long) (Math.random() * 10000);
        Long optionId = insertTest(phaseId1);

        HappyCodeOptionDto option = remoteHappyCodeOptionService.findById(optionId);
        String oldPrizeName = option.getPrizeName();
        option.setPrizeName(oldPrizeName + "1");
        remoteHappyCodeOptionService.updateOption(option);
        HappyCodeOptionDto newOption = remoteHappyCodeOptionService.findById(optionId);
        Assert.assertTrue(Objects.equals(newOption.getPrizeName(), oldPrizeName + "1"));
    }

}
