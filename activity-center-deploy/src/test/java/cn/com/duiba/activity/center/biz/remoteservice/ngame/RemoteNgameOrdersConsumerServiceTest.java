package cn.com.duiba.activity.center.biz.remoteservice.ngame;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.ngame.NgameOrdersDto;
import cn.com.duiba.activity.center.api.remoteservice.ngame_con.RemoteNgameOrdersConsumerService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by pc on 2017/9/11.
 */
@Transactional(DsConstants.DATABASE_NGAME)
public class RemoteNgameOrdersConsumerServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteNgameOrdersConsumerService orderService;

    @Test
    public void test() {
        NgameOrdersDto insertDto = getNgame();
        Long score = (long) (Math.random() * 10000);
        orderService.insert(insertDto);
        orderService.updateScore(insertDto.getConsumerId(), insertDto.getId(), score);
        NgameOrdersDto check = orderService.find(insertDto.getConsumerId(), insertDto.getId());
        Assert.assertEquals(check.getScore(), score);
    }

    private NgameOrdersDto getNgame() {
        NgameOrdersDto ngame = new NgameOrdersDto();
        ngame.setAppId(1L);
        ngame.setConsumerId(1L);
        ngame.setOperatingActivityId(1L);
        ngame.setOrderStatus(NgameOrdersDto.StatusConsumeSuccess);
        ngame.setExchangeStatus(NgameOrdersDto.ExchangeStatusWait);
        ngame.setCredits(0L);
        return ngame;
    }




}
