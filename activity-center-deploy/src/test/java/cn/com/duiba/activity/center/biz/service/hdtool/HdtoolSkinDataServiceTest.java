package cn.com.duiba.activity.center.biz.service.hdtool;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolSkinDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/7/6.
 */
@Transactional(DsConstants.DATABASE_CREDITS)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class HdtoolSkinDataServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private HdtoolSkinDataService hdtoolSkinDataService;

    @Test
    public void testInsert() throws Exception {
        HdtoolSkinDto e=new HdtoolSkinDto();
        TestUtils.setRandomAttributesForBean(e,false);
        hdtoolSkinDataService.createHdtoolSkinData(e);
    }

    @Test
    public void testUpdateDataJson() throws Exception {
        HdtoolSkinDto e=new HdtoolSkinDto();
        TestUtils.setRandomAttributesForBean(e,false);
        hdtoolSkinDataService.createHdtoolSkinData(e);
        Assert.assertTrue(hdtoolSkinDataService.updateHdtoolSkinData(e.getHdtoolId(),e.getType(),"test")>0);
    }

    @Test
    public void testSelectJsonByHdtoolIdAndType() throws Exception {
        HdtoolSkinDto e=new HdtoolSkinDto();
        TestUtils.setRandomAttributesForBean(e,false);
        hdtoolSkinDataService.createHdtoolSkinData(e);
        Assert.assertNotNull(hdtoolSkinDataService.querySkin(e.getHdtoolId(),e.getType()));
    }

    @Test
    public void testSelectByHdtoolIdAndType() throws Exception {
        HdtoolSkinDto e=new HdtoolSkinDto();
        TestUtils.setRandomAttributesForBean(e,false);
        hdtoolSkinDataService.createHdtoolSkinData(e);
        Assert.assertNotNull(hdtoolSkinDataService.queryBaseHdtoolSkin(e.getHdtoolId(),e.getType()));
    }
}
