package cn.com.duiba.activity.center.biz.dao.activity_order;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;

/**
 * Created by yansen on 16/9/20.
 */

@Transactional(DsConstants.DATABASE_ACTIVITY_ORDER)
public class ActivityOrderDaoTest extends TransactionalTestCaseBase {

    @Autowired
    private ActivityOrderHouseDao activityOrderHouseDao;

    @Test
    public void findByExpirationTime() {
        Date now = new Date();
        String expirationTime = DateUtils.getSecondStr(DateUtils.daysAddOrSub(now, -10));
        String activityType = "hello";
        activityOrderHouseDao.findByExpirationTime(expirationTime, activityType);
    }

    @Test
    public void findOptionIds() {
        Long consumerId = 1L;
        Long appId = 1L;
        String activityType = "hello";
        activityOrderHouseDao.findOptionIds(consumerId, appId, activityType);
    }

    @Test
    public void findByAppIdWithPage() {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("duibaActivityId", "1");
        queryMap.put("appId", "1");
        queryMap.put("start", 1);
        queryMap.put("pageSize", 10);
        activityOrderHouseDao.findByAppIdWithPage(queryMap);
    }

    @Test
    public void getCountByAppId() {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("appId", "1");
        queryMap.put("duibaActivityId", "1");
        activityOrderHouseDao.getCountByAppId(queryMap);
    }

    @Test
    public void findFailByActivity4App() {
        List<Long> activityIds = new ArrayList<>();
        activityIds.add(1L);
        Long appId = 1L;
        activityOrderHouseDao.findFailByActivity4App(activityIds, appId);
    }

}
