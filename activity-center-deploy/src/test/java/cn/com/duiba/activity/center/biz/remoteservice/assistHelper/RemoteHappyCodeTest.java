package cn.com.duiba.activity.center.biz.remoteservice.assistHelper;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.happycodenew.HappyCodeUserRecordDto;
import cn.com.duiba.activity.center.api.params.happycode.HappyCodePageParams;
import cn.com.duiba.activity.center.api.remoteservice.happycodenew.RemoteHappyCodeUserRecordService;
import cn.com.duiba.api.bo.page.Page;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class RemoteHappyCodeTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteHappyCodeUserRecordService remoteHappyCodeUserRecordService;

    @Test
    public void haggleOpenRecords() {
        HappyCodePageParams params = new HappyCodePageParams();
        params.setPageNum(1);
        params.setPageSize(10);
        params.setConsumerId(1539361L);
        params.setActId(16L);
        params.setPhaseId(13L);
        Page<HappyCodeUserRecordDto> result = remoteHappyCodeUserRecordService.findRecordByCidAndActIdAndPhaseId(params);
        Assert.assertTrue(result.getTotalCount() > 0);
    }
}
