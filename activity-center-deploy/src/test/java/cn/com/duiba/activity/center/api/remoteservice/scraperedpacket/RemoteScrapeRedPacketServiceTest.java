package cn.com.duiba.activity.center.api.remoteservice.scraperedpacket;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.scraperedpacket.MiniProgramConfig;
import cn.com.duiba.activity.center.api.dto.scraperedpacket.OfficialAccountConfig;
import cn.com.duiba.activity.center.api.dto.scraperedpacket.ScrapeRedPacketDto;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

public class RemoteScrapeRedPacketServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteScrapeRedPacketService remoteScrapeRedPacketService;


    @Test
    public void testInert() {
        ScrapeRedPacketDto dto = getDto();
        ScrapeRedPacketDto newDto = remoteScrapeRedPacketService.insert(dto);
        Assert.assertNotNull(newDto.getId());
        Assert.assertNotNull(newDto.getOperatingActivityId());
    }

    @Test
    public void testUpdate() {
        ScrapeRedPacketDto dto = getDto();
        ScrapeRedPacketDto newDto = remoteScrapeRedPacketService.insert(dto);
        Assert.assertNotNull(newDto.getId());
        Assert.assertNotNull(newDto.getOperatingActivityId());
        dto.setId(newDto.getId());
        dto.setOfficialAccountConfig(null);
        dto.setWarnBudget(null);
        remoteScrapeRedPacketService.update(dto);
        ScrapeRedPacketDto config = remoteScrapeRedPacketService.findById(newDto.getId());
        Assert.assertNull(config.getOfficialAccountConfig());
        Assert.assertNull(config.getWarnBudget());
    }

    @Test
    public void testDeleteById() {
        ScrapeRedPacketDto dto = getDto();
        ScrapeRedPacketDto newDto = remoteScrapeRedPacketService.insert(dto);
        Assert.assertNotNull(newDto.getId());
        int skip = remoteScrapeRedPacketService.deleteById(newDto.getId());
        Assert.assertEquals(1, skip);
        ScrapeRedPacketDto config = remoteScrapeRedPacketService.findById(newDto.getId());
        Assert.assertNull(config);
    }

    private ScrapeRedPacketDto getDto() {
        ScrapeRedPacketDto entity = new ScrapeRedPacketDto();
        entity.setTitle("这是标题");
        entity.setRule("这是规则");
        OfficialAccountConfig config = new OfficialAccountConfig();
        config.setTarget(1);
        config.setTitle("标题");
        config.setSubTitle("副标题");
        config.setImage("图片");
        config.setCode("二维码");
        entity.setOfficialAccountConfig(config);
        MiniProgramConfig config1 = new MiniProgramConfig();
        config1.setTarget(1);
        config1.setTitle("标题2");
        config1.setImage("图片2");
        entity.setMiniProgramConfig(config1);
        entity.setStartTime(new Date());
        entity.setEndTime(new Date());
        entity.setBannerImage("这是banner图");
        entity.setSmallImage("这是缩略图");
        entity.setOpenWheel(0);
        entity.setBonusLeft(1L);
        entity.setBonusRight(2L);
        entity.setTargetNumber(0);
        entity.setBonusNumber(0);
        entity.setHighestRate("1.5");
        entity.setBonusLimit(0L);
        entity.setTotalBudget(0L);
        entity.setWarnBudget(0L);
        entity.setPhoneNumber("110");
        entity.setOperatingActivityId(0L);
        entity.setAppId(0L);
        entity.setCustomAccountId(0L);
        entity.setDataJson("dataJson");
        return entity;
    }

}
