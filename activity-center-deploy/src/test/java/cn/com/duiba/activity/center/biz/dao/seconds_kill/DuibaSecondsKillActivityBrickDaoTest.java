package cn.com.duiba.activity.center.biz.dao.seconds_kill;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.seconds_kill.DuibaSecondsKillActivityBrickEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * 
 * ClassName: DuibaSecondsKillActivityBrickDaoTest <br/>
 * date: 2016年12月1日 下午8:03:59 <br/>
 *
 * @version 
 * @since JDK 1.6
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class DuibaSecondsKillActivityBrickDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaSecondsKillActivityBrickDao duibaSecondsKillActivityBrickDao;
	
	private DuibaSecondsKillActivityBrickEntity info;
	
	private String[] exceptFields = new String[] { "gmtCreate", "gmtModified" };
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaSecondsKillActivityBrickEntity.class);
		duibaSecondsKillActivityBrickDao.insert(info);
	}
	
	@Test
	public void findPageTest(){
		DuibaSecondsKillActivityBrickEntity test = TestUtils.createRandomBean(DuibaSecondsKillActivityBrickEntity.class);
		test.setDeleted(false);
		duibaSecondsKillActivityBrickDao.insert(test);
		Map<String, Object> map = new HashMap<>();
		map.put("offset", 0);
		map.put("max", 10);
		List<DuibaSecondsKillActivityBrickEntity> list = duibaSecondsKillActivityBrickDao.findPage(map);
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void findPageCountTest(){
		DuibaSecondsKillActivityBrickEntity test = TestUtils.createRandomBean(DuibaSecondsKillActivityBrickEntity.class);
		test.setDeleted(false);
		duibaSecondsKillActivityBrickDao.insert(test);
		long count = duibaSecondsKillActivityBrickDao.findPageCount();
		Assert.assertTrue(count > 0);
	}

	@Test
	public void findTest(){
		DuibaSecondsKillActivityBrickEntity test = duibaSecondsKillActivityBrickDao.find(info.getId());
		TestUtils.assertEqualsReflect(info, test, false, exceptFields);
	}

	@Test
	public void updateTest(){
		DuibaSecondsKillActivityBrickEntity test = TestUtils.createRandomBean(DuibaSecondsKillActivityBrickEntity.class);
		test.setId(info.getId());
		duibaSecondsKillActivityBrickDao.update(test);
		DuibaSecondsKillActivityBrickEntity e = duibaSecondsKillActivityBrickDao.find(info.getId());
		TestUtils.assertEqualsReflect(e, test, false, exceptFields);
	}
	
	@Test
	public void openTest(){
		duibaSecondsKillActivityBrickDao.open(info.getId());
		int status = duibaSecondsKillActivityBrickDao.find(info.getId()).getStatus();
		Assert.assertTrue(status == 1);
	}
	
	@Test
	public void disableTest(){
		duibaSecondsKillActivityBrickDao.disable(info.getId());
		int status = duibaSecondsKillActivityBrickDao.find(info.getId()).getStatus();
		Assert.assertTrue(status == 0);
	}
	
	@Test
	public void findByTitleTest(){
		DuibaSecondsKillActivityBrickEntity test = TestUtils.createRandomBean(DuibaSecondsKillActivityBrickEntity.class);
		test.setDeleted(false);
		duibaSecondsKillActivityBrickDao.insert(test);
		DuibaSecondsKillActivityBrickEntity e = duibaSecondsKillActivityBrickDao.findByTitle(test.getTitle());
		Assert.assertNotNull(e);
	}
	
	@Test
	public void findAllEnableTest(){
		DuibaSecondsKillActivityBrickEntity test = TestUtils.createRandomBean(DuibaSecondsKillActivityBrickEntity.class);
		test.setDeleted(false);
		test.setStatus(1);
		duibaSecondsKillActivityBrickDao.insert(test);
		List<DuibaSecondsKillActivityBrickEntity> list = duibaSecondsKillActivityBrickDao.findAllEnable();
		Assert.assertTrue(list.size() > 0);
	}
}
