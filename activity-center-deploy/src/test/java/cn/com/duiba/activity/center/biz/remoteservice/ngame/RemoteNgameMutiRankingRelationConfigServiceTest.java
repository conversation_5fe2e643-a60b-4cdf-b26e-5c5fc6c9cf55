package cn.com.duiba.activity.center.biz.remoteservice.ngame;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.remoteservice.ngame.RemoteNgameMutiRankingRelationConfigService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by sty on 12/9/17.
 */
@Transactional(DsConstants.DATABASE_NGAME)
public class RemoteNgameMutiRankingRelationConfigServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteNgameMutiRankingRelationConfigService remoteNgameMutiRankingRelationConfigService;

    @Test
    public void testFind(){
        Assert.assertNotNull(remoteNgameMutiRankingRelationConfigService.findByBaseConfigId(1L));

    }
}
