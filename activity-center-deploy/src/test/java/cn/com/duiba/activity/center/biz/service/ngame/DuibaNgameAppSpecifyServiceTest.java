package cn.com.duiba.activity.center.biz.service.ngame;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.ngame.DuibaNgameAppSpecifyDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/8.
 */
@Transactional(DsConstants.DATABASE_NGAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaNgameAppSpecifyServiceTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaNgameAppSpecifyService duibaNgameAppSpecifyService;

    private ThreadLocal<DuibaNgameAppSpecifyDto> duibaNgameAppSpecifyDO=new ThreadLocal<>();


    @Before
    public void testAdd() {
        DuibaNgameAppSpecifyDto e=new DuibaNgameAppSpecifyDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaNgameAppSpecifyService.add(e);
        duibaNgameAppSpecifyDO.set(e);
    }

    @Test
    public void testFindByGameId() {
        DuibaNgameAppSpecifyDto e=duibaNgameAppSpecifyDO.get();
        Assert.assertTrue(duibaNgameAppSpecifyService.findByGameId(e.getDuibaGameId()).size()>0);
    }

    @Test
    public void testFindByGameConfigAndAppId() {
        DuibaNgameAppSpecifyDto e=duibaNgameAppSpecifyDO.get();
        DuibaNgameAppSpecifyDto e1=duibaNgameAppSpecifyService.findByGameConfigAndAppId(e.getAppId(),e.getDuibaGameId());
        assertDO(e,e1);
    }

    @Test
    public void testAddBatch() {
        DuibaNgameAppSpecifyDto e=new DuibaNgameAppSpecifyDto();
        TestUtils.setRandomAttributesForBean(e,false);
        List<DuibaNgameAppSpecifyDto> list=new ArrayList<>();
        list.add(e);
        duibaNgameAppSpecifyService.addBatch(list);
        Assert.assertNotNull(duibaNgameAppSpecifyService.findByGameConfigAndAppId(e.getAppId(),e.getDuibaGameId()));
    }

    @Test
    public void testDelete() {
        DuibaNgameAppSpecifyDto e=duibaNgameAppSpecifyDO.get();
        DuibaNgameAppSpecifyDto e1=duibaNgameAppSpecifyService.findByGameConfigAndAppId(e.getAppId(),e.getDuibaGameId());
        duibaNgameAppSpecifyService.delete(e1.getId());
        Assert.assertNull(duibaNgameAppSpecifyService.findByGameConfigAndAppId(e.getAppId(),e.getDuibaGameId()));
    }

    private void assertDO(DuibaNgameAppSpecifyDto e, DuibaNgameAppSpecifyDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaNgameAppSpecifyDto e, DuibaNgameAppSpecifyDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","id","deleted"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }
    
//    @Override
//    public DatabaseSchema chooseSchema() {
//        return DatabaseSchema.nameOf(THIS_DATABASE_SCHEMA);
//    }
}
