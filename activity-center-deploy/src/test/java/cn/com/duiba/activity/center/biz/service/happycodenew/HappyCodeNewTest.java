package cn.com.duiba.activity.center.biz.service.happycodenew;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.happycodenew.HappyCodeConfigDto;
import cn.com.duiba.activity.center.api.dto.happycodenew.HappyCodeItemBasicDto;
import cn.com.duiba.activity.center.biz.dao.happycodenew.HappyCodeConfigDao;
import cn.com.duiba.activity.center.biz.dao.happycodenew.HappyCodeItemBasicDao;
import cn.com.duiba.activity.center.biz.dao.happycodenew.HappyCodeItemPhaseDao;
import cn.com.duiba.activity.center.biz.entity.happycodenew.HappyCodeConfigEntity;
import cn.com.duiba.activity.center.biz.entity.happycodenew.HappyCodeItemBasicEntity;
import cn.com.duiba.activity.center.biz.entity.happycodenew.HappyCodeItemPhaseEntity;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemKeyService;
import cn.com.duiba.wolf.utils.TestUtils;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019/3/29
 */
public class HappyCodeNewTest extends TransactionalTestCaseBase {
    @Autowired
    private HappyCodeConfigDao happyCodeConfigDao;
    @Autowired
    private HappyCodeItemPhaseDao happyCodeItemPhaseDao;
    @Autowired
    private HappyCodeItemBasicDao happyCodeItemBasicDao;
    @Autowired
    private HappyCodeBackendService happyCodeBackendService;
    @Autowired
    private RemoteItemKeyService remoteItemKeyService;

    @Test
    public void testInsert(){
        HappyCodeConfigEntity config = new HappyCodeConfigEntity();
        HappyCodeItemBasicEntity basic = new HappyCodeItemBasicEntity();
        HappyCodeItemPhaseEntity phase = new HappyCodeItemPhaseEntity();
        TestUtils.setRandomAttributesForBean(config,false);
        TestUtils.setRandomAttributesForBean(basic,false);
        TestUtils.setRandomAttributesForBean(phase,false);
        happyCodeConfigDao.insert(config);
        basic.setFacePrice(111111L);
        basic.setBasicStatus(1);
        happyCodeItemBasicDao.insert(basic);
        phase.setPhaseStatus(1);
        happyCodeItemPhaseDao.insert(phase);
    }

    @Test
    public void testSaveConfig(){
        HappyCodeConfigDto happyCodeConfigDto = new HappyCodeConfigDto();
        TestUtils.setRandomAttributesForBean(happyCodeConfigDto,false);
        happyCodeConfigDto.setId(null);
        happyCodeBackendService.saveHappyCodeConfig(happyCodeConfigDto,null,false);
    }

    @Test
    public void testSaveOption() throws BizException {
        HappyCodeItemBasicDto happyCodeItemBasicDto = new HappyCodeItemBasicDto();
        TestUtils.setRandomAttributesForBean(happyCodeItemBasicDto,false);
        happyCodeItemBasicDto.setId(null);
        happyCodeItemBasicDto.setStartDate(new Date());
        happyCodeBackendService.saveHappyCodeItemBasic(happyCodeItemBasicDto);
    }

    @Test
    public void testCount(){
        int resutl = happyCodeItemBasicDao.countByActId(16L,null);
        Assert.assertTrue("正确",resutl==2);
    }

    @Test
    public void testItemKey(){
        ItemKeyDto itemKeyDto = remoteItemKeyService.findItemKeyNew(7758358L,null,1L);
//        ItemKeyDto itemKeyDtoSelf = remoteItemKeyService.findItemKeyNew(1717268L,null,1L);
        if (null == itemKeyDto || (null == itemKeyDto.getAppItem() && null == itemKeyDto.getItem())) {
            System.out.println("商品不存在,无法重新开启赛事！");
        }
        System.out.println("itemKeyDto :"+JSON.toJSONString(itemKeyDto));
    }


}
