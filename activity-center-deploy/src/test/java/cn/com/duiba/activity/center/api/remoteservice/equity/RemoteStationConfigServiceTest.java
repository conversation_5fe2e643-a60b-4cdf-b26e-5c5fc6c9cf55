package cn.com.duiba.activity.center.api.remoteservice.equity;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.equity.StationConfigDto;
import cn.com.duiba.activity.center.api.enums.equity.EquityValidateEnum;
import cn.com.duiba.activity.center.api.request.equity.StationConfigSaveRequest;
import cn.com.duiba.activity.center.api.request.equity.StationStockRequest;
import cn.com.duiba.boot.exception.BizException;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.List;

/**
 * <AUTHOR>
 * @Description:
 * @date 2020/1/35:18 下午
 */
public class RemoteStationConfigServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteStationConfigService remoteStationConfigService;


    @Test
    @Rollback(false)
    public void testSave() {
        StationConfigDto dto = new StationConfigDto();
        dto.setInvalid(EquityValidateEnum.VALIDA.getCode());
        dto.setAdminCode("1");
        dto.setAppId(1L);
        dto.setAdminPhone("15990066579");
        dto.setStationAddress("AAAA");
        dto.setStationCode("1");
        dto.setStationName("name");
        remoteStationConfigService.save(dto);
    }

    @Test
    public void updateById() {
        StationConfigDto dto = remoteStationConfigService.getById(1L);
        dto.setStationName("BBBB");
        Integer size =  remoteStationConfigService.updateById(dto);
        Assert.assertTrue(size>0);
    }


    @Test
    public void getByAidAcodeAphoneInd() {
        StationConfigDto dto = remoteStationConfigService.getByAidAcodeAphoneInd(1L,"1","15990066579",0);
        Assert.assertNotNull(dto);
    }

    @Test
    public void getByPartOfFields() {
        StationConfigSaveRequest stationConfigSaveRequest = new StationConfigSaveRequest();
        stationConfigSaveRequest.setAppId(1L);
        stationConfigSaveRequest.setAdminCode("1");
        List<StationConfigDto> dto = null;
        try {
            dto = remoteStationConfigService.getByPartOfFields(stationConfigSaveRequest);
        } catch (BizException e) {
            e.printStackTrace();
        }
        Assert.assertNotNull(dto);
    }

    @Test
    public void getByAppIdAndValida() {

        List<StationConfigDto> dto = null;
        try {
            dto = remoteStationConfigService.getByAppIdAndValida(1L,0);
        } catch (BizException e) {
            e.printStackTrace();
        }
        Assert.assertNotNull(dto);
    }

    @Test
    @Rollback(false)
    public void initAddAppItem() {

        StationStockRequest stationStockRequest = new StationStockRequest();
        stationStockRequest.setAppItemId(1717993L);
        stationStockRequest.setStationId(1L);
        stationStockRequest.setStockNum(10);
        try {
            remoteStationConfigService.initAddAppItem(stationStockRequest);
        } catch (BizException e) {
            e.printStackTrace();
        }
        Assert.assertNotNull("1");
    }


    @Test
    @Rollback(false)
    public void addAppItem() {

        StationStockRequest stationStockRequest = new StationStockRequest();
        stationStockRequest.setAppItemId(1717993L);
        stationStockRequest.setStationId(1L);
        stationStockRequest.setStockNum(10);
        stationStockRequest.setStationStockId(8L);
        try {
            remoteStationConfigService.addAppItem(stationStockRequest);
        } catch (BizException e) {
            e.printStackTrace();
        }
        Assert.assertNotNull("1");
    }


}
