package cn.com.duiba.activity.center.biz.dao.ngame;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.ngame.NgameConsumerRecordEntity;

@Transactional(DsConstants.DATABASE_NGAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class NgameConsumerRecordDaoTest extends TransactionalTestCaseBase  {

	@Resource
	private NgameConsumerRecordDao ngameConsumerRecordDao;

	@Before
	public void init() throws Exception {
	}

	@Test
	public void findRecordByConIdAndNgameIdTest() {
		System.out.println(ngameConsumerRecordDao.findRecordByConIdAndNgameId(1539361l, 13l).getOperatingActivityId());
	}
	
	@Test
	public void markCheatTest() {
		ngameConsumerRecordDao.markCheat(3l, false);
	}
	
	@Test
	public void findGameConfigDuibaScoreByDescTest() {
		List<NgameConsumerRecordEntity> list = ngameConsumerRecordDao.findGameConfigDuibaScoreByDesc(13l, 2);
		System.out.println(list.size());
	}
	
	@Test
	public void findGameConfigDuibaScoreByAscTest() {
		List<NgameConsumerRecordEntity> list = ngameConsumerRecordDao.findGameConfigDuibaScoreByAsc(13l, 2);
		System.out.println(list.size());
	}
	
	@Test
	public void findConsumerGameConfigDuibaScoreByDescTest() {
		List<NgameConsumerRecordEntity> list = ngameConsumerRecordDao.findConsumerGameConfigDuibaScoreByDesc(13l, 2);
		System.out.println(list.size());
	}
	
	@Test
	public void findConsumerGameConfigDuibaScoreByAscTest() {
		List<NgameConsumerRecordEntity> list = ngameConsumerRecordDao.findConsumerGameConfigDuibaScoreByAsc(13l, 2);
		System.out.println(list.size());
	}
	
	@Test
	public void insertTest() {
		NgameConsumerRecordEntity r = new NgameConsumerRecordEntity();
		r.setAppId(Long.valueOf(1));
		r.setConsumerId(-1L);
		r.setOperatingActivityId(Long.valueOf(1));
		r.setMaxScoreDate(new Date());
		r.setDuibaNgameId(Long.valueOf(2));
		r.setGmtCreate(new Date());
		r.setGmtModified(new Date());
		ngameConsumerRecordDao.insert(r);
	}
	
	@Test
	public void updateScoreTest() {
		ngameConsumerRecordDao.updateScore(10l,2l,"3",4l);
	}
	
	@Test
	public void updateScoreAndTotalScoreTest() {
		ngameConsumerRecordDao.updateScoreAndTotalScore(10l,3l,1,"5",4l);
	}
	
	@Test
	public void updateTotalScoreTest() {
		ngameConsumerRecordDao.updateTotalScore(10l,2);
	}
	
	@Test
	public void updateIsGivePrizeTest() {
		ngameConsumerRecordDao.updateIsGivePrize(10l,true);
	}
	
	@Test
	public void updateAutoOpenPrizeIdTest() {
		ngameConsumerRecordDao.updateAutoOpenPrizeId(10l,"2");
	}
	
	@Test
	public void findByConsumerAndIdsTest() {
		List<Long> ids = new ArrayList<Long>();
		ids.add(1l);
		ids.add(2l);
		System.out.println(ngameConsumerRecordDao.findByConsumerAndIds(1539361l, ids).size());
	}

	@Test
	public void findTopWinningTest() {
		Map<String, Object> params = new HashMap<>();
		params.put("offset", 0);
		params.put("max", 10);
		List<NgameConsumerRecordEntity> list = ngameConsumerRecordDao.findTopWinning(params);
		System.out.println(list.size()+",,,,");
	}

	@Test
	public void findTopWinningAscTest() {
		Map<String, Object> params = new HashMap<>();
		params.put("offset", 0);
		params.put("max", 10);
		List<NgameConsumerRecordEntity> list = ngameConsumerRecordDao.findTopWinningAsc(params);
		System.out.println(list.size()+",,,,");
	}

	@Test
	public void countCheatBeforeTest() {
		System.out.println(ngameConsumerRecordDao.countCheatBefore(2,13l));
	}

	@Test
	public void countTopWinningTest() {
		Map<String, Object> params = new HashMap<>();
		System.out.println(ngameConsumerRecordDao.countTopWinning(params));
	}

	@Test
	public void findTest() {
		System.out.println(ngameConsumerRecordDao.find(3l).getConsumerId());
	}

	@Test
	public void setCheatReasonTest() {
		ngameConsumerRecordDao.setCheatReason(3l, "dsfads");
	}

	@Test
	public void findShareConsumerByGameConfigDuibaIdTest() {
		List<NgameConsumerRecordEntity> list = ngameConsumerRecordDao.findShareConsumerByGameConfigDuibaId(13l);
		System.out.println(list.size());
	}

	@Test
	public void updateByGameConfigShareTest() {
		ngameConsumerRecordDao.updateByGameConfigShare(1l,13l, 5l);
	}

	@Test
	public void findByConsumerIdAndGameConfigDuibaIdTest() {
		NgameConsumerRecordEntity r = ngameConsumerRecordDao.findByConsumerIdAndGameConfigDuibaId(1539361l, 13l);
		System.out.println(r.getCheatReason());
	}
}
