package cn.com.duiba.activity.center.biz.dao.quizz;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.quizz.QuizzOrdersEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ_CONSUMER)
public class QuizzOrdersStatusChangeDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private QuizzOrdersStatusChangeDao quizzOrdersStatusChangeDao;
	
	@Autowired
	private QuizzOrdersSequenceDao quizzOrdersSequenceDao;
	
	private QuizzOrdersEntity info;
	
	@Before
	public void insertTest() {
		QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		quizzOrdersStatusChangeDao.insert(e, quizzOrdersSequenceDao.getId());
		info = e;
	}
	
	@Test
	public void updateExchangeStatusToFailTest(){
		QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setStatus(2);
		e.setExchangeStatus(1);
		quizzOrdersStatusChangeDao.insert(e, quizzOrdersSequenceDao.getId());
		
		quizzOrdersStatusChangeDao.updateExchangeStatusToFail(e.getConsumerId(), e.getId(), info.getError4admin(), info.getError4developer(), info.getError4consumer());
		
		String error4admin = quizzOrdersStatusChangeDao.find(e.getConsumerId(), e.getId()).getError4admin();
		Assert.assertTrue(error4admin.equals(info.getError4admin()));
	}

	/**
	* 修改订单领奖状态为过期失效
	*/
	@Test
	public void updateExchangeStatusToOverdueTest(){
		QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setExchangeStatus(1);
		quizzOrdersStatusChangeDao.insert(e, quizzOrdersSequenceDao.getId());
		
		quizzOrdersStatusChangeDao.updateExchangeStatusToOverdue(e.getId(), e.getConsumerId(), info.getError4admin(), info.getError4developer(), info.getError4consumer());
		
		String error4admin = quizzOrdersStatusChangeDao.find(e.getConsumerId(), e.getId()).getError4admin();
		Assert.assertTrue(error4admin.equals(info.getError4admin()));
	}
	
	/**
	* 修改订单状态失败
	*/
	@Test
	public void updateStatusToFailTest(){
		QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setStatus(1);
		quizzOrdersStatusChangeDao.insert(e, quizzOrdersSequenceDao.getId());
		
		quizzOrdersStatusChangeDao.updateStatusToFail(e.getConsumerId(), e.getId(), info.getError4admin(), info.getError4developer(), info.getError4consumer());
		
		String error4admin = quizzOrdersStatusChangeDao.find(e.getConsumerId(), e.getId()).getError4admin();
		Assert.assertTrue(error4admin.equals(info.getError4admin()));
	}
	
	/**
	* 处理领奖
	*/
	@Test
	public void doTakePrizeTest(){
		QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setExchangeStatus(1);
		e.setMainOrderId(null);
		quizzOrdersStatusChangeDao.insert(e, quizzOrdersSequenceDao.getId());
		
		quizzOrdersStatusChangeDao.doTakePrize(e.getConsumerId(), e.getId());
		
		int exchangeStatus = quizzOrdersStatusChangeDao.find(e.getConsumerId(), e.getId()).getExchangeStatus();
		Assert.assertTrue(exchangeStatus == 2);
	}
	
	/**
	* 领奖创建订单失败，回滚领奖
	*/
	@Test
	public void rollbackTakePrizeTest(){
		QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setExchangeStatus(2);
		e.setMainOrderId(null);
		quizzOrdersStatusChangeDao.insert(e, quizzOrdersSequenceDao.getId());
		
		quizzOrdersStatusChangeDao.rollbackTakePrize(e.getConsumerId(), e.getId());
		
		int exchangeStatus = quizzOrdersStatusChangeDao.find(e.getConsumerId(), e.getId()).getExchangeStatus();
		Assert.assertTrue(exchangeStatus == 1);
	}
	
	/**
	* 处理抽奖结果
	*/
	@Test
	public void updateLotteryResultTest(){
		QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setStatus(2);
		quizzOrdersStatusChangeDao.insert(e, quizzOrdersSequenceDao.getId());
		
		quizzOrdersStatusChangeDao.updateLotteryResult(e.getConsumerId(), e.getId(), info.getAppItemId(), info.getItemId(), 
				info.getPrizeId(), info.getPrizeName(), info.getPrizeType(), info.getPrizeFacePrice(), info.getCouponId());
		
		String prizeType = quizzOrdersStatusChangeDao.find(e.getConsumerId(), e.getId()).getPrizeType();
		Assert.assertTrue(prizeType.equals(info.getPrizeType()));
	}

	/**
	* 福袋请求降级处理时更新订单为谢谢参与
	*/
	@Test
	public void updateLotteryLuckResultTest(){
		QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		
		quizzOrdersStatusChangeDao.updateLotteryLuckResult(info.getConsumerId(), info.getId(), e.getAppItemId(), e.getItemId(), 
				e.getPrizeId(), e.getPrizeName(), e.getPrizeType(), e.getPrizeFacePrice(), e.getCouponId());
		
		String prizeType = quizzOrdersStatusChangeDao.find(info.getConsumerId(), info.getId()).getPrizeType();
		Assert.assertTrue(prizeType.equals(e.getPrizeType()));
	}
}
