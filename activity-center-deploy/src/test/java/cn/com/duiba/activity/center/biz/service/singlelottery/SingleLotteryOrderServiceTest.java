package cn.com.duiba.activity.center.biz.service.singlelottery;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.singlelottery.SingleLotteryOrderDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/27.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class SingleLotteryOrderServiceTest extends TransactionalTestCaseBase {
    @Resource
    private SingleLotteryOrderService singleLotteryOrderService;

    ThreadLocal<SingleLotteryOrderDto> threadLocal=new ThreadLocal<>();

    @Before
    public void testInsert() {
        SingleLotteryOrderDto e=new SingleLotteryOrderDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        singleLotteryOrderService.insert(e);
        threadLocal.set(e);
    }

    @Test
    public void testFind() throws Exception {
        SingleLotteryOrderDto e=threadLocal.get();
        SingleLotteryOrderDto e1=singleLotteryOrderService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testCountByConsumerIdAndOptionType() throws Exception {
        SingleLotteryOrderDto e=threadLocal.get();
        Assert.assertTrue(singleLotteryOrderService.countByConsumerIdAndOptionType(e.getOperatingActivityId(),e.getConsumerId(),e.getOptionType())>0);
    }

    @Test
    public void testFindAllByIds() throws Exception {
        SingleLotteryOrderDto e=threadLocal.get();
        Assert.assertTrue(singleLotteryOrderService.findAllByIds(Arrays.asList(e.getId())).size()>0);
    }

    @Test
    public void testFindLotteryCountByComsumer() throws Exception {
        SingleLotteryOrderDto e=threadLocal.get();
        Assert.assertTrue(singleLotteryOrderService.findLotteryCountByComsumer(e.getOperatingActivityId(),e.getAppId(),e.getConsumerId())>0);
    }

    @Test
    public void testFindCountByComsumerTime() throws Exception {
        SingleLotteryOrderDto e=threadLocal.get();
        Assert.assertTrue(singleLotteryOrderService.findCountByComsumerTime(e.getOperatingActivityId(),e.getAppId(),e.getConsumerId(),new Date(System.currentTimeMillis()-10000000l),new Date(System.currentTimeMillis()+1000000l))>0);
    }

    @Test
    public void testFindFrontLotteryList() throws Exception {
        SingleLotteryOrderDto e=new SingleLotteryOrderDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(1);
        e.setOptionType(1);
        singleLotteryOrderService.insert(e);
        Assert.assertTrue(singleLotteryOrderService.findFrontLotteryList(e.getOperatingActivityId(),e.getAppId()).size()>0);
    }

    @Test
    public void testFindByAppAndDeveloperBizId() throws Exception {
        SingleLotteryOrderDto e=threadLocal.get();
        assertDO(singleLotteryOrderService.findByAppAndDeveloperBizId(e.getAppId(),e.getDeveloperBizId()),e);
    }

    @Test
    public void testFindByIds() throws Exception {
        SingleLotteryOrderDto e=threadLocal.get();
        Assert.assertTrue(singleLotteryOrderService.findByIds(Arrays.asList(e.getId())).size()>0);
    }

    @Test
    public void testFindByInOrderIds() throws Exception {
        SingleLotteryOrderDto e=threadLocal.get();
        Assert.assertTrue(singleLotteryOrderService.findByInOrderIds(Arrays.asList(e.getOrderId())).size()>0);
    }

    @Test
    public void testFindAllByLtGmtCreateAndExchangeStatus() throws Exception {
        SingleLotteryOrderDto e=new SingleLotteryOrderDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(1);
        e.setGmtCreate(new Date(System.currentTimeMillis()-87000*1000l));
        singleLotteryOrderService.insert(e);
        Assert.assertTrue(singleLotteryOrderService.findAllByLtGmtCreateAndExchangeStatus().size()>0);
    }

    @Test
    public void testFindFailCountByOperatingActivityIds() throws Exception {
        SingleLotteryOrderDto e=new SingleLotteryOrderDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(3);
        singleLotteryOrderService.insert(e);
        Assert.assertTrue(singleLotteryOrderService.findFailCountByOperatingActivityIds(Arrays.asList(e.getOperatingActivityId())).size()>0);
    }

    @Test
    public void testFindByLimit() throws Exception {
        SingleLotteryOrderDto e=threadLocal.get();
        Map<String,Object> params=new HashMap<>();
        params.put("operationActivityId",e.getOperatingActivityId());
        params.put("startTime",DateUtils.minutesAddOrSub(e.getGmtCreate(),-10));
        params.put("endTime",DateUtils.minutesAddOrSub(e.getGmtCreate(),10));
        params.put("start",0);
        params.put("pageSize",10);
        Assert.assertTrue(singleLotteryOrderService.findByLimit(params).size()>0);
    }

    @Test
    public void testFindByCount() throws Exception {
        SingleLotteryOrderDto e=threadLocal.get();
        Map<String,Object> params=new HashMap<>();
        params.put("operationActivityId",e.getOperatingActivityId());
        params.put("startTime",DateUtils.minutesAddOrSub(e.getGmtCreate(),-10));
        params.put("endTime",DateUtils.minutesAddOrSub(e.getGmtCreate(),10));
        Assert.assertTrue(singleLotteryOrderService.findByCount(params)>0);
    }

    @Test
    public void testCountWintimesByOptionType() throws Exception {
        SingleLotteryOrderDto e=new SingleLotteryOrderDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(1);
        singleLotteryOrderService.insert(e);
        Assert.assertTrue(singleLotteryOrderService.countWintimesByOptionType(Arrays.asList(e.getOperatingActivityId()),new Date(System.currentTimeMillis()-100000l),new Date(System.currentTimeMillis()+1000000l),e.getOptionType())>0);
    }

    @Test
    public void testGetWinningListByOperatingActivityIds() throws Exception {
        SingleLotteryOrderDto e=new SingleLotteryOrderDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setOptionType(1);
        singleLotteryOrderService.insert(e);
        Assert.assertTrue(singleLotteryOrderService.getWinningListByOperatingActivityIds(Arrays.asList(e.getOperatingActivityId())).size()>0);
    }

    @Test
    public void testUpdateExchangeStatusToFail() throws Exception {
        SingleLotteryOrderDto e=threadLocal.get();
        singleLotteryOrderService.updateExchangeStatusToFail(e.getId(),"testadmin","testdev","testconsumer");
        SingleLotteryOrderDto e1=singleLotteryOrderService.find(e.getId());
        Assert.assertEquals(e1.getError4admin(),"testadmin");
    }

    @Test
    public void testUpdateExchangeStatusToOverdue() throws Exception {
        SingleLotteryOrderDto e=new SingleLotteryOrderDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(1);
        singleLotteryOrderService.insert(e);
        singleLotteryOrderService.updateExchangeStatusToOverdue(e.getId(),"testadmin","testdev","testconsumer");
        SingleLotteryOrderDto e1=singleLotteryOrderService.find(e.getId());
        Assert.assertEquals(e1.getError4admin(),"testadmin");
    }

    @Test
    public void testUpdateStatusToSuccess() throws Exception {
        SingleLotteryOrderDto e=threadLocal.get();
        singleLotteryOrderService.updateStatusToSuccess(e.getId());
        SingleLotteryOrderDto e1=singleLotteryOrderService.find(e.getId());
        Assert.assertTrue(e1.getStatus().equals(SingleLotteryOrderDto.StatusSuccess));
    }

    @Test
    public void testUpdateStatusToFail() throws Exception {
        SingleLotteryOrderDto e=threadLocal.get();
        singleLotteryOrderService.updateStatusToFail(e.getId(),"testadmin","testdev","testconsumer");
        SingleLotteryOrderDto e1=singleLotteryOrderService.find(e.getId());
        Assert.assertTrue(e1.getStatus().equals(SingleLotteryOrderDto.StatusFail));
    }

    @Test
    public void testUpdatePrizeTypeToThanks() throws Exception {
        SingleLotteryOrderDto e=threadLocal.get();
        singleLotteryOrderService.updatePrizeTypeToThanks(e.getId());
        SingleLotteryOrderDto e1=singleLotteryOrderService.find(e.getId());
        Assert.assertTrue(e1.getStatus().equals(SingleLotteryOrderDto.StatusSuccess));
    }

    @Test
    public void testDoTakePrize() throws Exception {
        SingleLotteryOrderDto e=new SingleLotteryOrderDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(1);
        e.setOrderId(null);
        singleLotteryOrderService.insert(e);
        Assert.assertTrue(singleLotteryOrderService.doTakePrize(e.getId())>0);
    }

    @Test
    public void testRollbackTakePrize() throws Exception {
        SingleLotteryOrderDto e=new SingleLotteryOrderDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(2);
        e.setOrderId(null);
        singleLotteryOrderService.insert(e);
        Assert.assertTrue(singleLotteryOrderService.rollbackTakePrize(e.getId())>0);
    }

    @Test
    public void testUpdateLotteryResult() throws Exception {
        SingleLotteryOrderDto e=threadLocal.get();
        singleLotteryOrderService.updateLotteryResult(e.getId(),e.getAppItemId()+1,e.getItemId(),e.getPrizeName(),e.getPrizeType(),e.getPrizeDegree(),e.getPrizeFacePrice(),e.getOptionType(),e.getCouponId()+1);
        SingleLotteryOrderDto e1=singleLotteryOrderService.find(e.getId());
        Assert.assertTrue(e.getAppItemId().equals(e1.getAppItemId()-1));
    }

    @Test
    public void testUpdateDeveloperBizId() throws Exception {
        SingleLotteryOrderDto e=threadLocal.get();
        singleLotteryOrderService.updateDeveloperBizId(e.getId(),"testBiz");
        SingleLotteryOrderDto e1=singleLotteryOrderService.find(e.getId());
        Assert.assertEquals(e1.getDeveloperBizId(),"testBiz");
    }

    @Test
    public void testupdateMainOrderId() throws Exception {
        SingleLotteryOrderDto e=threadLocal.get();
        singleLotteryOrderService.updateMainOrderId(e.getId(),1l);
        SingleLotteryOrderDto e1=singleLotteryOrderService.find(e.getId());
        Assert.assertTrue(e1.getOrderId().equals(1l));
    }

    private void assertDO(SingleLotteryOrderDto e, SingleLotteryOrderDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(SingleLotteryOrderDto e, SingleLotteryOrderDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","orderNum","partnerUserId","winningNum"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }
}
