package cn.com.duiba.activity.center.api.remoteservice.happycode;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.happy_code.HappyCodeBasicDto;
import cn.com.duiba.activity.center.api.remoteservice.happycodenew.RemoteHappyCodeDetailService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;

/**
 * Created by hww on 2017/12/11
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class RemoteHappyCodeBasicServiceTest extends TransactionalTestCaseBase {

    private static boolean open = true;
    private static boolean close = false;

    @Autowired
    private RemoteHappyCodeBasicService remoteHappyCodeBasicService;

    @Autowired
    private RemoteHappyCodeDetailService remoteHappyCodeDetailService;

    @Test
    public void scheduleRefundHappyCodePhase() {
        remoteHappyCodeDetailService.scheduleRefundHappyCodePhase();
    }

    @Test
    public void findByIdTest() {
        Long id = insertTest();
        HappyCodeBasicDto basic = remoteHappyCodeBasicService.findById(id);
        Assert.assertNotNull(basic);
    }

    @Test
    public void updateBasicStatusTest() {
        Long id = insertTest();
        remoteHappyCodeBasicService.updateBasicStatus(id, open);
        HappyCodeBasicDto basic1 = remoteHappyCodeBasicService.findById(id);
        Assert.assertTrue(basic1.getBasicStatus() == HappyCodeBasicDto.STATUS_OPENING);
        remoteHappyCodeBasicService.updateBasicStatus(id, close);
        HappyCodeBasicDto basic2 = remoteHappyCodeBasicService.findById(id);
        Assert.assertTrue(basic2.getBasicStatus() == HappyCodeBasicDto.STATUS_END);
    }

    @Test
    public void findAllBasicByPhaseIdsTest() {

    }

    @Test
    public void generalUpdateTest() {
        Long id = insertTest();
        HappyCodeBasicDto basic = remoteHappyCodeBasicService.findById(id);
        String appIds = basic.getAppIds() + "a";
        Long phaseId = basic.getPhaseId() + 1;
        Long nextPhaseId = basic.getNextPhaseId() + 1;
        String cheatUserIds = basic.getCheatUserIds() + "a";

        basic.setAppIds(appIds);
        basic.setPhaseId(phaseId);
        basic.setNextPhaseId(nextPhaseId);
        basic.setCheatUserIds(cheatUserIds);

        remoteHappyCodeBasicService.generalUpdate(basic);
        HappyCodeBasicDto newBasic = remoteHappyCodeBasicService.findById(id);
        Assert.assertTrue(Objects.equals(newBasic.getAppIds(), appIds));
        Assert.assertTrue(Objects.equals(newBasic.getPhaseId(), phaseId));
        Assert.assertTrue(Objects.equals(newBasic.getNextPhaseId(), nextPhaseId));
        Assert.assertTrue(Objects.equals(newBasic.getCheatUserIds(), cheatUserIds));
    }

    private Long insertTest() {
        HappyCodeBasicDto basic = new HappyCodeBasicDto();
        basic.setCheatUserIds("[]");
        basic.setPhaseId(1L);
        basic.setNextPhaseId(2L);
        //basic.setAppIds();
        basic.setPhaseType(HappyCodeBasicDto.PHASE_TYPE_A);
        basic.setGmtModified(new Date());
        basic.setDeleted(0);
        basic.setBasicStatus(HappyCodeBasicDto.STATUS_READY);
        basic.setAutoContinue(HappyCodeBasicDto.AUTO_CONTINUE_TRUE);
        basic.setGmtCreate(new Date());
        return remoteHappyCodeBasicService.insert(basic);
    }

}
