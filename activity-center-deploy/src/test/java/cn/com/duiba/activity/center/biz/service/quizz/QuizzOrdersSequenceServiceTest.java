package cn.com.duiba.activity.center.biz.service.quizz;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;

@Transactional(DsConstants.DATABASE_QUIZZ_CONSUMER)
public class QuizzOrdersSequenceServiceTest extends TransactionalTestCaseBase{

	@Autowired
	private QuizzOrdersSequenceService quizzOrdersSequenceService;
	
	@Test
	public void getId() {
		long num = quizzOrdersSequenceService.getId().getId();
		Assert.assertTrue(num > 0);
	}
}
