package cn.com.duiba.activity.center.biz.service.game;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionAnswerOrdersDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/2.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaQuestionAnswerOrdersServiceTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaQuestionAnswerOrdersService duibaQuestionAnswerOrdersService;


    @Test
    public void testInsert() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionAnswerOrdersService.insert(e);
    }

    @Test
    public void testFind() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionAnswerOrdersService.insert(e);
        DuibaQuestionAnswerOrdersDto e1=duibaQuestionAnswerOrdersService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testUpdateStatusToConsumeSuccess() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(0);
        duibaQuestionAnswerOrdersService.insert(e);
        duibaQuestionAnswerOrdersService.updateStatusToConsumeSuccess(e.getId());
        Assert.assertTrue(duibaQuestionAnswerOrdersService.find(e.getId()).getStatus().equals(1));
    }

    @Test
    public void testUpdateStatusToConsumeFail() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(0);
        duibaQuestionAnswerOrdersService.insert(e);
        duibaQuestionAnswerOrdersService.updateStatusToConsumeFail(e.getId(),"test","test","test");
        Assert.assertTrue(duibaQuestionAnswerOrdersService.find(e.getId()).getStatus().equals(2));
    }

    @Test
    public void testUpdateStatusToSuccess() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(1);
        duibaQuestionAnswerOrdersService.insert(e);
        duibaQuestionAnswerOrdersService.updateStatusToSuccess(e.getId());
        Assert.assertTrue(duibaQuestionAnswerOrdersService.find(e.getId()).getStatus().equals(3));
    }

    @Test
    public void testUpdateDeveloperBizId() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(0);
        duibaQuestionAnswerOrdersService.insert(e);
        duibaQuestionAnswerOrdersService.updateDeveloperBizId(e.getId(),"test");
        Assert.assertTrue(duibaQuestionAnswerOrdersService.find(e.getId()).getDeveloperBizId().equals("test"));
    }

    @Test
    public void testCountByConsumerIdAndOperatingActivityId() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(0);
        duibaQuestionAnswerOrdersService.insert(e);
        Assert.assertTrue(duibaQuestionAnswerOrdersService.countByConsumerIdAndOperatingActivityId(e.getConsumerId(),e.getOperatingActivityId())>0);
    }

    @Test
    public void testCountByConsumerIdAndOperatingActivityIdAndDate() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(0);
        duibaQuestionAnswerOrdersService.insert(e);
        Assert.assertTrue(duibaQuestionAnswerOrdersService.countByConsumerIdAndOperatingActivityIdAndDate(e.getConsumerId(),e.getOperatingActivityId(),new Date(System.currentTimeMillis()-100000l),new Date(System.currentTimeMillis()+100000l))>0);
    }

    @Test
    public void testCountFreeByConsumerIdAndOperatingActivityId() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setCredits(0l);
        duibaQuestionAnswerOrdersService.insert(e);
        Assert.assertTrue(duibaQuestionAnswerOrdersService.countFreeByConsumerIdAndOperatingActivityId(e.getConsumerId(),e.getOperatingActivityId())>0);
    }

    @Test
    public void testCountFreeByConsumerIdAndOperatingActivityIdAndDate() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setCredits(0l);
        e.setStatus(1);
        duibaQuestionAnswerOrdersService.insert(e);
        Assert.assertTrue(duibaQuestionAnswerOrdersService.countFreeByConsumerIdAndOperatingActivityIdAndDate(e.getConsumerId(),e.getOperatingActivityId(),new Date(System.currentTimeMillis()-100000l),new Date(System.currentTimeMillis()+1000000l))>0);
    }

    @Test
    public void testUpdateExchangeStatusToFail() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setCredits(0l);
        e.setStatus(1);
        e.setExchangeStatus(1);
        duibaQuestionAnswerOrdersService.insert(e);
        duibaQuestionAnswerOrdersService.updateExchangeStatusToFail(e.getId(),"test","test","test");
        Assert.assertTrue(duibaQuestionAnswerOrdersService.find(e.getId()).getExchangeStatus().equals(3));
    }

    @Test
    public void testUpdateMainOrderId() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setCredits(0l);
        e.setStatus(1);
        duibaQuestionAnswerOrdersService.insert(e);
        duibaQuestionAnswerOrdersService.updateMainOrderId(e.getId(),20l,"test");
        Assert.assertTrue(duibaQuestionAnswerOrdersService.find(e.getId()).getMainOrderId().equals(20l));
    }

    @Test
    public void testUpdateExchangeStatusToOverdue() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setCredits(0l);
        e.setStatus(1);
        e.setExchangeStatus(1);
        duibaQuestionAnswerOrdersService.insert(e);
        duibaQuestionAnswerOrdersService.updateExchangeStatusToOverdue(e.getId(),"test","test","test");
        Assert.assertTrue(duibaQuestionAnswerOrdersService.find(e.getId()).getExchangeStatus().equals(DuibaQuestionAnswerOrdersDto.ExchangeStatusOverdue));
    }

    @Test
    public void testUpdateExchangeStatusToSucess() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setCredits(0l);
        e.setStatus(1);
        e.setExchangeStatus(1);
        duibaQuestionAnswerOrdersService.insert(e);
        duibaQuestionAnswerOrdersService.updateExchangeStatusToSucess(e.getId(),"","","");
        Assert.assertTrue(duibaQuestionAnswerOrdersService.find(e.getId()).getExchangeStatus().equals(2));
    }

    @Test
    public void testUpdateExchangeStatusToWait() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setCredits(0l);
        e.setStatus(1);
        e.setExchangeStatus(0);
        duibaQuestionAnswerOrdersService.insert(e);
        duibaQuestionAnswerOrdersService.updateExchangeStatusToWait(e.getId(),"","","");
        Assert.assertTrue(duibaQuestionAnswerOrdersService.find(e.getId()).getExchangeStatus().equals(DuibaQuestionAnswerOrdersDto.ExchangeStatusWait));
    }

    @Test
    public void testDoTakePrize() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setMainOrderId(null);
        e.setExchangeStatus(1);
        duibaQuestionAnswerOrdersService.insert(e);
        duibaQuestionAnswerOrdersService.doTakePrize(e.getId());
        Assert.assertTrue(duibaQuestionAnswerOrdersService.find(e.getId()).getExchangeStatus().equals(2));
    }

    @Test
    public void testRollbackTakePrize() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setMainOrderId(null);
        e.setExchangeStatus(2);
        duibaQuestionAnswerOrdersService.insert(e);
        duibaQuestionAnswerOrdersService.rollbackTakePrize(e.getId());
        Assert.assertTrue(duibaQuestionAnswerOrdersService.find(e.getId()).getExchangeStatus().equals(1));
    }

    @Test
    public void testFindOverdueOrder() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setMainOrderId(null);
        e.setExchangeStatus(1);
        e.setPrizeOverdueDate(new Date(System.currentTimeMillis()-1000000l));
        e.setStatus(3);
        duibaQuestionAnswerOrdersService.insert(e);
        Assert.assertTrue(duibaQuestionAnswerOrdersService.findOverdueOrder().size()>0);
    }

    @Test
    public void testFindByIds() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setMainOrderId(null);
        e.setExchangeStatus(1);
        e.setPrizeOverdueDate(new Date(System.currentTimeMillis()-1000000l));
        e.setStatus(3);
        duibaQuestionAnswerOrdersService.insert(e);
        Assert.assertTrue(duibaQuestionAnswerOrdersService.findByIds(Arrays.asList(e.getId())).size()>0);
    }

    @Test
    public void testUpdatePrizeInfo() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(1);
        e.setStatus(3);
        duibaQuestionAnswerOrdersService.insert(e);
        e.setPrizeId(20l);
        duibaQuestionAnswerOrdersService.updatePrizeInfo(e);

        Assert.assertTrue(duibaQuestionAnswerOrdersService.find(e.getId()).getPrizeId().equals(20l));
    }

    @Test
    public void testUpdateScore() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(1);
        e.setStatus(3);
        duibaQuestionAnswerOrdersService.insert(e);
        duibaQuestionAnswerOrdersService.updateScore(e.getId(),42);
        Assert.assertTrue(duibaQuestionAnswerOrdersService.find(e.getId()).getScore().equals(42));
    }

    @Test
    public void testFindByAppAndDeveloperBizId() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(1);
        e.setStatus(3);
        duibaQuestionAnswerOrdersService.insert(e);
        Assert.assertNotNull(duibaQuestionAnswerOrdersService.findByAppAndDeveloperBizId(e.getAppId(),e.getDeveloperBizId()));
    }

    @Test
    public void testFindQuestionOrderLimit50() {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(2);
        e.setStatus(3);
        duibaQuestionAnswerOrdersService.insert(e);
        Assert.assertTrue(duibaQuestionAnswerOrdersService.findQuestionOrderLimit50(e.getAppId(),e.getOperatingActivityId()).size()>0);
    }

    @Test
    public void testCountFailByOperatingActivityIds() throws Exception {
        DuibaQuestionAnswerOrdersDto e=new DuibaQuestionAnswerOrdersDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setExchangeStatus(3);
        e.setStatus(3);
        duibaQuestionAnswerOrdersService.insert(e);
        Assert.assertTrue(duibaQuestionAnswerOrdersService.countFailByOperatingActivityIds(Arrays.asList(e.getOperatingActivityId())).size()>0);
    }

    @Test
    public void testFindByLimit() throws Exception {
        Map<String,Object> params=new HashMap<>();
        params.put("start",0);
        params.put("pageSize",20);
        Assert.assertTrue(duibaQuestionAnswerOrdersService.findByLimit(params).size()>0);
    }

    @Test
    public void testTotalCount() throws Exception {
        Map<String,Object> params=new HashMap<>();
        Assert.assertTrue(duibaQuestionAnswerOrdersService.totalCount(params)>0);
    }

    private void assertDO(DuibaQuestionAnswerOrdersDto e, DuibaQuestionAnswerOrdersDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaQuestionAnswerOrdersDto e, DuibaQuestionAnswerOrdersDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","questionType","prizeOverdueDate"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

}
