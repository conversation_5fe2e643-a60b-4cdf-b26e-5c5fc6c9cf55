package cn.com.duiba.activity.center.api.remoteservice.recommend;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.recommend.RecommendSkinDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by zhengjy on 2017/2/8.
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class RemoteRecommendSkinBackendServiceTest extends TransactionalTestCaseBase {
    @Autowired
    RemoteRecommendSkinBackendService remoteRecommendSkinBackendService;


    private RecommendSkinDto save(){
        RecommendSkinDto recommendSkinDto =new RecommendSkinDto();
        recommendSkinDto.setHtmlContext("xxxxx");
        recommendSkinDto.setShowStatus("OPEN");
        recommendSkinDto.setSkinName("wawawa");
        Long l = remoteRecommendSkinBackendService.insert(recommendSkinDto).getResult();
        recommendSkinDto.setId(l);
        return  recommendSkinDto;

    }

    @Test
    public void testInsert(){
        Assert.assertTrue(save().getId()>0);
    }

    @Test
    public void testUpdate(){
        Assert.assertTrue(remoteRecommendSkinBackendService.update(save()).getResult());
    }

    @Test
    public void testFindPageList(){
        save();
        List<RecommendSkinDto> ret= remoteRecommendSkinBackendService.findPageList(0,20,new RecommendSkinDto()).getResult().getRows();
        Assert.assertTrue(ret != null);
    }
    @Test
    public void testFindList(){
        save();
        List<RecommendSkinDto> ret= remoteRecommendSkinBackendService.findList(new RecommendSkinDto()).getResult();
        Assert.assertTrue(ret != null);
    }

    @Test
    public void testFind(){
        Assert.assertTrue(remoteRecommendSkinBackendService.find(save().getId()) != null);
    }

    @Test
    public void testDelete(){
        Assert.assertTrue(remoteRecommendSkinBackendService.delete(save().getId()).getResult());
    }


}
