package cn.com.duiba.activity.center.biz.dao.manualottery;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.manuallottery.AppManualLotteryDao;
import cn.com.duiba.activity.center.biz.entity.manual.AppManualLotteryEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/16.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class AppManualLotteryDaoTest extends TransactionalTestCaseBase {

    @Autowired
    private AppManualLotteryDao    appManualLotteryDao;

    private AppManualLotteryEntity info;

    @Before
    public void insertTest() {
        AppManualLotteryEntity e = new AppManualLotteryEntity(true);
        TestUtils.setRandomAttributesForBean(e, false);
        info = e;
        appManualLotteryDao.insert(e);
    }

    @Test
    public void findTest() {
        AppManualLotteryEntity infoTest = appManualLotteryDao.find(info.getId());
        assertDO(info, infoTest);
    }

    @Test
    public void scanOverManualLotteryTest() {
        AppManualLotteryEntity e = new AppManualLotteryEntity(true);
        TestUtils.setRandomAttributesForBean(e, false);
        e.setOverDate(new Date(System.currentTimeMillis() - 86400 * 2 * 1000));
        appManualLotteryDao.insert(e);
        List<AppManualLotteryEntity> list = appManualLotteryDao.scanOverManualLottery();
        Assert.assertTrue(list.size() > 0);
        boolean isFind = false;
        for (AppManualLotteryEntity appDO : list) {
            if (appDO.getId().equals(e.getId())) {
                assertDO(e, appDO);
                isFind = true;
            }
        }
        Assert.assertTrue(isFind);

    }

    @Test
    public void updateManualLotteryTest() {
        AppManualLotteryEntity e = new AppManualLotteryEntity(info.getId());
        TestUtils.setRandomAttributesForBean(e, false);
        appManualLotteryDao.updateManualLottery(e);
        AppManualLotteryEntity e1 = appManualLotteryDao.find(e.getId());
        Assert.assertEquals(e.getIntroduction(), e1.getIntroduction());
        Assert.assertEquals(e.getFreeLimit(), e1.getFreeLimit());
    }

    @Test
    public void findAllByIdsTest() {
        AppManualLotteryEntity e = info;
        List<Long> ids = new ArrayList<>();
        ids.add(e.getId());
        List<AppManualLotteryEntity> e1 = appManualLotteryDao.findAllByIds(ids);
        assertDO(e, e1.get(0));
    }

    @Test
    public void updateTest() {
        AppManualLotteryEntity e = new AppManualLotteryEntity(info.getId());
        TestUtils.setRandomAttributesForBean(e, false);
        appManualLotteryDao.update(e);
        AppManualLotteryEntity e1 = appManualLotteryDao.find(e.getId());
        assertDO(e, e1);
    }

    private void assertDO(AppManualLotteryEntity e, AppManualLotteryEntity e1) {
        String[] exceptFields = new String[] { "gmtCreate", "gmtModified", "overDate" };
        TestUtils.assertEqualsReflect(e, e1, false, exceptFields);// 时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr(e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr(e1.getGmtModified()));
    }

}
