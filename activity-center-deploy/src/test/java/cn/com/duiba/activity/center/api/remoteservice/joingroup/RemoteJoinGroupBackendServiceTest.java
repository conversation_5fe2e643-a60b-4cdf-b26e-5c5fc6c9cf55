package cn.com.duiba.activity.center.api.remoteservice.joingroup;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.joingroup.JoinGroupConfigDto;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Desc
 * <AUTHOR>
 * @Date 2018/9/17
 */
public class RemoteJoinGroupBackendServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteJoinGroupBackendService remoteJoinGroupBackendService;

    @Test
    public void test() throws Exception {
        String json = "{\"joinAwardType\":1,\"activityType\":1,\"joinGroupItemList\":[{\"expiredTime\":90060,\"cheatRuleType\":3,\"openLimitType\":1,\"joinLimitType\":1,\"prizeType\":\"phonebill\",\"facePrice\":2,\"name\":\"1\",\"expiredDay\":\"1\",\"expiredHour\":\"1\",\"expiredMin\":\"1\",\"groupNumber\":\"1\",\"openLimit\":\"1\",\"joinLimit\":\"1\"}],\"title\":\"1\",\"otherGroupCount\":\"1\",\"relateActList\":\"1\",\"relateActDetail\":\"1\",\"plannerName\":\"1\",\"joinAward\":\"1\"}";
        JoinGroupConfigDto dto = JSON.parseObject(json, JoinGroupConfigDto.class);
        remoteJoinGroupBackendService.save(dto);
    }
}
