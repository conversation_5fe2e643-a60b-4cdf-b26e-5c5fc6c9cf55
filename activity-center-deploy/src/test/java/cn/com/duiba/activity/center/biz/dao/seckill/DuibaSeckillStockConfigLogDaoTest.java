package cn.com.duiba.activity.center.biz.dao.seckill;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.seckill.DuibaSeckillStockConfigConsumeEntity;
import cn.com.duiba.wolf.utils.TestUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * Created by wenqi.huang on 2016/11/24.
 */
@Transactional(DsConstants.DATABASE_SECKILL)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaSeckillStockConfigLogDaoTest extends TransactionalTestCaseBase {
    @Resource
    private DuibaSeckillStockConfigLogDao duibaSeckillStockConfigLogDao;

    private ThreadLocal<DuibaSeckillStockConfigConsumeEntity> threadLocal = new ThreadLocal<>();

    @Before
    public void testInsertConsumeLog() {
        DuibaSeckillStockConfigConsumeEntity log = TestUtils.createRandomBean(DuibaSeckillStockConfigConsumeEntity.class);
        duibaSeckillStockConfigLogDao.insertConsumeLog(log);
        threadLocal.set(log);
    }

    @Test
    public void testFindByUnique() {
        DuibaSeckillStockConfigConsumeEntity e = threadLocal.get();
        DuibaSeckillStockConfigConsumeEntity e1 = duibaSeckillStockConfigLogDao.findByUnique(e.getAction(), e.getRelationType(), e.getRelationId());
        Assert.assertEquals(e.getId(), e1.getId());
    }
}
