package cn.com.duiba.activity.center.biz.service.singlelottery;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.ActivityExtraInfoDto;
import cn.com.duiba.activity.center.api.dto.singlelottery.DuibaSingleLotteryDto;
import cn.com.duiba.activity.center.api.dto.singlelottery.SingleLotteryAppSpecifyDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on s16/6/20.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class DuibaSingleLotteryServiceTest extends TransactionalTestCaseBase{
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS;

    @Resource
    private DuibaSingleLotteryService duibaSingleLotteryService;

//    @Resource
//    private OperatingActivityDao operatingActivityDao;


    private DuibaSingleLotteryDto duibasingleLottery;



    @Before
    public void testInsert(){
        DuibaSingleLotteryDto e=new DuibaSingleLotteryDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        duibaSingleLotteryService.insert(e);
        duibasingleLottery=e;
    }

    @Test
    public void testFind() {
        DuibaSingleLotteryDto e=duibasingleLottery;
        DuibaSingleLotteryDto e1=duibaSingleLotteryService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindTagById() {
        DuibaSingleLotteryDto e=duibasingleLottery;
        String tag = duibaSingleLotteryService.findTagById(e.getId());
        Assert.assertEquals(tag, e.getTag());
    }

    @Test
    public void testFindAutoOff() {
        DuibaSingleLotteryDto e=new DuibaSingleLotteryDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-300000l));
        duibaSingleLotteryService.insert(e);
        List<DuibaSingleLotteryDto> list=duibaSingleLotteryService.findAutoOff();
        boolean isfind=false;
        for (DuibaSingleLotteryDto d:list){
            if(e.getId().equals(d.getId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindSingleLotteryPage() {
        DuibaSingleLotteryDto e=new DuibaSingleLotteryDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-300000l));
        duibaSingleLotteryService.insert(e);
        Map<String,Object> params=new HashMap<>();
        params.put("max",10);
        params.put("offset",0);
        params.put("title",e.getTitle());
        List<DuibaSingleLotteryDto> list=duibaSingleLotteryService.findSingleLotteryPage(params);
        boolean isfind=false;
        for (DuibaSingleLotteryDto d:list){
            if(e.getId().equals(d.getId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindSingleLottery() {
        DuibaSingleLotteryDto e=new DuibaSingleLotteryDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-300000l));
        duibaSingleLotteryService.insert(e);
        Map<String,Object> params=new HashMap<>();
        params.put("max",10);
        params.put("offset",0);
        List<DuibaSingleLotteryDto> list=duibaSingleLotteryService.findSingleLottery(params);
        boolean isfind=false;
        for (DuibaSingleLotteryDto d:list){
            if(e.getId().equals(d.getId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindSingleLotteryPageCount() {
        DuibaSingleLotteryDto e=new DuibaSingleLotteryDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-300000l));
        duibaSingleLotteryService.insert(e);
        Map<String,Object> params=new HashMap<>();
        params.put("max",10);
        params.put("offset",0);
        Assert.assertTrue(duibaSingleLotteryService.findSingleLotteryPageCount(params)>0);
    }

    @Test
    public void testFindAllDuibaSingleLottery() {
//        DuibaSingleLotteryDto e=new DuibaSingleLotteryDto(true);
//        TestUtils.setRandomAttributesForBean(e,false);
//        e.setDeleted(false);
//        e.setStatus(1);
//        e.setAutoOffDate(new Date(System.currentTimeMillis()-300000l));
//        duibaSingleLotteryService.insert(e);
//
//        OperatingActivityDO o=new OperatingActivityDO(true);
//        TestUtils.setRandomAttributesForBean(o,false);
//        o.setDeleted(false);
//        o.setStatus(1);
//        o.setActivityId(e.getId());
//        operatingActivityDao.insert(o);

        duibaSingleLotteryService.findAllDuibaSingleLottery(1l);
    }

    @Test
    public void testFindAllByIds() {
        DuibaSingleLotteryDto e=duibasingleLottery;
        assertDO(e,duibaSingleLotteryService.findAllByIds(Arrays.asList(e.getId())).get(0));
    }

    @Test
    public void testGetCountDuibaSingleLottery() {
        DuibaSingleLotteryDto e=new DuibaSingleLotteryDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-300000l));
        duibaSingleLotteryService.insert(e);
        Map<String,Object> params=new HashMap<>();
        params.put("activityId",e.getId());
        Assert.assertTrue(duibaSingleLotteryService.getCountDuibaSingleLottery(params)>0);
    }

    @Test
    public void testFindHasUserdSingleIds() {
        duibaSingleLotteryService.findHasUserdSingleIds(duibasingleLottery.getMainItemId());
    }

    @Test
    public void testFindExtraInfoById() {
        DuibaSingleLotteryDto e=duibasingleLottery;
        ActivityExtraInfoDto vo=duibaSingleLotteryService.findExtraInfoById(e.getId());
        Assert.assertEquals(e.getFreeRule(),vo.getFreeRule());
    }

    @Test
    public void testUpdateAutoOffDate() {
        Assert.assertTrue(duibaSingleLotteryService.updateAutoOffDate(duibasingleLottery.getId())>0);
    }

    @Test
    public void testUpdate() {
        DuibaSingleLotteryDto e=new DuibaSingleLotteryDto(duibasingleLottery.getId());
        TestUtils.setRandomAttributesForBean(e,false);
        duibaSingleLotteryService.update(e);
        DuibaSingleLotteryDto e1=duibaSingleLotteryService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testReduceMainItemRemaining() {
        DuibaSingleLotteryDto e=duibasingleLottery;
        duibaSingleLotteryService.reduceMainItemRemaining(e.getId());
        DuibaSingleLotteryDto e1=duibaSingleLotteryService.find(e.getId());
        Assert.assertEquals(e.getMainItemRemaining(),(Integer) (e1.getMainItemRemaining()+1));
    }

    @Test
    public void testAddMainItemRemaining() {
        DuibaSingleLotteryDto e=duibasingleLottery;
        duibaSingleLotteryService.addMainItemRemaining(e.getId());
        DuibaSingleLotteryDto e1=duibaSingleLotteryService.find(e.getId());
        Assert.assertEquals(e.getMainItemRemaining(),(Integer) (e1.getMainItemRemaining()-1));
    }

    @Test
    public void testReduceInciteItemRemaining() {
        DuibaSingleLotteryDto e=duibasingleLottery;
        duibaSingleLotteryService.reduceInciteItemRemaining(e.getId());
        DuibaSingleLotteryDto e1=duibaSingleLotteryService.find(e.getId());
        Assert.assertEquals(e.getInciteItemRemaining(),(Integer) (e1.getInciteItemRemaining()+1));
    }

    @Test
    public void testAddInciteItemRemaining() {
        DuibaSingleLotteryDto e=duibasingleLottery;
        duibaSingleLotteryService.addInciteItemRemaining(e.getId());
        DuibaSingleLotteryDto e1=duibaSingleLotteryService.find(e.getId());
        Assert.assertEquals(e.getInciteItemRemaining(),(Integer) (e1.getInciteItemRemaining()-1));
    }

    @Test
    public void testUpdateForAdminEdit() {
        DuibaSingleLotteryDto e=duibasingleLottery;
        TestUtils.setRandomAttributesForBean(e,false);
        Assert.assertTrue(duibaSingleLotteryService.updateForAdminEdit(e)>0);
    }

    @Test
    public void testUpdateTagById() {
        DuibaSingleLotteryDto e=duibasingleLottery;
        String tag = "newTag1";
        duibaSingleLotteryService.updateTagById(e.getId(), tag);

        Assert.assertEquals(tag, duibaSingleLotteryService.findTagById(e.getId()));
    }

    @Test
    public void testAddMainItemRemainingById() {
        DuibaSingleLotteryDto e=duibasingleLottery;
        duibaSingleLotteryService.addMainItemRemainingById(e.getId(),1);
        DuibaSingleLotteryDto e1=duibaSingleLotteryService.find(e.getId());
        Assert.assertEquals(e.getMainItemRemaining(),(Integer)(e1.getMainItemRemaining()-1));
    }

    @Test
    public void testSubMainItemRemainingById() {
        DuibaSingleLotteryDto e=duibasingleLottery;
        duibaSingleLotteryService.subMainItemRemainingById(e.getId(),1);
        DuibaSingleLotteryDto e1=duibaSingleLotteryService.find(e.getId());
        Assert.assertEquals(e.getMainItemRemaining(),(Integer)(e1.getMainItemRemaining()+1));
    }

    @Test
    public void testAddInciteItemRemainingById() {
        DuibaSingleLotteryDto e=duibasingleLottery;
        duibaSingleLotteryService.addInciteItemRemainingById(e.getId(),1);
        DuibaSingleLotteryDto e1=duibaSingleLotteryService.find(e.getId());
        Assert.assertEquals(e.getInciteItemRemaining(),(Integer)(e1.getInciteItemRemaining()-1));
    }

    @Test
    public void testSubInciteItemRemainingById() {
        DuibaSingleLotteryDto e=duibasingleLottery;
        duibaSingleLotteryService.subInciteItemRemainingById(e.getId(),1);
        DuibaSingleLotteryDto e1=duibaSingleLotteryService.find(e.getId());
        Assert.assertEquals(e.getInciteItemRemaining(),(Integer)(e1.getInciteItemRemaining()+1));
    }

    @Test
    public void testFindForupdate() {
        DuibaSingleLotteryDto e=duibasingleLottery;
        DuibaSingleLotteryDto e1=duibaSingleLotteryService.findForupdate(e.getId());
        Assert.assertEquals(e.getMainItemRemaining(),e1.getMainItemRemaining());
    }

    private SingleLotteryAppSpecifyDto getInsertAbleAppDO(){
        SingleLotteryAppSpecifyDto e=new SingleLotteryAppSpecifyDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        return e;
    }

    private void assertAppDO(SingleLotteryAppSpecifyDto e,SingleLotteryAppSpecifyDto e1){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified"}));

        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }

    private void assertDO(DuibaSingleLotteryDto e, DuibaSingleLotteryDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaSingleLotteryDto e, DuibaSingleLotteryDto e1,String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","activityCategoryId","tag",}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }
}
