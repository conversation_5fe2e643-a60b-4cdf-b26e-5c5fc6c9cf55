package cn.com.duiba.activity.center.biz.service.guessredpacket;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.guessredpacket.GuessRedPacketDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;



@Transactional(DsConstants.DATABASE_ACT_RECORD)
public class GuessRedPacketServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private GuessRedPacketService guessRedPacketService;
	
	@Test
	public void insertTest(){
		GuessRedPacketDto dto = new GuessRedPacketDto();
		dto.setAppId(31005L);
		dto.setActivityId(310000L);
		dto.setRedPacketId(123456L);
		dto.setConsumerId(1L);
		dto.setOwnerConsumerId(2L);
		dto.setAmount(2702L);
		Long id = guessRedPacketService.insert(dto);
	}
	


}
