package cn.com.duiba.activity.center.biz.dao.seckill;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.seckill.DuibaSeckillStockLogEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by wen<PERSON>.huang on 2016/11/24.
 */
@Transactional(DsConstants.DATABASE_SECKILL)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaSeckillStockLogDaoTest extends TransactionalTestCaseBase {
    @Resource
    private DuibaSeckillStockLogDao duibaSeckillStockLogDao;

    private DuibaSeckillStockLogEntity log;

    @Test
    public void insert() {
        log = TestUtils.createRandomBean(DuibaSeckillStockLogEntity.class);
        duibaSeckillStockLogDao.insert(log);
        Assert.assertNotNull(log.getId());
    }
}
