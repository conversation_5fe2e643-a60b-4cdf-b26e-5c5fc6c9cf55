package cn.com.duiba.activity.center.api.remoteservice.iqiyi;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.iqiyi.HappyCodeDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author: lufeng
 * @Description:
 * @Date: Created in 2019/11/18
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class HappyCodeTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteHappyCodeService remoteHappyCodeService;

    @Test
    public void insertTest() {
        HappyCodeDto happyCodeDto = new HappyCodeDto();
        happyCodeDto.setAppId(1L);
        happyCodeDto.setConsumerId(1111L);
        happyCodeDto.setHappyCode("45654646");
        happyCodeDto.setItemId(12L);
        happyCodeDto.setPhaseId(1L);
        happyCodeDto.setPhaseNumber("20191118");
        happyCodeDto.setStatus(0);
        Long id = remoteHappyCodeService.insert(happyCodeDto);
        System.out.println("id========" + id);
    }

    @Test
    public void getByIdTest() {
        HappyCodeDto happyCodeDto = remoteHappyCodeService.getById(1L);
        System.out.println("happyCodeDto====" + JSONObject.toJSONString(happyCodeDto));
    }
}
