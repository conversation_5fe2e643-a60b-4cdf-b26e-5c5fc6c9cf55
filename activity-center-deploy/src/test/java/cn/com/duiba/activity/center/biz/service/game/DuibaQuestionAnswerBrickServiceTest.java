package cn.com.duiba.activity.center.biz.service.game;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionAnswerBrickDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/3.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaQuestionAnswerBrickServiceTest extends TransactionalTestCaseBase {


    @Resource
    private DuibaQuestionAnswerBrickService duibaQuestionAnswerBrickService;

    private ThreadLocal<DuibaQuestionAnswerBrickDto> duibaQuestionAnswerBrickDO=new ThreadLocal<>();

    @Before
    public void testInsert() {
        DuibaQuestionAnswerBrickDto e=new DuibaQuestionAnswerBrickDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionAnswerBrickService.insert(e);
        duibaQuestionAnswerBrickDO.set(e);
    }

    @Test
    public void testFind() {
        DuibaQuestionAnswerBrickDto e=duibaQuestionAnswerBrickDO.get();
        DuibaQuestionAnswerBrickDto e1=duibaQuestionAnswerBrickService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testUpdate4Admin() {
        DuibaQuestionAnswerBrickDto e=duibaQuestionAnswerBrickDO.get();
        duibaQuestionAnswerBrickService.update4Admin(e.getId(),"test1","test2","test3");
        DuibaQuestionAnswerBrickDto e1=duibaQuestionAnswerBrickService.find(e.getId());
        Assert.assertTrue(e1.getTitle().equals("test1"));
    }

    @Test
    public void testFindByTitle() {
        DuibaQuestionAnswerBrickDto e=duibaQuestionAnswerBrickDO.get();
        DuibaQuestionAnswerBrickDto e1=duibaQuestionAnswerBrickService.findByTitle(e.getTitle());
        assertDO(e,e1);
    }

    @Test
    public void testOpen() {
        DuibaQuestionAnswerBrickDto e=duibaQuestionAnswerBrickDO.get();
        duibaQuestionAnswerBrickService.open(e.getId());
        DuibaQuestionAnswerBrickDto e1=duibaQuestionAnswerBrickService.find(e.getId());
        Assert.assertTrue(e1.getStatus().equals(1));
    }

    @Test
    public void testDisable() {
        DuibaQuestionAnswerBrickDto e=duibaQuestionAnswerBrickDO.get();
        duibaQuestionAnswerBrickService.disable(e.getId());
        DuibaQuestionAnswerBrickDto e1=duibaQuestionAnswerBrickService.find(e.getId());
        Assert.assertTrue(e1.getStatus().equals(0));
    }

    @Test
    public void testFindPage() {
        Map<String,Object> params=new HashMap<>();
        params.put("max",10);
        params.put("offset",0);
        Assert.assertTrue(duibaQuestionAnswerBrickService.findPage(params).size()>0);
    }

    @Test
    public void testFindPageCount() {
        Assert.assertTrue(duibaQuestionAnswerBrickService.findPageCount()>0);
    }

    @Test
    public void testFindAll() {
        Assert.assertTrue(duibaQuestionAnswerBrickService.findAll().size()>0);
    }

    @Test
    public void testGetBrickContentById() {
        DuibaQuestionAnswerBrickDto e=duibaQuestionAnswerBrickDO.get();
        Assert.assertTrue(duibaQuestionAnswerBrickService.getBrickContentById(e.getId()).equals(e.getContent()));
    }

    @Test
    public void testFindNoContent() {
        DuibaQuestionAnswerBrickDto e=duibaQuestionAnswerBrickDO.get();
        Assert.assertNotNull(duibaQuestionAnswerBrickService.findNoContent(e.getId()));
    }

    private void assertDO(DuibaQuestionAnswerBrickDto e, DuibaQuestionAnswerBrickDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaQuestionAnswerBrickDto e, DuibaQuestionAnswerBrickDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

}
