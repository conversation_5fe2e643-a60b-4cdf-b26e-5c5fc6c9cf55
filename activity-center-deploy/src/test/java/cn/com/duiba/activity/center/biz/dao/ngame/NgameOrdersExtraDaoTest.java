package cn.com.duiba.activity.center.biz.dao.ngame;

import java.util.Date;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.ngame.NgameOrdersExtraEntity;

@Transactional(DsConstants.DATABASE_NGAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class NgameOrdersExtraDaoTest extends TransactionalTestCaseBase {

	@Resource
	private NgameOrdersExtraDao ngameOrdersExtraDao;

	@Test
	public void findTest() {
		System.out.println(ngameOrdersExtraDao.find(1l).getItemId());
	}
	
	@Test
	public void findByGameOrderIdTest() {
		System.out.println(ngameOrdersExtraDao.findByGameOrderId(25l).getId());
	}
	
	@Test
	public void insertTest() {
		NgameOrdersExtraEntity e = new NgameOrdersExtraEntity();
		e.setGameOrderId(1l);
		e.setGmtCreate(new Date());
		e.setGmtModified(new Date());
		ngameOrdersExtraDao.insert(e);
	}
	
	@Test
	public void updateOrderIdTest() {
		ngameOrdersExtraDao.updateOrderId(20l, 1l, "122");
	}
}
