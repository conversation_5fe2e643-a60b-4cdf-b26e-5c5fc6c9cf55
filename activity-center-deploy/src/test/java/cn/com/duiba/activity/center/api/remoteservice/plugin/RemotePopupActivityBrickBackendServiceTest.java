package cn.com.duiba.activity.center.api.remoteservice.plugin;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity_brick.PopupActivityBrickDto;
import cn.com.duiba.activity.center.api.dto.activity_brick.PopupActivityBrickSimpleDto;
import cn.com.duiba.activity.center.api.remoteservice.activity_brick.RemotePopupActivityBrickBackendService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by xiaoxuda on 2017/12/13.
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class RemotePopupActivityBrickBackendServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemotePopupActivityBrickBackendService remotePopupActivityBrickBackendService;

    @Test
    public void testFind(){
        List<PopupActivityBrickDto> list = remotePopupActivityBrickBackendService
                .findAll(PopupActivityBrickDto.TYPE_PLUGIN_TOOL).getResult();
        List<PopupActivityBrickSimpleDto> simples = remotePopupActivityBrickBackendService
                .batchFindByTypeAndIds(PopupActivityBrickDto.TYPE_PLUGIN_TOOL, Lists.newArrayList(list.get(0).getId()));
        Assert.assertTrue(CollectionUtils.isNotEmpty(simples));
        Assert.assertTrue(simples.size() == 1);
    }
}
