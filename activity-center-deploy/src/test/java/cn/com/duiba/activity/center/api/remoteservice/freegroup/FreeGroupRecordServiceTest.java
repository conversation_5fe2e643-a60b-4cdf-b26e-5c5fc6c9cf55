package cn.com.duiba.activity.center.api.remoteservice.freegroup;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.freegroup.FreeGroupUserRecordDto;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @Author: lufeng
 * @Description:
 * @Date: Created in 2019/3/18
 */
public class FreeGroupRecordServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteFreeGroupRecordService remoteFreeGroupRecordService;

    @Test
    public void testDoOpenGroup() {
        Long id = remoteFreeGroupRecordService.doOpenGroup(1L, 2L, 3L, 4L, 1);
        System.out.println("============="+ id);
    }

    @Test
    public void testDoJoinGroup() {
        Boolean isSuc = remoteFreeGroupRecordService.doJoinGroup(1L, 2L,3L, 4L, 1L,1, false);
        System.out.println("============="+ isSuc);
    }

    @Test
    public void testListFreeGroupRecord() {
        List<FreeGroupUserRecordDto> list = remoteFreeGroupRecordService.listGroupUserRecordByConsumerId(2L, 4L);
        if (CollectionUtils.isNotEmpty(list)) {
            System.out.println(list.size());
        }
    }


}
