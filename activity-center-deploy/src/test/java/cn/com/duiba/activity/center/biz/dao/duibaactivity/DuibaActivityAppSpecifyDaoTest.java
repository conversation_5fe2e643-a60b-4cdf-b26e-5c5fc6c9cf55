package cn.com.duiba.activity.center.biz.dao.duibaactivity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.duibaactivity.DuibaActivityAppSpecifyEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/23.
 */
@Transactional(DuibaActivityAppSpecifyDaoTest.THIS_DATABASE_SCHEMA)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaActivityAppSpecifyDaoTest extends TransactionalTestCaseBase {
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS;

    @Resource
    private DuibaActivityAppSpecifyDao duibaActivityAppSpecifyDao;


    @Test
    public void testInsert() {
        DuibaActivityAppSpecifyEntity e = new DuibaActivityAppSpecifyEntity(true);
        TestUtils.setRandomAttributesForBean(e, false);
        duibaActivityAppSpecifyDao.insert(e);
    }

    @Test
    public void testFind() {
        DuibaActivityAppSpecifyEntity e = new DuibaActivityAppSpecifyEntity(true);
        TestUtils.setRandomAttributesForBean(e, false);
        duibaActivityAppSpecifyDao.insert(e);
        DuibaActivityAppSpecifyEntity e1 = duibaActivityAppSpecifyDao.find(e.getId());
        assertDO(e1, e);
    }

    @Test
    public void testFindDuiBaActivitySpecifyDO() {
        DuibaActivityAppSpecifyEntity e = new DuibaActivityAppSpecifyEntity(true);
        TestUtils.setRandomAttributesForBean(e, false);
        duibaActivityAppSpecifyDao.insert(e);
        Assert.assertTrue(duibaActivityAppSpecifyDao.findDuiBaActivitySpecifyDO(e.getDuibaActivityId()).size() > 0);
    }

    @Test
    public void testDelete() {
        DuibaActivityAppSpecifyEntity e = new DuibaActivityAppSpecifyEntity(true);
        TestUtils.setRandomAttributesForBean(e, false);
        duibaActivityAppSpecifyDao.insert(e);
        duibaActivityAppSpecifyDao.delete(e.getId());
        Assert.assertNull(duibaActivityAppSpecifyDao.find(e.getId()));
    }

    @Test
    public void testFindByDuibaActivityAndApp() {
        DuibaActivityAppSpecifyEntity e = new DuibaActivityAppSpecifyEntity(true);
        TestUtils.setRandomAttributesForBean(e, false);
        duibaActivityAppSpecifyDao.insert(e);
        duibaActivityAppSpecifyDao.findByDuibaActivityAndApp(e.getDuibaActivityId(), e.getAppId());
    }

    private void assertDO(DuibaActivityAppSpecifyEntity e, DuibaActivityAppSpecifyEntity e1) {
        assertDO(e, e1, null);
    }

    private void assertDO(DuibaActivityAppSpecifyEntity e, DuibaActivityAppSpecifyEntity e1, String[] exculdeFields) {
        List<String> exculdeFieldsList = new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate", "gmtModified"}));
        if (exculdeFields != null && exculdeFields.length > 0) {
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1, false, exculdeFieldsList.toArray(new String[exculdeFieldsList.size() + 1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr(e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr(e1.getGmtModified()));
    }

}
