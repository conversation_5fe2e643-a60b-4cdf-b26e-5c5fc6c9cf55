package cn.com.duiba.activity.center.api.remoteservice.activityredpack;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.remoteservice.alipayactivityredpack.RemoteAlipayActivityConfigService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RemoteActivityConfigTest.java
 * @Description
 * @createTime 2022年11月18日 16:11:00
 */
public class RemoteActivityConfigTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteAlipayActivityConfigService remoteActivityConfig;

    @Test

    public  void pageByParamsTest(){
//        AlipayActivityConfigPageParam AlipayActivityConfigPageParam=new AlipayActivityConfigPageParam();
//        AlipayActivityConfigPageParam.setRedpackActivityId();
//        remoteActivityConfig.pageByParams()
    }




}
