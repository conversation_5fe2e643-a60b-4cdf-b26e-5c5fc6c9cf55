package cn.com.duiba.activity.center.biz.bo;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolStockConsumeDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.hdtool.DuibaHdtoolDao;
import cn.com.duiba.activity.center.biz.dao.hdtool.DuibaHdtoolOptionsDao;
import cn.com.duiba.activity.center.biz.entity.hdtool.DuibaHdtoolOptionsEntity;
import cn.com.duiba.activity.center.biz.service.hdtool.HdtoolStockConsumeService;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/27.
 */
@Transactional(DsConstants.DATABASE_CREDITS)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class HdtoolStockConsumeBoTest extends TransactionalTestCaseBase {

    @Resource
    private HdtoolStockConsumeBo hdtoolStockConsumeBo;

    @Resource
    private DuibaHdtoolDao duibaHdtoolDao;
    @Resource
    private DuibaHdtoolOptionsDao hdtoolOptionsDao;

    @Resource
    private HdtoolStockConsumeService hdtoolStockConsumeService;



    @Test
    public void testConsumeDuibaHdtoolOptionStock() throws Exception {
//        TurntableStockConsumeDO e=new TurntableStockConsumeDO(true);
//        TestUtils.setRandomAttributesForBean(e,false);
//        e.setAction("pay");
//        turntableStockConsumeDao.insert(e);
        DuibaHdtoolOptionsEntity e=new DuibaHdtoolOptionsEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        hdtoolOptionsDao.insertHdtoolOption(e);
        hdtoolStockConsumeBo.consumeDuibaHdtoolOptionStock(e.getId(),1l,"test","test");
    }

    @Test
    public void testPaybackDuibaHdtoolOptionStock() throws Exception {
        DuibaHdtoolOptionsEntity e=new DuibaHdtoolOptionsEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        hdtoolOptionsDao.insertHdtoolOption(e);
        HdtoolStockConsumeDto h=new HdtoolStockConsumeDto(true);
        TestUtils.setRandomAttributesForBean(h,false);
        h.setRelationId(e.getId());
        h.setAction("pay");
        hdtoolStockConsumeService.insert(h);
        hdtoolStockConsumeBo.paybackDuibaHdtoolOptionStock(h.getBizId(),h.getBizSource());
    }

}
