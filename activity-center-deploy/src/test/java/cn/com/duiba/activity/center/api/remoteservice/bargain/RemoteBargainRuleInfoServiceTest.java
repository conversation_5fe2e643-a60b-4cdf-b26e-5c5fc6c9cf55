package cn.com.duiba.activity.center.api.remoteservice.bargain;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.bargain.BargainRuleInfoDto;
import cn.com.duiba.activity.center.api.enums.DeletedEnum;
import cn.com.duiba.activity.center.api.params.bargain.BargainRuleInfoParam;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (<EMAIL>)
 * @date 2018/12/7 0007 11:03
 */
@Transactional(value = DsConstants.DATABASE_ACT_COM_CONF)
@Rollback(value = false)
public class RemoteBargainRuleInfoServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteBargainRuleInfoService remoteBargainRuleInfoService;

    @Test
    public void insert() {
        BargainRuleInfoDto pojo = new BargainRuleInfoDto();
        pojo.setOneselfMin(3);
        pojo.setOneselfMax(5);
        pojo.setNewUserMin(6);
        pojo.setNewUserMax(7);
        pojo.setOldUserMin(8);
        pojo.setOldUserMax(9);
        pojo.setBargainActivityId(1L);
        pojo.setDeleted(DeletedEnum.UNDELETED.value());
        Long in = remoteBargainRuleInfoService.insert(pojo);
        System.out.println("ruleId=" + in);
    }

    @Test
    public void batchInsert() {
        List<BargainRuleInfoDto> pojos = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            BargainRuleInfoDto pojo = new BargainRuleInfoDto();
            pojo.setOneselfMin(3);
            pojo.setOneselfMax(5);
            pojo.setNewUserMin(6);
            pojo.setNewUserMax(7);
            pojo.setOldUserMin(8);
            pojo.setOldUserMax(9);
            pojo.setBargainActivityId(1L);
            pojo.setDeleted(DeletedEnum.UNDELETED.value());
            pojos.add(pojo);
        }
        List<Long> in = remoteBargainRuleInfoService.batchInsert(pojos);
        System.out.println(in);
    }

    /**
     * 分页查询砍价活动列表
     * 1.过滤已经删除的
     */
    @Test
    public void pageByParams() {
        BargainRuleInfoParam param = new BargainRuleInfoParam();
        param.setDeleted(DeletedEnum.UNDELETED.value());
        List<BargainRuleInfoDto> page = remoteBargainRuleInfoService.listByParams(param);
        System.out.println(JSONObject.toJSONString(page));
    }

    /**
     * 修改
     * 1.删除也是调用此接口，设置deleted=DeletedEnum.DELETED
     */
    @Test
    public void update() {
        BargainRuleInfoDto pojo = new BargainRuleInfoDto();
        pojo.setId(12L);
        pojo.setOldUserMin(0);
        pojo.setOldUserMax(9);
        remoteBargainRuleInfoService.update(pojo);
    }

    @Test
    public void deleteById() {
        remoteBargainRuleInfoService.deleteById(5L);
    }

    @Test
    public void findById() {
        BargainRuleInfoDto dto = remoteBargainRuleInfoService.findById(5L);
        System.out.println(JSONObject.toJSONString(dto));
    }

}
