package cn.com.duiba.activity.center.biz.remoteservice.activity_proya;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity_proya.ActivityProyaDto;
import cn.com.duiba.activity.center.api.dto.activity_proya.WxTemplatePushDto;
import cn.com.duiba.activity.center.api.remoteservice.activity_proya.RemoteActivityProyaService;
import cn.com.duiba.activity.center.api.remoteservice.activity_proya.RemoteWxTemplatePushService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/7/25 15:14
 */
@Transactional(DsConstants.DATABASE_DUIBA_CUSTOM)
public class RemoteActivityProyaServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteActivityProyaService remoteActivityProyaService;

    @Autowired
    private RemoteWxTemplatePushService RemoteProyaPushService;


    @Test
    public void findByCidTest(){
        ActivityProyaDto dto = remoteActivityProyaService.findPhoneByCid(6L);
        System.out.println(JSONObject.toJSONString(dto));
    }

    @Test
    @Rollback(false)
    public void saveTest(){
        ActivityProyaDto dto = new ActivityProyaDto();
        dto.setCid(2L);
        dto.setPhone("19987561234");
        remoteActivityProyaService.saveProyaConsumer(dto);
    }

    @Test
    @Rollback(false)
    public void savePushTest(){
        WxTemplatePushDto dto = new WxTemplatePushDto();
        dto.setDeadline(DateUtils.daysAddOrSub(new Date(),7));
        dto.setTouser("GHJ2354");
        dto.setUsed(false);
        dto.setTemplateId("AFSAD2345");
        dto.setFormId("44");
        dto.setTemplateData("111");
        dto.setWxAppId("wxdf6b9017933b1cd5");
        dto.setSecret("ef8ad46f25e79701329caa5f9069a168");
        RemoteProyaPushService.saveTemplatePush(dto);
    }

    @Test
    public void findTemplateTest(){
        List<WxTemplatePushDto> proyaDtoList = RemoteProyaPushService.findTemplateByType();
        proyaDtoList.stream().forEach(dto -> System.out.println(JSONObject.toJSONString(dto)));
    }

    @Test
    @Rollback(false)
    public void deleteByIdTest(){
        RemoteProyaPushService.deleteById(2L);
    }

    @Test
    @Rollback(false)
    public void deleteBatchIdTest(){
        RemoteProyaPushService.deleteBatchId(Arrays.asList(1L,4L));
    }


}
