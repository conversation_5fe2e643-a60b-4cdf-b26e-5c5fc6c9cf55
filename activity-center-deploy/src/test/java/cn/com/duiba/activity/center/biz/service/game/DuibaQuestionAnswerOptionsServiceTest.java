package cn.com.duiba.activity.center.biz.service.game;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionAnswerOptionsDto;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionAnswerOrdersDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/2.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaQuestionAnswerOptionsServiceTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaQuestionAnswerOptionsService duibaQuestionAnswerOptionsService;
    

    private ThreadLocal<DuibaQuestionAnswerOptionsDto> duibaQuestionAnswerOptionsDO=new ThreadLocal<>();

    @Before
    public void testInsert() {
        DuibaQuestionAnswerOptionsDto e=new DuibaQuestionAnswerOptionsDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionAnswerOptionsService.insert(e);
        duibaQuestionAnswerOptionsDO.set(e);
    }

    @Test
    public void testFind() {
        DuibaQuestionAnswerOptionsDto e=duibaQuestionAnswerOptionsDO.get();
        DuibaQuestionAnswerOptionsDto e1=duibaQuestionAnswerOptionsService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindById() {
        DuibaQuestionAnswerOptionsDto e=duibaQuestionAnswerOptionsDO.get();
        DuibaQuestionAnswerOptionsDto e1=duibaQuestionAnswerOptionsService.findById(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testDelete() {
        DuibaQuestionAnswerOptionsDto e=duibaQuestionAnswerOptionsDO.get();
        duibaQuestionAnswerOptionsService.delete(Arrays.asList(e.getId()));
        DuibaQuestionAnswerOptionsDto e1=duibaQuestionAnswerOptionsService.findById(e.getId());
        Assert.assertTrue(e1.getDeleted());
    }

    @Test
    public void testUpdate() {
        DuibaQuestionAnswerOptionsDto e=duibaQuestionAnswerOptionsDO.get();
        e.setDescription("test");
        duibaQuestionAnswerOptionsService.update(e);
        Assert.assertTrue(duibaQuestionAnswerOptionsService.find(e.getId()).getDescription().equals("test"));
    }

    @Test
    public void testFindOptionsByQuestionId() {
        Assert.assertTrue(duibaQuestionAnswerOptionsService.findOptionsByQuestionId(duibaQuestionAnswerOptionsDO.get().getDuibaQuestionAnswerId()).size()>0);
    }

//    @Test
//    public void testUpdateRemainingById() {
//        DuibaQuestionAnswerOptionsDto e=duibaQuestionAnswerOptionsDO.get();
//        duibaQuestionAnswerOptionsService.updateRemainingById(e.getId(),42);
//        Assert.assertTrue(duibaQuestionAnswerOptionsService.find(e.getId()).getRemaining().equals(42l));
//    }

    private void assertDO(DuibaQuestionAnswerOptionsDto e, DuibaQuestionAnswerOptionsDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaQuestionAnswerOptionsDto e, DuibaQuestionAnswerOptionsDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","remaining","optionCount","newOptionCount","deleted","itemName"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }








    private void assertDO(DuibaQuestionAnswerOrdersDto e, DuibaQuestionAnswerOrdersDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaQuestionAnswerOrdersDto e, DuibaQuestionAnswerOrdersDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","questionType","prizeOverdueDate"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }
}
