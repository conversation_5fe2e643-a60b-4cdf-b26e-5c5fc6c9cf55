package cn.com.duiba.activity.center.biz.remoteservice.quizz;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.DeveloperActivityStatisticsDto;
import cn.com.duiba.activity.center.api.dto.quizz.QuizzOrdersDto;
import cn.com.duiba.activity.center.api.remoteservice.quizz.RemoteQuizzAppOrderService;
import cn.com.duiba.activity.center.api.remoteservice.quizz.RemoteQuizzOrdersSequenceService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.support.TableHelper;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ_CONSUMER)
@Ignore
public class RemoteQuizzAppOrderServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private RemoteQuizzAppOrderService quizzAppOrderService;
	
	@Autowired
	private RemoteQuizzOrdersSequenceService quizzOrdersSequenceService;
	
	private QuizzOrdersDto info;
	
	@Before
	public void insertTest(){
		QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		long id = quizzOrdersSequenceService.getId().getId();
		quizzAppOrderService.insert(e, id);
		info = e;
	}
	
	@Test
	public void findQuizzOrderLimit50Test(){
		QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setExchangeStatus(2);
		e.setPrizeType("nothanks");
		e.setMainOrderId(100L);
		quizzAppOrderService.insert(e, quizzOrdersSequenceService.getId().getId());
		List<QuizzOrdersDto> list = quizzAppOrderService.findQuizzOrderLimit50(e.getAppId(), e.getOperatingActivityId());
		Assert.assertTrue(list.size() > 0);
	}
    
	@Test
    public void findByLimitTest(){
    	Map<String, Object> map = TableHelper.getTbSuffixParamsMap(info.getAppId());
    	map.put("appId", info.getAppId());
    	map.put("operatingActivityId", info.getOperatingActivityId());
    	map.put("start", 0);
    	map.put("pageSize", 10);
    	List<QuizzOrdersDto> list = quizzAppOrderService.findByLimit(map);
    	Assert.assertTrue(list.size() > 0);
    }
    
	@Test
    public void totalCountTest(){
    	Map<String, Object> map = TableHelper.getTbSuffixParamsMap(info.getAppId());
    	map.put("appId", info.getAppId());
    	map.put("operatingActivityId", info.getOperatingActivityId());
    	long count = quizzAppOrderService.totalCount(map);
    	Assert.assertTrue(count > 0);
    }
    
	@Test
    public void countFailByOperatingActivityIdsTest(){
		QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setExchangeStatus(3);
		quizzAppOrderService.insert(e, quizzOrdersSequenceService.getId().getId());
    	List<Long> ids = new ArrayList<Long>();
    	ids.add(e.getOperatingActivityId());
    	List<DeveloperActivityStatisticsDto> list = quizzAppOrderService.countFailByOperatingActivityIds(ids, e.getAppId());
    	Assert.assertTrue(list.size() > 0);
    }

}
