package cn.com.duiba.activity.center.biz.service.guess;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.guess.DuibaGuessAppSpecifyDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class DuibaGuessAppSpecifyServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaGuessAppSpecifyService duibaGuessAppSpecifyService;
	
	private DuibaGuessAppSpecifyDto info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaGuessAppSpecifyDto.class);
		duibaGuessAppSpecifyService.insert(info);
	}
	
	@Test
	public void findTest(){
		DuibaGuessAppSpecifyDto e = duibaGuessAppSpecifyService.find(info.getId());
		Assert.assertNotNull(e);
	}
	
	@Test
	public void findByDuibaGuessIdTest(){
		List<DuibaGuessAppSpecifyDto> list = duibaGuessAppSpecifyService.findByDuibaGuessId(info.getDuibaGuessId());
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void deleteTest(){
		DuibaGuessAppSpecifyDto e = TestUtils.createRandomBean(DuibaGuessAppSpecifyDto.class);
		duibaGuessAppSpecifyService.insert(e);
		duibaGuessAppSpecifyService.delete(e.getId());
		DuibaGuessAppSpecifyDto entity = duibaGuessAppSpecifyService.find(e.getId());
		Assert.assertNull(entity);
	}
	
	@Test
	public void findByDuibaGuessAndAppTest(){
		DuibaGuessAppSpecifyDto e = duibaGuessAppSpecifyService.findByDuibaGuessAndApp(info.getDuibaGuessId(), info.getAppId());
		Assert.assertNotNull(e);
		duibaGuessAppSpecifyService.delete(e.getId());
		e = duibaGuessAppSpecifyService.findByDuibaGuessAndApp(info.getDuibaGuessId(), info.getAppId());
		Assert.assertNull(e);
	}

	@Test
	public void testFindByDuibaGuessesAndApp(){
		Map<Long, DuibaGuessAppSpecifyDto> map = duibaGuessAppSpecifyService.findByDuibaGuessesAndApp(Collections.singletonList(info.getDuibaGuessId()), info.getAppId());
		Assert.assertTrue(!map.isEmpty());
	}

}
