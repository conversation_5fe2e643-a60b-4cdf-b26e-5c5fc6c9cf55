package cn.com.duiba.activity.center.api.remoteservice.bargain;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.bargain.BargainItemInfoDto;
import cn.com.duiba.activity.center.api.enums.DeletedEnum;
import cn.com.duiba.activity.center.api.params.bargain.BargainItemInfoParam;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (<EMAIL>)
 * @date 2018/12/7 0007 11:03
 */
@Transactional(value = DsConstants.DATABASE_ACT_COM_CONF)
@Rollback(value = false)
public class RemoteBargainItemInfoServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteBargainItemInfoService remoteBargainItemInfoService;

    @Test
    public void insert() {
        BargainItemInfoDto pojo = new BargainItemInfoDto();
        pojo.setCheatBase(11);
        pojo.setCheatMultiple(1);
        pojo.setBargainActivityId(1L);
        pojo.setItemId(9L);
        pojo.setBargainRuleId(1L);
        pojo.setRemaining(0);
        pojo.setCompletedCount(11);
        pojo.setBargainDuration(11111);
        Long in = remoteBargainItemInfoService.insert(pojo);
        System.out.println(in);
    }

    @Test
    public void batchInsert() {
        List<BargainItemInfoDto> pojos = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            BargainItemInfoDto pojo = new BargainItemInfoDto();
            pojo.setCheatBase(11);
            pojo.setCheatMultiple(1);
            pojo.setBargainActivityId(1L);
            pojo.setItemId(9L);
            pojo.setBargainRuleId(1L);
            pojo.setRemaining(0);
            pojo.setCompletedCount(11);
            pojo.setBargainDuration(11111);
            pojo.setDeleted(DeletedEnum.UNDELETED.value());
            pojos.add(pojo);
        }
        List<Long> in = remoteBargainItemInfoService.batchInsert(pojos);
        System.out.println(in);
    }

    /**
     * 分页查询砍价活动列表
     * 1.过滤已经删除的
     */
    @Test
    public void listByParams() {
        BargainItemInfoParam param = new BargainItemInfoParam();
        param.setDeleted(DeletedEnum.DELETED.value());
        List<BargainItemInfoDto> page = remoteBargainItemInfoService.listByParam(param);
        System.out.println(JSONObject.toJSONString(page));
    }


    @Test
    public void pageByParams() {
        BargainItemInfoParam param = new BargainItemInfoParam();
        param.setDeleted(DeletedEnum.UNDELETED.value());
        cn.com.duiba.api.bo.page.Page page = remoteBargainItemInfoService.pageByParam(param);
        System.out.println(JSONObject.toJSONString(page));
    }

    @Test
    public void countByParam() {
        BargainItemInfoParam param = new BargainItemInfoParam();
        param.setDeleted(DeletedEnum.UNDELETED.value());
        param.setPageSize(1);
        Integer in = remoteBargainItemInfoService.countByParam(param);
        System.out.println(in);
    }


    /**
     * 修改
     * 1.删除也是调用此接口，设置deleted=DeletedEnum.DELETED
     */
    @Test
    public void update() {
        BargainItemInfoDto pojo = new BargainItemInfoDto();
        pojo.setId(11L);
        pojo.setRemaining(99);
        pojo.setImageUrl("ddd");
        remoteBargainItemInfoService.update(pojo);
    }


    @Test
    public void deleteById() {
        remoteBargainItemInfoService.deleteById(4L);
    }
}
