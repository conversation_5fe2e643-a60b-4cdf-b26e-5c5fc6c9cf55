package cn.com.duiba.activity.center.api.remoteservice.activity;

import static org.junit.Assert.assertTrue;

import javax.annotation.Resource;

import cn.com.duiba.activity.center.api.dto.scraperedpacket.ScrapeRedpacketDetailDto;
import cn.com.duiba.activity.center.api.dto.scraperedpacket.ScrapeRedpacketRecordDto;
import cn.com.duiba.activity.center.api.remoteservice.scraperedpacket.RemoteScrapeRedPacketDetailService;
import cn.com.duiba.activity.center.api.remoteservice.scraperedpacket.RemoteScrapeRedPacketRecordService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.support.RedisKeyFactory;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.redis.RedisClient;
import org.springframework.test.annotation.Rollback;

@Rollback(value = false)
public class RemoteActivityConsumerLimitServiceTest extends TransactionalTestCaseBase{

	@Autowired
	private RedisClient redisClient;
	@Resource(name = "redisClient2")
	private RedisClient redisClient2;

	@Autowired
	private RemoteActivityConsumerLimitService remoteActivityConsumerLimitService;

	@Autowired
	private RemoteScrapeRedPacketRecordService remoteScrapeRedPacketRecordService;
	@Autowired
	private RemoteScrapeRedPacketDetailService remoteScrapeRedPacketDetailService;


	@Test
	public void tset(){
//		ScrapeRedpacketRecordDto dto = new ScrapeRedpacketRecordDto();
//		dto.setFromType(2);
//		dto.setScrapeNode(2);
//		dto.setActivityId(1L);
//		dto.setAvatar("ac");
//		dto.setNickname("nick");
//		dto.setRedPacketId(1L);
//		dto.setConsumerId(1L);
//		dto.setScrapeRate("123");
//		remoteScrapeRedPacketRecordService.insert(dto);
//
//		remoteScrapeRedPacketRecordService.findById(1L);

		boolean bo = remoteScrapeRedPacketRecordService.selectExistByActIdAndCid(123L, 123L);
		System.out.println(bo);
	}

	@Test
	public void test(){
//		ScrapeRedpacketDetailDto dto = new ScrapeRedpacketDetailDto();
//		dto.setFromType(1);
//		dto.setRedPacketCode("123");
//		dto.setAmountReceived(21);
//		dto.setInviteFriends(2);
//		dto.setDetailStatus(1);
//		dto.setRedPacketLoc(1);
//		dto.setConsumerId(123L);
//		dto.setAppId(1L);
//		dto.setAllFriends(10);
//		dto.setActivityId(1l);
//		remoteScrapeRedPacketDetailService.insert(dto);
//
//		remoteScrapeRedPacketDetailService.findById(1L);

		boolean bo = remoteScrapeRedPacketDetailService.updateMultipleCardById(1l, "weqeqe");
		System.out.println(bo);
	}

	@Test
	public void testFindConsumerJoinNumToday() {
		Long consumerId = 0L;
		Long operatingActivityId = 0L;
		redisClient2.del(RedisKeyFactory.getConsumerJoinNumTodayKeyNew(operatingActivityId));
		redisClient2.hset(RedisKeyFactory.getConsumerJoinNumTodayKeyNew(operatingActivityId), consumerId.toString(), "1");
		DubboResult<Integer> ret = remoteActivityConsumerLimitService.findConsumerJoinNumToday(consumerId, operatingActivityId);
		assertTrue(ret.isSuccess());
		assertTrue(ret.getResult() == null || ret.getResult() != null);
		assertTrue(ret.getResult() == 1);
	}

	@Test
	public void testIncrConsumerJoinNumToday() {
		Long consumerId = 0L;
		Long operatingActivityId = 0L;
		redisClient2.del(RedisKeyFactory.getConsumerJoinNumTodayKeyNew(operatingActivityId));
		DubboResult<Boolean> ret = remoteActivityConsumerLimitService.incrConsumerJoinNumToday(consumerId, operatingActivityId);
		assertTrue(ret.isSuccess());
		assertTrue(ret.getResult());
		String b = redisClient2.hget(RedisKeyFactory.getConsumerJoinNumTodayKeyNew(operatingActivityId), consumerId.toString());
		assertTrue(b.equals("1"));
	}

	@Test
	public void testDecrConsumerJoinNumToday() {
		Long consumerId = 0L;
		Long operatingActivityId = 0L;
		redisClient2.del(RedisKeyFactory.getConsumerJoinNumTodayKeyNew(operatingActivityId));
		remoteActivityConsumerLimitService.incrConsumerJoinNumToday(consumerId, operatingActivityId);
		DubboResult<Boolean> ret = remoteActivityConsumerLimitService.decrConsumerJoinNumToday(consumerId, operatingActivityId);
		assertTrue(ret.isSuccess());
		assertTrue(ret.getResult());
		String b = redisClient2.hget(RedisKeyFactory.getConsumerJoinNumTodayKeyNew(operatingActivityId), consumerId.toString());
		assertTrue(b.equals("0"));
	}

	@Test
	public void testFindConsumerJoinNumForever() {
		Long consumerId = 0L;
		Long operatingActivityId = 0L;
		DubboResult<Integer> ret = remoteActivityConsumerLimitService.findConsumerJoinNumForever(consumerId, operatingActivityId);
		assertTrue(ret.isSuccess());
		assertTrue(ret.getResult() == null || ret.getResult() != null);
	}

	@Test
	public void testIncrConsumerJoinNumForever() {
		Long consumerId = 0L;
		Long operatingActivityId = 0L;
		DubboResult<Boolean> ret = remoteActivityConsumerLimitService.incrConsumerJoinNumForever(consumerId, operatingActivityId);
		assertTrue(ret.isSuccess());
		assertTrue(ret.getResult());
	}

	@Test
	public void testDecrConsumerJoinNumForever() {
		Long consumerId = 0L;
		Long operatingActivityId = 0L;
		DubboResult<Boolean> ret = remoteActivityConsumerLimitService.decrConsumerJoinNumForever(consumerId, operatingActivityId);
		assertTrue(ret.isSuccess());
		assertTrue(ret.getResult());
	}

	@Test
	public void testFindConsumerFreeNumToday() {
		Long consumerId = 0L;
		Long operatingActivityId = 0L;
		redisClient2.del(RedisKeyFactory.getConsumerFreeNumTodayKeyNew(operatingActivityId));
		redisClient2.hset(RedisKeyFactory.getConsumerFreeNumTodayKeyNew(operatingActivityId), consumerId.toString(), "1");
		DubboResult<Integer> ret = remoteActivityConsumerLimitService.findConsumerFreeNumToday(consumerId, operatingActivityId);
		assertTrue(ret.isSuccess());
		assertTrue(ret.getResult() == null || ret.getResult() != null);
		assertTrue(ret.getResult() == 1);
	}

	@Test
	public void testIncrConsumerFreeNumToday() {
		Long consumerId = 0L;
		Long operatingActivityId = 0L;
		redisClient2.del(RedisKeyFactory.getConsumerFreeNumTodayKeyNew(operatingActivityId));
		DubboResult<Boolean> ret = remoteActivityConsumerLimitService.incrConsumerFreeNumToday(consumerId, operatingActivityId);
		assertTrue(ret.isSuccess());
		assertTrue(ret.getResult());
		String b = redisClient2.hget(RedisKeyFactory.getConsumerFreeNumTodayKeyNew(operatingActivityId), consumerId.toString());
		assertTrue(b.equals("1"));
	}

	@Test
	public void testDecrConsumerFreeNumToday() {
		Long consumerId = 0L;
		Long operatingActivityId = 0L;
		redisClient2.del(RedisKeyFactory.getConsumerFreeNumTodayKeyNew(operatingActivityId));
		remoteActivityConsumerLimitService.incrConsumerFreeNumToday(consumerId, operatingActivityId);
		DubboResult<Boolean> ret = remoteActivityConsumerLimitService.decrConsumerFreeNumToday(consumerId, operatingActivityId);
		assertTrue(ret.isSuccess());
		assertTrue(ret.getResult());
		String b = redisClient2.hget(RedisKeyFactory.getConsumerFreeNumTodayKeyNew(operatingActivityId), consumerId.toString());
		assertTrue(b.equals("0"));
	}

	@Test
	public void testFindConsumerFreeNumForever() {
		Long consumerId = 0L;
		Long operatingActivityId = 0L;
		DubboResult<Integer> ret = remoteActivityConsumerLimitService.findConsumerFreeNumForever(consumerId, operatingActivityId);
		assertTrue(ret.isSuccess());
		assertTrue(ret.getResult() == null || ret.getResult() != null);
	}

	@Test
	public void testIncrConsumerFreeNumForever() {
		Long consumerId = 0L;
		Long operatingActivityId = 0L;
		DubboResult<Boolean> ret = remoteActivityConsumerLimitService.incrConsumerFreeNumForever(consumerId, operatingActivityId);
		assertTrue(ret.isSuccess());
		assertTrue(ret.getResult());
	}

	@Test
	public void testDecrConsumerFreeNumForever() {
		Long consumerId = 0L;
		Long operatingActivityId = 0L;
		DubboResult<Boolean> ret = remoteActivityConsumerLimitService.decrConsumerFreeNumForever(consumerId, operatingActivityId);
		assertTrue(ret.isSuccess());
		assertTrue(ret.getResult());
	}

	@Test
	public void testFindConsumerWinOptionNum() {
		Long consumerId = 0L;
		Long operatingActivityId = 0L;
		Long optionId = 0L;
		DubboResult<Integer> ret = remoteActivityConsumerLimitService.findConsumerWinOptionNum(consumerId, operatingActivityId, optionId.toString());
		assertTrue(ret.isSuccess());
		assertTrue(ret.getResult() == null || ret.getResult() != null);
	}

	@Test
	public void testIncrConsumerWinOptionNum() {
		Long consumerId = 0L;
		Long operatingActivityId = 0L;
		Long optionId = 0L;
		DubboResult<Boolean> ret = remoteActivityConsumerLimitService.incrConsumerWinOptionNum(consumerId, operatingActivityId, optionId.toString());
		assertTrue(ret.isSuccess());
		assertTrue(ret.getResult());
	}

}
