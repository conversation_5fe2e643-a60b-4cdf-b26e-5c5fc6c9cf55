package cn.com.duiba.activity.center.biz.dao.bet;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.enums.BetStatusEnum;
import cn.com.duiba.activity.center.api.enums.ConfigStatusEnum;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.bet.BetGroupRelationEntity;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 * @date 2018/05/03
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class BetGroupRelationDaoTest extends TransactionalTestCaseBase {
    @Autowired
    private BetGroupRelationDao betGroupRelationDao;
    private BetGroupRelationEntity betGroupRelationEntity;

    @Before
    public void setup() {
        betGroupRelationEntity = new BetGroupRelationEntity();
        betGroupRelationEntity.setBetId(1L);
        betGroupRelationEntity.setGroupId(2L);

        betGroupRelationEntity.setId(betGroupRelationDao.insert(betGroupRelationEntity));
    }

    @Test
    public void shouldInsertCorrectly() {
        assertThat(betGroupRelationEntity.getId()).isNotNull().isGreaterThan(0);
    }

    @Test
    public void shouldDeleteCorrectly() {
        assertThat(betGroupRelationDao.delete(betGroupRelationEntity.getId())).isEqualTo(1);
    }

    @Test
    public void shouldDeleteByGroupCorrectly() {
        assertThat(betGroupRelationDao.deleteByGroupId(betGroupRelationEntity.getGroupId())).isEqualTo(1);
    }

    @Test
    public void shouldListByBetIdCorrectly() {
        List<BetGroupRelationEntity> betGroupRelationEntityList = betGroupRelationDao.listByBetId(
                betGroupRelationEntity.getBetId(), 1, 1);

        assertThat(betGroupRelationEntityList).isNotNull();
        assertThat(betGroupRelationEntityList.size()).isEqualTo(1);
    }

    @Test
    public void shouldCountByBetId() {
        assertThat(betGroupRelationDao.countByBetId(betGroupRelationEntity.getBetId())).isGreaterThan(0);
    }

    @Test
    public void shouldCountByGroupId() {
        assertThat(betGroupRelationDao.countByGroupId(betGroupRelationEntity.getGroupId())).isGreaterThan(0);
    }

    @Test
    public void shouldBatchInsertCorrectly() {
        BetGroupRelationEntity entity = new BetGroupRelationEntity();
        entity.setGroupId(2L);
        entity.setBetId(2L);

        BetGroupRelationEntity entity1 = new BetGroupRelationEntity();
        entity1.setBetId(2L);
        entity1.setGroupId(3L);
        List<BetGroupRelationEntity> betGroupRelationEntityList = betGroupRelationDao
                .batchInsert(Arrays.asList(entity, entity1));

        assertThat(betGroupRelationEntityList).isNotNull();
        assertThat(betGroupRelationEntityList.size()).isEqualTo(2);
        assertThat(betGroupRelationEntityList.get(0).getId()).isGreaterThan(0);
        assertThat(betGroupRelationEntityList.get(1).getId()).isGreaterThan(0);
    }

    @Test
    public void shouldDeleteByBetIdCorrectly() {
        assertThat(betGroupRelationDao.deleteByBetId(betGroupRelationEntity.getBetId())).isEqualTo(1);
    }

    @Test
    public void shouldListByBetIdAndGroupIdsCorrectly() {
        List<BetGroupRelationEntity> betGroupRelationEntityList = betGroupRelationDao.listByBetIdAndGroupIds(
                betGroupRelationEntity.getBetId(), Arrays.asList(betGroupRelationEntity.getGroupId()));

        assertThat(betGroupRelationEntityList).isNotNull();
        assertThat(betGroupRelationEntityList.size()).isGreaterThan(0);
    }

    @Test
    public void shouldUpdateStatusByBetId() {
        assertThat(
                betGroupRelationDao.updateStatusByBetId(
                        betGroupRelationEntity.getBetId(), ConfigStatusEnum.OPEN.getCode())).isGreaterThan(0);
    }
}
