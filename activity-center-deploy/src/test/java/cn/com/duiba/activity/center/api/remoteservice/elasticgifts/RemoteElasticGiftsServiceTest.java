package cn.com.duiba.activity.center.api.remoteservice.elasticgifts;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.elasticgifts.ElasticGiftsDto;
import cn.com.duiba.activity.center.api.dto.elasticgifts.ElasticGiftsTermDto;
import cn.com.duiba.activity.center.api.enums.ElasticGiftsBizCodeEnum;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.dcommons.domain.Tuple;
import cn.com.duiba.service.exception.BusinessException;
import cn.com.duiba.wolf.dubbo.DubboResult;
import com.alibaba.fastjson.JSON;
import org.javatuples.Pair;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.test.annotation.Rollback;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/2/21 09:58
 * @description:
 */
@Rollback(value = false)
public class RemoteElasticGiftsServiceTest extends TransactionalTestCaseBase {

	@Resource
	private RemoteElasticGiftsService remoteElasticGiftsService;

	@Test
	public void testGetOperatingActivityDtos() throws BusinessException{
		DubboResult<Tuple.Tuple2<ElasticGiftsDto, List<ElasticGiftsTermDto>>> result = remoteElasticGiftsService.getElasticGiftsForMobile(
				ElasticGiftsBizCodeEnum.ROB, 1L);
		System.out.println(JSON.toJSON(result.getResult()));
		System.out.println(JSON.toJSON(result.getResult()._1()));
		System.out.println(JSON.toJSON(result.getResult()._2()));
		Assert.assertTrue(result.isSuccess());
		Assert.assertNotNull(result.getResult());
	}

	@Test
	public void testGetOperatingActivityDtosApi() throws BizException{
		DubboResult<Pair<ElasticGiftsDto, List<ElasticGiftsTermDto>>> result = remoteElasticGiftsService.getElasticGiftsForMobileApi(
				ElasticGiftsBizCodeEnum.ROB, 1L);
		System.out.println(JSON.toJSON(result.getResult()));
		System.out.println(JSON.toJSON(result.getResult().getValue0()));
		System.out.println(JSON.toJSON(result.getResult().getValue1()));
		Assert.assertTrue(result.isSuccess());
		Assert.assertNotNull(result.getResult());
	}
}
