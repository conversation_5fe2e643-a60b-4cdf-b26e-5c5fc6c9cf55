package cn.com.duiba.activity.center.biz.remoteservice.activity;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.equity.EquityWhiteListDto;
import cn.com.duiba.activity.center.api.enums.equity.EquityBatchTypeEnum;
import cn.com.duiba.activity.center.api.remoteservice.equity.RemoteEquityWhiteListService;
import com.google.common.collect.Lists;
import org.apache.commons.lang.ObjectUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.List;

/**
 * Created by zzy on 2017/2/16.
 */
//@Transactional(DsConstants.DATABASE_CKVTABLE)
public class RemoteEquiltWhiltListServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteEquityWhiteListService remoteEquityWhiteListService;

    @Test
    @Rollback(false)
    public void batchInsert() {
        EquityWhiteListDto equityWhiteListDto = new EquityWhiteListDto();
        equityWhiteListDto.setAppId(1L);
        equityWhiteListDto.setBatchId(1L);
        equityWhiteListDto.setRelType(1);
        equityWhiteListDto.setRelValue("dasd");
        equityWhiteListDto.setEquityId(13L);
        Integer size  = remoteEquityWhiteListService.batchInsert(Lists.newArrayList(equityWhiteListDto));
        Assert.assertTrue(ObjectUtils.equals(size,1));
    }

    @Test
    @Rollback(false)
    public void batchInsert2() {
        EquityWhiteListDto equityWhiteListDto = new EquityWhiteListDto();
        equityWhiteListDto.setAppId(1L);
        equityWhiteListDto.setBatchId(1L);
        equityWhiteListDto.setRelType(EquityBatchTypeEnum.REPEAT_WHITE_LIST_EXCHANGE.getCode());
        equityWhiteListDto.setRelValue("dasd");
        equityWhiteListDto.setEquityId(13L);
        equityWhiteListDto.setRelStatus(1);
        Integer size  = remoteEquityWhiteListService.batchInsert(Lists.newArrayList(equityWhiteListDto));
        Assert.assertTrue(ObjectUtils.equals(size,1));
    }


    @Test
    @Rollback(false)
    public void updateStatusById1() {
        Integer size  = remoteEquityWhiteListService.updateStatusById(0,64183L);
        Assert.assertTrue(ObjectUtils.equals(size,1));
    }



    @Test
    @Rollback(false)
    public void getByAidEidRtypeRval() {
        EquityWhiteListDto equityWhiteListDto = new EquityWhiteListDto();
        equityWhiteListDto.setAppId(1L);
        equityWhiteListDto.setBatchId(1L);
        equityWhiteListDto.setRelType(1);
        equityWhiteListDto.setRelValue("dasd");
        equityWhiteListDto.setEquityId(13L);
        EquityWhiteListDto size  = remoteEquityWhiteListService.getByAidEidRtypeRval(1L,13L,1,"dasd");
        Assert.assertNotNull(size);
    }

    @Test
    @Rollback(false)
    public void getByAidEidRtypeRvals() {
        EquityWhiteListDto equityWhiteListDto = new EquityWhiteListDto();
        equityWhiteListDto.setAppId(1L);
        equityWhiteListDto.setBatchId(1L);
        equityWhiteListDto.setRelType(1);
        equityWhiteListDto.setRelValue("dasd");
        equityWhiteListDto.setEquityId(13L);
        List<EquityWhiteListDto> size  = remoteEquityWhiteListService.getByAidRelValueAndRelStatus(1L,13L,1,Lists.newArrayList("dasd"),1);
        Assert.assertEquals(1,size.size());
    }

    @Test
    @Rollback(false)
    public void getByAidRtypeRval() {
        EquityWhiteListDto equityWhiteListDto = new EquityWhiteListDto();
        equityWhiteListDto.setAppId(1L);
        equityWhiteListDto.setBatchId(1L);
        equityWhiteListDto.setRelType(1);
        equityWhiteListDto.setRelValue("dasd");
        equityWhiteListDto.setEquityId(13L);
        List<EquityWhiteListDto> size  = remoteEquityWhiteListService.getByAidRtypeRval(1L,1,"dasd");
        Assert.assertEquals(1,size.size());
    }

    @Test
    @Rollback(false)
    public void getByAidRelValueAndRelStatus() {
        List<EquityWhiteListDto> size  = remoteEquityWhiteListService.getByAidRelValueAndRelStatus(1L,1L,3,Lists.newArrayList("dasd"),1);
        Assert.assertEquals(1,size.size());
    }

    @Test
    @Rollback(false)
    public void updateStatusById() {
       Integer size  = remoteEquityWhiteListService.updateStatusById(0,1024L);
        Assert.assertEquals(String.valueOf("1"),size.toString());
    }

    @Test
    @Rollback(false)
    public void listByEquityIdAndRelType() {
        EquityWhiteListDto equityWhiteListDto = new EquityWhiteListDto();
        equityWhiteListDto.setAppId(1L);
        equityWhiteListDto.setBatchId(1L);
        equityWhiteListDto.setRelType(1);
        equityWhiteListDto.setRelValue("dasd");
        equityWhiteListDto.setEquityId(13L);
        List<EquityWhiteListDto> size  = remoteEquityWhiteListService.listByEquityIdAndRelType(13L,1L,1);
        Assert.assertEquals(1,size.size());
    }

    @Test
    @Rollback(false)
    public void deleteByBatchIds() {
        EquityWhiteListDto equityWhiteListDto = new EquityWhiteListDto();
        equityWhiteListDto.setAppId(1L);
        equityWhiteListDto.setBatchId(1L);
        equityWhiteListDto.setRelType(1);
        equityWhiteListDto.setRelValue("dasd");
        equityWhiteListDto.setEquityId(13L);
        Integer size =  remoteEquityWhiteListService.deleteByBatchIds(Lists.newArrayList(1L));
        Assert.assertEquals("1",size.toString());
    }
}
