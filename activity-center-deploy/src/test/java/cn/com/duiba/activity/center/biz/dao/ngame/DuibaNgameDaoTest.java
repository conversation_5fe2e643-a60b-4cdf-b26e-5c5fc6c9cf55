package cn.com.duiba.activity.center.biz.dao.ngame;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.ngame.DuibaNgameEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/8.
 */
@Transactional(DsConstants.DATABASE_NGAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaNgameDaoTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaNgameDao duibaNgameDao;

    private ThreadLocal<DuibaNgameEntity> duibaNgame=new ThreadLocal<>();

    @Before
    public void testAdd() {
        DuibaNgameEntity e=new DuibaNgameEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        duibaNgameDao.add(e);
        duibaNgame.set(e);
    }

    @Test
    public void testFind() {
        DuibaNgameEntity e=duibaNgame.get();
        DuibaNgameEntity e1=duibaNgameDao.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindByPage() {
        DuibaNgameEntity e=duibaNgame.get();
        Assert.assertTrue(duibaNgameDao.findByPage(0,10,e.getTitle(),null).size()>0);
    }

    @Test
    public void testFindByPageCount() {
        DuibaNgameEntity e=duibaNgame.get();
        Assert.assertTrue(duibaNgameDao.findByPageCount(e.getTitle(),null)>0);
    }

    @Test
    public void testDelete() {
        DuibaNgameEntity e=duibaNgame.get();
        duibaNgameDao.delete(e.getId());
        Assert.assertTrue(duibaNgameDao.find(e.getId()).getDeleted());
    }

    @Test
    public void testUpdate() {
        DuibaNgameEntity e=duibaNgame.get();
        e.setBanner("test");
        duibaNgameDao.update(e);
        Assert.assertEquals(e.getBanner(),duibaNgameDao.find(e.getId()).getBanner());
    }

    @Test
    public void testCount() {
        DuibaNgameEntity e=duibaNgame.get();
        Assert.assertTrue(duibaNgameDao.count()>0);
    }

    @Test
    public void testFindOpenPrizeForUpdate() {
        DuibaNgameEntity e=new DuibaNgameEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setIsOpenPrize(false);
        duibaNgameDao.add(e);
    }

    @Test
    public void testUpdateSwitch() {
        DuibaNgameEntity e=duibaNgame.get();
        e.setSwitches(42);
        duibaNgameDao.updateSwitch(e);
        Assert.assertTrue(duibaNgameDao.find(e.getId()).getSwitches().equals(42));
    }

    @Test
    public void testFindAllByIds() {
        DuibaNgameEntity e=duibaNgame.get();
        Assert.assertTrue(duibaNgameDao.findAllByIds(Arrays.asList(e.getId())).size()>0);
    }

    @Test
    public void testFindAllNgame() {
        DuibaNgameEntity e=new DuibaNgameEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setGameStatus(1);
        duibaNgameDao.add(e);
        Assert.assertTrue(duibaNgameDao.findAllNgame(0l).size()>0);
    }

    private void assertDO(DuibaNgameEntity e, DuibaNgameEntity e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaNgameEntity e, DuibaNgameEntity e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","deleted","optionsJson","openSpecify","openBlack","openFreeRule","recommendImage","dCreditsPrice","unopen","anticheatLimit"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

//    @Override
//    public DatabaseSchema chooseSchema() {
//        return DatabaseSchema.nameOf(THIS_DATABASE_SCHEMA);
//    }
}
