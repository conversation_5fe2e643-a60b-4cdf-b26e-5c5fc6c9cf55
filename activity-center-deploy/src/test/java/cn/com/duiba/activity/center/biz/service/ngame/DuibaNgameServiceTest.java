package cn.com.duiba.activity.center.biz.service.ngame;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.ngame.DuibaNgameDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/8.
 */
@Transactional(DsConstants.DATABASE_NGAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaNgameServiceTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaNgameService duibaNgameService;

    private ThreadLocal<DuibaNgameDto> duibaNgame=new ThreadLocal<>();

    @Before
    public void testAdd() {
        DuibaNgameDto e=new DuibaNgameDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        duibaNgameService.add(e);
        duibaNgame.set(e);
    }

    @Test
    public void testFind() {
        DuibaNgameDto e=duibaNgame.get();
        DuibaNgameDto e1=duibaNgameService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindByPage() {
        DuibaNgameDto e=duibaNgame.get();
        Assert.assertTrue(duibaNgameService.findByPage(0,10,e.getTitle(),null).size()>0);
    }

    @Test
    public void testFindByPageCount() {
        DuibaNgameDto e=duibaNgame.get();
        Assert.assertTrue(duibaNgameService.findByPageCount(e.getTitle(),null)>0);
    }

    @Test
    public void testDelete() {
        DuibaNgameDto e=duibaNgame.get();
        duibaNgameService.delete(e.getId());
        Assert.assertTrue(duibaNgameService.find(e.getId()).getDeleted());
    }

    @Test
    public void testUpdate() {
        DuibaNgameDto e=duibaNgame.get();
        e.setBanner("test");
        duibaNgameService.update(e);
        Assert.assertEquals(e.getBanner(),duibaNgameService.find(e.getId()).getBanner());
    }

    @Test
    public void testCount() {
        DuibaNgameDto e=duibaNgame.get();
        Assert.assertTrue(duibaNgameService.count()>0);
    }

    @Test
    public void testFindOpenPrizeForUpdate() {
        DuibaNgameDto e=new DuibaNgameDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setIsOpenPrize(false);
        duibaNgameService.add(e);
    }

    @Test
    public void testUpdateSwitch() {
        DuibaNgameDto e=duibaNgame.get();
        e.setSwitches(42);
        duibaNgameService.updateSwitch(e);
        Assert.assertTrue(duibaNgameService.find(e.getId()).getSwitches().equals(42));
    }

    @Test
    public void testFindAllByIds() {
        DuibaNgameDto e=duibaNgame.get();
        Assert.assertTrue(duibaNgameService.findAllByIds(Arrays.asList(e.getId())).size()>0);
    }

    @Test
    public void testFindAllNgame() {
        DuibaNgameDto e=new DuibaNgameDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setGameStatus(1);
        duibaNgameService.add(e);
        Assert.assertTrue(duibaNgameService.findAllNgame(0l).size()>0);
    }

    private void assertDO(DuibaNgameDto e, DuibaNgameDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaNgameDto e, DuibaNgameDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","deleted","optionsJson","openSpecify","openBlack","openFreeRule","recommendImage","dCreditsPrice","unopen","anticheatLimit"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

//    @Override
//    public DatabaseSchema chooseSchema() {
//        return DatabaseSchema.nameOf(THIS_DATABASE_SCHEMA);
//    }
}
