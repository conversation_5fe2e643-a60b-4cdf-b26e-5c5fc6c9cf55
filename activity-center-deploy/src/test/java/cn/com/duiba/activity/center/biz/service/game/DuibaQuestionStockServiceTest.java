package cn.com.duiba.activity.center.biz.service.game;

import java.util.Arrays;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionStockDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/2.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaQuestionStockServiceTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaQuestionStockService duibaQuestionStockService;


    @Test
    public void testAdd() {
        DuibaQuestionStockDto e=new DuibaQuestionStockDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionStockService.add(e);
    }

    @Test
    public void testAddBatch() {
        DuibaQuestionStockDto e=new DuibaQuestionStockDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionStockService.addBatch(Arrays.asList(e));
    }

    @Test
    public void testSubStock() {
        DuibaQuestionStockDto e=new DuibaQuestionStockDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStock(20);
        e.setQuestionOptionId(-212L);
        duibaQuestionStockService.add(e);
        duibaQuestionStockService.subStock(e.getId(),1);
        Assert.assertEquals(duibaQuestionStockService.findByQuestionOptionId(e.getQuestionOptionId()).getStock(), Integer.valueOf(19));
    }

    @Test
    public void testAddStock() {
        DuibaQuestionStockDto e=new DuibaQuestionStockDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStock(20);
        duibaQuestionStockService.add(e);
        duibaQuestionStockService.addStock(e.getId(),1);
        Assert.assertTrue(duibaQuestionStockService.findByQuestionOptionId(e.getQuestionOptionId()).getStock().equals(21));
    }

    @Test
    public void testFindRemaining() {
        DuibaQuestionStockDto e=new DuibaQuestionStockDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStock(20);
        duibaQuestionStockService.add(e);
        Assert.assertNotNull(duibaQuestionStockService.findRemaining(e.getQuestionOptionId(),e.getRelationType()));
    }

    @Test
    public void testFindByQuestionOptionId() {
        DuibaQuestionStockDto e=new DuibaQuestionStockDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStock(20);
        duibaQuestionStockService.add(e);
        Assert.assertNotNull(duibaQuestionStockService.findByQuestionOptionId(e.getQuestionOptionId()));
    }

    @Test
    public void testFindByQuestionOptionIds() {
        DuibaQuestionStockDto e=new DuibaQuestionStockDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStock(20);
        duibaQuestionStockService.add(e);
        Assert.assertTrue(duibaQuestionStockService.findByQuestionOptionIds(Arrays.asList(e.getQuestionOptionId())).size()>0);
    }

    @Test
    public void testUpdateStockAdd() {
        DuibaQuestionStockDto e=new DuibaQuestionStockDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStock(20);
        duibaQuestionStockService.add(e);
        duibaQuestionStockService.updateStockAdd(e.getId(),1,e.getRelationType());
        duibaQuestionStockService.findByQuestionOptionId(e.getQuestionOptionId()).getStock().equals(21);
    }

    @Test
    public void testUpdateStockSub() {
        DuibaQuestionStockDto e=new DuibaQuestionStockDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStock(20);
        duibaQuestionStockService.add(e);
        duibaQuestionStockService.updateStockSub(e.getId(),1,e.getRelationType());
        duibaQuestionStockService.findByQuestionOptionId(e.getQuestionOptionId()).getStock().equals(19);
    }

}
