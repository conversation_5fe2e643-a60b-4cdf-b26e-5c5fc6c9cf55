package cn.com.duiba.activity.center.biz.service.game;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.DeveloperActivityStatisticsDto;
import cn.com.duiba.activity.center.api.dto.game.GameOrdersDto;
import cn.com.duiba.activity.center.api.dto.game.GameOrdersSimpleDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by ZQian on 2016/8/3.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class GameOrdersSimpleServiceTest extends TransactionalTestCaseBase {

    @Resource
    private GameOrdersSimpleService gameOrdersSimpleService;

    private ThreadLocal<GameOrdersDto> gameOrdersDto=new ThreadLocal<>();

    @Before
    public void testInsert(){
        GameOrdersDto dto=new GameOrdersDto();
        TestUtils.setRandomAttributesForBean(dto,true);
        dto.setCredits(0L);
        dto.setStatus(2);
        gameOrdersSimpleService.insert(dto);
        gameOrdersDto.set(dto);
    }

    @Test
    public void testFind(){
        GameOrdersDto dto1 = gameOrdersDto.get();
        GameOrdersDto dto2 = gameOrdersSimpleService.find(dto1.getId());
        assertDO(dto1, dto2,new String[]{"prizeType","prizeFacePrice"});
    }



    @Test
    public void testFindByIds(){
        GameOrdersDto dto = gameOrdersDto.get();
        Assert.assertTrue(gameOrdersSimpleService.findByIds(Arrays.asList(dto.getId())).size()>0);
    }


    private void assertDO(GameOrdersDto e, GameOrdersDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","questionType","prizeOverdueDate"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }
}
