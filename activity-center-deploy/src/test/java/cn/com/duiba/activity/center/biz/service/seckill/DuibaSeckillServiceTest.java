package cn.com.duiba.activity.center.biz.service.seckill;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.seckill.DuibaSeckillDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created by yansen on 16/5/24.
 */
@Transactional(DsConstants.DATABASE_SECKILL)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaSeckillServiceTest extends TransactionalTestCaseBase{

    @Resource
    private DuibaSeckillService duibaSeckillService;

    private DuibaSeckillDto duibaSeckillDO;

    @Before
    public void testInsert() {
        DuibaSeckillDto e=new DuibaSeckillDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaSeckillService.insert(e);
        duibaSeckillDO=e;
    }

    @Test
    public void testFind() {
        DuibaSeckillDto e=duibaSeckillDO;
        DuibaSeckillDto e1=duibaSeckillService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindByPage() {
        Map<String,Object> params=new HashMap<>();
        params.put("title",duibaSeckillDO.getTitle());
        params.put("max",10);
        params.put("offset",0);
        Assert.assertTrue(duibaSeckillService.findByPage(params).size()>0);
    }

    @Test
    public void testFindPageCount() {
        Map<String,Object> params=new HashMap<>();
        params.put("title",duibaSeckillDO.getTitle());
        params.put("max",10);
        params.put("offset",0);
        Assert.assertTrue(duibaSeckillService.findPageCount()>0);
        Assert.assertTrue(duibaSeckillService.findPageCount(params)>0);
    }

    @Test
    public void testUpdateStatus() {
        duibaSeckillService.updateStatus(duibaSeckillDO.getId(),42);
        Assert.assertTrue(duibaSeckillService.find(duibaSeckillDO.getId()).getStatus().equals(42));
    }

    @Test
    public void testDelete() {
        duibaSeckillService.delete(duibaSeckillDO.getId());
        Assert.assertTrue(duibaSeckillService.find(duibaSeckillDO.getId()).getDeleted());
    }

    @Test
    public void testUpdateAutoOffDateNull() {
        duibaSeckillService.updateAutoOffDateNull(duibaSeckillDO.getId());
        Assert.assertNull(duibaSeckillService.find(duibaSeckillDO.getId()).getAutoOffDate());
    }

    @Test
    public void testUpdateInfoForm() {
        duibaSeckillDO.setTitle("saf");
        Assert.assertTrue(duibaSeckillService.updateInfoForm(duibaSeckillDO)>0);
    }

    @Test
    public void testUpdateSwitches() {
        duibaSeckillService.updateSwitches(duibaSeckillDO.getId(),42l);
        Assert.assertTrue(duibaSeckillService.find(duibaSeckillDO.getId()).getSwitches().equals(42));
    }

    @Test
    public void testFindByMap() {
        Map<String,Object> params=new HashMap<>();
        params.put("activityName",duibaSeckillDO.getTitle());
        params.put("max",10);
        params.put("offset",0);
        Assert.assertTrue(duibaSeckillService.findByMap(params).size()>0);
    }

    @Test
    public void testFindAllByIds() {
        assertDO(duibaSeckillDO,duibaSeckillService.findAllByIds(Arrays.asList(duibaSeckillDO.getId())).get(0));
    }

    @Test
    public void testFindAllSeckill() {
        Assert.assertTrue(duibaSeckillService.findAllSeckill(1l).size()>0);
    }

    @Test
    public void testFindEffective() {
        DuibaSeckillDto e=new DuibaSeckillDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStatus(1);
        e.setDeleted(false);
        duibaSeckillService.insert(e);
        Assert.assertTrue(duibaSeckillService.findEffective().size()>0);
    }


    private void assertDO(DuibaSeckillDto e, DuibaSeckillDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaSeckillDto e, DuibaSeckillDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","dateStart","dateEnd","timeStart","timeEnd","deleted"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }
    
}
