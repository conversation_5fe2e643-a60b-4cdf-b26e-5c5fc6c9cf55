package cn.com.duiba.activity.center.biz.dao.creditgame;

import java.util.Date;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.creditgame.CreditGameSkinEntity;

/**
 * 积分游戏DAO单元测试
 * <AUTHOR>
 * @since 2016-09-05
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)
public class CreditGameSkinDaoTest extends CreditGameDaoTestCaseBase<CreditGameSkinEntity> {


    @Autowired
    private CreditGameSkinDao creditGameSkinDao;

    @Override
    protected CreditGameSkinEntity genEntity(){
        CreditGameSkinEntity entity=new CreditGameSkinEntity();
        entity.setGmtCreate(new Date());
        entity.setGmtModified(new Date());
        entity.setCreditGameSkinName("皮肤名称");
        entity.setCreditGameType((byte)1);
        entity.setGameSkinId(123l);
        entity.setCreditGameId(123l);
        entity.setCreditGameSkinImgUri("skin uri");
        return entity;
    }
    @Test
    public void testInsert(){
        doTestInsert(creditGameSkinDao);
    }

    @Test
    public void testQueryById(){
       doTestQueryById(creditGameSkinDao);
    }

    /*
    @Test
    public void testQuery(){
       doTestQuery(creditGameSkinDao);
    }
*/
   /* @Test
    public void testUpdate(){
        doTestUpdate(creditGameSkinDao, new PreUpdateHandler<CreditGameSkinEntity>() {
            @Override
            public void preHandle(CreditGameSkinEntity creditGameSkinEntity) {
                creditGameSkinEntity.setGmtModified(new Date());
            }
        });
    }
*/

    @Test
    public void testDelete(){
        doTestDelete(creditGameSkinDao);
    }
}
