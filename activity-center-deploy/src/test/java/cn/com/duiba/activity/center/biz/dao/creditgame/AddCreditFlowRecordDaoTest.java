package cn.com.duiba.activity.center.biz.dao.creditgame;

import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.creditgame.AddCreditFlowRecordEntity;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 积分游戏DAO单元测试
 * <AUTHOR>
 * @since 2016-09-05
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)
public class AddCreditFlowRecordDaoTest extends CreditGameDaoTestCaseBase<AddCreditFlowRecordEntity> {


    @Autowired
    private AddCreditFlowRecordDao addCreditFlowRecordDao;

    @Override
    protected AddCreditFlowRecordEntity genEntity(){
        AddCreditFlowRecordEntity entity=new AddCreditFlowRecordEntity();
        entity.setGmtModified(new Date());
        entity.setAddCreditType(1l);
        entity.setAddCreditReason("add credit reason");
        entity.setOrderNum("123l");
        entity.setAddCreditNum(100l);
        entity.setAddCreditStatus((byte)0);
        entity.setAppId(123l);
        entity.setGmtCreate(new Date());
        entity.setAppId(1L);
        return entity;
    }
    @Test
    public void testInsert(){
        doTestInsert(addCreditFlowRecordDao);
    }

    @Test
    public void testQueryById(){
        doTestQueryById(addCreditFlowRecordDao);
    }

    /*
    @Test
    public void testQuery(){
        doTestQuery(addCreditFlowRecordDao);
    }
*/
    @Test
    public void testUpdate(){
        doTestUpdate(addCreditFlowRecordDao, new PreUpdateHandler<AddCreditFlowRecordEntity>() {
            @Override
            public void preHandle(AddCreditFlowRecordEntity addCreditFlowRecordEntity) {
                addCreditFlowRecordEntity.setGmtModified(new Date());
            }
        });


    }


    @Test
    public void testDelete(){
        doTestDelete(addCreditFlowRecordDao);
    }
}
