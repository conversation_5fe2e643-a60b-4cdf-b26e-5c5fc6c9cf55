package cn.com.duiba.activity.center.api.remoteservice.joingroup;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.joingroup.JoinGroupRecordDto;
import cn.com.duiba.activity.center.api.enums.YesOrNoEnum;
import cn.com.duiba.activity.center.api.params.JoinGroupRecordPageParam;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 18/6/25
 * @description
 */
public class RemoteJoinGroupRecordAppServiceTest extends TransactionalTestCaseBase {

	@Autowired
	private RemoteJoinGroupRecordAppService remoteJoinGroupRecordAppService;

	@Test
	public void testAdd() throws Exception{
		JoinGroupRecordDto joinGroupRecordDto = new JoinGroupRecordDto();
		joinGroupRecordDto.setAppId(1L);
		joinGroupRecordDto.setJoinGroupConfigId(1L);
		joinGroupRecordDto.setJoinGroupItemId(1L);
		joinGroupRecordDto.setGroupInfoId(1L);
		joinGroupRecordDto.setConsumerId(123L);
		joinGroupRecordDto.setOwnerType(YesOrNoEnum.NO.getCode());
		joinGroupRecordDto.setFakeType(YesOrNoEnum.YES.getCode());
		joinGroupRecordDto.setWinStatus(YesOrNoEnum.NO.getCode());

		System.out.println(JSON.toJSON(joinGroupRecordDto));
		int count = remoteJoinGroupRecordAppService.add(joinGroupRecordDto);
		Assert.assertTrue(count > 0);
	}

	@Test
	public void testModifyStatusForWin(){
		int a = remoteJoinGroupRecordAppService.modifyStatusForWin(2L, "24242");
		System.out.println(a);
		Assert.assertTrue(a > 0);
	}

	@Test
	public void testGetOwnersByGroupIds(){
		List<JoinGroupRecordDto> joinGroupRecordDtoList = remoteJoinGroupRecordAppService.getOwnersByGroupIds(Arrays.asList(1L,2L));
		System.out.println(JSON.toJSON(joinGroupRecordDtoList));
		Assert.assertNotNull(joinGroupRecordDtoList);
	}

	@Test
	public void testGetByGroupId(){
		List<JoinGroupRecordDto> joinGroupRecordDtoList = remoteJoinGroupRecordAppService.getByGroupId(1L);
		System.out.println(JSON.toJSON(joinGroupRecordDtoList));
		Assert.assertNotNull(joinGroupRecordDtoList);
	}

	@Test
	public void testGetByAppConfigIdAndUserId(){
		JoinGroupRecordPageParam joinGroupRecordPageParam = new JoinGroupRecordPageParam();
		joinGroupRecordPageParam.setAppId(1L);
		joinGroupRecordPageParam.setJoinGroupConfigId(1L);
		joinGroupRecordPageParam.setConsumerId(111L);
		joinGroupRecordPageParam.setPageNum(1);
		joinGroupRecordPageParam.setPageSize(2);
		List<JoinGroupRecordDto> joinGroupRecordDtoList = remoteJoinGroupRecordAppService.getPageByAppConfigIdAndUserId(joinGroupRecordPageParam);
		System.out.println(JSON.toJSON(joinGroupRecordDtoList));
		Assert.assertNotNull(joinGroupRecordDtoList);
	}

	@Test
	public void testGetByGroupIdAndConsumerId(){
		List<JoinGroupRecordDto> joinGroupRecordDtoList = remoteJoinGroupRecordAppService.getByGroupIdAndConsumerId(3L, 1539361L);
		System.out.println(JSON.toJSON(joinGroupRecordDtoList));
	}
}

