package cn.com.duiba.activity.center.biz.dao.scraperedpacket;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.entity.scraperedpacket.ScrapeRedPacketEntity;
import com.alibaba.fastjson.JSONObject;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

public class ScrapeRedPacketDaoTest extends TransactionalTestCaseBase {

    @Autowired
    private ScrapeRedPacketDao scrapeRedPacketDao;

    @Test
    public void testInsertAndFind() {
        Long id = scrapeRedPacketDao.insert(newEntity());
        Assert.assertTrue(id > 0);
        ScrapeRedPacketEntity entity = scrapeRedPacketDao.findById(id);
        Assert.assertNotNull(entity);
        System.out.println(JSONObject.toJSON(entity));
    }

    private ScrapeRedPacketEntity newEntity() {
        ScrapeRedPacketEntity entity = new ScrapeRedPacketEntity();
        entity.setTitle("这是标题");
        entity.setRule("这是规则");
        entity.setOfficialAccountConfig("这是公众号配置");
        entity.setMiniProgramConfig("这是小程序配置");
        entity.setStartTime(new Date());
        entity.setEndTime(new Date());
        entity.setBannerImage("这是banner图");
        entity.setSmallImage("这是缩略图");
        entity.setOpenWheel(0);
        entity.setBonusLeft(1L);
        entity.setBonusRight(2L);
        entity.setTargetNumber(0);
        entity.setBonusNumber(0);
        entity.setHighestRate("1.5");
        entity.setBonusLimit(0L);
        entity.setTotalBudget(0L);
        entity.setWarnBudget(0L);
        entity.setPhoneNumber("110");
        entity.setOperatingActivityId(0L);
        entity.setAppId(0L);
        entity.setCustomAccountId(0L);
        entity.setDataJson("dataJson");
        return entity;
    }


}
