package cn.com.duiba.activity.center.biz.service.guess;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.guess.DuibaGuessDto;
import cn.com.duiba.activity.center.api.dto.quizz.AddActivityDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class DuibaGuessServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaGuessService duibaGuessService;
	
	private DuibaGuessDto info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaGuessDto.class);
		info.setLimitScope(10);
		info.setFreeScope(10);
		info.setIsAccurate(10);
		duibaGuessService.insert(info);
	}
	
	@Test
	public void findTest(){
		DuibaGuessDto e = duibaGuessService.find(info.getId());
		Assert.assertNotNull(e);
	}
	
	@Test
	public void findByPageTest(){
		DuibaGuessDto e = TestUtils.createRandomBean(DuibaGuessDto.class);
		e.setDeleted(false);
		e.setLimitScope(10);
		e.setFreeScope(10);
		e.setIsAccurate(10);
		duibaGuessService.insert(e);
		List<DuibaGuessDto> list = duibaGuessService.findByPage(0, 10, e.getTitle(), Integer.parseInt(e.getId() + ""));
		Assert.assertTrue(list.size() > 0);
	}
	
	@Test
	public void findPageCountTest(){
		DuibaGuessDto e = TestUtils.createRandomBean(DuibaGuessDto.class);
		e.setDeleted(false);
		e.setLimitScope(10);
		e.setFreeScope(10);
		e.setIsAccurate(10);
		duibaGuessService.insert(e);
		long num = duibaGuessService.findPageCount(e.getTitle(), Integer.parseInt(e.getId() + ""));
		Assert.assertTrue(num > 0);
	}
	
	@Test
	public void updateStatusTest(){
		DuibaGuessDto e = TestUtils.createRandomBean(DuibaGuessDto.class);
		duibaGuessService.updateStatus(info.getId(), e.getStatus());
		int status = duibaGuessService.find(info.getId()).getStatus();
		Assert.assertTrue(status == e.getStatus());
	}
	
	@Test
	public void deleteTest(){
		duibaGuessService.delete(info.getId());
		boolean deleted = duibaGuessService.find(info.getId()).getDeleted();
		Assert.assertTrue(deleted);
	}
	
	@Test
	public void updateInfoFormTest(){
		DuibaGuessDto e = TestUtils.createRandomBean(DuibaGuessDto.class);
		e.setId(info.getId());
		e.setLimitScope(10);
		e.setFreeScope(10);
		e.setIsAccurate(10);
		duibaGuessService.updateInfoForm(e);
		String logo = duibaGuessService.find(info.getId()).getLogo();
		Assert.assertTrue(logo.equals(e.getLogo()));
	}
	
	@Test
	public void updateAutoOffDateNullTest(){
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		DuibaGuessDto e = TestUtils.createRandomBean(DuibaGuessDto.class);
		duibaGuessService.updateAutoOffDateNull(e.getAutoOffDate(), info.getId());
		Date autoOffDate = duibaGuessService.find(info.getId()).getAutoOffDate();
		Assert.assertTrue(format.format(autoOffDate).equals(format.format(e.getAutoOffDate())));
	}
	
	@Test
	public void updateSwitchesTest(){
		DuibaGuessDto e = TestUtils.createRandomBean(DuibaGuessDto.class);
		duibaGuessService.updateSwitches(info.getId(), e.getSwitches());
		int switches = duibaGuessService.find(info.getId()).getSwitches();
		Assert.assertTrue(switches == e.getSwitches());
	}
	
	/**
	 * 修改开奖后的主表信息
	 * updateOpenWinning:(这里用一句话描述这个方法的作用). <br/>
	 *
	 * <AUTHOR>
	 * @param id
	 * @since JDK 1.6
	 */
	@Test
	public void updateOpenWinningTest(){
		DuibaGuessDto e = TestUtils.createRandomBean(DuibaGuessDto.class);
		e.setLimitScope(10);
		e.setFreeScope(10);
		e.setIsAccurate(10);
		duibaGuessService.updateOpenWinning(info.getId(), e.getLuckNum(), e.getRightSelectionId(), e.getIsAccurate());
		String luckNum = duibaGuessService.find(info.getId()).getLuckNum();
		Assert.assertTrue(luckNum.equals(e.getLuckNum()));
	}

	@Test
	public void findAllByIdsTest(){
		List<Long> ids = new ArrayList<Long>();
		ids.add(info.getId());
		List<DuibaGuessDto> list = duibaGuessService.findAllByIds(ids);
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void findAllGuessTest(){
		DuibaGuessDto e = TestUtils.createRandomBean(DuibaGuessDto.class);
		e.setDeleted(false);
		e.setStatus(1);
		e.setLimitScope(10);
		e.setFreeScope(10);
		e.setIsAccurate(10);
		duibaGuessService.insert(e);
		List<AddActivityDto> list = duibaGuessService.findAllGuess(e.getId());
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void findAutoOffTest(){
		DuibaGuessDto e = TestUtils.createRandomBean(DuibaGuessDto.class);
		e.setDeleted(false);
		e.setStatus(1);
		Calendar calendar = Calendar.getInstance();
    	calendar.setTime(e.getAutoOffDate());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	Date start = calendar.getTime();
    	e.setAutoOffDate(start);
    	e.setLimitScope(10);
		e.setFreeScope(10);
		e.setIsAccurate(10);
		duibaGuessService.insert(e);
		List<DuibaGuessDto> list = duibaGuessService.findAutoOff();
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void updateOpenPrizeTest(){
		DuibaGuessDto e = TestUtils.createRandomBean(DuibaGuessDto.class);
		e.setIsOpenPrize(false);
		e.setStatus(2);
		e.setLimitScope(10);
		e.setFreeScope(10);
		e.setIsAccurate(10);
		duibaGuessService.insert(e);
		duibaGuessService.updateOpenPrize(e.getId());
		boolean isOpenPrize = duibaGuessService.find(e.getId()).getIsOpenPrize();
		Assert.assertTrue(isOpenPrize);
	}
}
