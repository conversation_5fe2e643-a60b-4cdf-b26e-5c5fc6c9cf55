///**
// * Project Name:activity-center-deploy
// * File Name:LittleGameDaoTest.java
// * Package Name:cn.com.duiba.activity.center.biz.dao.littlegame
// * Date:2016年9月28日下午7:52:16
// * Copyright (c) 2016, duiba.com.cn All Rights Reserved.
// *
//*/
//
//package cn.com.duiba.activity.center.biz.dao.littlegame;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//
//import javax.annotation.Resource;
//
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.transaction.annotation.Transactional;
//
//import cn.com.duiba.activity.center.TransactionalTestCaseBase;
//import cn.com.duiba.activity.center.biz.dao.DsConstants;
//import cn.com.duiba.activity.center.biz.entity.littlegame.LittleGameEntity;
//import cn.com.duiba.wolf.utils.DateUtils;
//import cn.com.duiba.wolf.utils.TestUtils;
//
///**
// * ClassName:LittleGameDaoTest <br/>
// * Date:     2016年9月28日 下午7:52:16 <br/>
// * <AUTHOR>
// * @version
// * @since    JDK 1.6
// * @see
// */
//@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
//public class LittleGameDaoTest extends TransactionalTestCaseBase {
//    @Resource
//    private LittleGameDao littleGameDao;
//
//    private LittleGameEntity entity;
//
//   /* @Before
//    public void testInsert(){
//        LittleGameEntity e=new LittleGameEntity();
//        TestUtils.setRandomAttributesForBean(e,false);
//        e.setType((byte) 1);
//        e.setStatus((byte) 1);
//        e.setDeleted(false);
//        this.littleGameDao.insert(e);
//        entity = e;
//    }
//
//    @Test
//    public void testSelect(){
//        LittleGameEntity rs = this.littleGameDao.selectById(entity.getId());
//        assertDO(rs,entity);
//    }
//    @Test
//    public void testUpdate(){
//        LittleGameEntity e=new LittleGameEntity();
//        TestUtils.setRandomAttributesForBean(e,false);
//        e.setId(entity.getId());
//        e.setType((byte) 2);
//        e.setStatus((byte) 2);
//        e.setDeleted(false);
//        log.error("-------before update entity:"+entity.toString());
//        this.littleGameDao.update(e);
//        LittleGameEntity rs = this.littleGameDao.selectById(entity.getId());
//        log.error("---------rs:"+rs.toString());
//        assertDO(rs,e);
//    }
//    @Test
//    public void testselectCount(){
//        Long count = this.littleGameDao.selectCount(entity.getTitle());
//        Assert.assertTrue(count>0);
//    }
//    @Test
//    public void testSelectList(){
//        List<LittleGameEntity> lists = this.littleGameDao.selectList(entity.getTitle(), 0, 20);
//        for(LittleGameEntity e:lists){
//            Assert.assertEquals(e.getTitle(), entity.getTitle());
//        }
//    }
//
//    private void assertDO(LittleGameEntity e, LittleGameEntity e1){
//        assertDO(e,e1,null);
//
//    }
//    private void assertDO(LittleGameEntity e, LittleGameEntity e1, String[] exculdeFields){
//        List<String> exculdeFieldsList=new ArrayList<>();
//        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified"}));
//        if(exculdeFields!=null&&exculdeFields.length>0){
//            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
//        }
//        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
//        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
//        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
//    }*/
//}
//
