package cn.com.duiba.activity.center.api.remoteservice.activity;

import java.util.Date;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity.InsuranceResultDto;

import com.alibaba.fastjson.JSON;

public class RemoteInsuranceResultServiceTest extends TransactionalTestCaseBase {

	@Autowired
	private RemoteInsuranceResultService remoteInsuranceResultService;
	
	@Test
	public void testFindById() {
		InsuranceResultDto dto = remoteInsuranceResultService.findById(1L);
		System.out.println(JSON.toJSONString(dto));
	}
	
	@Test
	public void testInsert() {
		InsuranceResultDto dto = new InsuranceResultDto();
		Date createDate = new Date();
		dto.setConsumerId(1L);
		dto.setUserId(1L);
		dto.setStatus("success");
		dto.setMessage("投保信息");
		dto.setInsuredOrder("20170118150200001");
		dto.setInsuredUrl("http://www.google.com.hk");
		dto.setGmtCreate(createDate);
		dto.setGmtModified(createDate);
		
		Long id = remoteInsuranceResultService.insert(dto);
		System.out.println("id:" + id);
	}
}
