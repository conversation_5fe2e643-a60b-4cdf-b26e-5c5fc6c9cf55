package cn.com.duiba.activity.center.biz.dao.guess;

import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.guess.DuibaGuessBrickEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class DuibaGuessBrickDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaGuessBrickDao duibaGuessBrickDao;
	
	private DuibaGuessBrickEntity info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaGuessBrickEntity.class);
		duibaGuessBrickDao.insert(info);
	}
	
	@Test
	public void findTest(){
		DuibaGuessBrickEntity e = duibaGuessBrickDao.find(info.getId());
		Assert.assertNotNull(e);
	}

	@Test
    public void getBrickContentByIdTest(){
		String content = duibaGuessBrickDao.getBrickContentById(info.getId());
		Assert.assertNotNull(content);
    }

	@Test
    public void findNoContentTest(){
    	DuibaGuessBrickEntity e = duibaGuessBrickDao.findNoContent(info.getId());
    	Assert.assertNotNull(e);
    }

	@Test
    public void getBrickPrizeContentByIdTest(){
    	String prizeContent = duibaGuessBrickDao.getBrickPrizeContentById(info.getId());
		Assert.assertNotNull(prizeContent);
    }

	@Test
    public void update4AdminTest(){
    	DuibaGuessBrickEntity e = TestUtils.createRandomBean(DuibaGuessBrickEntity.class);
    	duibaGuessBrickDao.update4Admin(info.getId(), e.getTitle(), e.getContent(), e.getPrizeContent(), e.getMd5());
    	String title = duibaGuessBrickDao.find(info.getId()).getTitle();
    	Assert.assertTrue(title.equals(e.getTitle()));
    }

	@Test
    public void findByTitleTest(){
    	DuibaGuessBrickEntity e = duibaGuessBrickDao.findByTitle(info.getTitle());
    	Assert.assertNotNull(e);
    }

	@Test
    public void openTest(){
    	duibaGuessBrickDao.open(info.getId());
    	int status = duibaGuessBrickDao.find(info.getId()).getStatus();
    	Assert.assertTrue(status == 1);
    }

	@Test
    public void disableTest(){
    	duibaGuessBrickDao.disable(info.getId());
    	int status = duibaGuessBrickDao.find(info.getId()).getStatus();
    	Assert.assertTrue(status == 0);
    }

	@Test
    public void findPageTest(){
    	DuibaGuessBrickEntity e = TestUtils.createRandomBean(DuibaGuessBrickEntity.class);
    	e.setDeleted(false);
    	duibaGuessBrickDao.insert(e);
    	List<DuibaGuessBrickEntity> list = duibaGuessBrickDao.findPage(0, 10);
    	Assert.assertTrue(list.size() > 0);
    }

	@Test
    public void findPageCountTest(){
    	DuibaGuessBrickEntity e = TestUtils.createRandomBean(DuibaGuessBrickEntity.class);
    	e.setDeleted(false);
    	duibaGuessBrickDao.insert(e);
    	long count = duibaGuessBrickDao.findPageCount();
    	Assert.assertTrue(count > 0);
    }

	@Test
    public void findAllTest(){
    	DuibaGuessBrickEntity e = TestUtils.createRandomBean(DuibaGuessBrickEntity.class);
    	e.setStatus(1);
    	duibaGuessBrickDao.insert(e);
    	List<DuibaGuessBrickEntity> list = duibaGuessBrickDao.findAll();
    	Assert.assertTrue(list.size() > 0);
    }
}
