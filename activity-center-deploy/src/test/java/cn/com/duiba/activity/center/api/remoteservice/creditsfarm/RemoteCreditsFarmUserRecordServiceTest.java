package cn.com.duiba.activity.center.api.remoteservice.creditsfarm;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmUserRecordDto;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * created by liugq in 2019/08/16
 **/
public class RemoteCreditsFarmUserRecordServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteCreditsFarmUserRecordService remoteCreditsFarmUserRecordService;

    @Test
    public void testFind(){
       CreditsFarmUserRecordDto dto = remoteCreditsFarmUserRecordService.findByUnikey(1539361L,1L);
       System.out.println(dto.getId());
    }
}

