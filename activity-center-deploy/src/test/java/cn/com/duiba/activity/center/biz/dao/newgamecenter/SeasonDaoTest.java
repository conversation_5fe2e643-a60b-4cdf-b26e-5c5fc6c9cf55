package cn.com.duiba.activity.center.biz.dao.newgamecenter;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.newgamecenter.SeasonConfigEntity;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 * @date 2018/09/03
 */
@Transactional(DsConstants.DATABASE_NGAME)
public class SeasonDaoTest extends TransactionalTestCaseBase {
    @Autowired
    private SeasonDao seasonDao;
    private SeasonConfigEntity seasonConfigEntity;

    @Before
    public void setup() {
        seasonConfigEntity = new SeasonConfigEntity();
        seasonConfigEntity.setEndTime(new Date());
        seasonConfigEntity.setStartTime(new Date());
        seasonConfigEntity.setGameCenterId(1L);
        seasonConfigEntity.setPrevSeasonId(0L);
        seasonConfigEntity.setRankListId(1L);
        seasonConfigEntity.setTitle("title");
        seasonConfigEntity.setRedPacketConfig("");

        seasonConfigEntity.setId(seasonDao.insert(seasonConfigEntity));
    }

    @Test
    public void shouldInsertCorrectly() {
        assertThat(seasonConfigEntity.getId()).isGreaterThan(0);
    }

    @Test
    public void shouldFindByIdCorrectly() {
        SeasonConfigEntity entity = seasonDao.findById(seasonConfigEntity.getId());

        assertThat(entity).isNotNull();
    }

    @Test
    public void shouldListCorrectly() {
        List<SeasonConfigEntity> list = seasonDao.list(seasonConfigEntity.getGameCenterId(), 1, 1);
        assertThat(list).isNotNull();
        assertThat(list.size()).isEqualTo(1);
    }

    @Test
    public void shouldUpdateCorrectly() {
        seasonConfigEntity.setTitle("");

        assertThat(seasonDao.update(seasonConfigEntity)).isEqualTo(1);
    }

    @Test
    public void shouldFindByListIdCorrectly() {
        SeasonConfigEntity entity = seasonDao.findByRankListId(1L);
        assertThat(entity).isNotNull();
        assertThat(entity.getGameCenterId()).isNotNull();
        assertThat(entity.getEndTime()).isNotNull();
        assertThat(entity.getStartTime()).isNotNull();
    }

    @Test
    public void shouldFingByGameCenterIdCorrectly(){
        assertThat(seasonDao.findByGameCenterId(23L).size()).isEqualTo(1);
    }

}
