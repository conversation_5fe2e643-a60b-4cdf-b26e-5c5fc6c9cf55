package cn.com.duiba.activity.center.api.remoteservice.wxFavorCouponUserRelService;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.wxcoupon.WxFavorCouponUserRelDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.wxcoupon.WxFavorCouponUserRelEntity;
import cn.com.duiba.activity.center.biz.service.WxFavorCouponUserRelService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2023/6/12 4:32 PM （可以根据需要修改）
 * @Version 1.0 （版本号）
 */
@Transactional(DsConstants.DATABASE_ACT_RECORD)
public class WxFavorCouponUserRelServiceTest extends TransactionalTestCaseBase {



    @Autowired
    private WxFavorCouponUserRelService wxFavorCouponUserRelService;

    @Test
    @Rollback(value = false)
    public void updateById(){

        WxFavorCouponUserRelDto wx = new WxFavorCouponUserRelDto();
//        wx.setAppId(1L);
//        wx.setConsumerId(1L);
//        wx.setOpenId("2");
//        wxFavorCouponUserRelService.save(wx);
        wx.setId(2L);
        wx.setOpenId("3");
        wxFavorCouponUserRelService.updateById(wx);

    }

}
