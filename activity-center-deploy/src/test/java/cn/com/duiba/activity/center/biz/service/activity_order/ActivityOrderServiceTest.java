package cn.com.duiba.activity.center.biz.service.activity_order;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity_order.ActivityOrderDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by yansen on 16/9/20.
 */

@Transactional(DsConstants.DATABASE_ACTIVITY_ORDER_CON)
public class ActivityOrderServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private ActivityOrderService activityOrderService;

    private ThreadLocal<ActivityOrderDto> entity = new ThreadLocal<>();

    @Before
    public void testInsert() throws Exception {
        ActivityOrderDto entity = new ActivityOrderDto();
        entity.setExchangeStatus(2);
        TestUtils.setRandomAttributesForBean(entity,false);
        activityOrderService.createOrder(entity);
        this.entity.set(entity);
    }
    
    @Test
    public void findByExpirationTime() {
        Date now = new Date();
        String expirationTime = DateUtils.getSecondStr(DateUtils.daysAddOrSub(now, -10));
        String activityType = "hello";
        activityOrderService.findByExpirationTime(expirationTime, activityType);
    }

    @Test
    public void findOptionIds() {
        Long consumerId = 1L;
        Long appId = 1L;
        String activityType = "hello";
        activityOrderService.findOptionIds(consumerId, appId, activityType);
    }

    @Test
    public void findByAppIdWithPage() {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("duibaActivityId", "1");
        queryMap.put("appId", "1");
        queryMap.put("start", 1);
        queryMap.put("pageSize", 10);
        activityOrderService.findByAppIdWithPage(queryMap);
    }

    @Test
    public void getCountByAppId() {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("appId", "1");
        queryMap.put("duibaActivityId", "1");
        activityOrderService.getCountByAppId(queryMap);
    }

    @Test
    public void findFailByActivity4App() {
        List<Long> activityIds = new ArrayList<>();
        activityIds.add(1L);
        Long appId = 1L;
        activityOrderService.findFailByActivity4App(activityIds, appId);
    }




    @Test
    public void testFindByOrderNum() throws Exception {
        ActivityOrderDto e = activityOrderService.findByOrderNum(this.entity.get().getOrderNum());
        Assert.assertEquals(e.getOrderNum(), entity.get().getOrderNum());
    }

    @Test
    public void testConsumeCreditsSuccess() {
        ActivityOrderDto e = entity.get();
        String orderNum = e.getOrderNum();
        e.setActivityOptionId(21L);
        e.setActivityOptionName("fasfds");
        e.setActivityOptionType("212");
        e.setFacePrice("1236");
        e.setAppItemId(55L);
        e.setItemId(12266L);
        e.setGid(7L);
        e.setGtype("ddf");
        e.setCouponId(29L);
        e.setConsumeCreditsStatus(ActivityOrderDto.ConsumeCreditsSuccess);
        activityOrderService.consumeCreditsSuccess(orderNum, e.getActivityOptionId(), e.getActivityOptionName(), e.getActivityOptionType(), e.getFacePrice(), e.getAppItemId(), e.getItemId(), e.getGid(), e.getGtype(), e.getCouponId());
        ActivityOrderDto e1 = activityOrderService.findByOrderNum(this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void testConsumeCreditsSuccessDowngrade() {
        ActivityOrderDto e = entity.get();
        String orderNum = e.getOrderNum();
        e.setActivityOptionId(21L);
        e.setActivityOptionName("fasfds");
        e.setActivityOptionType("212");
        e.setFacePrice("1236");
        e.setAppItemId(55L);
        e.setItemId(12266L);
        e.setGid(7L);
        e.setGtype("ddf");
        e.setCouponId(29L);
        e.setConsumeCreditsStatus(ActivityOrderDto.ConsumeCreditsSuccess);
        activityOrderService.consumeCreditsSuccessDowngrade(orderNum, e.getActivityOptionId(), e.getActivityOptionName(), e.getActivityOptionType(), e.getFacePrice(), e.getAppItemId(), e.getItemId(), e.getGid(), e.getGtype(), e.getCouponId());
        ActivityOrderDto e1 = activityOrderService.findByOrderNum(this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    private void assertEquals(ActivityOrderDto e, ActivityOrderDto e1){
        TestUtils.assertEqualsReflect(e,e1,false,new String[]{"gmtCreate","gmtModified","suffix","addCreditsDevBizId"});
    }

    @Test
    public void testAddCreditsSuccess() {
        ActivityOrderDto e = entity.get();
        String orderNum = e.getOrderNum();
        e.setActivityOptionId(21L);
        e.setActivityOptionName("fasfds");
        e.setActivityOptionType("212");
        e.setFacePrice("1236");
        e.setAppItemId(55L);
        e.setItemId(12266L);
        e.setGid(7L);
        e.setGtype("ddf");
        e.setCouponId(29L);
        e.setAddCreditsStatus(ActivityOrderDto.AddCreditsSuccess);
        activityOrderService.addCreditsSuccess(orderNum, e.getActivityOptionId(), e.getActivityOptionName(), e.getActivityOptionType(), e.getFacePrice(), e.getAppItemId(), e.getItemId(), e.getGid(), e.getGtype(), e.getCouponId());
        ActivityOrderDto e1 = activityOrderService.findByOrderNum(this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void testConsumeCreditsFail() {
        ActivityOrderDto e = entity.get();
        String orderNum = e.getOrderNum();
        e.setActivityOptionId(21L);
        e.setActivityOptionName("fasfds");
        e.setActivityOptionType("212");
        e.setFacePrice("1236");
        e.setAppItemId(55L);
        e.setItemId(12266L);
        e.setGid(7L);
        e.setGtype("ddf");
        e.setCouponId(29L);
        e.setError4admin("212");
        e.setError4consumer("yf");
        e.setError4developer("t66");
        e.setConsumeCreditsStatus(ActivityOrderDto.ConsumeCreditsFail);
        activityOrderService.consumeCreditsFail(orderNum, e.getActivityOptionId(), e.getActivityOptionName(), e.getActivityOptionType(), e.getFacePrice(), e.getAppItemId(), e.getItemId(), e.getGid(), e.getGtype(), e.getCouponId(), e.getError4admin(), e.getError4developer(), e.getError4consumer());
        ActivityOrderDto e1 = activityOrderService.findByOrderNum(this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void testAddCreditsFail() {
        ActivityOrderDto e = entity.get();
        String orderNum = e.getOrderNum();
        e.setActivityOptionId(21L);
        e.setActivityOptionName("fasfds");
        e.setActivityOptionType("212");
        e.setFacePrice("1236");
        e.setAppItemId(55L);
        e.setItemId(12266L);
        e.setGid(7L);
        e.setGtype("ddf");
        e.setCouponId(29L);
        e.setError4admin("212");
        e.setError4consumer("yf");
        e.setError4developer("t66");
        e.setAddCreditsStatus(ActivityOrderDto.AddCreditsFail);
        activityOrderService.addCreditsFail(orderNum, e.getActivityOptionId(), e.getActivityOptionName(), e.getActivityOptionType(), e.getFacePrice(), e.getAppItemId(), e.getItemId(), e.getGid(), e.getGtype(), e.getCouponId(), e.getError4admin(), e.getError4developer(), e.getError4consumer());
        ActivityOrderDto e1 = activityOrderService.findByOrderNum(this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void addCreditsStatusToProcessing() {
        ActivityOrderDto e = entity.get();
        String orderNum = e.getOrderNum();
        e.setAddCreditsStatus(ActivityOrderDto.AddCreditsProcessing);
        activityOrderService.addCreditsStatusToProcessing(orderNum);
        ActivityOrderDto e1 = activityOrderService.findByOrderNum(this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void testExchangeStatusToWait() {
        ActivityOrderDto e = entity.get();
        String orderNum = e.getOrderNum();
        e.setExchangeStatus(ActivityOrderDto.ExchangeWait);
        activityOrderService.exchangeStatusToWait(orderNum);
        ActivityOrderDto e1 = activityOrderService.findByOrderNum(this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void testExchangeStatusToSuccess() {
        ActivityOrderDto e = entity.get();
        String orderNum = e.getOrderNum();
        e.setExchangeStatus(ActivityOrderDto.ExchangeSuccess);
        activityOrderService.exchangeStatusToSuccess(orderNum);
        ActivityOrderDto e1 = activityOrderService.findByOrderNum(this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void testExchangeStatusToFail() {
        ActivityOrderDto e = entity.get();
        String orderNum = e.getOrderNum();
        e.setExchangeStatus(ActivityOrderDto.ExchangeFail);
        activityOrderService.exchangeStatusToFail(orderNum);
        ActivityOrderDto e1 = activityOrderService.findByOrderNum(this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void testExchangeStatusToOverdue() {
        ActivityOrderDto e = entity.get();
        String orderNum = e.getOrderNum();
        e.setExchangeStatus(ActivityOrderDto.ExchangeOverdue);
        activityOrderService.exchangeStatusToOverdue(orderNum);
        ActivityOrderDto e1 = activityOrderService.findByOrderNum(this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void testExchangeStatusToOverdueBatch() {
        String failOrderNum = "0000";
        ActivityOrderDto e = entity.get();
        String orderNum = e.getOrderNum();
        List<String> orderNums = Lists.newArrayList(orderNum, failOrderNum);
        e.setExchangeStatus(ActivityOrderDto.ExchangeOverdue);
        List<String> result = activityOrderService.exchangeStatusToOverdueBatch(orderNums);
        Assert.assertEquals(1, result.size());
        Assert.assertEquals(result.get(0), failOrderNum);
    }

    @Test
    public void testUpdateDeveloperBizId() {
        ActivityOrderDto e = entity.get();
        String orderNum = e.getOrderNum();
        e.setDeveloperBizId("fsd");
        activityOrderService.updateDeveloperBizId(orderNum, e.getDeveloperBizId());
        ActivityOrderDto e1 = activityOrderService.findByOrderNum(this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void updateAddDeveloperBizId() {
        ActivityOrderDto e = entity.get();
        String orderNum = e.getOrderNum();
        e.setAddCreditsDevBizId("fsd");
        activityOrderService.updateAddDeveloperBizId(orderNum, e.getAddCreditsDevBizId());
        ActivityOrderDto e1 = activityOrderService.findByOrderNum(this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void updateMainOrderNum() {
        ActivityOrderDto e = entity.get();
        String orderNum = e.getOrderNum();
        e.setMainOrderNum("fsd");
        activityOrderService.updateMainOrderNum(orderNum, e.getMainOrderNum());
        ActivityOrderDto e1 = activityOrderService.findByOrderNum(this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void testFindConsumerDuibaActivityNum() {
        ActivityOrderDto e = entity.get();
        int count = activityOrderService.findConsumerDuibaActivityNum(e.getConsumerId(), e.getDuibaActivityId(), e.getActivityType());
        Assert.assertTrue(count > 0);
    }

    @Test
    public void testFindConsumerDuibaActivityNumList() {
        ActivityOrderDto e = entity.get();
        List<Long> duibaActivityIds = new ArrayList<>();
        duibaActivityIds.add(e.getDuibaActivityId());
        List<ActivityOrderDto> list = activityOrderService.findConsumerDuibaActivityNumList(e.getConsumerId(), duibaActivityIds, e.getActivityType());
        int count = 0;
        for(ActivityOrderDto ei : list){
            if(ei.getOrderNum().equals(e.getOrderNum())){
                count++;
            }
        }
        Assert.assertTrue(count == 1);
    }

    @Test
    public void testFindConsumerDuibaActivity4day() {
        ActivityOrderDto e = entity.get();
        Date now = new Date();
        String start = DateUtils.getSecondStr(DateUtils.minutesAddOrSub(now, -1));
        String end = DateUtils.getSecondStr(DateUtils.minutesAddOrSub(now, 1));
        ActivityOrderDto e1 = activityOrderService.findConsumerDuibaActivity4day(e.getConsumerId(), e.getDuibaActivityId(), e.getActivityType(), start, end);

        Assert.assertTrue(e1 != null);
    }

    @Test
    public void findByOrderNums() {
        ActivityOrderDto e = entity.get();
        String orderNum = e.getOrderNum();
        List<ActivityOrderDto> list = activityOrderService.findByOrderNums(e.getConsumerId(), Collections.singletonList(orderNum));
        assertEquals(list.get(0), e);
    }

    @Test
    public void updateAddCredits() {
        ActivityOrderDto e = entity.get();
        String orderNum = e.getOrderNum();
        e.setAddCredits(212L);
        activityOrderService.updateAddCredits(orderNum, e.getAddCredits());
        ActivityOrderDto e1 = activityOrderService.findByOrderNum(this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

}
