package cn.com.duiba.activity.center.biz.dao.game;

import java.util.Arrays;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.game.DuibaQuestionStockEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/2.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaQuestionStockDaoTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaQuestionStockDao duibaQuestionStockDao;
    

    @Test
    public void testAdd() {
        DuibaQuestionStockEntity e=new DuibaQuestionStockEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionStockDao.add(e);
    }

    @Test
    public void testAddBatch() {
        DuibaQuestionStockEntity e=new DuibaQuestionStockEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionStockDao.addBatch(Arrays.asList(e));
    }

    @Test
    public void testSubStock() {
        DuibaQuestionStockEntity e=new DuibaQuestionStockEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStock(20);
        duibaQuestionStockDao.add(e);
        duibaQuestionStockDao.subStock(e.getId(),1);
        Assert.assertEquals(duibaQuestionStockDao.findByQuestionOptionId(e.getQuestionOptionId()).getStock(),new Integer(19));
    }

    @Test
    public void testAddStock() {
        DuibaQuestionStockEntity e=new DuibaQuestionStockEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStock(20);
        duibaQuestionStockDao.add(e);
        duibaQuestionStockDao.addStock(e.getId(),1);
        Assert.assertTrue(duibaQuestionStockDao.findByQuestionOptionId(e.getQuestionOptionId()).getStock().equals(21));
    }

    @Test
    public void testFindRemaining() {
        DuibaQuestionStockEntity e=new DuibaQuestionStockEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStock(20);
        duibaQuestionStockDao.add(e);
        Assert.assertNotNull(duibaQuestionStockDao.findRemaining(e.getQuestionOptionId(),e.getRelationType()));
    }

    @Test
    public void testFindByQuestionOptionId() {
        DuibaQuestionStockEntity e=new DuibaQuestionStockEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStock(20);
        duibaQuestionStockDao.add(e);
        Assert.assertNotNull(duibaQuestionStockDao.findByQuestionOptionId(e.getQuestionOptionId()));
    }

    @Test
    public void testFindByQuestionOptionIds() {
        DuibaQuestionStockEntity e=new DuibaQuestionStockEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStock(20);
        duibaQuestionStockDao.add(e);
        Assert.assertTrue(duibaQuestionStockDao.findByQuestionOptionIds(Arrays.asList(e.getQuestionOptionId())).size()>0);
    }

    @Test
    public void testUpdateStockAdd() {
        DuibaQuestionStockEntity e=new DuibaQuestionStockEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStock(20);
        duibaQuestionStockDao.add(e);
        duibaQuestionStockDao.updateStockAdd(e.getId(),1,e.getRelationType());
        duibaQuestionStockDao.findByQuestionOptionId(e.getQuestionOptionId()).getStock().equals(21);
    }

    @Test
    public void testUpdateStockSub() {
        DuibaQuestionStockEntity e=new DuibaQuestionStockEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setStock(20);
        duibaQuestionStockDao.add(e);
        duibaQuestionStockDao.updateStockSub(e.getId(),1,e.getRelationType());
        duibaQuestionStockDao.findByQuestionOptionId(e.getQuestionOptionId()).getStock().equals(19);
    }

}
