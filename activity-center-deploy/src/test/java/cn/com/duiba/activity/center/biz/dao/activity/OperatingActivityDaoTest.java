package cn.com.duiba.activity.center.biz.dao.activity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.activity.OperatingActivityEntity;
import cn.com.duiba.activity.center.biz.entity.activity.OperatingActivityOptionsEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/13.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class OperatingActivityDaoTest extends TransactionalTestCaseBase {
    @Resource
    private OperatingActivityDao operatingActivityDao;


    @Resource
    private OperatingActivityOptionsDao operatingActivityOptionsDao;

    private OperatingActivityEntity operatingActivityDO;

    @Before
    public void testInsert() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        operatingActivityDao.insert(e);
        operatingActivityDO=e;
    }

    @Test
    public void testFind() {
        OperatingActivityEntity e=operatingActivityDO;
        OperatingActivityEntity e1=operatingActivityDao.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testSelect() throws Exception {
        OperatingActivityEntity e=operatingActivityDO;
        OperatingActivityEntity e1=operatingActivityDao.select(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindGameByAppIdAndActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(1);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findGameByAppIdAndActivityId(e.getAppId(), Arrays.asList(e.getActivityId()),e.getType());
        assertDO(e,list.get(0));
    }

    @Test
    public void testFindAllAppTasksContent() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(1);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllAppTasksContent(e.getAppId());
        assertDO(e,list.get(0));
    }

    @Test
    public void testFindOpenLotteryIds() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(2);
        e.setStatus(2);
        operatingActivityDao.insert(e);
        List<Long> list=operatingActivityDao.findOpenLotteryIds();
        Boolean isfind=false;
        for(Long o:list){
            if(o.equals(e.getId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllOpenDuibaActivity() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(0);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllOpenDuibaActivity(e.getActivityId());
        Boolean isfind=false;
        for(OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindByAppIdAndDuibaActivityIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(0);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findByAppIdAndDuibaActivityIdAndDeleted(e.getAppId(),e.getActivityId(),false);
        assertDO(e,e1);

    }

    @Test
    public void testFindByAppIdAndDuibaTurntableIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(4);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findByAppIdAndDuibaTurntableIdAndDeleted(e.getAppId(),e.getActivityId(),false);
        assertDO(e,e1);
    }

    @Test
    public void testFindByAppIdAndGameConfigDuibaIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(20);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findByAppIdAndGameConfigDuibaIdAndDeleted(e.getAppId(),e.getActivityId(),false);
        assertDO(e,e1);
    }

    @Test
    public void testFindByAppIdAndDuibaSingleLotteryIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(2);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findByAppIdAndDuibaSingleLotteryIdAndDeleted(e.getAppId(),e.getActivityId(),false);
        assertDO(e,e1);
    }

    @Test
    public void testFindByAppIdAndDuibaQuestionAnswerIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(40);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findByAppIdAndDuibaQuestionAnswerIdAndDeleted(e.getAppId(),e.getActivityId(),false);
        assertDO(e,e1);
    }

    @Test
    public void testFindByAppIdAndDuibaSeckillIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(31);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findByAppIdAndDuibaSeckillIdAndDeleted(e.getAppId(),e.getActivityId(),false);
        assertDO(e,e1);
    }


    @Test
    public void testFindByAppIdAndDuibaQuizzIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(41);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findByAppIdAndDuibaQuizzIdAndDeleted(e.getAppId(),e.getActivityId(),false);
        assertDO(e,e1);
    }

    @Test
    public void testFindByAppIdAndDuibaHdtoolIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(6);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findByAppIdAndDuibaHdtoolIdAndDeleted(e.getAppId(),e.getActivityId(),false);
        assertDO(e,e1);
    }

    @Test
    public void testFindByAppIdAndAppSingleLotteryIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(3);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findByAppIdAndAppSingleLotteryIdAndDeleted(e.getAppId(),e.getActivityId(),false);
        assertDO(e,e1);
    }

    @Test
    public void testFindByAppIdAndAppManualLotteryAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(5);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findByAppIdAndAppManualLotteryAndDeleted(e.getAppId(),e.getActivityId(),false);
        assertDO(e,e1);
    }

    @Test
    public void testCountActivity() {
        OperatingActivityEntity e=operatingActivityDO;
        Map<String,Object> params=new HashMap<>();
        params.put("appId",e.getAppId());
        params.put("title",e.getTitle());
        params.put("parentId",e.getParentActivityId());
        Assert.assertTrue(operatingActivityDao.countActivity(params)>0);
    }

    @Test
    public void testFindActivityList() {
        OperatingActivityEntity e=operatingActivityDO;
        Map<String,Object> params=new HashMap<>();
        params.put("appId",e.getAppId());
        params.put("title",e.getTitle());
        params.put("parentId",e.getParentActivityId());
        params.put("deleted",e.getDeleted());
        Assert.assertTrue(operatingActivityDao.findActivityList(params).size()>0);
        params.put("filterPK", false);
        Assert.assertTrue(operatingActivityDao.findActivityList(params).size()>0);

    }

    @Test
    public void testFindRecommendActivityList() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(0);
        operatingActivityDao.insert(e);
        Assert.assertTrue(operatingActivityDao.findRecommendActivityList(e.getAppId()).size()>0);
    }

    @Test
    public void testFindActivityListByParent() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(5);
        operatingActivityDao.insert(e);
        Map<String,Object> params=new HashMap<>();
        params.put("parentId",e.getParentActivityId());
        params.put("appId",e.getAppId());
        params.put("start",0);
        params.put("pageSize",20);
        Assert.assertTrue(operatingActivityDao.findActivityListByParent(params).size()>0);
    }

    @Test
    public void testDeleteTurntable() {
        OperatingActivityEntity e=operatingActivityDO;
        operatingActivityDao.deleteTurntable(e.getId(),e.getAppId(),false,20);
        OperatingActivityEntity e1=operatingActivityDao.find(e.getId());
        Assert.assertTrue(!e1.getDeleted());
        Assert.assertEquals((int)e1.getStatus(),20);
    }

    @Test
    public void testFindAppIdsByDuibaActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(0);
        operatingActivityDao.insert(e);
        List<Long> appIds=operatingActivityDao.findAppIdsByDuibaActivityId(e.getActivityId());
        boolean isfind=false;
        for (Long appId:appIds){
            if(appId.equals(e.getAppId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAppIdsBySingleLotteryId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        operatingActivityDao.insert(e);
        List<Long> appIds=operatingActivityDao.findAppIdsBySingleLotteryId(e.getActivityId());
        boolean isfind=false;
        for (Long appId:appIds){
            if(appId.equals(e.getAppId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAppIdsByActivityIdAndType() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        operatingActivityDao.insert(e);
        List<Long> appIds=operatingActivityDao.findAppIdsByActivityIdAndType(e.getActivityId(),e.getType());
        boolean isfind=false;
        for (Long appId:appIds){
            if(appId.equals(e.getAppId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAppIdsByDuibaSingleLotteryId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        operatingActivityDao.insert(e);
        List<Long> appIds=operatingActivityDao.findAppIdsByDuibaSingleLotteryId(e.getActivityId());
        boolean isfind=false;
        for (Long appId:appIds){
            if(appId.equals(e.getAppId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testCountAppByActivityIdAndType() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        operatingActivityDao.insert(e);
        int count=operatingActivityDao.countAppByActivityIdAndType(e.getActivityId(),e.getType());
        Assert.assertTrue(count>0);
    }

    @Test
    public void testFindAllByDuibaActivityIds() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(0);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllByDuibaActivityIds(Arrays.asList(e.getActivityId()));
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllByActivityIdsAndType() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllByActivityIdsAndType(Arrays.asList(e.getActivityId()),e.getType());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllByDuibaSingleLotteryIds() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllByDuibaSingleLotteryIds(Arrays.asList(e.getActivityId()));
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindIdsByDuibaActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(0);
        operatingActivityDao.insert(e);

        List<Long> ids=operatingActivityDao.findIdsByDuibaActivityId(e.getActivityId());

        boolean isfind=false;
        for (Long id:ids){
            if(id.equals(e.getId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testUpdateStatusByDuibaActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(0);
        operatingActivityDao.insert(e);
        operatingActivityDao.updateStatusByDuibaActivityId(20,e.getActivityId());

        OperatingActivityEntity e1=operatingActivityDao.find(e.getId());
        Assert.assertEquals((int) e1.getStatus(),20);
    }

    @Test
    public void testUpdateStatusBySingleLotteryId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        operatingActivityDao.insert(e);
        operatingActivityDao.updateStatusBySingleLotteryId(20,e.getActivityId());

        OperatingActivityEntity e1=operatingActivityDao.find(e.getId());
        Assert.assertEquals((int) e1.getStatus(),20);
    }


    @Test
    public void testFindIdsBySingleLotteryId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        operatingActivityDao.insert(e);
        List<Long> ids=operatingActivityDao.findIdsBySingleLotteryId(e.getActivityId());

        boolean isfind=false;
        for (Long id:ids){
            if(id.equals(e.getId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllByType() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllByType(e.getType());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllBySingleLotteryId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllBySingleLotteryId(e.getActivityId());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllBySingleLotteryIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllBySingleLotteryIdAndDeleted(e.getActivityId(),e.getDeleted());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllByDuibaSeckillIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(31);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllByDuibaSeckillIdAndDeleted(e.getActivityId(),e.getDeleted());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllByDuibaActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(0);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllByDuibaActivityId(e.getActivityId());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllByDuibaSecondsKillId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(31);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllByDuibaSeckillId(e.getActivityId());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllByDuibaActivityIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(0);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllByDuibaActivityIdAndDeleted(e.getActivityId(),e.getDeleted());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllByDuibaQuestionAnswerIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(40);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllByDuibaQuestionAnswerIdAndDeleted(e.getActivityId(),e.getDeleted());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllByDuibaQuizzIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(41);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllByDuibaQuizzIdAndDeleted(e.getActivityId(),e.getDeleted());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testUpdateStatusByActivityIdAndType() {
        OperatingActivityEntity e=operatingActivityDO;
        operatingActivityDao.updateStatusByActivityIdAndType(e.getActivityId(),e.getType(),42);
        OperatingActivityEntity e1=operatingActivityDao.find(e.getId());
        Assert.assertEquals((int)e1.getStatus(),42);
    }

    @Test
    public void testFindByActivityIdAndTypeAndAppIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(41);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findByActivityIdAndTypeAndAppIdAndDeleted(e.getActivityId(),e.getType(),e.getAppId(),e.getDeleted());
        assertDO(e,e1);
    }

    @Test
    public void testFindIdsByDuibaActivityIdAndType() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        operatingActivityDao.insert(e);
        List<Long> ids=operatingActivityDao.findIdsByDuibaActivityIdAndType(e.getActivityId(),e.getType());
        boolean isfind=false;
        for (Long id:ids){
            if(id.equals(e.getId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindSingleLotteryOperaList() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findSingleLotteryOperaList(Arrays.asList(e.getActivityId()),e.getAppId());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }



    @Test
    public void testFindByActivityIdAndParentIdAndTypeAndAppId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(41);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findByActivityIdAndParentIdAndTypeAndAppId(e.getActivityId(),e.getParentActivityId(),e.getType(),e.getAppId());
        assertDO(e,e1);
    }

    @Test
    public void testDeleteByParentActivityIds() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        operatingActivityDao.insert(e);
        operatingActivityDao.deleteByParentActivityIds(Arrays.asList(e.getParentActivityId()));
        OperatingActivityEntity e1=operatingActivityDao.find(e.getId());
        Assert.assertEquals((int)e1.getStatus(),1);
        Assert.assertTrue(e1.getDeleted());
    }

    @Test
    public void testFindOperatingSingleLottery() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findOperatingSingleLottery(e.getActivityId(),e.getAppId());
        assertDO(e,e1);
    }

    @Test
    public void testFindAllByIds() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllByIds(Arrays.asList(e.getId()));
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testUpdateManualLotteryByIds() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(5);
        e.setStatus(2);
        operatingActivityDao.insert(e);
        int count=operatingActivityDao.updateManualLotteryByIds(Arrays.asList(e.getActivityId()));
        System.out.println(count);
        OperatingActivityEntity e1=operatingActivityDao.find(e.getId());
        Assert.assertEquals((int)e1.getStatus(),1);
    }

    @Test
    public void testFindAllEnabledActivies() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(2);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllEnabledActivies(e.getAppId());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindActiveActivity() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(2);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findActiveActivity(e.getAppId());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllVirtualInSingleLottery() {//
        List<Long> list = new ArrayList<>();
        list.add(1L);
        operatingActivityDao.findAllVirtualInSingleLottery(1l, list);
    }

    @Test
    public void testFindAllVirtualInHdTool() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(2);
        operatingActivityDao.insert(e);
        OperatingActivityOptionsEntity o=new OperatingActivityOptionsEntity(true);
        TestUtils.setRandomAttributesForBean(o,false);
        operatingActivityOptionsDao.insertOption(o);
        Assert.assertNotNull(operatingActivityDao.findAllVirtualInHdTool(e.getAppId()));
    }

    @Test
    public void testUpdate() {
        OperatingActivityEntity e=new OperatingActivityEntity(operatingActivityDO.getId());
        TestUtils.setRandomAttributesForBean(e,false);
        operatingActivityDao.update(e);
        OperatingActivityEntity e1=operatingActivityDao.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindAppIdsByDuibaSecondsKillId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(30);
        operatingActivityDao.insert(e);
        List<Long> ids=operatingActivityDao.findAppIdsByDuibaSecondsKillId(e.getActivityId());
        boolean isfind=false;
        for (Long id:ids){
            if(id.equals(e.getAppId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllByDuibaSecondsKillActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(30);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllByDuibaSecondsKillActivityId(e.getActivityId());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllByDuibaQuestionAnswerId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(40);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllByDuibaQuestionAnswerId(e.getActivityId());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllByDuibaQuizzId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(41);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllByDuibaQuizzId(e.getActivityId());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllByDuibaSeckillId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(31);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllByDuibaSeckillId(e.getActivityId());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllByDuibaSecondsKillActivityIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(30);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllByDuibaSecondsKillActivityIdAndDeleted(e.getActivityId(),e.getDeleted());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testUpdateStatusByDuibaSecondsKillActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(30);
        operatingActivityDao.insert(e);
        operatingActivityDao.updateStatusByDuibaSecondsKillActivityId(42,e.getActivityId());
        OperatingActivityEntity e1=operatingActivityDao.find(e.getId());
        Assert.assertEquals((int)e1.getStatus(),42);
    }

    @Test
    public void testFindGameOperatingActivity() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(20);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findGameOperatingActivity(e.getAppId(),e.getActivityId(),e.getType());
        assertDO(e,e1);
    }

    @Test
    public void testFindQuestionAnswerOperatingActivity() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(40);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findQuestionAnswerOperatingActivity(e.getAppId(),e.getActivityId());
        assertDO(e1,e);
    }

    @Test
    public void testFindSecondsKillOperatingActivity() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(30);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findSecondsKillOperatingActivity(e.getAppId(),e.getActivityId());
        assertDO(e1,e);
    }

    @Test
    public void testFindByAppIdAndDuibaGameIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(20);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findByAppIdAndDuibaGameIdAndDeleted(e.getAppId(),e.getActivityId(),e.getDeleted());
        assertDO(e1,e);
    }

    @Test
    public void testFindByAppIdLimit() {
//        AppBannerDO appBanner=new AppBannerDO(true);
//        TestUtils.setRandomAttributesForBean(appBanner,false);
//        appBannerDao.insert(appBanner);
//        OperatingActivityEntity e=new OperatingActivityEntity(true);
//        TestUtils.setRandomAttributesForBean(e,false);
//        e.setDeleted(false);
//        e.setParentActivityId(null);
//        e.setStatus(2);
//        e.setType(20);
//        e.setAppBannerId(appBanner.getId());
//        operatingActivityDao.insert(e);
         operatingActivityDao.findByAppIdLimit(1l,2l);
    }

    @Test
    public void testFindAllByAppId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(30);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findAllByAppId(e.getAppId());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindByAppIdAndDuibaSecondsKillActivityIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(30);
        operatingActivityDao.insert(e);
        OperatingActivityEntity e1=operatingActivityDao.findByAppIdAndDuibaSecondsKillActivityIdAndDeleted(e.getAppId(),e.getActivityId(),e.getDeleted());
        assertDO(e,e1);
    }

    @Test
    public void testFindDuibaQuestionAnswerByAppIdAndActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(2);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findDuibaQuestionAnswerByAppIdAndActivityId(e.getAppId(),Arrays.asList(e.getActivityId()),e.getType());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindDuibaQuizzByAppIdAndActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(2);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findDuibaQuizzByAppIdAndActivityId(e.getAppId(),Arrays.asList(e.getActivityId()),e.getType());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindDuibaSeckillByAppIdAndActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(2);
        operatingActivityDao.insert(e);
        List<OperatingActivityEntity> list=operatingActivityDao.findDuibaSeckillByAppIdAndActivityId(e.getAppId(),Arrays.asList(e.getActivityId()),e.getType());
        boolean isfind=false;
        for (OperatingActivityEntity o:list){
            if(o.getId().equals(e.getId())){
                assertDO(e,o);
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindOnlineActivity() {
//        AppBannerDO appBanner=new AppBannerDO(true);
//        TestUtils.setRandomAttributesForBean(appBanner,false);
//        appBannerDao.insert(appBanner);
//        OperatingActivityEntity e=new OperatingActivityEntity(true);
//        TestUtils.setRandomAttributesForBean(e,false);
//        e.setDeleted(false);
//        e.setParentActivityId(null);
//        e.setStatus(2);
//        e.setType(20);
//        e.setAppBannerId(appBanner.getId());
//        operatingActivityDao.insert(e);
        Map<String, Object> params = new HashMap<>();
        params.put("start", 0);
        params.put("pageSize", 10);
        params.put("appId", 1l);
        operatingActivityDao.findOnlineActivity(params);
    }

    @Test
    public void testFindOnlineActivityWithOutTopic() {
//        AppBannerDO appBanner=new AppBannerDO(true);
//        TestUtils.setRandomAttributesForBean(appBanner,false);
//        appBannerDao.insert(appBanner);
//        OperatingActivityEntity e=new OperatingActivityEntity(true);
//        TestUtils.setRandomAttributesForBean(e,false);
//        e.setDeleted(false);
//        e.setParentActivityId(null);
//        e.setStatus(2);
//        e.setType(20);
//        e.setAppBannerId(appBanner.getId());
//        operatingActivityDao.insert(e);
        Map<String,Object> params=new HashMap<>();
        params.put("start",0);
        params.put("pageSize",10);
        params.put("appId",1l);
        operatingActivityDao.findOnlineActivityWithOutTopic(params);
    }

    @Test
    public void testFindSeckillIdsByActivityId() throws Exception {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(2);
        e.setType(31);
        operatingActivityDao.insert(e);
        Assert.assertTrue(operatingActivityDao.findSeckillIdsByActivityId(e.getActivityId()).size()>0);
    }

    @Test
    public void testFindIdsByParentIdsAndType() throws Exception {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        operatingActivityDao.insert(e);
        Assert.assertTrue(operatingActivityDao.findIdsByParentIdsAndType(Arrays.asList(e.getParentActivityId()),e.getType()).size()>0);

    }

    private void assertDO(OperatingActivityEntity e, OperatingActivityEntity e1){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","failCount","customCredits","customCredits","exchangeLimit"}));

        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }
}
