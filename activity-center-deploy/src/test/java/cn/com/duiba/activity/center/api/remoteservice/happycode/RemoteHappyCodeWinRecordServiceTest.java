package cn.com.duiba.activity.center.api.remoteservice.happycode;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.happy_code.HappyCodePrizeCountDto;
import cn.com.duiba.activity.center.api.dto.happy_code.HappyCodeWinRecordDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;
import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.Multimaps;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * Created by hww on 2018/2/5 上午10:30.
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)

public class RemoteHappyCodeWinRecordServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteHappyCodeWinRecordService remoteHappyCodeWinRecordService;

    @Test
    public void testSelectRewardUser(){
        List<Long> ids =  remoteHappyCodeWinRecordService.selectLatestRewardUser(20L,10);
        System.out.println(ids.size());
    }


    @Test
    public void testCountByIds(){
        List<HappyCodePrizeCountDto> list = remoteHappyCodeWinRecordService.countByBasicIds(Lists.newArrayList(3L,4L,5L));
        for(HappyCodePrizeCountDto dto : list){
            System.out.println(dto.getBasicId()+ ":" + dto.getPrizeCount());
        }
    }

    @Test
    public void testFindByBasicIds() {
        Integer times = 3;
        List<Long> basicIds = Lists.newArrayList();
        for (long i = 0; i < times; i++) {
            HappyCodeWinRecordDto winRecord1 = TestUtils.createRandomBean(HappyCodeWinRecordDto.class);
            HappyCodeWinRecordDto winRecord2 = TestUtils.createRandomBean(HappyCodeWinRecordDto.class);
            HappyCodeWinRecordDto winRecord3 = TestUtils.createRandomBean(HappyCodeWinRecordDto.class);
            winRecord1.setBasicId(i);
            winRecord2.setBasicId(i);
            winRecord3.setBasicId(i);

            //数据库tiny字段，防止超长
            winRecord1.setCheat(0);
            winRecord2.setCheat(0);
            winRecord3.setCheat(0);
            winRecord1.setDeleted(0);
            winRecord2.setDeleted(0);
            winRecord3.setDeleted(0);

            remoteHappyCodeWinRecordService.insertWinRecord(winRecord1);
            remoteHappyCodeWinRecordService.insertWinRecord(winRecord2);
            remoteHappyCodeWinRecordService.insertWinRecord(winRecord3);
            basicIds.add(i);
        }
        List<HappyCodeWinRecordDto> winList = remoteHappyCodeWinRecordService.findByBasicIds(basicIds);
        Assert.assertTrue(Objects.equals(winList.size(),3 * times));
        ImmutableListMultimap<Long, HappyCodeWinRecordDto> winMap = Multimaps.index(winList, HappyCodeWinRecordDto::getBasicId);
        Assert.assertTrue(Objects.equals(winMap.keySet().size(), times));
        basicIds.forEach(id -> {
            List<HappyCodeWinRecordDto> list = winMap.get(id);
            Assert.assertTrue(Objects.equals(list.size(), 3));
        });
    }
}
