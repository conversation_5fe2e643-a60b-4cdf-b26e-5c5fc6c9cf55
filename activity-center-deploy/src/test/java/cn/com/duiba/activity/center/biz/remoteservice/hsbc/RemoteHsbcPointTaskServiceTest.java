package cn.com.duiba.activity.center.biz.remoteservice.hsbc;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.hsbc.HsbcPointTaskDto;
import cn.com.duiba.activity.center.api.remoteservice.hsbc.RemoteHsbcPointTaskService;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Collections;

public class RemoteHsbcPointTaskServiceTest extends TransactionalTestCaseBase {

    @Resource
    private RemoteHsbcPointTaskService remoteHsbcPointTaskService;

    @Test
    public void batchInsertTest() {
        HsbcPointTaskDto hsbcPointTaskDto = new HsbcPointTaskDto();
        hsbcPointTaskDto.setAppId(19441L);
        hsbcPointTaskDto.setTaskId("TASK2021102510002");
        hsbcPointTaskDto.setScopeType(1);

        boolean flag = remoteHsbcPointTaskService.batchSavePointTasks(19441L, Collections.singletonList(hsbcPointTaskDto));
        Assert.assertTrue(flag);
    }
}
