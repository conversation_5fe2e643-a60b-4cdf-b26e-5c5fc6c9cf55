package cn.com.duiba.activity.center.biz.service;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.cctv.TaskUnitDto;
import cn.com.duiba.activity.center.api.dto.equity.EquityWhiteListDto;
import cn.com.duiba.activity.center.api.params.cctv.TaskQueryParam;
import cn.com.duiba.activity.center.api.remoteservice.cctv.RemoteTaskUnitService;
import cn.com.duiba.activity.center.api.remoteservice.equity.RemoteEquityWhiteListService;
import cn.com.duiba.activity.center.biz.service.equity.EquityWhiteListService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

public class ChangshaEquityTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteEquityWhiteListService remoteEquityWhiteListService;

    private EquityWhiteListService equityWhiteListService;

    @Test
    public void save() {
        List<EquityWhiteListDto> result = remoteEquityWhiteListService.getByAidAndRtypesAndRval(19224L, Arrays.asList(2, 3), "5100");
        System.out.println(JSON.toJSONString(result));
    }
}
