package cn.com.duiba.activity.center.biz.service.creditgame;

import cn.com.duiba.activity.center.biz.dao.creditgame.PreUpdateHandler;
import cn.com.duiba.activity.center.biz.entity.creditgame.AddCreditExceptionRecordEntity;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * 积分游戏DAO单元测试
 * <AUTHOR>
 * @since 2016-09-05
 */
public class AddCreditExceptionRecordServiceTest extends CreditGameServiceTestCaseBase<AddCreditExceptionRecordEntity> {


    @Autowired
    private AddCreditExceptionRecordService addCreditExceptionRecordService;

    @Override
    protected AddCreditExceptionRecordEntity genEntity(){
        AddCreditExceptionRecordEntity addCreditExceptionRecordEntity=new AddCreditExceptionRecordEntity();
        addCreditExceptionRecordEntity.setGmtModified(new Date());
        addCreditExceptionRecordEntity.setGmtCreate(new Date());
        addCreditExceptionRecordEntity.setOrderNum("123l");
        addCreditExceptionRecordEntity.setAddCreditError("error msg");
        addCreditExceptionRecordEntity.setAddCreditNum(100l);
        addCreditExceptionRecordEntity.setAddCreditReason("reason");
        addCreditExceptionRecordEntity.setAddCreditType(1l);
        addCreditExceptionRecordEntity.setCloseReason("close reason");
        addCreditExceptionRecordEntity.setAppId(1L);
        addCreditExceptionRecordEntity.setIsClose((byte)0);
        addCreditExceptionRecordEntity.setAppId(123l);
        addCreditExceptionRecordEntity.setExceptionType((byte)0);

        return addCreditExceptionRecordEntity;
    }
    @Test
    public void testInsert(){
        doTestInsert(addCreditExceptionRecordService);
    }


    @Test
    public void testQueryById(){
        doTestQueryById(addCreditExceptionRecordService);
    }

    /*
    @Test
    public void testQuery(){
        doTestQuery(addCreditExceptionRecordService);
    }
    */

    @Test
    public void testUpdate(){
        doTestUpdate(addCreditExceptionRecordService, new PreUpdateHandler<AddCreditExceptionRecordEntity>() {
            @Override
            public void preHandle(AddCreditExceptionRecordEntity addCreditExceptionRecordEntity) {
                addCreditExceptionRecordEntity.setGmtModified(new Date());
            }
        });
    }


    @Test
    public void testDelete(){
        doTestDelete(addCreditExceptionRecordService);
    }
}
