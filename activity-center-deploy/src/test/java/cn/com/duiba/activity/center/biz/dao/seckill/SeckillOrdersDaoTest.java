package cn.com.duiba.activity.center.biz.dao.seckill;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;

/**
 * Created by wenqi.huang on 2016/11/24.
 */
@Transactional(DsConstants.DATABASE_SECKILL)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class SeckillOrdersDaoTest extends TransactionalTestCaseBase {

    @Resource
    private SeckillOrdersDao seckillOrdersDao;

    @Test
    public void find() {
        seckillOrdersDao.find(1L);
    }

    @Test
    public void totalCount() {
        Map<String, Object> queryMap = new HashMap<>();
        seckillOrdersDao.totalCount(queryMap);
    }

    @Test
    public void findByLimit() {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("start",0);
        queryMap.put("pageSize",10);
         seckillOrdersDao.findByLimit(queryMap);
    }
}
