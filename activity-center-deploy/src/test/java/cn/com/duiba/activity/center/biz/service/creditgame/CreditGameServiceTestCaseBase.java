package cn.com.duiba.activity.center.biz.service.creditgame;

import org.junit.Assert;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.creditgame.PreUpdateHandler;
import cn.com.duiba.activity.center.biz.entity.creditgame.IdTimeable;
import cn.com.duiba.activity.center.biz.service.GenericCURDService;

/**
 * 积分游戏 DAO 测试基类
 */
public abstract class CreditGameServiceTestCaseBase<E extends IdTimeable> extends TransactionalTestCaseBase{

    protected abstract E genEntity();


    protected void doTestInsert(GenericCURDService service){
        E entity=genEntity();
        Assert.assertNotNull(entity);

        int insertNum=service.insert(entity);
        Assert.assertTrue(insertNum==1);
        Assert.assertTrue(null!=entity.getId());

        E queryEntity= (E)service.queryById(entity.getId());
        Assert.assertNotNull(queryEntity);
        int deleteNum=service.delete(queryEntity.getId());
        Assert.assertTrue(deleteNum==1);
    }


    protected void doTestQueryById(GenericCURDService service){
        E entity=genEntity();
        Assert.assertNotNull(entity);

        int insertNum=service.insert(entity);
        Assert.assertTrue(insertNum==1);
        Assert.assertTrue(null!=entity.getId());

        E queryEntity= (E)service.queryById(entity.getId());
        Assert.assertNotNull(queryEntity);

        int deleteNum=service.delete(queryEntity.getId());
        Assert.assertTrue(deleteNum==1);
    }

    /*

    protected void doTestQuery(GenericCURDService service){
        E entity=genEntity();
        Assert.assertNotNull(entity);
        int insertNum=service.insert(entity);
        Assert.assertTrue(insertNum==1);
        Assert.assertTrue(null!=entity.getId());

        entity.setGmtModified(null);
        entity.setGmtCreate(null);

        List<E> entities=service.query(entity);
        Assert.assertTrue(!CollectionUtils.isEmpty(entities));
        E queryEntity= entities.get(0);
        Assert.assertNotNull(queryEntity);

        int deleteNum=service.delete(queryEntity.getId());
        Assert.assertTrue(deleteNum==1);
    }
    */


    protected void doTestUpdate(GenericCURDService service,PreUpdateHandler<E> preUpdateHandler){
        E entity=genEntity();
        Assert.assertNotNull(entity);
        int insertNum=service.insert(entity);
        Assert.assertTrue(insertNum==1);
        Assert.assertTrue(null!=entity.getId());

        if(null!=preUpdateHandler){
            preUpdateHandler.preHandle(entity);
        }
        int updateNum=service.update(entity);
        Assert.assertTrue(updateNum==1);

        int deleteNum=service.delete(entity.getId());
        Assert.assertTrue(deleteNum==1);
    }



    protected void doTestDelete(GenericCURDService service){
        E entity=genEntity();
        Assert.assertNotNull(entity);
        int insertNum=service.insert(entity);
        Assert.assertTrue(insertNum==1);
        Assert.assertTrue(null!=entity.getId());

        int deleteNum=service.delete(entity.getId());
        Assert.assertTrue(deleteNum==1);
    }
}
