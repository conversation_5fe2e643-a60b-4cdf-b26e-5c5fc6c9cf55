package cn.com.duiba.activity.center.biz.dao.newgamecenter;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.enums.NewGameCenterConfigStatus;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.newgamecenter.NewGameCenterConfigEntity;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 * @date 2018/09/03
 */
@Transactional(DsConstants.DATABASE_NGAME)
public class NewGameCenterDaoTest extends TransactionalTestCaseBase {
    @Autowired
    private NewGameCenterDao gameCenterConfigDao;
    private NewGameCenterConfigEntity configEntity;

    @Before
    public void setup() {
        configEntity = new NewGameCenterConfigEntity();
        configEntity.setCurrentSeasonId(1L);
        configEntity.setTitle("title");
        configEntity.setConfigStatus(NewGameCenterConfigStatus.CLOSE.getCode());

        configEntity.setId(gameCenterConfigDao.insert(configEntity));
    }

    @Test
    public void shouldInsertCorrectly() {
        assertThat(configEntity.getId()).isGreaterThan(0);
    }

    @Test
    public void shouldListCorrectly() {
        List<NewGameCenterConfigEntity> list = gameCenterConfigDao.list(1, 1);

        assertThat(list).isNotNull();
        assertThat(list.size()).isEqualTo(1);
    }

    @Test
    public void shouldUpdateCorrectly() {
        assertThat(gameCenterConfigDao.update(NewGameCenterConfigStatus.OPEN.getCode(), configEntity.getId()))
                .isEqualTo(1);
    }

    @Test
    public void shouldCountCorrectly() {
        assertThat(gameCenterConfigDao.count()).isGreaterThan(0);
    }

    @Test
    public void shouldUpdateCurrentCorrectly() {
        assertThat(gameCenterConfigDao.updateCurrentSeason(configEntity.getId(), 2L)).isEqualTo(1);
    }

}

