package cn.com.duiba.activity.center.api.remoteservice.openredpacket;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.openredpacket.OpenRedPacketRecordDto;
import cn.com.duiba.activity.center.api.enums.OpenRedPacketRecordStatusEnum;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/5/9 11:24
 * @description:
 */
public class RemoteOpenRedPacketRecordServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteOpenRedPacketRecordService remoteOpenRedPacketRecordService;

    @Test
    public void testAdd(){
        OpenRedPacketRecordDto entity = new OpenRedPacketRecordDto();
        entity.setAppId(1L);
        entity.setConfigId(22L);
        entity.setGearId(3);
        entity.setConsumerId(1L);
        entity.setPartnerUserId("test");
        entity.setNeedFriendsNum(4);
        entity.setTotalAmount(400L);
        entity.setShareCode("t22rrww");
        Long recordId = remoteOpenRedPacketRecordService.add(entity, 220L);
        System.out.println(recordId);
        Assert.assertNotNull(recordId);
    }

    @Test
    public void testGetListByCidAndConfigId(){
        List<OpenRedPacketRecordDto> records = remoteOpenRedPacketRecordService.getListByCidAndConfigId(1L, 22L);
        System.out.println(JSON.toJSON(records));
        Assert.assertNotNull(records);
    }

    @Test
    public void testGetUnderWayByCidAndConfigId(){
        OpenRedPacketRecordDto record = remoteOpenRedPacketRecordService.getUnderWayByCidAndConfigId(1L, 22L);
        System.out.println(JSON.toJSON(record));
        Assert.assertNotNull(record);
    }

    @Test
    public void testGetExchangeWaitByCidAndConfigId(){
        List<OpenRedPacketRecordDto> records = remoteOpenRedPacketRecordService.getExchangeWaitByCidAndConfigId(1L, 22L);
        System.out.println(JSON.toJSON(records));
        Assert.assertNotNull(records);
    }

    @Test
    public void testGetById(){
        OpenRedPacketRecordDto record = remoteOpenRedPacketRecordService.getById(1L);
        System.out.println(JSON.toJSON(record));
        Assert.assertNotNull(record);
    }

    @Test
    public void testGetByCode(){
        OpenRedPacketRecordDto record = remoteOpenRedPacketRecordService.getByCode("t22t2");
        System.out.println(JSON.toJSON(record));
        Assert.assertNotNull(record);
    }

    @Test
    public void testModifyStatus(){
        int result = remoteOpenRedPacketRecordService.modifyStatus(2L, OpenRedPacketRecordStatusEnum.EXCHANGE_WAITING);
        System.out.println(JSON.toJSON(result));
        Assert.assertNotNull(result);
    }
}
