package cn.com.duiba.activity.center.api.remoteservice.joingroup;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.joingroup.JoinGroupInfoDto;
import cn.com.duiba.activity.center.api.enums.JoinGroupStatusEnum;
import cn.com.duiba.activity.center.api.enums.YesOrNoEnum;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 18/6/25
 * @description
 */
public class RemoteJoinGroupInfoAppServiceTest extends TransactionalTestCaseBase {

	@Autowired
	private RemoteJoinGroupInfoAppService remoteJoinGroupInfoAppService;

	@Test
	public void testAdd() throws Exception{
		JoinGroupInfoDto joinGroupInfoDto = new JoinGroupInfoDto();
		joinGroupInfoDto.setAppId(1L);
		joinGroupInfoDto.setJoinGroupConfigId(1L);
		joinGroupInfoDto.setJoinGroupItemId(1L);
		joinGroupInfoDto.setGroupNumber(10);
		joinGroupInfoDto.setGroupStatus(JoinGroupStatusEnum.UNDER_WAY.getCode());
		joinGroupInfoDto.setCheatType(YesOrNoEnum.YES.getCode());
		joinGroupInfoDto.setCheatSequence(4);
		String dateStr = "2018-09-18 12:00:00";
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		joinGroupInfoDto.setEndTime(simpleDateFormat.parse(dateStr));

		System.out.println(JSON.toJSON(joinGroupInfoDto));
		Long joinGroupId = remoteJoinGroupInfoAppService.add(joinGroupInfoDto);
		Assert.assertNotNull(joinGroupId);
	}

	@Test
	public void testModifyToSuccess(){
		int a = remoteJoinGroupInfoAppService.modifyToSuccess(2L, 112L);
		System.out.println(a);
		Assert.assertTrue(a > 0);
	}

	@Test
	public void testGetById(){
		JoinGroupInfoDto joinGroupInfoDto = remoteJoinGroupInfoAppService.getById(1L);
		System.out.println(JSON.toJSON(joinGroupInfoDto));
		Assert.assertNotNull(joinGroupInfoDto);
	}

	@Test
	public void testGetByIdList(){
		List<JoinGroupInfoDto> joinGroupInfoDtoList = remoteJoinGroupInfoAppService.getByIdList(Arrays.asList(1L, 2L));
		System.out.println(JSON.toJSON(joinGroupInfoDtoList));
		Assert.assertNotNull(joinGroupInfoDtoList);
	}

	@Test
	public void testGetByItemIdAndStatus(){
		List<JoinGroupInfoDto> joinGroupInfoDtoList = remoteJoinGroupInfoAppService.getByItemIdAndStatus(1L,2L, 1);
		System.out.println(JSON.toJSON(joinGroupInfoDtoList));
		Assert.assertNotNull(joinGroupInfoDtoList);
	}

	@Test
	public void testGetByAppConfigIdAndStatus(){
		List<JoinGroupInfoDto> joinGroupInfoDtoList = remoteJoinGroupInfoAppService.getByAppConfigIdAndStatus(1L, 1L, 1, 1, 3L);
		System.out.println(JSON.toJSON(joinGroupInfoDtoList));
		Assert.assertNotNull(joinGroupInfoDtoList);
	}
}

