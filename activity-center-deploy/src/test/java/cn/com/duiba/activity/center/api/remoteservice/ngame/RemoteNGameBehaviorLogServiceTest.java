package cn.com.duiba.activity.center.api.remoteservice.ngame;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.ngame.NgameBehaviorLogDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by hww on 2018/1/24 下午4:17.
 */
@Transactional(DsConstants.DATABASE_NGAME)
public class RemoteNGameBehaviorLogServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteNgameBehaviorLogService remoteNgameBehaviorLogService;

    @Test
    public void testInsert() {
        NgameBehaviorLogDto log = TestUtils.createRandomBean(NgameBehaviorLogDto.class);
        log.setId(null);
        //数据库字段类型为tinyint，防止随机字段过大
        log.setDeleted(1);
        Long id = remoteNgameBehaviorLogService.insert(log);
        Assert.assertTrue(id != null && id > 0);
    }

    @Test
    public void testFindMD5ByOrderId() {
        NgameBehaviorLogDto log1 = TestUtils.createRandomBean(NgameBehaviorLogDto.class);
        NgameBehaviorLogDto log2 = TestUtils.createRandomBean(NgameBehaviorLogDto.class);
        log1.setId(null);
        log2.setId(null);
        //数据库字段类型为tinyint，防止随机字段过大
        log1.setDeleted(1);
        log2.setDeleted(1);
        Long orderId = (long) (Math.random() * 10000000000L);
        log1.setOrderId(orderId);
        log2.setOrderId(orderId);
        String md5_1 = log1.getMd5();
        String md5_2 = log2.getMd5();
        remoteNgameBehaviorLogService.insert(log1);
        remoteNgameBehaviorLogService.insert(log2);
        List<String> list = remoteNgameBehaviorLogService.findMd5ByOrderId(orderId);
        Assert.assertTrue(CollectionUtils.isNotEmpty(list) && list.size() == 2);
        Assert.assertTrue(list.remove(md5_1));
        Assert.assertTrue(list.remove(md5_2));
    }

}
