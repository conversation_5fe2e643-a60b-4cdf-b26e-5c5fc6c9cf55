package cn.com.duiba.activity.center.biz.remoteservice.ngame;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.ngame.NgameMutiRankingBaseConfigDto;
import cn.com.duiba.activity.center.api.params.NgameMutiRankingBaseConfigQueryParam;
import cn.com.duiba.activity.center.api.remoteservice.ngame.RemoteNgameMutiRankingBaseConfigService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by sty on 12/9/17.
 */
@Transactional(DsConstants.DATABASE_NGAME)
public class RemoteNgameMutiRankingBaseConfigServiceTest  extends TransactionalTestCaseBase {

    @Autowired
    private RemoteNgameMutiRankingBaseConfigService remoteNgameMutiRankingBaseConfigService;


    @Test
    public void testFindByBaseConfigId(){
       Assert.assertNotNull(remoteNgameMutiRankingBaseConfigService.findByBaseConfigId(1L));
    }

    @Test
    public void testDeleteByBaseConfigId(){
        Assert.assertEquals(remoteNgameMutiRankingBaseConfigService.deleteByBaseConfigId(1L)>0,true);
    }

    @Test
    public void testChangeSwitch(){
        Assert.assertEquals(remoteNgameMutiRankingBaseConfigService.switchByBaseConfigId(1L,true)>0,true);
    }

    @Test
    public void testPageSearch(){
        NgameMutiRankingBaseConfigQueryParam param=new NgameMutiRankingBaseConfigQueryParam();
        param.setConfigName("测试");
        param.setPageNum(1);
        param.setPageSize(20);
        Assert.assertEquals(remoteNgameMutiRankingBaseConfigService.countBaseConfigByPage(param)==3,true);
        Assert.assertEquals(remoteNgameMutiRankingBaseConfigService.findBaseConfigByPage(param).size()==3,true);

    }

    @Test
    public void testfindOpenedConfigNum(){
        Assert.assertEquals(remoteNgameMutiRankingBaseConfigService.findOpenedBaseConfigNum()>0,true);
    }

    private NgameMutiRankingBaseConfigDto prepareData(){
        NgameMutiRankingBaseConfigDto ngameMutiRankingBaseConfigDto=new NgameMutiRankingBaseConfigDto();
        ngameMutiRankingBaseConfigDto.setConfigName("test1");
        return  ngameMutiRankingBaseConfigDto;
    }

    @Test
    public void testFindBaseConfigIdByNgameId(){
        remoteNgameMutiRankingBaseConfigService.findBaseConfigIdByNgameId(1L);
    }
}
