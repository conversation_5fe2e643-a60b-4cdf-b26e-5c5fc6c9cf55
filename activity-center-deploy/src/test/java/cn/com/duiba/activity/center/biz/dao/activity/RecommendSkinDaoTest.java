package cn.com.duiba.activity.center.biz.dao.activity;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.recommend.RecommendSkinDao;
import cn.com.duiba.activity.center.biz.entity.activity.RecommendSkinEntity;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by zhengjy on 2017/2/8.
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class RecommendSkinDaoTest extends TransactionalTestCaseBase {
    @Autowired
    RecommendSkinDao recommendSkinDao;

    private RecommendSkinEntity save(){
        RecommendSkinEntity re =new RecommendSkinEntity();
        re.setHtmlContext("xxxxx2");
        re.setShowStatus("OPEN");
        re.setSkinName("wawawa");
        Long l = recommendSkinDao.insert(re);
        re.setId(l);
        return  re;

    }

    @Test
    public void testInsert(){
        Assert.assertTrue(save().getId()>0);
    }

    @Test
    public void testUpdate(){
        Assert.assertTrue(recommendSkinDao.update(save()));
    }

    @Test
    public void testFindList(){
        save();
        List<RecommendSkinEntity> ret= recommendSkinDao.selectList(0,20,new RecommendSkinEntity());
        Assert.assertTrue(ret != null);
    }

    @Test
    public void testFind(){
        Assert.assertTrue(recommendSkinDao.select(save().getId()) != null);
    }

    @Test
    public void testFindCount(){
        Assert.assertTrue(recommendSkinDao.selectCount(save())>0);
    }

    @Test
    public void testDelete(){
        Assert.assertTrue(recommendSkinDao.delete(save().getId()));
    }


}
