package cn.com.duiba.activity.center.api.remoteservice.bargain;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.bargain.BargainActivityInfoDto;
import cn.com.duiba.activity.center.api.enums.DeletedEnum;
import cn.com.duiba.activity.center.api.params.bargain.BargainActivityInfoParam;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.api.bo.page.Page;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> (<EMAIL>)
 * @date 2018/12/7 0007 11:03
 */
@Transactional(value = DsConstants.DATABASE_ACT_COM_CONF)
@Rollback(value = false)
public class RemoteBargainActivityInfoServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteBargainActivityInfoService remoteBargainActivityInfoService;

    @Test
    public void insert() {
        BargainActivityInfoDto pojo = new BargainActivityInfoDto();
        pojo.setActivityRule("dddd");
        pojo.setTitle("dddd");
        pojo.setDeleted(DeletedEnum.UNDELETED.value());
        Long in = remoteBargainActivityInfoService.insert(pojo);
        System.out.println(in);
    }

    @Test
    public void findById() {
        BargainActivityInfoDto in = remoteBargainActivityInfoService.findById(6L);
        System.out.println(JSONObject.toJSONString(in));
    }

    @Test
    public void deleteById() {
        int in = remoteBargainActivityInfoService.deleteById(6L);
        System.out.println(in);
    }

    /**
     * 分页查询砍价活动列表
     * 1.过滤已经删除的
     */
    @Test
    public void pageByParams() {
        BargainActivityInfoParam param = new BargainActivityInfoParam();
        param.setDeleted(DeletedEnum.UNDELETED.value());
        param.setTitle("");
        Page<BargainActivityInfoDto> page = remoteBargainActivityInfoService.pageByParams(param);
        System.out.println(JSONObject.toJSONString(page));
    }

    /**
     * 修改
     * 1.删除也是调用此接口，设置deleted=DeletedEnum.DELETED
     */
    @Test
    public void update() {
        BargainActivityInfoDto pojo = new BargainActivityInfoDto();
        pojo.setId(11L);
        pojo.setDeleted(DeletedEnum.DELETED.value());
        remoteBargainActivityInfoService.update(pojo);
    }

}
