package cn.com.duiba.activity.center.biz.dao.shuqi;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.enums.ShuQiPKRecordTypeEnum;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.shuqipk.PkTeamRecordDao;
import cn.com.duiba.activity.center.biz.entity.shuqipk.PkTeamRecordEntity;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.wolf.utils.DateUtils;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: zhen<PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/1/6 23:21
 * @description:
 */
@Transactional(DsConstants.DATABASE_CUSTOM_RECORD)
@Rollback(value = false)
public class PkTeamRecordDaoTest extends TransactionalTestCaseBase {

    @Autowired
    private PkTeamRecordDao pkTeamRecordDao;

    @Test
    public void testSelectById(){
        PkTeamRecordEntity pkTeamRecordEntity = pkTeamRecordDao.selectById(1000030000003L);
        System.out.println(JSON.toJSON(pkTeamRecordEntity));
        Assert.assertNotNull(pkTeamRecordEntity);
    }

    @Test
    public void testSelectByTeamIdAndDate(){
        PkTeamRecordEntity pkTeamRecordEntity = pkTeamRecordDao.selectByTeamIdAndDate(3L, getRecordDate(DateUtils.daysAddOrSub(new Date(), -1)));
        System.out.println(JSON.toJSON(pkTeamRecordEntity));
        Assert.assertNotNull(pkTeamRecordEntity);
    }

    @Test
    public void testInsert() throws BizException{
        PkTeamRecordEntity teamRecord = new PkTeamRecordEntity();
        teamRecord.setAppId(1L);
        teamRecord.setActivityId(12431L);
        teamRecord.setTeamId(3L);
        teamRecord.setTeamName("单元测试name2");
        teamRecord.setRecordDate(getRecordDate(DateUtils.daysAddOrSub(new Date(), -3)));
        teamRecord.setRecordType(ShuQiPKRecordTypeEnum.TEAM_PK.getCode());
        teamRecord.setReadValue(0L);
        teamRecord.setRelateId(4L);
        teamRecord.setEnemyName("单元测试name3");
        teamRecord.setEnemyReadValue(0L);
        teamRecord.setSyncStatus(0);

        Long result = pkTeamRecordDao.insert(teamRecord);
        System.out.println(result);
        Assert.assertTrue(ObjectUtils.compare(result, 0L) > 0);
    }

    private Long getRecordDate(Date date){
        return DateUtils.getDayStartTime(date).getTime()/1000;
    }

    @Test
    public void testInsertBatch() throws BizException{
        List<PkTeamRecordEntity> entityList = new ArrayList<>();
        PkTeamRecordEntity myTeamRecord = new PkTeamRecordEntity();
        myTeamRecord.setAppId(1L);
        myTeamRecord.setActivityId(12431L);
        myTeamRecord.setTeamId(3L);
        myTeamRecord.setTeamName("单元测试name2");
        myTeamRecord.setRecordDate(getRecordDate(DateUtils.daysAddOrSub(new Date(), -2)));
        myTeamRecord.setRecordType(ShuQiPKRecordTypeEnum.TEAM_PK.getCode());
        myTeamRecord.setReadValue(0L);
        myTeamRecord.setRelateId(4L);
        myTeamRecord.setEnemyName("单元测试name3");
        myTeamRecord.setEnemyReadValue(0L);
        myTeamRecord.setSyncStatus(0);
        entityList.add(myTeamRecord);

        PkTeamRecordEntity enemyTeamRecord = new PkTeamRecordEntity();
        enemyTeamRecord.setAppId(1L);
        enemyTeamRecord.setActivityId(12431L);
        enemyTeamRecord.setTeamId(4L);
        enemyTeamRecord.setTeamName("单元测试name3");
        enemyTeamRecord.setRecordDate(getRecordDate(DateUtils.daysAddOrSub(new Date(), -2)));
        enemyTeamRecord.setRecordType(ShuQiPKRecordTypeEnum.TEAM_PK.getCode());
        enemyTeamRecord.setReadValue(0L);
        enemyTeamRecord.setRelateId(3L);
        enemyTeamRecord.setEnemyName("单元测试name2");
        enemyTeamRecord.setEnemyReadValue(0L);
        enemyTeamRecord.setSyncStatus(0);
        entityList.add(enemyTeamRecord);
        List<PkTeamRecordEntity> result = pkTeamRecordDao.insertBatch(entityList);
        System.out.println(JSON.toJSON(result));
        Assert.assertNotNull(result);
    }

    @Test
    public void testUpdateBatch(){
        List<PkTeamRecordEntity> entityList = new ArrayList<>();
        PkTeamRecordEntity myTeamRecord = new PkTeamRecordEntity();
        myTeamRecord.setId(1000130000003L);
        myTeamRecord.setAppId(1L);
        myTeamRecord.setActivityId(12431L);
        myTeamRecord.setTeamId(3L);
        myTeamRecord.setTeamName("单元测试name2");
        myTeamRecord.setRecordDate(getRecordDate(DateUtils.daysAddOrSub(new Date(), -2)));
        myTeamRecord.setRecordType(ShuQiPKRecordTypeEnum.TEAM_PK.getCode());
        myTeamRecord.setReadValue(51L);
        myTeamRecord.setRelateId(4L);
        myTeamRecord.setEnemyName("单元测试name3");
        myTeamRecord.setEnemyReadValue(51L);
        myTeamRecord.setSyncStatus(0);
        entityList.add(myTeamRecord);

        PkTeamRecordEntity enemyTeamRecord = new PkTeamRecordEntity();
        enemyTeamRecord.setId(1000130010004L);
        enemyTeamRecord.setAppId(1L);
        enemyTeamRecord.setActivityId(12431L);
        enemyTeamRecord.setTeamId(4L);
        enemyTeamRecord.setTeamName("单元测试name3");
        enemyTeamRecord.setRecordDate(getRecordDate(DateUtils.daysAddOrSub(new Date(), -2)));
        enemyTeamRecord.setRecordType(ShuQiPKRecordTypeEnum.TEAM_PK.getCode());
        enemyTeamRecord.setReadValue(51L);
        enemyTeamRecord.setRelateId(3L);
        enemyTeamRecord.setEnemyName("单元测试name2");
        enemyTeamRecord.setEnemyReadValue(51L);
        enemyTeamRecord.setSyncStatus(0);
        entityList.add(enemyTeamRecord);

        int i = pkTeamRecordDao.updateBatch(entityList);
        System.out.println(i);
        Assert.assertTrue(i > 0);
    }

    @Test
    public void testSelectPastByTeamId(){
        List<PkTeamRecordEntity> pkTeamRecordEntityList = pkTeamRecordDao.selectPastByTeamId(3L);
        System.out.println(JSON.toJSON(pkTeamRecordEntityList));
        Assert.assertNotNull(pkTeamRecordEntityList);
    }
}
