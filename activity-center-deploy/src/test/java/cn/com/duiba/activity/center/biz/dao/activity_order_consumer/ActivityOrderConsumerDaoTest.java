package cn.com.duiba.activity.center.biz.dao.activity_order_consumer;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.activity_order.ActivityOrderEntity;
import cn.com.duiba.activity.center.biz.support.TableHelper;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.idmaker.service.api.enums.IDMakerTypeEnums;
import cn.com.duiba.idmaker.service.api.remoteservice.RemoteIDMakerService;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * Created by yansen on 16/9/20.
 */

@Transactional(DsConstants.DATABASE_ACTIVITY_ORDER_CON)
public class ActivityOrderConsumerDaoTest extends TransactionalTestCaseBase {

    @Autowired
    private ActivityOrderConsumerDao activityOrderConsumerDao;
    @Autowired
    private RemoteIDMakerService remoteIDMakerService;

    private ThreadLocal<ActivityOrderEntity> entity = new ThreadLocal<>();

    @Before
    public void testInsert() throws Exception {
        DubboResult<Long> ret = remoteIDMakerService.getNextID(IDMakerTypeEnums.ACTIVITY_ORDER.getType());
        if (!ret.isSuccess()) {
            throw new BizException("ID生成失败");
        }
        ActivityOrderEntity entity = new ActivityOrderEntity();
        entity.setExchangeStatus(2);
        TestUtils.setRandomAttributesForBean(entity,false);
        String suffix = TableHelper.getTableSuffix(entity.getConsumerId());
        String orderNum = ret.getResult() + suffix;
        entity.setSuffix(suffix);
        entity.setOrderNum(orderNum);
        activityOrderConsumerDao.insert(entity);
        this.entity.set(entity);
    }

    @Test
    public void testFind() throws Exception {
        ActivityOrderEntity e = activityOrderConsumerDao.find(getTableSuffixByOrderNum(this.entity.get().getOrderNum()), this.entity.get().getOrderNum());
        Assert.assertEquals(e.getId(), entity.get().getId());
    }

    @Test
    public void updateCreditsSuccess() {
        ActivityOrderEntity e = entity.get();
        String orderNum = e.getOrderNum();
        String suffix = getTableSuffixByOrderNum(e.getOrderNum());
        e.setActivityOptionId(21L);
        e.setActivityOptionName("fasfds");
        e.setActivityOptionType("212");
        e.setFacePrice("1236");
        e.setAppItemId(55L);
        e.setItemId(12266L);
        e.setGid(7L);
        e.setGtype("ddf");
        e.setCouponId(29L);
        e.setConsumeCreditsStatus(e.getConsumeCreditsStatus()+1);
        activityOrderConsumerDao.updateCreditsSuccess(suffix, orderNum, e.getActivityOptionId(), e.getActivityOptionName(), e.getActivityOptionType(), e.getFacePrice(), e.getAppItemId(), e.getItemId(), e.getGid(), e.getGtype(), e.getCouponId(), e.getConsumeCreditsStatus());
        ActivityOrderEntity e1 = activityOrderConsumerDao.find(getTableSuffixByOrderNum(this.entity.get().getOrderNum()), this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    private void assertEquals(ActivityOrderEntity e, ActivityOrderEntity e1){
        TestUtils.assertEqualsReflect(e,e1,false,new String[]{"gmtCreate","gmtModified","suffix","addCreditsDevBizId"});
    }

    @Test
    public void updateCreditsSuccessDowngrade() {
        ActivityOrderEntity e = entity.get();
        String orderNum = e.getOrderNum();
        String suffix = getTableSuffixByOrderNum(e.getOrderNum());
        e.setActivityOptionId(21L);
        e.setActivityOptionName("fasfds");
        e.setActivityOptionType("212");
        e.setFacePrice("1236");
        e.setAppItemId(55L);
        e.setItemId(12266L);
        e.setGid(7L);
        e.setGtype("ddf");
        e.setCouponId(29L);
        e.setConsumeCreditsStatus(22);
        activityOrderConsumerDao.updateCreditsSuccessDowngrade(suffix, orderNum, e.getActivityOptionId(), e.getActivityOptionName(), e.getActivityOptionType(), e.getFacePrice(), e.getAppItemId(), e.getItemId(), e.getGid(), e.getGtype(), e.getCouponId(), e.getConsumeCreditsStatus());
        ActivityOrderEntity e1 = activityOrderConsumerDao.find(getTableSuffixByOrderNum(this.entity.get().getOrderNum()), this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void updateCreditsFail() {
        ActivityOrderEntity e = entity.get();
        String orderNum = e.getOrderNum();
        String suffix = getTableSuffixByOrderNum(e.getOrderNum());
        e.setActivityOptionId(21L);
        e.setActivityOptionName("fasfds");
        e.setActivityOptionType("212");
        e.setFacePrice("1236");
        e.setAppItemId(55L);
        e.setItemId(12266L);
        e.setGid(7L);
        e.setGtype("ddf");
        e.setCouponId(29L);
        e.setError4admin("212");
        e.setError4consumer("yf");
        e.setError4developer("t66");
        e.setConsumeCreditsStatus(e.getConsumeCreditsStatus()+1);
        activityOrderConsumerDao.updateCreditsFail(suffix, orderNum, e.getActivityOptionId(), e.getActivityOptionName(), e.getActivityOptionType(), e.getFacePrice(), e.getAppItemId(), e.getItemId(), e.getGid(), e.getGtype(), e.getCouponId(), e.getError4admin(), e.getError4developer(), e.getError4consumer(), e.getConsumeCreditsStatus());
        ActivityOrderEntity e1 = activityOrderConsumerDao.find(getTableSuffixByOrderNum(this.entity.get().getOrderNum()), this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void updateAddCreditsSuccess() {
        ActivityOrderEntity e = entity.get();
        String orderNum = e.getOrderNum();
        String suffix = getTableSuffixByOrderNum(e.getOrderNum());
        e.setActivityOptionId(21L);
        e.setActivityOptionName("fasfds");
        e.setActivityOptionType("212");
        e.setFacePrice("1236");
        e.setAppItemId(55L);
        e.setItemId(12266L);
        e.setGid(7L);
        e.setGtype("ddf");
        e.setCouponId(29L);
        e.setAddCreditsStatus(22);
        activityOrderConsumerDao.updateAddCreditsSuccess(suffix, orderNum, e.getActivityOptionId(), e.getActivityOptionName(), e.getActivityOptionType(), e.getFacePrice(), e.getAppItemId(), e.getItemId(), e.getGid(), e.getGtype(), e.getCouponId(), e.getAddCreditsStatus());
        ActivityOrderEntity e1 = activityOrderConsumerDao.find(getTableSuffixByOrderNum(this.entity.get().getOrderNum()), this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void updateAddCreditsFail() {
        ActivityOrderEntity e = entity.get();
        String orderNum = e.getOrderNum();
        String suffix = getTableSuffixByOrderNum(e.getOrderNum());
        e.setActivityOptionId(21L);
        e.setActivityOptionName("fasfds");
        e.setActivityOptionType("212");
        e.setFacePrice("1236");
        e.setAppItemId(55L);
        e.setItemId(12266L);
        e.setGid(7L);
        e.setGtype("ddf");
        e.setCouponId(29L);
        e.setError4admin("212");
        e.setError4consumer("yf");
        e.setError4developer("t66");
        e.setAddCreditsStatus(22);
        activityOrderConsumerDao.updateAddCreditsFail(suffix, orderNum, e.getActivityOptionId(), e.getActivityOptionName(), e.getActivityOptionType(), e.getFacePrice(), e.getAppItemId(), e.getItemId(), e.getGid(), e.getGtype(), e.getCouponId(), e.getError4admin(), e.getError4developer(), e.getError4consumer(), e.getAddCreditsStatus());
        ActivityOrderEntity e1 = activityOrderConsumerDao.find(getTableSuffixByOrderNum(this.entity.get().getOrderNum()), this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void addCreditsStatusToProcessing() {
        ActivityOrderEntity e = entity.get();
        String orderNum = e.getOrderNum();
        String suffix = getTableSuffixByOrderNum(e.getOrderNum());
        e.setAddCreditsStatus(e.getAddCreditsStatus()+1);
        activityOrderConsumerDao.addCreditsStatusToProcessing(suffix, orderNum, e.getAddCreditsStatus());
        ActivityOrderEntity e1 = activityOrderConsumerDao.find(getTableSuffixByOrderNum(this.entity.get().getOrderNum()), this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void updateExchangeStatus() {
        ActivityOrderEntity e = entity.get();
        String orderNum = e.getOrderNum();
        String suffix = getTableSuffixByOrderNum(e.getOrderNum());
        e.setExchangeStatus(e.getExchangeStatus()+1);
        activityOrderConsumerDao.updateExchangeStatus(suffix, orderNum, e.getExchangeStatus());
        ActivityOrderEntity e1 = activityOrderConsumerDao.find(getTableSuffixByOrderNum(this.entity.get().getOrderNum()), this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void updateDeveloperBizId() {
        ActivityOrderEntity e = entity.get();
        String orderNum = e.getOrderNum();
        String suffix = getTableSuffixByOrderNum(e.getOrderNum());
        e.setDeveloperBizId("fsd");
        activityOrderConsumerDao.updateDeveloperBizId(suffix, orderNum, e.getDeveloperBizId());
        ActivityOrderEntity e1 = activityOrderConsumerDao.find(getTableSuffixByOrderNum(this.entity.get().getOrderNum()), this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void updateAddDeveloperBizId() {
        ActivityOrderEntity e = entity.get();
        String orderNum = e.getOrderNum();
        String suffix = getTableSuffixByOrderNum(e.getOrderNum());
        e.setAddCreditsDevBizId("fsd");
        activityOrderConsumerDao.updateAddDeveloperBizId(suffix, orderNum, e.getAddCreditsDevBizId());
        ActivityOrderEntity e1 = activityOrderConsumerDao.find(getTableSuffixByOrderNum(this.entity.get().getOrderNum()), this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void updateMainOrderNum() {
        ActivityOrderEntity e = entity.get();
        String orderNum = e.getOrderNum();
        String suffix = getTableSuffixByOrderNum(e.getOrderNum());
        e.setMainOrderNum("fsd");
        activityOrderConsumerDao.updateMainOrderNum(suffix, orderNum, e.getMainOrderNum());
        ActivityOrderEntity e1 = activityOrderConsumerDao.find(getTableSuffixByOrderNum(this.entity.get().getOrderNum()), this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    @Test
    public void countConsumerJoinNum() {
        ActivityOrderEntity e = entity.get();
        String suffix = getTableSuffixByOrderNum(e.getOrderNum());
        int count = activityOrderConsumerDao.countConsumerJoinNum(suffix, e.getConsumerId(), e.getDuibaActivityId(), e.getActivityType());
        Assert.assertTrue(count > 0);
    }

    @Test
    public void findConsumerJoinList() {
        ActivityOrderEntity e = entity.get();
        String suffix = getTableSuffixByOrderNum(e.getOrderNum());
        List<Long> duibaActivityIds = new ArrayList<>();
        duibaActivityIds.add(e.getDuibaActivityId());
        List<ActivityOrderEntity> list = activityOrderConsumerDao.findConsumerJoinList(suffix, e.getConsumerId(), duibaActivityIds, e.getActivityType());
        int count = 0;
        for(ActivityOrderEntity ei : list){
            if(ei.getOrderNum().equals(e.getOrderNum())){
                count++;
            }
        }
        Assert.assertTrue(count == 1);
    }

    @Test
    public void findConsumerDuibaActivity4day() {
        ActivityOrderEntity e = entity.get();
        String suffix = getTableSuffixByOrderNum(e.getOrderNum());
        Date now = new Date();
        String start = DateUtils.getSecondStr(DateUtils.minutesAddOrSub(now, -1));
        String end = DateUtils.getSecondStr(DateUtils.minutesAddOrSub(now, 1));
        ActivityOrderEntity e1 = activityOrderConsumerDao.findConsumerDuibaActivity4day(suffix, e.getConsumerId(), e.getDuibaActivityId(), e.getActivityType(), start, end);

        Assert.assertTrue(e1 != null);
    }

    @Test
    public void findByOrderNums() {
        ActivityOrderEntity e = entity.get();
        String orderNum = e.getOrderNum();
        String suffix = getTableSuffixByOrderNum(e.getOrderNum());
        List<ActivityOrderEntity> list = activityOrderConsumerDao.findByOrderNums(suffix, Collections.singletonList(orderNum));
        assertEquals(list.get(0), e);
    }

    @Test
    public void findOptionIds() {
        ActivityOrderEntity e = entity.get();
        String suffix = getTableSuffixByOrderNum(e.getOrderNum());
        List<Long> list = activityOrderConsumerDao.findOptionIds(e.getConsumerId(), e.getAppId(), e.getActivityType(), suffix);
        Assert.assertTrue(list.contains(e.getActivityOptionId()));
    }

    @Test
    public void updateAddCredits() {
        ActivityOrderEntity e = entity.get();
        String orderNum = e.getOrderNum();
        String suffix = getTableSuffixByOrderNum(e.getOrderNum());
        e.setAddCredits(212L);
        activityOrderConsumerDao.updateAddCredits(suffix, orderNum, e.getAddCredits());
        ActivityOrderEntity e1 = activityOrderConsumerDao.find(getTableSuffixByOrderNum(this.entity.get().getOrderNum()), this.entity.get().getOrderNum());
        assertEquals(e, e1);
    }

    /**
     *
     * @param orderNum
     * @return
     */
    private String getTableSuffixByOrderNum(String orderNum) {
        return orderNum.substring(orderNum.length() - 4, orderNum.length());
    }
}
