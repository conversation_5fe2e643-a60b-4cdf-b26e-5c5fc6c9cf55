package cn.com.duiba.activity.center.biz.service.lotterysquare;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created by Liugq on 2019/1/9.
 */
public class LotterySquareServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private LotterySquareGradientRewardService lotterySquareGradientRewardService;
    @Autowired
    private LotterySquareBonusConfigService lotterySquareBonusConfigService;


    @Test
    public void testBatchDelBonusConfig(){
        List<Long> ids = Lists.newArrayList(3L,5L,6L);
//        Assert.assertTrue(lotterySquareBonusConfigService.batchDelete(ids) > 0);
    }

    @Test
    public void testBatchDelGradientReward(){
        List<Long> ids = Lists.newArrayList(3L,5L,6L);
//        Assert.assertTrue(lotterySquareGradientRewardService.batchDelete(ids) > 0);
    }
}
