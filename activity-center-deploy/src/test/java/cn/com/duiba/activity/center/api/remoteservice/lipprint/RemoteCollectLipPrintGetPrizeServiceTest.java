package cn.com.duiba.activity.center.api.remoteservice.lipprint;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.lipprint.CollectLipPrintGetPrizeDto;
import com.alibaba.fastjson.JSON;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 18/6/25
 * @description
 */
public class RemoteCollectLipPrintGetPrizeServiceTest extends TransactionalTestCaseBase {

	@Resource
	private RemoteCollectLipPrintGetPrizeService remoteCollectLipPrintGetPrizeService;

	@Test
	public void testAdd(){
		CollectLipPrintGetPrizeDto collectLipPrintGetPrizeDto = new CollectLipPrintGetPrizeDto();
		collectLipPrintGetPrizeDto.setAppId(1L);
		collectLipPrintGetPrizeDto.setConsumerId(1234324L);
		collectLipPrintGetPrizeDto.setPartnerUserId("323");
		collectLipPrintGetPrizeDto.setPhone("13423434345");
		remoteCollectLipPrintGetPrizeService.add(collectLipPrintGetPrizeDto);
	}

	@Test
	public void testGetByPhone(){
		CollectLipPrintGetPrizeDto collectLipPrintGetPrizeDto = remoteCollectLipPrintGetPrizeService.findByPhone("13423434345");
		System.out.println(JSON.toJSONString(collectLipPrintGetPrizeDto));
	}
}
