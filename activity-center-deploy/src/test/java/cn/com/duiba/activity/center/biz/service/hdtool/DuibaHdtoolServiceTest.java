package cn.com.duiba.activity.center.biz.service.hdtool;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.hdtool.DuibaHdtoolDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/24.
 */
@Transactional(DsConstants.DATABASE_CREDITS)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaHdtoolServiceTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaHdtoolService duibaHdtoolService;

    private DuibaHdtoolDto duibaHdtoolDto;

    @Before
    public void testInsert(){
        DuibaHdtoolDto e=new DuibaHdtoolDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setSwitches(0);
        duibaHdtoolService.insert(e);
        duibaHdtoolDto =e;
    }

    @Test
    public void testFind() {
        DuibaHdtoolDto e= duibaHdtoolDto;
        DuibaHdtoolDto e1=duibaHdtoolService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindAutoOff() {
        DuibaHdtoolDto e=new DuibaHdtoolDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-10000000l));
        duibaHdtoolService.insert(e);
        Assert.assertTrue(duibaHdtoolService.findAutoOff().size()>0);
    }

    @Test
    public void testFindDuibaHdToolsList() {
        DuibaHdtoolDto e= duibaHdtoolDto;
        Map<String,Object> params=new HashMap<>();
        params.put("activityActionType",e.getActivityActionType());
        params.put("max",10);
        params.put("offset",0);
        params.put("title",e.getTitle());
        Assert.assertTrue(duibaHdtoolService.findDuibaHdToolsList(params).size()>0);
    }

    @Test
    public void testCountDuibaHdToolsList() {
        DuibaHdtoolDto e= duibaHdtoolDto;
        Map<String,Object> params=new HashMap<>();
        params.put("title",e.getTitle());
        params.put("activityActionType",e.getActivityActionType());
        Assert.assertTrue(duibaHdtoolService.countDuibaHdToolsList(params)>0);
    }

    @Test
    public void testFindAllDuibaHdTools() {
        DuibaHdtoolDto e=new DuibaHdtoolDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-10000000l));
        duibaHdtoolService.insert(e);
        Assert.assertTrue(duibaHdtoolService.findAllDuibaHdTools(1l).size()>0);
    }

    @Test
    public void testFindAllByIds() {
        DuibaHdtoolDto e= duibaHdtoolDto;
        assertDO(e,duibaHdtoolService.findAllByIds(Arrays.asList(e.getId())).get(0));
    }

    @Test
    public void testFindExtraInfoById() {
        DuibaHdtoolDto e= duibaHdtoolDto;
        Assert.assertEquals(e.getFreeRule(),duibaHdtoolService.findExtraInfoById(e.getId()).getFreeRule());
    }

    @Test
    public void testGetCountDuibaHdTool() {
        Map<String,Object> params=new HashMap<>();
        params.put("activityName", duibaHdtoolDto.getTitle());
        Assert.assertTrue(duibaHdtoolService.getCountDuibaHdTool(params)>0);
    }

    @Test
    public void testFindDuibaToolList() {
        Map<String,Object> params=new HashMap<>();
        params.put("activityName", duibaHdtoolDto.getTitle());
        params.put("max",10);
        params.put("offset",0);
        Assert.assertTrue(duibaHdtoolService.findDuibaToolList(params).size()>0);
    }

    @Test
    public void testUpdateAutoOffDateNull() {
        duibaHdtoolService.updateAutoOffDateNull(duibaHdtoolDto.getId());
        Assert.assertNull(duibaHdtoolService.find(duibaHdtoolDto.getId()).getAutoOffDate());
    }

    @Test
    public void testDeleteById() {
        duibaHdtoolService.deleteById(duibaHdtoolDto.getId());
        Assert.assertTrue(duibaHdtoolService.find(duibaHdtoolDto.getId()).getDeleted());
    }

    @Test
    public void testUpdate() {
        DuibaHdtoolDto e=new DuibaHdtoolDto(duibaHdtoolDto.getId());
        e.setTitle("haha");
        duibaHdtoolService.update(e);
        Assert.assertTrue(duibaHdtoolService.find(duibaHdtoolDto.getId()).getTitle().equals("haha"));
    }

    @Test
    public void testUpdateStatus() {
        duibaHdtoolService.updateStatus(duibaHdtoolDto.getId(),42);
        Assert.assertEquals(duibaHdtoolService.find(duibaHdtoolDto.getId()).getStatus(),(Integer) 42);
    }

    @Test
    public void testUpdateActivityCategory() {
        Long aid = 1232111L;
        duibaHdtoolService.updateActivityCategory(duibaHdtoolDto.getId(),aid);
        DuibaHdtoolDto d = duibaHdtoolService.find(duibaHdtoolDto.getId());
        Assert.assertEquals(d.getActivityCategoryId(), aid);
    }

    @Test
    public void testFindTag() {
        String tag = duibaHdtoolService.findTag(duibaHdtoolDto.getId());
        Assert.assertEquals(tag,duibaHdtoolDto.getTag());
    }

    private void assertDO(DuibaHdtoolDto e, DuibaHdtoolDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaHdtoolDto e, DuibaHdtoolDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","tag","activityActionType",}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

}
