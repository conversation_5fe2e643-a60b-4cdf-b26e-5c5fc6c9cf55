package cn.com.duiba.activity.center.biz.dao.hdtool;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.hdtool.HdtoolStockManualChangeEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/25.
 */
@Transactional(DsConstants.DATABASE_CREDITS)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class HdtoolStockManualChangeDaoTest extends TransactionalTestCaseBase {

    @Resource
    private HdtoolStockManualChangeDao hdtoolStockManualChangeDao;

    @Test
    public void testInsert() {
        HdtoolStockManualChangeEntity e=new HdtoolStockManualChangeEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        hdtoolStockManualChangeDao.insert(e);
    }

}
