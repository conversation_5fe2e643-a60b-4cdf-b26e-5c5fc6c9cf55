package cn.com.duiba.activity.center.biz.dao.bet;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.enums.AttributionTypeEnum;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.bet.BetActGroupEntity;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 * @date 2018/04/28
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class BetActGroupDaoTest extends TransactionalTestCaseBase {
    @Autowired
    private BetActGroupDao betActGroupDao;
    private BetActGroupEntity betActGroupEntity;

    @Before
    public void setup() {
        betActGroupEntity = new BetActGroupEntity();

        betActGroupEntity.setTitle("test");
        betActGroupEntity.setId(betActGroupDao.insert(betActGroupEntity));
    }

    @Test
    public void shouldInsertCorrectly() {
        assertThat(betActGroupEntity.getId()).isNotNull().isGreaterThan(0);
    }

    @Test
    public void shouldUpdateCorrectly() {
        betActGroupEntity.setTitle("test2");

        assertThat(betActGroupDao.update(betActGroupEntity)).isEqualTo(1);
    }

    @Test
    public void shouldListCorrectly() {
        List<BetActGroupEntity> betActGroupEntityList = betActGroupDao.list(1L,AttributionTypeEnum.DEVELOPER,1, 1);
        assertThat(betActGroupEntityList).isNotNull();
        assertThat(betActGroupEntityList.size()).isEqualTo(1);
    }

    @Test
    public void shouldFindByIdCorrectly() {
        BetActGroupEntity entity = betActGroupDao.findById(betActGroupEntity.getId());

        assertThat(entity).isNotNull();
        assertThat(entity.getTitle()).isEqualTo(betActGroupEntity.getTitle());
    }

    @Test
    public void shouldDelete() {
        Integer count = betActGroupDao.delete(betActGroupEntity.getId());

        assertThat(count).isEqualTo(1);
    }

    @Test
    public void shouldCount() {
        Integer count = betActGroupDao.count(AttributionTypeEnum.DUIBA);

        assertThat(count).isGreaterThan(0);
    }
}
