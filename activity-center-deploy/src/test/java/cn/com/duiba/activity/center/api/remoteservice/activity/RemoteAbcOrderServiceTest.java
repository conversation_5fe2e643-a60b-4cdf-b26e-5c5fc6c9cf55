package cn.com.duiba.activity.center.api.remoteservice.activity;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity.AbcOrderCouponDto;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.List;

import static org.junit.Assert.assertTrue;

@Rollback(value = false)
public class RemoteAbcOrderServiceTest extends TransactionalTestCaseBase{


	@Autowired
	private RemoteAbcOrderCouponService remoteAbcOrderCouponService;



	@Test
	@Rollback(false)
	public void insert() {

		List<AbcOrderCouponDto> abcOrderCouponDtos = Lists.newArrayList();
		AbcOrderCouponDto a1 = new AbcOrderCouponDto();
		a1.setCouponCode("1");
		a1.setOrderId(1L);
		abcOrderCouponDtos.add(a1);
		AbcOrderCouponDto a2 = new AbcOrderCouponDto();
		a2.setCouponCode("2");
		a2.setOrderId(2L);
		abcOrderCouponDtos.add(a2);
		remoteAbcOrderCouponService.insertBatch(abcOrderCouponDtos);
	}

	@Test
	public void testIncrConsumerWinOptionNum() {
		final AbcOrderCouponDto orderId = remoteAbcOrderCouponService.getOrderId(1L);
		System.out.printf(orderId.getCouponCode());
		assertTrue(orderId!=null);
	}

}
