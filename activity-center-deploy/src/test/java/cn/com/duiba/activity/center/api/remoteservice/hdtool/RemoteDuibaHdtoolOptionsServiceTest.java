package cn.com.duiba.activity.center.api.remoteservice.hdtool;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.hdtool.DuibaHdtoolOptionsDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by hww on 2018/3/30 下午3:59.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class RemoteDuibaHdtoolOptionsServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteDuibaHdtoolOptionsService remoteDuibaHdtoolOptionsService;

    @Test
    public void test() {
        List<DuibaHdtoolOptionsDto> a = remoteDuibaHdtoolOptionsService.findOptionsByHdToolIds(Lists.newArrayList(12175L, 12172L), "collectGoods");
        Assert.assertTrue(a.size() > 0);
    }


}
