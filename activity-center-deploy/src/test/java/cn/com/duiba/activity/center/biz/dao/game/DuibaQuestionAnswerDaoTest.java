package cn.com.duiba.activity.center.biz.dao.game;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.AddActivityEntity;
import cn.com.duiba.activity.center.biz.entity.game.DuibaQuestionAnswerEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/23.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaQuestionAnswerDaoTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaQuestionAnswerDao duibaQuestionAnswerDao;

    private DuibaQuestionAnswerEntity duibaQuestionAnswerDto;

    @Before
    public void testInsert(){
        DuibaQuestionAnswerEntity e=new DuibaQuestionAnswerEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        duibaQuestionAnswerDao.insert(e);
        duibaQuestionAnswerDto =e;
    }

    @Test
    public void testFind() {
        DuibaQuestionAnswerEntity e= duibaQuestionAnswerDto;
        DuibaQuestionAnswerEntity e1=duibaQuestionAnswerDao.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testUpdate() {
        duibaQuestionAnswerDto.setBanner("test");
        duibaQuestionAnswerDao.update(duibaQuestionAnswerDto);
        Assert.assertEquals(duibaQuestionAnswerDto.getBanner(),duibaQuestionAnswerDao.find(duibaQuestionAnswerDto.getId()).getBanner());
        DuibaQuestionAnswerEntity a = new DuibaQuestionAnswerEntity();
        a.setId(2546l);
        a.setTag("233");
        a.setTitle("3434");
        duibaQuestionAnswerDao.update(a);
    }

    @Test
    public void testFindAutoOff() {
        DuibaQuestionAnswerEntity e=new DuibaQuestionAnswerEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-1000000l));
        duibaQuestionAnswerDao.insert(e);
        Assert.assertTrue(duibaQuestionAnswerDao.findAutoOff().size()>0);
    }

    @Test
    public void testUpdateStatus() {
        DuibaQuestionAnswerEntity e= duibaQuestionAnswerDto;
        Assert.assertTrue(duibaQuestionAnswerDao.updateStatus(e.getId(),3)>0);
    }

    @Test
    public void testFindByIds() {
        DuibaQuestionAnswerEntity e= duibaQuestionAnswerDto;
        List<DuibaQuestionAnswerEntity> list=duibaQuestionAnswerDao.findByIds(Arrays.asList(e.getId()));
        assertDO(e,list.get(0));
    }

    @Test
    public void testFindExtraInfoById() {
        DuibaQuestionAnswerEntity e= duibaQuestionAnswerDto;
        Assert.assertEquals(duibaQuestionAnswerDao.findExtraInfoById(e.getId()).getFreeRule(),e.getFreeRule());
    }

    @Test
    public void testFindAllByIds() {
        DuibaQuestionAnswerEntity e= duibaQuestionAnswerDto;
        List<DuibaQuestionAnswerEntity> list=duibaQuestionAnswerDao.findAllByIds(Arrays.asList(e.getId()));
        assertDO(e,list.get(0));
    }

    @Test
    public void testFindAllQuestion() {
        DuibaQuestionAnswerEntity e=new DuibaQuestionAnswerEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-1000000l));
        duibaQuestionAnswerDao.insert(e);
        List<AddActivityEntity> list=duibaQuestionAnswerDao.findAllQuestion(null);
        boolean isfind=false;
        for(AddActivityEntity a:list){
            if(a.getId().equals(e.getId())) {
                isfind = true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindByPage() {
        Assert.assertTrue(duibaQuestionAnswerDao.findByPage(0,10).size()>0);
        Map<String,Object> params=new HashMap<>();
        params.put("max",10);
        params.put("offset",0);
        Assert.assertTrue(duibaQuestionAnswerDao.findByPage(params).size()>0);
    }

    @Test
    public void testFindPageCount() {
        Assert.assertTrue(duibaQuestionAnswerDao.findPageCount()>0);
        Map<String,Object> params=new HashMap<>();
        params.put("title", duibaQuestionAnswerDto.getTitle());
        Assert.assertTrue(duibaQuestionAnswerDao.findPageCount(params)>0);
    }

    @Test
    public void testDelete() {
        duibaQuestionAnswerDao.delete(duibaQuestionAnswerDto.getId());
        Assert.assertTrue(duibaQuestionAnswerDao.find(duibaQuestionAnswerDto.getId()).getDeleted());
    }

    @Test
    public void testUpdateAutoOffDateNull() {
        DuibaQuestionAnswerEntity e= duibaQuestionAnswerDto;
        duibaQuestionAnswerDao.updateAutoOffDateNull(e.getId());
        Assert.assertNull(duibaQuestionAnswerDao.find(e.getId()).getAutoOffDate());
    }

    @Test
    public void testUpdateSwitches() {
        DuibaQuestionAnswerEntity e= duibaQuestionAnswerDto;
        duibaQuestionAnswerDao.updateSwitches(e.getId(),42l);
        Assert.assertEquals((Integer)42,duibaQuestionAnswerDao.find(e.getId()).getSwitches());
    }

    private void assertDO(DuibaQuestionAnswerEntity e, DuibaQuestionAnswerEntity e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaQuestionAnswerEntity e, DuibaQuestionAnswerEntity e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","tag",}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }


}
