package cn.com.duiba.activity.center.api.remoteservice.bet;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.bet.BetActGroupDto;
import cn.com.duiba.activity.center.api.enums.AttributionTypeEnum;
import cn.com.duiba.activity.center.api.tool.Page;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> (<EMAIL>)
 * @date 2018/10/16 0016 14:37
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class RemoteBackendBetActGroupServiceTest extends TransactionalTestCaseBase {
    @Autowired
    RemoteBackendBetActGroupService remoteBackendBetActGroupService;

    @Test
    public void list(){
        Page<BetActGroupDto> betConfigDtoList = remoteBackendBetActGroupService.list(1,10);

        System.out.println(JSON.toJSON(betConfigDtoList));
    }

    @Test
    public void getListByAppIdIdAndType(){
        List<BetActGroupDto> betConfigDtoList = remoteBackendBetActGroupService.getListByAppIdIdAndType(1L,AttributionTypeEnum.PK_H5);

        System.out.println(JSON.toJSON(betConfigDtoList));
    }


    @Test
    public void list2(){
        Page<BetActGroupDto> betConfigDtoList = remoteBackendBetActGroupService.listByType(1L,AttributionTypeEnum.DEVELOPER,1,10);

        System.out.println(JSON.toJSON(betConfigDtoList));
    }


}
