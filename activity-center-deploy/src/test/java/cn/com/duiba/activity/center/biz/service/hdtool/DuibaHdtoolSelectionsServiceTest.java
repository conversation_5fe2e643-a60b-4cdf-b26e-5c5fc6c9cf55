package cn.com.duiba.activity.center.biz.service.hdtool;

import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.hdtool.DuibaHdtoolSelectionsDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

import com.google.common.collect.Lists;

/** 
 * ClassName:DuibaHdtoolSelectionsServiceTest.java <br/>
 * <AUTHOR> 
 * @date 创建时间：2017年11月9日 下午2:13:06 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaHdtoolSelectionsServiceTest extends TransactionalTestCaseBase {
	@Resource
	private DuibaHdtoolSelectionsService duibaHdtoolSelectionsService;
	Long duibaHdtoolId = 10L;

	@Before
	public void testInsert(){
		DuibaHdtoolSelectionsDto dt=new DuibaHdtoolSelectionsDto();
		dt.setDuibaHdtoolId(duibaHdtoolId);
		dt.setPosition(1);
		dt.setContent("selec1");
		DuibaHdtoolSelectionsDto dt1=new DuibaHdtoolSelectionsDto();
		dt1.setPosition(2);
		dt1.setContent("selec2");
		dt1.setDuibaHdtoolId(duibaHdtoolId);
		List<DuibaHdtoolSelectionsDto> duibaHdtoolSelections = Lists.newArrayList();
		duibaHdtoolSelections.add(dt);
		duibaHdtoolSelections.add(dt1);
		duibaHdtoolSelectionsService.insert(duibaHdtoolId, duibaHdtoolSelections);
	}
	
	@Test
	public void testFindAllByHdtoolId() {
		List<DuibaHdtoolSelectionsDto> list = duibaHdtoolSelectionsService.findAllByHdtoolId(duibaHdtoolId);
		for(DuibaHdtoolSelectionsDto dto :list){
			duibaHdtoolSelectionsService.find(dto.getId());
		}
		Assert.assertTrue(list.size()>0);
	}
}
