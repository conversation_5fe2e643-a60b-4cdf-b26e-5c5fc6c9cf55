package cn.com.duiba.activity.center.biz.dao.game;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.game.QuestionStockConsumeEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/2.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaQuestionStockConsumeDaoTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaQuestionStockConsumeDao duibaQuestionStockConsumeDao;


    @Test
    public void test() {
        QuestionStockConsumeEntity e=new QuestionStockConsumeEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionStockConsumeDao.insert(e);
        QuestionStockConsumeEntity e1=duibaQuestionStockConsumeDao.findByBizId(e.getBizId(),e.getAction());
        Assert.assertTrue(e.getId().equals(e1.getId()));
    }

}
