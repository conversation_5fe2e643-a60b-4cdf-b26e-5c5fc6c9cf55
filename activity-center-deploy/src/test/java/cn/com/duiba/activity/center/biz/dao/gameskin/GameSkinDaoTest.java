/**
 * Project Name:activity-center-deploy
 * File Name:GameSkinDaoTest.java
 * Package Name:cn.com.duiba.activity.center.biz.dao.gameskin
 * Date:2016年9月29日下午2:14:23
 * Copyright (c) 2016, duiba.com.cn All Rights Reserved.
 *
*/

package cn.com.duiba.activity.center.biz.dao.gameskin;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.gameskin.GameSkinEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * ClassName:GameSkinDaoTest <br/>
 * Date:     2016年9月29日 下午2:14:23 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)
// 这行注释用于决定使用哪个事务管理器以支持自动回滚
public class GameSkinDaoTest extends TransactionalTestCaseBase {

    @Resource
    private GameSkinDao    gameSkinDao;

    private GameSkinEntity entity;

    @Before
    public void testInsert() {
        entity = new GameSkinEntity();
        TestUtils.setRandomAttributesForBean(entity, false);
        entity.setType((byte) 1);
        entity.setStatus((byte) 1);
        entity.setSort((byte) 1);
        this.gameSkinDao.insert(entity);
    }

    @Test
    public void testSelect() {
        GameSkinEntity e = this.gameSkinDao.selectById(entity.getId());
        assertDO(e, entity);
    }

    @Test
    public void testSelectList() {
        List<Byte> types = Lists.newArrayList((byte)1,(byte)2);
        List<GameSkinEntity> list = this.gameSkinDao.selectByPage(0, 20,types);
        Assert.assertTrue(list.size() > 0);
        for (GameSkinEntity e : list) {
            if (e.getId().longValue() == entity.getId().longValue()) {
                Assert.assertEquals(e.getName(), entity.getName());
                Assert.assertEquals(e.getStatus(), entity.getStatus());
            }
        }
    }

    @Test
    public void testSelectByType() {
        List<GameSkinEntity> list = this.gameSkinDao.selectByType(entity.getType());
        for (GameSkinEntity e : list) {
            Assert.assertTrue(e.getType().byteValue() == entity.getType().byteValue());
            if (e.getId().longValue() == entity.getId().longValue()) {
                assertDO(e, entity);
            }
        }
    }

    @Test
    public void testUpdate() {
        GameSkinEntity e = new GameSkinEntity();
        TestUtils.setRandomAttributesForBean(e, false);
        e.setId(entity.getId());
        e.setType((byte) 2);
        e.setStatus((byte) 2);
        e.setSort((byte) 2);
        this.gameSkinDao.update(e);

        GameSkinEntity rs = this.gameSkinDao.selectById(entity.getId());
        assertDO(rs, e);
    }

    @Test
    public void testSelectCount() {
        List<Byte> types = Lists.newArrayList((byte)1,(byte)2);
        Long count = this.gameSkinDao.selectCount(entity.getName(),types);
        Assert.assertTrue(count > 0L);
    }

    private void assertDO(GameSkinEntity e, GameSkinEntity e1) {
        assertDO(e, e1, null);

    }

    private void assertDO(GameSkinEntity e, GameSkinEntity e1, String[] exculdeFields) {
        List<String> exculdeFieldsList = new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[] { "gmtCreate", "gmtModified","dom","skinImage","timeOut","shareInfo" }));
        if (exculdeFields != null && exculdeFields.length > 0) {
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1, false, exculdeFieldsList.toArray(new String[exculdeFieldsList.size() + 1]));// 时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr(e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr(e1.getGmtModified()));
    }
}

