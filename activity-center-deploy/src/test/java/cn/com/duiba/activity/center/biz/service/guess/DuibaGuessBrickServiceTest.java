package cn.com.duiba.activity.center.biz.service.guess;

import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.guess.DuibaGuessBrickDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class DuibaGuessBrickServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaGuessBrickService duibaGuessBrickService;
	
	private DuibaGuessBrickDto info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaGuessBrickDto.class);
		duibaGuessBrickService.insert(info);
	}
	
	@Test
	public void findTest(){
		DuibaGuessBrickDto e = duibaGuessBrickService.find(info.getId());
		Assert.assertNotNull(e);
	}

	@Test
    public void getBrickContentByIdTest(){
		String content = duibaGuessBrickService.getBrickContentById(info.getId());
		Assert.assertNotNull(content);
    }

	@Test
    public void findNoContentTest(){
    	DuibaGuessBrickDto e = duibaGuessBrickService.findNoContent(info.getId());
    	Assert.assertNotNull(e);
    }

	@Test
    public void getBrickPrizeContentByIdTest(){
    	String prizeContent = duibaGuessBrickService.getBrickPrizeContentById(info.getId());
		Assert.assertNotNull(prizeContent);
    }

	@Test
    public void update4AdminTest(){
    	DuibaGuessBrickDto e = TestUtils.createRandomBean(DuibaGuessBrickDto.class);
    	duibaGuessBrickService.update4Admin(info.getId(), e.getTitle(), e.getContent(), e.getPrizeContent(), e.getMd5());
    	String title = duibaGuessBrickService.find(info.getId()).getTitle();
    	Assert.assertTrue(title.equals(e.getTitle()));
    }

	@Test
    public void findByTitleTest(){
    	DuibaGuessBrickDto e = duibaGuessBrickService.findByTitle(info.getTitle());
    	Assert.assertNotNull(e);
    }

	@Test
    public void openTest(){
    	duibaGuessBrickService.open(info.getId());
    	int status = duibaGuessBrickService.find(info.getId()).getStatus();
    	Assert.assertTrue(status == 1);
    }

	@Test
    public void disableTest(){
    	duibaGuessBrickService.disable(info.getId());
    	int status = duibaGuessBrickService.find(info.getId()).getStatus();
    	Assert.assertTrue(status == 0);
    }

	@Test
    public void findPageTest(){
    	DuibaGuessBrickDto e = TestUtils.createRandomBean(DuibaGuessBrickDto.class);
    	e.setDeleted(false);
    	duibaGuessBrickService.insert(e);
    	List<DuibaGuessBrickDto> list = duibaGuessBrickService.findPage(0, 10);
    	Assert.assertTrue(list.size() > 0);
    }

	@Test
    public void findPageCountTest(){
    	DuibaGuessBrickDto e = TestUtils.createRandomBean(DuibaGuessBrickDto.class);
    	e.setDeleted(false);
    	duibaGuessBrickService.insert(e);
    	long count = duibaGuessBrickService.findPageCount();
    	Assert.assertTrue(count > 0);
    }

	@Test
    public void findAllTest(){
    	DuibaGuessBrickDto e = TestUtils.createRandomBean(DuibaGuessBrickDto.class);
    	e.setStatus(1);
    	duibaGuessBrickService.insert(e);
    	List<DuibaGuessBrickDto> list = duibaGuessBrickService.findAll();
    	Assert.assertTrue(list.size() > 0);
    }
}
