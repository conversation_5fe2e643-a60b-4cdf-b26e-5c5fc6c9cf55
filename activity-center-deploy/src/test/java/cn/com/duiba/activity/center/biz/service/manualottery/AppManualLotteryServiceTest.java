package cn.com.duiba.activity.center.biz.service.manualottery;

import java.util.Date;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.manual.AppManualLotteryDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.service.manual.AppManualLotteryService;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/16.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class AppManualLotteryServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private AppManualLotteryService    appManualLotteryService;

    private AppManualLotteryDto info;

    @Before
    public void insertTest() {
    	AppManualLotteryDto e = new AppManualLotteryDto(true);
        TestUtils.setRandomAttributesForBean(e, false);
        info = e;
        appManualLotteryService.insert(e);
    }

    @Test
    public void findTest() {
        AppManualLotteryDto infoTest = appManualLotteryService.find(info.getId());
        assertDO(info, infoTest);
    }

    @Test
    public void scanOverManualLotteryTest() {
        AppManualLotteryDto e = new AppManualLotteryDto(true);
        TestUtils.setRandomAttributesForBean(e, false);
        e.setOverDate(new Date(System.currentTimeMillis() - 86400 * 2 * 1000));
        appManualLotteryService.insert(e);
        List<AppManualLotteryDto> list = appManualLotteryService.scanOverManualLottery();
        Assert.assertTrue(list.size() > 0);
        boolean isFind = false;
        for (AppManualLotteryDto appDO : list) {
            if (appDO.getId().equals(e.getId())) {
                assertDO(e, appDO);
                isFind = true;
            }
        }
        Assert.assertTrue(isFind);

    }

    @Test
    public void updateManualLotteryTest() {
        AppManualLotteryDto e = new AppManualLotteryDto(info.getId());
        TestUtils.setRandomAttributesForBean(e, false);
        appManualLotteryService.updateManualLottery(e);
        AppManualLotteryDto e1 = appManualLotteryService.find(e.getId());
        Assert.assertEquals(e.getIntroduction(), e1.getIntroduction());
        Assert.assertEquals(e.getFreeLimit(), e1.getFreeLimit());
    }

    @Test
    public void updateTest() {
        AppManualLotteryDto e = new AppManualLotteryDto(info.getId());
        TestUtils.setRandomAttributesForBean(e, false);
        appManualLotteryService.update(e);
        AppManualLotteryDto e1 = appManualLotteryService.find(e.getId());
        assertDO(e, e1);
    }

    private void assertDO(AppManualLotteryDto e, AppManualLotteryDto e1) {
        String[] exceptFields = new String[] { "gmtCreate", "gmtModified", "overDate" };
        TestUtils.assertEqualsReflect(e, e1, false, exceptFields);// 时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr(e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr(e1.getGmtModified()));
    }

}
