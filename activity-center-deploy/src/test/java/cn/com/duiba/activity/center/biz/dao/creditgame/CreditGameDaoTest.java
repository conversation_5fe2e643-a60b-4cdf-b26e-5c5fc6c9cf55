package cn.com.duiba.activity.center.biz.dao.creditgame;
import java.util.Date;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.creditgame.CreditGameEntity;


/**
 * 积分游戏DAO单元测试
 * <AUTHOR>
 * @since 2016-09-05
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)
public class CreditGameDaoTest extends TransactionalTestCaseBase {
    @Autowired
    private CreditGameDao creditGameDao;

    private CreditGameEntity genCreditGameEntity(){
        CreditGameEntity creditGameEntity=new CreditGameEntity();
        creditGameEntity.setActivityActionType((byte)1);
        creditGameEntity.setCreditGameType((byte)1);
        creditGameEntity.setCreditGameTitle("积分游戏Title");
        creditGameEntity.setCreatePerson("创建人");
        creditGameEntity.setCreditGameActivityCategoryId(1233444l);
        creditGameEntity.setCreditGameAppCount(100l);
        creditGameEntity.setCreditGameAutoOffDate(new Date());
        creditGameEntity.setCreditGameAwardConfig("CreditGameAwardConfig");
        creditGameEntity.setCreditGameAwardPosition("CreditGameAwardPosition");
        creditGameEntity.setCreditGameBannerImage("CreditGameBannerImage");
        creditGameEntity.setCreditGameBetConfig("CreditGameBetConfig");
        creditGameEntity.setCreditGameCreditsPrice(100l);
        creditGameEntity.setCreditGameCustomTag("CreditGameCustomTag");
        creditGameEntity.setCreditGameDrawLimit(101l);
        creditGameEntity.setCreditGameDrawScope("1111111111");
        creditGameEntity.setCreditGameDuibaPrice(10l);
        creditGameEntity.setCreditGameFreeLimit(100l);
        creditGameEntity.setCreditGameFreeScope("1111111111");
        creditGameEntity.setCreditGameLotteryCount(100l);
        creditGameEntity.setCreditGameRecommendImage("CreditGameRecommendImage");
        creditGameEntity.setCreditGameRuleScript("ruleScripts");
        creditGameEntity.setCreditGameSmallImage("CreditGameSmallImage");
        creditGameEntity.setCreditGameStatus((byte)1);
        creditGameEntity.setCreditGameSwitches(9l);
        creditGameEntity.setCreditGameValveConfig("CreditGameValveConfig");
        creditGameEntity.setCreditGameWhiteImage("CreditGameWhiteImage");
        creditGameEntity.setGmtCreate(new Date());
        creditGameEntity.setGmtModified(new Date());
        creditGameEntity.setRemarks("Remarks");

        creditGameEntity.setCreditGameRule("活动规则");
        creditGameEntity.setCreditGameFreeRule("免费参与活动规则");
        creditGameEntity.setCreditGameDesc("其他说明");
        creditGameEntity.setCreditGameExtDesc("扩展说明");

        creditGameEntity.setDeleted((byte)0);

        return creditGameEntity;
    }
    @Test
    public void testInsert(){
        CreditGameEntity creditGameEntity=genCreditGameEntity();
        Assert.assertNotNull(creditGameEntity);

        int insertNum=creditGameDao.insert(creditGameEntity);
        Assert.assertTrue(insertNum==1);
        Assert.assertTrue(null!=creditGameEntity.getId()&&creditGameEntity.getId()>0);

        CreditGameEntity queryCreditGameEntity= creditGameDao.queryById(creditGameEntity.getId());
        Assert.assertNotNull(queryCreditGameEntity);
        int deleteNum=creditGameDao.delete(queryCreditGameEntity.getId());
        Assert.assertTrue(deleteNum==1);
    }

    @Test
    public void testQueryById(){
        CreditGameEntity creditGameEntity=genCreditGameEntity();
        Assert.assertNotNull(creditGameEntity);

        int insertNum=creditGameDao.insert(creditGameEntity);
        Assert.assertTrue(insertNum==1);
        Assert.assertTrue(null!=creditGameEntity.getId()&&creditGameEntity.getId()>0);

        CreditGameEntity queryCreditGameEntity= creditGameDao.queryById(creditGameEntity.getId());
        Assert.assertNotNull(queryCreditGameEntity);
        int deleteNum=creditGameDao.delete(queryCreditGameEntity.getId());
        Assert.assertTrue(deleteNum==1);
    }

    /*
    @Test
    public void testQuery(){
        CreditGameEntity creditGameEntity=genCreditGameEntity();
        Assert.assertNotNull(creditGameEntity);
        int insertNum=creditGameDao.insert(creditGameEntity);
        Assert.assertTrue(insertNum==1);
        Assert.assertTrue(null!=creditGameEntity.getId()&&creditGameEntity.getId()>0);
        creditGameEntity.setGmtCreate(null);
        creditGameEntity.setGmtModified(null);

        List<CreditGameEntity> creditGameEntities=creditGameDao.query(creditGameEntity);
        Assert.assertTrue(!CollectionUtils.isEmpty(creditGameEntities));
        CreditGameEntity queryCreditGameEntity= creditGameEntities.get(0);
        Assert.assertNotNull(queryCreditGameEntity);

        int deleteNum=creditGameDao.delete(queryCreditGameEntity.getId());
        Assert.assertTrue(deleteNum==1);
    }
*/
    @Test
    public void testUpdate(){
        CreditGameEntity creditGameEntity=genCreditGameEntity();
        Assert.assertNotNull(creditGameEntity);
        int insertNum=creditGameDao.insert(creditGameEntity);
        Assert.assertTrue(insertNum==1);
        Assert.assertTrue(null!=creditGameEntity.getId()&&creditGameEntity.getId()>0);


        creditGameEntity.setRemarks("remarks123");
        int updateNum=creditGameDao.update(creditGameEntity);
        Assert.assertTrue(updateNum==1);

        int deleteNum=creditGameDao.delete(creditGameEntity.getId());
        Assert.assertTrue(deleteNum==1);
    }

    @Test
    public void testDelete(){
        CreditGameEntity creditGameEntity=genCreditGameEntity();
        Assert.assertNotNull(creditGameEntity);
        int insertNum=creditGameDao.insert(creditGameEntity);
        Assert.assertTrue(insertNum==1);
        Assert.assertTrue(null!=creditGameEntity.getId()&&creditGameEntity.getId()>0);

        int deleteNum=creditGameDao.delete(creditGameEntity.getId());
        Assert.assertTrue(deleteNum==1);
    }
}
