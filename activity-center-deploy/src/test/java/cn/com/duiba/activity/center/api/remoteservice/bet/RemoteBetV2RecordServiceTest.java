package cn.com.duiba.activity.center.api.remoteservice.bet;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.betv2.BetV2ListRecordDto;
import cn.com.duiba.activity.center.api.remoteservice.betv2.RemoteBetV2RecordService;
import cn.com.duiba.activity.center.api.tool.Page;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2020/1/191:58 下午
 */
@Transactional(DsConstants.DATABASE_ACT_RECORD)
public class RemoteBetV2RecordServiceTest  extends TransactionalTestCaseBase {

    @Autowired
    private RemoteBetV2RecordService remoteBetV2RecordService;


    @Test
    public void listByActGroupIds(){
        Page<BetV2ListRecordDto> page = remoteBetV2RecordService.listByActGroupIds(1539361l, Lists.newArrayList(167L,177L),1,20);
        System.out.printf(JSON.toJSONString(page));
        Assert.assertNotNull(page);
    }




}
