package cn.com.duiba.activity.center.biz.service.movebrick;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.movebrick.DuibaBrickConfPrizesDto;
import cn.com.duiba.activity.center.api.dto.movebrick.DuibaBrickConfigDto;
import cn.com.duiba.activity.center.api.dto.movebrick.DuibaBrickPrizeDto;
import cn.com.duiba.activity.center.biz.entity.movebrick.DuibaBrickConfigEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by sunchangji on 2019/1/7.
 */
public class DuibaBrickConfigServiceTest extends TransactionalTestCaseBase {

	@Autowired
	private DuibaBrickConfigService duibaBrickConfigService;

	@Test
	public void insertBrickConfig() {
		DuibaBrickConfPrizesDto brickConfPrizesDto = new DuibaBrickConfPrizesDto();
		DuibaBrickConfigDto duibaBrickConfigDto = new DuibaBrickConfigDto();
		duibaBrickConfigDto.setAppId(1L);
		duibaBrickConfigDto.setBrickRule("自定义配置");
		duibaBrickConfigDto.setClickTime(1);
		duibaBrickConfigDto.setDayLimit(10);
		duibaBrickConfigDto.setEndTime(DateUtils.daysAddOrSub(new Date(), 18));
		duibaBrickConfigDto.setCredits(10L);
		duibaBrickConfigDto.setExchangeTotalQuantity(100);
		duibaBrickConfigDto.setMiddleCallInterval(5);
		duibaBrickConfigDto.setPrimaryCallInterval(5);
		duibaBrickConfigDto.setSeniorCallInterval(5);
		duibaBrickConfigDto.setWinnedPrizeNum(10);
		duibaBrickConfigDto.setTitle("搬砖工活动1");
		duibaBrickConfigDto.setStartTime(DateUtils.daysAddOrSub(new Date(), 1));
		duibaBrickConfigDto.setMiddleWork(10);
		duibaBrickConfigDto.setPrimaryWork(10);
		duibaBrickConfigDto.setSeniorWork(10);
		duibaBrickConfigDto.setRewardType(2);
		duibaBrickConfigDto.setRedPacketBudget(3000);
		duibaBrickConfigDto.setObjectNotWinLimit(1);
		duibaBrickConfigDto.setRedPacketNotWinLimit(100);
		duibaBrickConfigDto.setObjectPrizeWarnEmail("<EMAIL>");
		brickConfPrizesDto.setDuibaBrickConfigDto(duibaBrickConfigDto);
		List<DuibaBrickPrizeDto> prizes = new ArrayList<>();
		DuibaBrickPrizeDto prizeDto = new DuibaBrickPrizeDto();
		prizeDto.setPrizeType(2);
		prizeDto.setPrizeImage("//yun.duiba.com.cn/images/201901/h3b7t133sj.jpg");
		prizeDto.setPrizeName("15元红包");
		prizeDto.setRedBacket(1500);
		prizeDto.setWinPrizeNum(2);
		prizeDto.setRedBacketSplit("5+4+3+2+1");
		prizes.add(prizeDto);
		brickConfPrizesDto.setPrizes(prizes);

		duibaBrickConfigService.insertBrickConfig(brickConfPrizesDto);
	}

	@Test
	public void rollbackInsertBrickConfig() {
		Long id = 8L;
		duibaBrickConfigService.rollbackInsertBrickConfig(id);
		DuibaBrickConfigEntity entity = duibaBrickConfigService.findById(id);
		Assert.assertTrue(entity == null);
	}

	@Test
	public void updatePeriodIdById() {
		Long periodId = 1L;
		Long id = 9L;
		duibaBrickConfigService.updatePeriodIdById(id, periodId);
		DuibaBrickConfigEntity entity = duibaBrickConfigService.findById(id);
		Assert.assertEquals(periodId,entity.getPeriodId());
	}

}
