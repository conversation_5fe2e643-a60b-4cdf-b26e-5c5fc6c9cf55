package cn.com.duiba.activity.center.biz.dao.hdtool;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.hdtool.HdtoolStockConsumeEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/24.
 */
@Transactional(DsConstants.DATABASE_CREDITS)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class HdtoolStockConsumeDaoTest extends TransactionalTestCaseBase {

    @Resource
    private HdtoolStockConsumeDao hdtoolStockConsumeDao;

    @Test
    public void testInsert() {
        HdtoolStockConsumeEntity hdtoolStockConsumeDto =new HdtoolStockConsumeEntity(true);
        TestUtils.setRandomAttributesForBean(hdtoolStockConsumeDto,false);
        hdtoolStockConsumeDao.insert(hdtoolStockConsumeDto);
    }

    @Test
    public void testfindByBizIdAndSourcePay() {
        HdtoolStockConsumeEntity hdtoolStockConsumeDto =new HdtoolStockConsumeEntity(true);
        TestUtils.setRandomAttributesForBean(hdtoolStockConsumeDto,false);
        hdtoolStockConsumeDto.setAction("pay");
        hdtoolStockConsumeDao.insert(hdtoolStockConsumeDto);
        Assert.assertNotNull(hdtoolStockConsumeDao.findByBizIdAndSourcePay(hdtoolStockConsumeDto.getBizId(), hdtoolStockConsumeDto.getBizSource()));
    }

}
