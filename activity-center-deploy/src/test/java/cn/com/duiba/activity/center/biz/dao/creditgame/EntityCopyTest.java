package cn.com.duiba.activity.center.biz.dao.creditgame;

import java.util.Date;

import org.apache.commons.beanutils.BeanUtils;
import org.junit.Test;

import cn.com.duiba.activity.center.api.dto.creditgame.CreditGameDto;
import cn.com.duiba.activity.center.biz.entity.creditgame.CreditGameEntity;
import cn.com.duiba.activity.center.biz.exception.ActivityRuntimeException;

/**
 * 实体属性拷贝测试
 * <AUTHOR>
 * @since 2016-11-04
 */
public class EntityCopyTest {

    @Test
    public void test(){
        CreditGameEntity entity=genCreditGameEntity();
        CreditGameDto dto=new CreditGameDto();
        try{
            BeanUtils.copyProperties(dto,entity);

            //cn.com.duiba.wolf.utils.BeanUtils.copy(entity,CreditGameDto.class);
            //dto.isDeleted();
        }catch(Exception ex){
            ex.printStackTrace();
            throw new ActivityRuntimeException("test", ex);
        }

    }

    private CreditGameEntity genCreditGameEntity(){
        CreditGameEntity creditGameEntity=new CreditGameEntity();
        creditGameEntity.setActivityActionType((byte)1);
        creditGameEntity.setCreditGameType((byte)1);
        creditGameEntity.setCreditGameTitle("积分游戏Title");
        creditGameEntity.setCreatePerson("创建人");
        creditGameEntity.setCreditGameActivityCategoryId(1233444l);
        creditGameEntity.setCreditGameAppCount(100l);
        creditGameEntity.setCreditGameAutoOffDate(new Date());
        creditGameEntity.setCreditGameAwardConfig("CreditGameAwardConfig");
        creditGameEntity.setCreditGameAwardPosition("CreditGameAwardPosition");
        creditGameEntity.setCreditGameBannerImage("CreditGameBannerImage");
        creditGameEntity.setCreditGameBetConfig("CreditGameBetConfig");
        creditGameEntity.setCreditGameCreditsPrice(100l);
        creditGameEntity.setCreditGameCustomTag("CreditGameCustomTag");
        creditGameEntity.setCreditGameDrawLimit(101l);
        creditGameEntity.setCreditGameDrawScope("1111111111");
        creditGameEntity.setCreditGameDuibaPrice(10l);
        creditGameEntity.setCreditGameFreeLimit(100l);
        creditGameEntity.setCreditGameFreeScope("1111111111");
        creditGameEntity.setCreditGameLotteryCount(100l);
        creditGameEntity.setCreditGameRecommendImage("CreditGameRecommendImage");
        creditGameEntity.setCreditGameRuleScript("ruleScripts");
        creditGameEntity.setCreditGameSmallImage("CreditGameSmallImage");
        creditGameEntity.setCreditGameStatus((byte)1);
        creditGameEntity.setCreditGameSwitches(9l);
        creditGameEntity.setCreditGameValveConfig("CreditGameValveConfig");
        creditGameEntity.setCreditGameWhiteImage("CreditGameWhiteImage");
        creditGameEntity.setGmtCreate(new Date());
        creditGameEntity.setGmtModified(new Date());
        creditGameEntity.setRemarks("Remarks");

        creditGameEntity.setCreditGameRule("活动规则");
        creditGameEntity.setCreditGameFreeRule("免费参与活动规则");
        creditGameEntity.setCreditGameDesc("其他说明");
        creditGameEntity.setCreditGameExtDesc("扩展说明");

        creditGameEntity.setDeleted((byte)0);

        return creditGameEntity;
    }
}
