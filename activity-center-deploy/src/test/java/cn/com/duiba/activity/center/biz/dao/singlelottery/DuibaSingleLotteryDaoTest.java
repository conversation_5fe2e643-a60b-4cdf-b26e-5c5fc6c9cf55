package cn.com.duiba.activity.center.biz.dao.singlelottery;

/**
 * Created by yansen on 16/6/17.
 */

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.ActivityExtraInfoEntity;
import cn.com.duiba.activity.center.biz.entity.singlelottery.DuibaSingleLotteryEntity;
import cn.com.duiba.activity.center.biz.entity.singlelottery.SingleLotteryAppSpecifyEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/20.
 */
@Transactional(DuibaSingleLotteryDaoTest.THIS_DATABASE_SCHEMA)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaSingleLotteryDaoTest extends TransactionalTestCaseBase {
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS;

    @Resource
    private DuibaSingleLotteryDao duibaSingleLotteryDao;



    private DuibaSingleLotteryEntity duibasingleLottery;

    @Before
    public void testInsert(){
        DuibaSingleLotteryEntity e=new DuibaSingleLotteryEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        duibaSingleLotteryDao.insert(e);
        duibasingleLottery=e;
    }

    @Test
    public void testFind() {
        DuibaSingleLotteryEntity e=duibasingleLottery;
        DuibaSingleLotteryEntity e1=duibaSingleLotteryDao.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindAutoOff() {
        DuibaSingleLotteryEntity e=new DuibaSingleLotteryEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-300000l));
        duibaSingleLotteryDao.insert(e);
        List<DuibaSingleLotteryEntity> list=duibaSingleLotteryDao.findAutoOff();
        boolean isfind=false;
        for (DuibaSingleLotteryEntity d:list){
            if(e.getId().equals(d.getId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindSingleLotteryPage() {
        DuibaSingleLotteryEntity e=new DuibaSingleLotteryEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-300000l));
        duibaSingleLotteryDao.insert(e);
        Map<String,Object> params=new HashMap<>();
        params.put("max",10);
        params.put("offset",0);
        params.put("title",e.getTitle());
        List<DuibaSingleLotteryEntity> list=duibaSingleLotteryDao.findSingleLotteryPage(params);
        boolean isfind=false;
        for (DuibaSingleLotteryEntity d:list){
            if(e.getId().equals(d.getId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindSingleLottery() {
        DuibaSingleLotteryEntity e=new DuibaSingleLotteryEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-300000l));
        duibaSingleLotteryDao.insert(e);
        Map<String,Object> params=new HashMap<>();
        params.put("max",10);
        params.put("offset",0);
        List<DuibaSingleLotteryEntity> list=duibaSingleLotteryDao.findSingleLottery(params);
        boolean isfind=false;
        for (DuibaSingleLotteryEntity d:list){
            if(e.getId().equals(d.getId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindSingleLotteryPageCount() {
        DuibaSingleLotteryEntity e=new DuibaSingleLotteryEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-300000l));
        duibaSingleLotteryDao.insert(e);
        Map<String,Object> params=new HashMap<>();
        params.put("max",10);
        params.put("offset",0);
        Assert.assertTrue(duibaSingleLotteryDao.findSingleLotteryPageCount(params)>0);
    }

    @Test
    public void testFindAllDuibaSingleLottery() {
        DuibaSingleLotteryEntity e=new DuibaSingleLotteryEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-300000l));
        duibaSingleLotteryDao.insert(e);


        duibaSingleLotteryDao.findAllDuibaSingleLottery(1l);
    }

    @Test
    public void testFindAllByIds() {
        DuibaSingleLotteryEntity e=duibasingleLottery;
        assertDO(e,duibaSingleLotteryDao.findAllByIds(Arrays.asList(e.getId())).get(0));
    }

    @Test
    public void testGetCountDuibaSingleLottery() {
        DuibaSingleLotteryEntity e=new DuibaSingleLotteryEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-300000l));
        duibaSingleLotteryDao.insert(e);
        Map<String,Object> params=new HashMap<>();
        params.put("activityId",e.getId());
        Assert.assertTrue(duibaSingleLotteryDao.getCountDuibaSingleLottery(params)>0);
    }

    @Test
    public void testFindHasUserdSingleIds() {
        duibaSingleLotteryDao.findHasUserdSingleIds(1l);
    }

    @Test
    public void testFindExtraInfoById() {
        DuibaSingleLotteryEntity e=duibasingleLottery;
        ActivityExtraInfoEntity vo=duibaSingleLotteryDao.findExtraInfoById(e.getId());
        Assert.assertEquals(e.getFreeRule(),vo.getFreeRule());
    }

    @Test
    public void testUpdateAutoOffDate() {
        Assert.assertTrue(duibaSingleLotteryDao.updateAutoOffDate(duibasingleLottery.getId())>0);
    }

    @Test
    public void testUpdate() {
        DuibaSingleLotteryEntity e=new DuibaSingleLotteryEntity(duibasingleLottery.getId());
        TestUtils.setRandomAttributesForBean(e,false);
        duibaSingleLotteryDao.update(e);
        DuibaSingleLotteryEntity e1=duibaSingleLotteryDao.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testReduceMainItemRemaining() {
        DuibaSingleLotteryEntity e=duibasingleLottery;
        duibaSingleLotteryDao.reduceMainItemRemaining(e.getId());
        DuibaSingleLotteryEntity e1=duibaSingleLotteryDao.find(e.getId());
        Assert.assertEquals(e.getMainItemRemaining(),(Integer) (e1.getMainItemRemaining()+1));
    }

    @Test
    public void testAddMainItemRemaining() {
        DuibaSingleLotteryEntity e=duibasingleLottery;
        duibaSingleLotteryDao.addMainItemRemaining(e.getId());
        DuibaSingleLotteryEntity e1=duibaSingleLotteryDao.find(e.getId());
        Assert.assertEquals(e.getMainItemRemaining(),(Integer) (e1.getMainItemRemaining()-1));
    }

    @Test
    public void testReduceInciteItemRemaining() {
        DuibaSingleLotteryEntity e=duibasingleLottery;
        duibaSingleLotteryDao.reduceInciteItemRemaining(e.getId());
        DuibaSingleLotteryEntity e1=duibaSingleLotteryDao.find(e.getId());
        Assert.assertEquals(e.getInciteItemRemaining(),(Integer) (e1.getInciteItemRemaining()+1));
    }

    @Test
    public void testAddInciteItemRemaining() {
        DuibaSingleLotteryEntity e=duibasingleLottery;
        duibaSingleLotteryDao.addInciteItemRemaining(e.getId());
        DuibaSingleLotteryEntity e1=duibaSingleLotteryDao.find(e.getId());
        Assert.assertEquals(e.getInciteItemRemaining(),(Integer) (e1.getInciteItemRemaining()-1));
    }

    @Test
    public void testUpdateForAdminEdit() {
        DuibaSingleLotteryEntity e=duibasingleLottery;
        TestUtils.setRandomAttributesForBean(e,false);
        Assert.assertTrue(duibaSingleLotteryDao.updateForAdminEdit(e)>0);
    }

    @Test
    public void testAddMainItemRemainingById() {
        DuibaSingleLotteryEntity e=duibasingleLottery;
        duibaSingleLotteryDao.addMainItemRemainingById(e.getId(),1);
        DuibaSingleLotteryEntity e1=duibaSingleLotteryDao.find(e.getId());
        Assert.assertEquals(e.getMainItemRemaining(),(Integer)(e1.getMainItemRemaining()-1));
    }

    @Test
    public void testSubMainItemRemainingById() {
        DuibaSingleLotteryEntity e=duibasingleLottery;
        duibaSingleLotteryDao.subMainItemRemainingById(e.getId(),1);
        DuibaSingleLotteryEntity e1=duibaSingleLotteryDao.find(e.getId());
        Assert.assertEquals(e.getMainItemRemaining(),(Integer)(e1.getMainItemRemaining()+1));
    }

    @Test
    public void testAddInciteItemRemainingById() {
        DuibaSingleLotteryEntity e=duibasingleLottery;
        duibaSingleLotteryDao.addInciteItemRemainingById(e.getId(),1);
        DuibaSingleLotteryEntity e1=duibaSingleLotteryDao.find(e.getId());
        Assert.assertEquals(e.getInciteItemRemaining(),(Integer)(e1.getInciteItemRemaining()-1));
    }

    @Test
    public void testSubInciteItemRemainingById() {
        DuibaSingleLotteryEntity e=duibasingleLottery;
        duibaSingleLotteryDao.subInciteItemRemainingById(e.getId(),1);
        DuibaSingleLotteryEntity e1=duibaSingleLotteryDao.find(e.getId());
        Assert.assertEquals(e.getInciteItemRemaining(),(Integer)(e1.getInciteItemRemaining()+1));
    }

    @Test
    public void testFindForupdate() {
        DuibaSingleLotteryEntity e=duibasingleLottery;
        DuibaSingleLotteryEntity e1=duibaSingleLotteryDao.findForupdate(e.getId());
        Assert.assertEquals(e.getMainItemRemaining(),e1.getMainItemRemaining());
    }

    private SingleLotteryAppSpecifyEntity getInsertAbleAppDO(){
        SingleLotteryAppSpecifyEntity e=new SingleLotteryAppSpecifyEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        return e;
    }

    private void assertAppDO(SingleLotteryAppSpecifyEntity e,SingleLotteryAppSpecifyEntity e1){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified"}));

        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }

    private void assertDO(DuibaSingleLotteryEntity e, DuibaSingleLotteryEntity e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaSingleLotteryEntity e, DuibaSingleLotteryEntity e1,String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","activityCategoryId","tag"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }

}
