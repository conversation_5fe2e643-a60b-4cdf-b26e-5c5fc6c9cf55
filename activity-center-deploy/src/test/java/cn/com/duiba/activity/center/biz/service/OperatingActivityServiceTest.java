package cn.com.duiba.activity.center.biz.service;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.activity.SimpleOperatingActivityDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.activity.OperatingActivityOptionsDao;
import cn.com.duiba.activity.center.biz.entity.activity.OperatingActivityEntity;
import cn.com.duiba.activity.center.biz.entity.activity.OperatingActivityOptionsEntity;
import cn.com.duiba.activity.center.biz.service.activity.OperatingActivityService;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created by yansen on 16/6/14.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
//@Test(dependsOnGroups = "OperatingActivityDaoTest")
public class OperatingActivityServiceTest extends TransactionalTestCaseBase{

    @Resource
    private OperatingActivityService operatingActivityService;

    @Resource
    private OperatingActivityOptionsDao operatingActivityOptionsDao;

    private OperatingActivityEntity operatingActivityDO;

    @Before
    public void testInsert() {
        OperatingActivityDto dto=new OperatingActivityDto();
        TestUtils.setRandomAttributesForBean(dto,false);
        dto.setDeleted(false);
        operatingActivityService.insert(dto);
        OperatingActivityEntity entity= BeanUtils.copy(dto,OperatingActivityEntity.class);
        dto.setId(dto.getId());
        operatingActivityDO=entity;
    }

    @Test
    public void testFind() {
        OperatingActivityEntity e=operatingActivityDO;
        Assert.assertNotNull(operatingActivityService.find(e.getId()));
    }

    private void insert(OperatingActivityEntity entity){
        OperatingActivityDto dto=BeanUtils.copy(entity,OperatingActivityDto.class);
        operatingActivityService.insert(dto);
        entity.setId(dto.getId());
    }


    @Test
    public void testFindGameByAppIdAndActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(1);
        insert(e);
        Assert.assertTrue(operatingActivityService.findGameByAppIdAndActivityId(e.getAppId(), Arrays.asList(e.getActivityId()),e.getType()).size()>0);
    }

    @Test
    public void testFindAllAppTasksContent() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(1);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllAppTasksContent(e.getAppId());

    }

    @Test
    public void testFindOpenLotteryIds() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(2);
        e.setStatus(2);
        insert(e);
        List<Long> list=operatingActivityService.findOpenLotteryIds();
        Boolean isfind=false;
        for(Long o:list){
            if(o.equals(e.getId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllOpenDuibaActivity() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(0);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllOpenDuibaActivity(e.getActivityId());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindByAppIdAndDuibaActivityIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(0);
        insert(e);
        Assert.assertNotNull(operatingActivityService.findByAppIdAndDuibaActivityIdAndDeleted(e.getAppId(),e.getActivityId(),false));
    }

    @Test
    public void testFindByAppIdAndDuibaTurntableIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(4);
        insert(e);
        Assert.assertNotNull(operatingActivityService.findByAppIdAndDuibaTurntableIdAndDeleted(e.getAppId(),e.getActivityId(),false));
    }

    @Test
    public void testFindByAppIdAndGameConfigDuibaIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(20);
        insert(e);
        Assert.assertNotNull(operatingActivityService.findByAppIdAndGameConfigDuibaIdAndDeleted(e.getAppId(),e.getActivityId(),false));
    }

    @Test
    public void testFindByAppIdAndDuibaSingleLotteryIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(2);
        insert(e);
        Assert.assertNotNull(operatingActivityService.findByAppIdAndDuibaSingleLotteryIdAndDeleted(e.getAppId(),e.getActivityId(),false));
    }

    @Test
    public void testFindByAppIdAndDuibaQuestionAnswerIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(40);
        insert(e);
        Assert.assertNotNull(operatingActivityService.findByAppIdAndDuibaQuestionAnswerIdAndDeleted(e.getAppId(),e.getActivityId(),false));
    }

    @Test
    public void testFindByAppIdAndDuibaSeckillIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(31);
        insert(e);
        Assert.assertNotNull(operatingActivityService.findByAppIdAndDuibaSeckillIdAndDeleted(e.getAppId(),e.getActivityId(),false));
    }


    @Test
    public void testFindByAppIdAndDuibaQuizzIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(41);
        insert(e);
        Assert.assertNotNull(operatingActivityService.findByAppIdAndDuibaQuizzIdAndDeleted(e.getAppId(),e.getActivityId(),false));
    }

    @Test
    public void testFindByAppIdAndDuibaHdtoolIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(6);
        insert(e);
        Assert.assertNotNull(operatingActivityService.findByAppIdAndDuibaHdtoolIdAndDeleted(e.getAppId(),e.getActivityId(),false));
    }

    @Test
    public void testFindByAppIdAndAppSingleLotteryIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(3);
        insert(e);
        Assert.assertNotNull(operatingActivityService.findByAppIdAndAppSingleLotteryIdAndDeleted(e.getAppId(),e.getActivityId(),false));
    }

    @Test
    public void testFindByAppIdAndAppManualLotteryAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(5);
        insert(e);
        Assert.assertNotNull(operatingActivityService.findByAppIdAndAppManualLotteryAndDeleted(e.getAppId(),e.getActivityId(),false));
    }

    @Test
    public void testCountActivity() {
        OperatingActivityEntity e=operatingActivityDO;
        Map<String,Object> params=new HashMap<>();
        params.put("appId",e.getAppId());
        params.put("title",e.getTitle());
        params.put("parentId",e.getParentActivityId());
        Assert.assertTrue(operatingActivityService.countActivity(params)>0);
    }

    @Test
    public void testFindActivityList() {
        OperatingActivityEntity e=operatingActivityDO;
        Map<String,Object> params=new HashMap<>();
        params.put("appId",e.getAppId());
        params.put("title",e.getTitle());
        params.put("parentId",e.getParentActivityId());
        params.put("deleted",e.getDeleted());
        Assert.assertTrue(operatingActivityService.findActivityList(params).size()>0);

    }

    @Test
    public void testFindActivityListByParent() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        e.setType(5);
        insert(e);
        Map<String,Object> params=new HashMap<>();
        params.put("parentId",e.getParentActivityId());
        params.put("appId",e.getAppId());
        params.put("start",0);
        params.put("pageSize",20);
        Assert.assertTrue(operatingActivityService.findActivityListByParent(params).size()>0);
    }

    @Test
    public void testDeleteTurntable() {
        OperatingActivityEntity e=operatingActivityDO;
        operatingActivityService.deleteTurntable(e.getId(),e.getAppId(),false,20);
        OperatingActivityDto e1=operatingActivityService.find(e.getId());
        Assert.assertTrue(!e1.getDeleted());
        Assert.assertEquals((int)e1.getStatus(),20);
    }

    @Test
    public void testFindAppIdsByDuibaActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(0);
        insert(e);
        List<Long> appIds=operatingActivityService.findAppIdsByDuibaActivityId(e.getActivityId());
        boolean isfind=false;
        for (Long appId:appIds){
            if(appId.equals(e.getAppId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAppIdsBySingleLotteryId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        insert(e);
        List<Long> appIds=operatingActivityService.findAppIdsBySingleLotteryId(e.getActivityId());
        boolean isfind=false;
        for (Long appId:appIds){
            if(appId.equals(e.getAppId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAppIdsByActivityIdAndType() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        insert(e);
        List<Long> appIds=operatingActivityService.findAppIdsByActivityIdAndType(e.getActivityId(),e.getType());
        boolean isfind=false;
        for (Long appId:appIds){
            if(appId.equals(e.getAppId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAppIdsByDuibaSingleLotteryId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        insert(e);
        List<Long> appIds=operatingActivityService.findAppIdsByDuibaSingleLotteryId(e.getActivityId());
        boolean isfind=false;
        for (Long appId:appIds){
            if(appId.equals(e.getAppId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testCountAppByActivityIdAndType() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        insert(e);
        int count=operatingActivityService.countAppByActivityIdAndType(e.getActivityId(),e.getType());
        Assert.assertTrue(count>0);
    }

    @Test
    public void testFindAllByDuibaActivityIds() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(0);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllByDuibaActivityIds(Arrays.asList(e.getActivityId()));
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindAllByActivityIdsAndType() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllByActivityIdsAndType(Arrays.asList(e.getActivityId()),e.getType());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindAllByDuibaSingleLotteryIds() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllByDuibaSingleLotteryIds(Arrays.asList(e.getActivityId()));
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindIdsByDuibaActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(0);
        insert(e);

        List<Long> ids=operatingActivityService.findIdsByDuibaActivityId(e.getActivityId());

        boolean isfind=false;
        for (Long id:ids){
            if(id.equals(e.getId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testUpdateStatusByDuibaActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(0);
        insert(e);
        operatingActivityService.updateStatusByDuibaActivityId(20,e.getActivityId());

        OperatingActivityDto e1=operatingActivityService.find(e.getId());
        Assert.assertEquals((int) e1.getStatus(),20);
    }

    @Test
    public void testUpdateStatusBySingleLotteryId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        insert(e);
        operatingActivityService.updateStatusBySingleLotteryId(20,e.getActivityId());

        OperatingActivityDto e1=operatingActivityService.find(e.getId());
        Assert.assertEquals((int) e1.getStatus(),20);
    }


    @Test
    public void testFindIdsBySingleLotteryId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        insert(e);
        List<Long> ids=operatingActivityService.findIdsBySingleLotteryId(e.getActivityId());

        boolean isfind=false;
        for (Long id:ids){
            if(id.equals(e.getId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllByType() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllByType(e.getType());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindAllBySingleLotteryId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllBySingleLotteryId(e.getActivityId());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindAllBySingleLotteryIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllBySingleLotteryIdAndDeleted(e.getActivityId(),e.getDeleted());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindAllByDuibaSeckillIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(31);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllByDuibaSeckillIdAndDeleted(e.getActivityId(),e.getDeleted());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindAllByDuibaActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(0);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllByDuibaActivityId(e.getActivityId());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindAllByDuibaSecondsKillId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(31);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllByDuibaSeckillId(e.getActivityId());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindAllByDuibaActivityIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(0);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllByDuibaActivityIdAndDeleted(e.getActivityId(),e.getDeleted());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindAllByDuibaQuestionAnswerIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(40);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllByDuibaQuestionAnswerIdAndDeleted(e.getActivityId(),e.getDeleted());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindAllByDuibaQuizzIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(41);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllByDuibaQuizzIdAndDeleted(e.getActivityId(),e.getDeleted());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testUpdateStatusByActivityIdAndType() {
        OperatingActivityEntity e=operatingActivityDO;
        operatingActivityService.updateStatusByActivityIdAndType(e.getActivityId(),e.getType(),42);
        OperatingActivityDto e1=operatingActivityService.find(e.getId());
        Assert.assertEquals((int)e1.getStatus(),42);
    }

    @Test
    public void testFindByActivityIdAndTypeAndAppIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(41);
        insert(e);
        OperatingActivityDto e1=operatingActivityService.findByActivityIdAndTypeAndAppIdAndDeleted(e.getActivityId(),e.getType(),e.getAppId(),e.getDeleted());
        Assert.assertNotNull(e1);
    }

    @Test
    public void testFindIdsByDuibaActivityIdAndType() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        insert(e);
        List<Long> ids=operatingActivityService.findIdsByDuibaActivityIdAndType(e.getActivityId(),e.getType());
        boolean isfind=false;
        for (Long id:ids){
            if(id.equals(e.getId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindSingleLotteryOperaList() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findSingleLotteryOperaList(Arrays.asList(e.getActivityId()),e.getAppId());
        Assert.assertTrue(list.size()>0);
    }



    @Test
    public void testFindByActivityIdAndParentIdAndTypeAndAppId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(41);
        insert(e);
        OperatingActivityDto e1=operatingActivityService.findByActivityIdAndParentIdAndTypeAndAppId(e.getActivityId(),e.getParentActivityId(),e.getType(),e.getAppId());
        Assert.assertNotNull(e1);
    }

    @Test
    public void testDeleteByParentActivityIds() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        insert(e);
        operatingActivityService.deleteByParentActivityIds(Arrays.asList(e.getParentActivityId()));
        OperatingActivityDto e1=operatingActivityService.find(e.getId());
        Assert.assertEquals((int)e1.getStatus(),1);
        Assert.assertTrue(e1.getDeleted());
    }

    @Test
    public void testFindOperatingSingleLottery() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        insert(e);
        OperatingActivityDto e1=operatingActivityService.findOperatingSingleLottery(e.getActivityId(),e.getAppId());
        Assert.assertNotNull(e1);
    }

    @Test
    public void testFindAllByIds() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllByIds(Arrays.asList(e.getId()));
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testUpdateManualLotteryByIds() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(5);
        e.setStatus(2);
        insert(e);
        int count=operatingActivityService.updateManualLotteryByIds(Arrays.asList(e.getActivityId()));
        System.out.println(count);
        OperatingActivityDto e1=operatingActivityService.find(e.getId());
        Assert.assertEquals((int)e1.getStatus(),1);
    }

    @Test
    public void testFindAllEnabledActivies() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(2);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllEnabledActivies(e.getAppId());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindActiveActivity() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(2);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findActiveActivity(e.getAppId());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindAllVirtualInSingleLottery() {
        operatingActivityService.findAllVirtualInSingleLottery(1l);
    }

    @Test
    public void testFindAllVirtualInHdTool() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(2);
        insert(e);
        OperatingActivityOptionsEntity o=new OperatingActivityOptionsEntity(true);
        TestUtils.setRandomAttributesForBean(o,false);
        operatingActivityOptionsDao.insertOption(o);
        Assert.assertNotNull(operatingActivityService.findAllVirtualInHdTool(e.getAppId()));
    }

    @Test
    public void testUpdate() {
        OperatingActivityEntity e=new OperatingActivityEntity(operatingActivityDO.getId());
        TestUtils.setRandomAttributesForBean(e,false);
        operatingActivityService.update(BeanUtils.copy(e,OperatingActivityDto.class));
        OperatingActivityDto e1=operatingActivityService.find(e.getId());
        Assert.assertNotNull(e1);
    }

    @Test
    public void testFindAppIdsByDuibaSecondsKillId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(30);
        insert(e);
        List<Long> ids=operatingActivityService.findAppIdsByDuibaSecondsKillId(e.getActivityId());
        boolean isfind=false;
        for (Long id:ids){
            if(id.equals(e.getAppId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAllByDuibaSecondsKillActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(30);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllByDuibaSecondsKillActivityId(e.getActivityId());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindAllByDuibaQuestionAnswerId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(40);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllByDuibaQuestionAnswerId(e.getActivityId());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindAllByDuibaQuizzId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(41);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllByDuibaQuizzId(e.getActivityId());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindAllByDuibaSeckillId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(31);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllByDuibaSeckillId(e.getActivityId());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindAllByDuibaSecondsKillActivityIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(30);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllByDuibaSecondsKillActivityIdAndDeleted(e.getActivityId(),e.getDeleted());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testUpdateStatusByDuibaSecondsKillActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(30);
        insert(e);
        operatingActivityService.updateStatusByDuibaSecondsKillActivityId(42,e.getActivityId());
        OperatingActivityDto e1=operatingActivityService.find(e.getId());
        Assert.assertEquals((int)e1.getStatus(),42);
    }

    @Test
    public void testFindGameOperatingActivity() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(20);
        insert(e);
        OperatingActivityDto e1=operatingActivityService.findGameOperatingActivity(e.getAppId(),e.getActivityId(),e.getType());
        Assert.assertNotNull(e1);
    }

    @Test
    public void testFindQuestionAnswerOperatingActivity() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(40);
        insert(e);
        OperatingActivityDto e1=operatingActivityService.findQuestionAnswerOperatingActivity(e.getAppId(),e.getActivityId());
        Assert.assertNotNull(e1);
    }

    @Test
    public void testFindSecondsKillOperatingActivity() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(30);
        insert(e);
        OperatingActivityDto e1=operatingActivityService.findSecondsKillOperatingActivity(e.getAppId(),e.getActivityId());
        Assert.assertNotNull(e1);
    }

    @Test
    public void testFindByAppIdAndDuibaGameIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(20);
        insert(e);
        OperatingActivityDto e1=operatingActivityService.findByAppIdAndDuibaGameIdAndDeleted(e.getAppId(),e.getActivityId(),e.getDeleted());
        Assert.assertNotNull(e1);
    }

    @Test
    public void testFindByAppIdLimit() {
//        AppBannerDO appBanner=new AppBannerDO(true);
//        TestUtils.setRandomAttributesForBean(appBanner,false);
//        appBannerDao.insert(appBanner);
//        OperatingActivityEntity e=new OperatingActivityEntity(true);
//        TestUtils.setRandomAttributesForBean(e,false);
//        e.setDeleted(false);
//        e.setParentActivityId(null);
//        e.setStatus(2);
//        e.setType(20);
//        e.setAppBannerId(appBanner.getId());
//        insert(e);
        operatingActivityService.findByAppIdLimit(1l,2l);
    }

    @Test
    public void testFindAllByAppId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(30);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findAllByAppId(e.getAppId());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindByAppIdAndDuibaSecondsKillActivityIdAndDeleted() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setType(30);
        insert(e);
        OperatingActivityDto e1=operatingActivityService.findByAppIdAndDuibaSecondsKillActivityIdAndDeleted(e.getAppId(),e.getActivityId(),e.getDeleted());
        Assert.assertNotNull(e1);
    }

    @Test
    public void testFindDuibaQuestionAnswerByAppIdAndActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(2);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findDuibaQuestionAnswerByAppIdAndActivityId(e.getAppId(),Arrays.asList(e.getActivityId()),e.getType());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindDuibaQuizzByAppIdAndActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(2);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findDuibaQuizzByAppIdAndActivityId(e.getAppId(),Arrays.asList(e.getActivityId()),e.getType());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindDuibaSeckillByAppIdAndActivityId() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(2);
        insert(e);
        List<OperatingActivityDto> list=operatingActivityService.findDuibaSeckillByAppIdAndActivityId(e.getAppId(),Arrays.asList(e.getActivityId()),e.getType());
        Assert.assertTrue(list.size()>0);
    }

    @Test
    public void testFindOnlineActivity() {
//        AppBannerDO appBanner=new AppBannerDO(true);
//        TestUtils.setRandomAttributesForBean(appBanner,false);
//        appBannerDao.insert(appBanner);
//        OperatingActivityEntity e=new OperatingActivityEntity(true);
//        TestUtils.setRandomAttributesForBean(e,false);
//        e.setDeleted(false);
//        e.setParentActivityId(null);
//        e.setStatus(2);
//        e.setType(20);
//        e.setAppBannerId(appBanner.getId());
//        insert(e);
        Map<String, Object> params = new HashMap<>();
        params.put("start", 0);
        params.put("pageSize", 10);
        params.put("appId", 1l);
        operatingActivityService.findOnlineActivity(params);
    }

    @Test
    public void testFindOnlineActivityWithOutTopic() {
//        AppBannerDO appBanner=new AppBannerDO(true);
//        TestUtils.setRandomAttributesForBean(appBanner,false);
//        appBannerDao.insert(appBanner);
//        OperatingActivityEntity e=new OperatingActivityEntity(true);
//        TestUtils.setRandomAttributesForBean(e,false);
//        e.setDeleted(false);
//        e.setParentActivityId(null);
//        e.setStatus(2);
//        e.setType(20);
//        e.setAppBannerId(appBanner.getId());
//        insert(e);
        Map<String,Object> params=new HashMap<>();
        params.put("start",0);
        params.put("pageSize",10);
        params.put("appId",1l);
        operatingActivityService.findOnlineActivityWithOutTopic(params);
    }

    @Test
    public void testFindSeckillIdsByActivityId() throws Exception {
        OperatingActivityDto e=new OperatingActivityDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setParentActivityId(null);
        e.setStatus(2);
        e.setType(31);
        operatingActivityService.insert(e);
        Assert.assertTrue(operatingActivityService.findSeckillIdsByActivityId(e.getActivityId()).size()>0);
    }

    private void assertDO(OperatingActivityEntity e, OperatingActivityEntity e1){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","failCount","customCredits","customCredits"}));

        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }
    
    @Test
    public void testFindOpenLotteryIdsNew() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setAppId(1L);
        e.setParentActivityId(null);
        e.setType(2);
        e.setStatus(2);
        insert(e);
        List<Long> list=operatingActivityService.findOpenLotteryIds(1L);
        Boolean isfind=false;
        for(Long o:list){
            if(o.equals(e.getId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }
    
    @Test
    public void testFindAllByTypeNew() {
        OperatingActivityEntity e=new OperatingActivityEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setType(2);
        insert(e);
        List<SimpleOperatingActivityDto> list=operatingActivityService.findAllByTypeNew(e.getType(), Arrays.asList(e.getActivityId()));
        Assert.assertTrue(list.size()>0);
    }
}
