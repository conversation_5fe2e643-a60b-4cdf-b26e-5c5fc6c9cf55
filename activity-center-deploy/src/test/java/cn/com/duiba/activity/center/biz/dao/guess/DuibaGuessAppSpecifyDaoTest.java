package cn.com.duiba.activity.center.biz.dao.guess;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.guess.DuibaGuessAppSpecifyEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class DuibaGuessAppSpecifyDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaGuessAppSpecifyDao duibaGuessAppSpecifyDao;

	private DuibaGuessAppSpecifyEntity info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaGuessAppSpecifyEntity.class);
		duibaGuessAppSpecifyDao.insert(info);
	}

	@Test
	public void testFindByDuibaGuessesAndApp(){
		List<Long> ids = new ArrayList<>();
		ids.add(info.getDuibaGuessId());
		List<DuibaGuessAppSpecifyEntity> list = duibaGuessAppSpecifyDao.findByDuibaGuessesAndApp(ids,info.getAppId());
		Assert.assertTrue(list.size() > 0);
	}
	
	@Test
	public void findTest(){
		DuibaGuessAppSpecifyEntity e = duibaGuessAppSpecifyDao.find(info.getId());
		Assert.assertNotNull(e);
	}
	
	@Test
	public void findByDuibaGuessIdTest(){
		List<DuibaGuessAppSpecifyEntity> list = duibaGuessAppSpecifyDao.findByDuibaGuessId(info.getDuibaGuessId());
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void deleteTest(){
		DuibaGuessAppSpecifyEntity e = TestUtils.createRandomBean(DuibaGuessAppSpecifyEntity.class);
		duibaGuessAppSpecifyDao.insert(e);
		duibaGuessAppSpecifyDao.delete(e.getId());
		DuibaGuessAppSpecifyEntity entity = duibaGuessAppSpecifyDao.find(e.getId());
		Assert.assertNull(entity);
	}
	
	@Test
	public void findByDuibaGuessAndAppTest(){
		DuibaGuessAppSpecifyEntity e = duibaGuessAppSpecifyDao.findByDuibaGuessAndApp(info.getDuibaGuessId(), info.getAppId());
		Assert.assertNotNull(e);
	}
	
}
