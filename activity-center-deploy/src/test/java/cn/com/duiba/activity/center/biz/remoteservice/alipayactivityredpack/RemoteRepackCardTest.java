package cn.com.duiba.activity.center.biz.remoteservice.alipayactivityredpack;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.alipayactivityredpack.AlipayRepackCardDto;
import cn.com.duiba.activity.center.api.enums.alipayactivityredpack.DrawStatusEnum;
import cn.com.duiba.activity.center.api.remoteservice.alipayactivityredpack.RemoteRepackCardService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Date;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RemoteRepackCardTest.java
 * @Description
 * @createTime 2022年11月20日 21:26:00
 */
public class RemoteRepackCardTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteRepackCardService remoteRepackCard;

    @Test
    public void test() throws ParseException {
        AlipayRepackCardDto alipayRepackCardDto=new AlipayRepackCardDto();
        alipayRepackCardDto.setAccount("1000");
        alipayRepackCardDto.setAlipayCouponId("123");
        alipayRepackCardDto.setBizNo("124");
        alipayRepackCardDto.setRedpackActivityId("1234");
        alipayRepackCardDto.setDrawStatus(DrawStatusEnum.DRAW_STATUS_NORMAL.getCode());
        alipayRepackCardDto.setExtJson("1234");

        alipayRepackCardDto.setDrawTime(new Date(new java.util.Date().getTime()));
        alipayRepackCardDto.setShortUrl("htt123901111232");

        AlipayRepackCardDto alipayRepackCardDto2=new AlipayRepackCardDto();
        alipayRepackCardDto2.setAccount("1001");
        alipayRepackCardDto2.setAlipayCouponId("12456");
        alipayRepackCardDto2.setBizNo("1242");
        //alipayRepackCardDto.setDrawTime(new Date());
        alipayRepackCardDto2.setShortUrl("httcjw9011123");
        alipayRepackCardDto2.setAlipayCouponId("10");

        remoteRepackCard.repackCardSave(Lists.newArrayList(alipayRepackCardDto));
//        List<AlipayRepackCardDto> repackCardInfoByUrl = remoteRepackCard.findRepackCardInfoByUrl(Lists.newArrayList("https://duibatest/cjw"));
//        Map<String, Integer> urlAndState = remoteRepackCard.getUrlAndState("0");
//        remoteRepackCard.updateByCouponId(alipayRepackCardDto2);
//        System.out.println("-------");
    }

    @Test
    public void getRepackCardInfoByActivityIdTest(){
        List<AlipayRepackCardDto> repackCardInfoByActivityId = remoteRepackCard.getRepackCardInfoByActivityId("1234");
        System.out.println("---");
    }

    @Test
    public void repackCardUsedCountTestNew(){
        Map<String, Integer> map = remoteRepackCard.repackCardUsedCount(Lists.newArrayList("1","8"), 1L);
        System.out.println("---");
    }

    @Test
    public void repackCardUsedCountTest(){
        AlipayRepackCardDto alipayRepackCardDto=new AlipayRepackCardDto();
        alipayRepackCardDto.setAccount("1000");
        alipayRepackCardDto.setAlipayCouponId("123");
        alipayRepackCardDto.setBizNo("124");
        alipayRepackCardDto.setRedpackActivityId("1234");
        alipayRepackCardDto.setDrawStatus(DrawStatusEnum.DRAW_STATUS_NORMAL.getCode());
        //alipayRepackCardDto.setExtJson("1234");
        alipayRepackCardDto.setShortUrl("htt123901111232212");
        alipayRepackCardDto.setDrawStatus(DrawStatusEnum.DRAW_STATUS_NORMAL_USED.getCode());
        remoteRepackCard.repackCardSave(Lists.newArrayList(alipayRepackCardDto));
        Map<String, Integer> map = remoteRepackCard.repackCardUsedCount(Lists.newArrayList("1","8"), 1L);
        System.out.println(map);
        System.out.println("---");
    }



}
