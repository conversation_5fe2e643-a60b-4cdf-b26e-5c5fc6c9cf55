package cn.com.duiba.activity.center.biz.dao.ngame_con;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.ngame.NgameOrdersEntity;
import cn.com.duiba.wolf.utils.DateUtils;

@Transactional(DsConstants.DATABASE_NGAME_CON)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class NgameOrdersConsumerDaoTest extends TransactionalTestCaseBase {

	@Resource
	private NgameOrdersConsumerDao ngameOrdersConsumerDao;

	@Test
	public void findTest() {
		System.out.println(ngameOrdersConsumerDao.find(1539361l, 17l).getOperatingActivityId());
	}
	
	@Test
	public void findConsumerFreeNumberTest() {
		System.out.println(ngameOrdersConsumerDao.findConsumerFreeNumber(1539361l, 2l));
	}
	
	@Test
	public void findConsumerFreeNumberByDateTest() {
		System.out.println(ngameOrdersConsumerDao.findConsumerFreeNumberByDate(1539361l, 2l, DateUtils.daysAddOrSub(new Date(),-1) ,new Date()));
	}
	
	@Test
	public void findConsumerLimitNumberTest() {
		System.out.println(ngameOrdersConsumerDao.findConsumerLimitNumber(1539361l, 2l));
	}
	
	@Test
	public void findConsumerLimitNumberByDateTest() {
		System.out.println(ngameOrdersConsumerDao.findConsumerLimitNumberByDate(1539361l, 2l,DateUtils.daysAddOrSub(new Date(),-1) ,new Date()));
	}
	
	@Test
	public void findByIdsTest() {
		List<Long> ids = new ArrayList<Long>();
		ids.add(17l);
		ids.add(18l);
		System.out.println(ngameOrdersConsumerDao.findByIds(1539361l, ids).size());
	}

	@Test
	public void updateStatusToConsumeSuccessTest() {
		ngameOrdersConsumerDao.updateStatusToConsumeSuccess(1539361l, 1l);
	}

	@Test
	public void updateStatusToSuccessTest() {
		ngameOrdersConsumerDao.updateStatusToSuccess(1539361l, 1l, null);
	}

	@Test
	public void updateStatusToFailTest() {
		ngameOrdersConsumerDao.updateStatusToFail(1539361l, 1l, "aa","bb","cc");
	}

	@Test
	public void updateExchangeStatusToWaitOpenTest() {
		ngameOrdersConsumerDao.updateExchangeStatusToWaitOpen(1539361l, 3l, "dd");
	}

	@Test
	public void updateExchangeStatusToWaitOpenAndExtraIdTest() {
		ngameOrdersConsumerDao.updateExchangeStatusToWaitOpenAndExtraId(1539361l, 3l, "dd22", null);
	}

	@Test
	public void updateExchangeStatusToWaitTest() {
		ngameOrdersConsumerDao.updateExchangeStatusToWait(1539361l, 1l, "dd33", null, null,null,null,null,null,null);
	}

	@Test
	public void updateExchangeStatusToOverdueTest() {
		ngameOrdersConsumerDao.updateExchangeStatusToOverdue(1l, 1l, "ff", "gg", "ll");
	}

	@Test
	public void updateExchangeStatusToFailTest() {
		ngameOrdersConsumerDao.updateExchangeStatusToFail(1539361l,1l, "ff22", "gg22", "ll22");
	}

	@Test
	public void doTakePrizeTest() {
		ngameOrdersConsumerDao.doTakePrize(1539361l,1l);
	}

	@Test
	public void rollbackTakePrizeTest() {
		ngameOrdersConsumerDao.rollbackTakePrize(1539361l,1l);
	}

	@Test
	public void updateManualOpenPrizeExchangeStatusToWaitTest() {
		ngameOrdersConsumerDao.updateManualOpenPrizeExchangeStatusToWait(1539361l,1l, null, null,null,null,null,null,null);
	}


	@Test
	public void insertTest() {
		NgameOrdersEntity o = new NgameOrdersEntity();
		o.setAppId(1l);
		o.setConsumerId(1539361l);
		o.setOperatingActivityId(1l);
		o.setCredits(1l);
		o.setOrderStatus(0);
		o.setExchangeStatus(0);
		o.setGmtCreate(new Date());
		o.setGmtModified(new Date());
		ngameOrdersConsumerDao.insert(o);
	}

	@Test
	public void updateDeveloperBizIdTest() {
		ngameOrdersConsumerDao.updateDeveloperBizId(1539361l, 96l, "42342");
	}

	@Test
	public void updateMainOrderIdTest() {
		ngameOrdersConsumerDao.updateMainOrderId(1539361l, 96l,2l, "5555");
	}
}
