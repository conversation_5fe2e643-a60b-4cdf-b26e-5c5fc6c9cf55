package cn.com.duiba.activity.center.biz.dao.seckill;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.seckill.DuibaSeckillStockEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by wenqi.huang on 2016/11/24.
 */
@Transactional(DsConstants.DATABASE_SECKILL)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaSeckillStockDaoTest extends TransactionalTestCaseBase {
    @Resource
    private DuibaSeckillStockDao duibaSeckillStockDao;

    private ThreadLocal<DuibaSeckillStockEntity> threadLocal = new ThreadLocal<>();

    @Before
    public void testInsert() {
        DuibaSeckillStockEntity stock = TestUtils.createRandomBean(DuibaSeckillStockEntity.class);
        stock.setStock(0);
        stock.setAllStock(0);
        duibaSeckillStockDao.insert(stock);
        threadLocal.set(stock);
    }

    @Test
    public void testFindBySeckillId() {
        DuibaSeckillStockEntity e = threadLocal.get();
        DuibaSeckillStockEntity e1 = duibaSeckillStockDao.findBySeckillId(e.getSeckillId());
        Assert.assertEquals(e.getSeckillId(),e1.getSeckillId());
    }

    @Test
    public void testFind() {
        DuibaSeckillStockEntity e = threadLocal.get();
        DuibaSeckillStockEntity e1 = duibaSeckillStockDao.find(e.getId());
        Assert.assertEquals(e.getSeckillId(),e1.getSeckillId());
    }

    @Test
    public void testSetTotalStock() {
        DuibaSeckillStockEntity e = threadLocal.get();
        duibaSeckillStockDao.setTotalStock(e.getId(), 10);
        DuibaSeckillStockEntity e1 = duibaSeckillStockDao.find(e.getId());
        Assert.assertTrue(e.getStock().equals(e1.getStock()-10));
        Assert.assertTrue(e.getAllStock().equals(e1.getAllStock()));

        duibaSeckillStockDao.setTotalStock(e.getId(), -2);
        DuibaSeckillStockEntity e2 = duibaSeckillStockDao.find(e.getId());
        Assert.assertTrue(e1.getStock().equals(e2.getStock()+2));
        Assert.assertTrue(e1.getAllStock().equals(e2.getAllStock()));
    }

    @Test
    public void testSetTotalStockFlush() {
        DuibaSeckillStockEntity e = threadLocal.get();
        duibaSeckillStockDao.setTotalStock(e.getId(), 10,true);
        DuibaSeckillStockEntity e1 = duibaSeckillStockDao.find(e.getId());
        Assert.assertTrue(e.getStock().equals(e1.getStock()-10));
        Assert.assertTrue(e.getAllStock().equals(e1.getAllStock()-10));

        duibaSeckillStockDao.setTotalStock(e.getId(), -2,true);
        DuibaSeckillStockEntity e2 = duibaSeckillStockDao.find(e.getId());
        Assert.assertTrue(e1.getStock().equals(e2.getStock()+2));
        Assert.assertTrue(e1.getAllStock().equals(e2.getAllStock()+2));
    }

    @Test
    public void testFindStockBySeckillIdForUpdate() {
        DuibaSeckillStockEntity e = threadLocal.get();
        DuibaSeckillStockEntity e1 = duibaSeckillStockDao.findStockBySeckillIdForUpdate(e.getSeckillId());
        Assert.assertEquals(e.getSeckillId(), e1.getSeckillId());
    }
}
