/**
 * Project Name:activity-center-deploy
 * File Name:ActivityCategoryDaoImplTest.java
 * Package Name:cn.com.duiba.activity.center.biz.dao.category.impl
 * Date:2016年6月7日下午12:49:56
 * Copyright (c) 2016, duiba.com.cn All Rights Reserved.
 *
*/

package cn.com.duiba.activity.center.biz.dao.category.impl;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.category.ActivityCategorySpecifyDao;
import cn.com.duiba.activity.center.biz.entity.chaos.ActivityCategorySpecifyEntity;


/**
 * 
 * Author ：zhengjy <br/>
 * Create Time：2016年9月10日 上午10:19:52 <br/>
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class ActivityCategorySpecifyDaoImplTest extends TransactionalTestCaseBase {
    @Autowired
    private ActivityCategorySpecifyDao activityCategorySpecifyDao;

    private ActivityCategorySpecifyEntity entity;

    @Before
    public void before(){
    	ActivityCategorySpecifyEntity e = new ActivityCategorySpecifyEntity();
        e.setActivityCategoryId(5L);
        e.setAppId(1L);
        int ret = activityCategorySpecifyDao.insertSpecify(e);
        entity = e;
        Assert.assertTrue(ret == 1);
    }
    

    @Test
    public void testDelete() {
    	int ret = activityCategorySpecifyDao.deleteSpecify(entity.getId());
    	assertEquals(1, ret);
    }

    @Test
    public void selectSpecifyAll() {
    	List<ActivityCategorySpecifyEntity> list = activityCategorySpecifyDao.selectSpecifyAll(entity.getActivityCategoryId(), 0, 20);
    	assertTrue(list.size() >= 1);
    }

    @Test
    public void testSelectByAppSpecifys() {
    	List<ActivityCategorySpecifyEntity> list = activityCategorySpecifyDao.selectByAppSpecifys(entity.getAppId());
        assertTrue(list.size() >= 1);
    }


}

