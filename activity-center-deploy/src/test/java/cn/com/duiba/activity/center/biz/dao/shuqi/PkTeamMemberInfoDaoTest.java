package cn.com.duiba.activity.center.biz.dao.shuqi;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.shuqipk.PkTeamMemberInfoDao;
import cn.com.duiba.activity.center.biz.entity.shuqipk.PkTeamMemberInfoEntity;
import cn.com.duiba.boot.exception.BizException;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/1/6 23:21
 * @description:
 */
@Transactional(DsConstants.DATABASE_CUSTOM_RECORD)
@Rollback(value = false)
public class PkTeamMemberInfoDaoTest extends TransactionalTestCaseBase {

    @Autowired
    private PkTeamMemberInfoDao pkTeamMemberInfoDao;

    @Test
    public void testInsert() throws BizException{
	    List<PkTeamMemberInfoEntity> entityList = new ArrayList<>();
	    PkTeamMemberInfoEntity teamMember1 = new PkTeamMemberInfoEntity();
	    teamMember1.setConsumerId(111L);
	    teamMember1.setTotalReadValue(2L);
	    entityList.add(teamMember1);

	    PkTeamMemberInfoEntity teamMember2 = new PkTeamMemberInfoEntity();
	    teamMember2.setConsumerId(112L);
	    teamMember2.setTotalReadValue(3L);
	    entityList.add(teamMember2);


        int count = pkTeamMemberInfoDao.batchUpdateReadValue(entityList);
        System.out.println(count);
        Assert.assertTrue(ObjectUtils.compare(count, 0) > 0);
    }
}
