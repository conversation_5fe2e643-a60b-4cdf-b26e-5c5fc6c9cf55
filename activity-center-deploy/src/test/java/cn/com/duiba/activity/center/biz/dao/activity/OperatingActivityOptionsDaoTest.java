package cn.com.duiba.activity.center.biz.dao.activity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.activity.OperatingActivityOptionsEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/13.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class OperatingActivityOptionsDaoTest extends TransactionalTestCaseBase {

    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS_ACTIVITY;

    @Resource
    private OperatingActivityOptionsDao operatingActivityOptionsDao;

    private OperatingActivityOptionsEntity operatingActivityOptionsEntity;

    @Before
    public void testInsertOption() {
        OperatingActivityOptionsEntity e = new OperatingActivityOptionsEntity(true);
        TestUtils.setRandomAttributesForBean(e, false);
        e.setOperatingActivityId(-9L);
        operatingActivityOptionsDao.insertOption(e);
        operatingActivityOptionsEntity = e;
    }

    @Test
    public void testFindOptionById() {
        OperatingActivityOptionsEntity e = operatingActivityOptionsEntity;
        OperatingActivityOptionsEntity e1 = operatingActivityOptionsDao.findOptionById(e.getId());
        assertDO(e, e1);
    }

    @Test
    public void testFindByOperatingActivityId() {
        OperatingActivityOptionsEntity e = operatingActivityOptionsEntity;
        List<OperatingActivityOptionsEntity> list = operatingActivityOptionsDao.findByOperatingActivityId(e.getOperatingActivityId());
        assertDO(e, list.get(0));
    }

    @Test
    public void testCountByOperatingActivityId() {
        Assert.assertTrue(operatingActivityOptionsDao.countByOperatingActivityId(operatingActivityOptionsEntity.getOperatingActivityId()) > 0);
    }

    @Test
    public void testDecrementOptionRemaining() {
        OperatingActivityOptionsEntity e = operatingActivityOptionsEntity;
        operatingActivityOptionsDao.decrementOptionRemaining(e.getId());
        Assert.assertTrue(operatingActivityOptionsDao.findOptionById(e.getId()).getRemaining().equals(e.getRemaining() - 1));
    }

    @Test
    public void testIncrementOptionRemaining() {
        OperatingActivityOptionsEntity e = operatingActivityOptionsEntity;
        operatingActivityOptionsDao.incrementOptionRemaining(e.getId());
        Assert.assertTrue(operatingActivityOptionsDao.findOptionById(e.getId()).getRemaining().equals(e.getRemaining() + 1));
    }

    @Test
    public void testUpdateOptionDeleteStatus() {
        OperatingActivityOptionsEntity e = operatingActivityOptionsEntity;
        operatingActivityOptionsDao.updateOptionDeleteStatus(Arrays.asList(e.getId()));
        Assert.assertTrue(operatingActivityOptionsDao.findOptionById(e.getId()).getDeleted());
    }

    @Test
    public void testUpdatePrize() {
        OperatingActivityOptionsEntity e = operatingActivityOptionsEntity;
        e.setRate("test");
        operatingActivityOptionsDao.updatePrize(e);
        Assert.assertTrue(operatingActivityOptionsDao.findOptionById(e.getId()).getRate().equals("test"));
    }

    @Test
    public void testUpdateOption() {
        OperatingActivityOptionsEntity e = new OperatingActivityOptionsEntity(operatingActivityOptionsEntity.getId());
        TestUtils.setRandomAttributesForBean(e, false);
        operatingActivityOptionsDao.updateOption(e);
        Assert.assertTrue(operatingActivityOptionsDao.findOptionById(e.getId()).getRate().equals(e.getRate()));
    }

    @Test
    public void testFindForupdate() {
        OperatingActivityOptionsEntity e = operatingActivityOptionsEntity;
        OperatingActivityOptionsEntity e1 = operatingActivityOptionsDao.findForupdate(e.getId());
        assertDO(e, e1);
    }

    @Test
    public void testAddRemainingById() {
        OperatingActivityOptionsEntity e = operatingActivityOptionsEntity;
        operatingActivityOptionsDao.addRemainingById(e.getId(), 1);
        Assert.assertTrue(operatingActivityOptionsDao.findOptionById(e.getId()).getRemaining().equals(e.getRemaining() + 1));
    }

    @Test
    public void testSubRemainingById() {
        OperatingActivityOptionsEntity e = operatingActivityOptionsEntity;
        operatingActivityOptionsDao.subRemainingById(e.getId(), 1);
        Assert.assertTrue(operatingActivityOptionsDao.findOptionById(e.getId()).getRemaining().equals(e.getRemaining() - 1));
    }

    @Test
    public void testUpdateRemainingById() {
        OperatingActivityOptionsEntity e = operatingActivityOptionsEntity;
        operatingActivityOptionsDao.updateRemainingById(e.getId(), 42);
        Assert.assertTrue(operatingActivityOptionsDao.findOptionById(e.getId()).getRemaining().equals(42));
    }

    @Test
    public void testFindRemaingForupdate() {
        OperatingActivityOptionsEntity e = operatingActivityOptionsEntity;
        Assert.assertTrue(e.getRemaining().equals(operatingActivityOptionsDao.findRemaingForupdate(e.getId())));
    }

    private void assertDO(OperatingActivityOptionsEntity e, OperatingActivityOptionsEntity e1) {
        assertDO(e, e1, null);
    }

    private void assertDO(OperatingActivityOptionsEntity e, OperatingActivityOptionsEntity e1, String[] exculdeFields) {
        List<String> exculdeFieldsList = new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate", "gmtModified", "valid", "addrlimit","itemName"}));
        if (exculdeFields != null && exculdeFields.length > 0) {
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1, false, exculdeFieldsList.toArray(new String[exculdeFieldsList.size() + 1]));//时间字段是转为String比较的,timeQuantum不存数据库
        //  Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        //Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }

}
