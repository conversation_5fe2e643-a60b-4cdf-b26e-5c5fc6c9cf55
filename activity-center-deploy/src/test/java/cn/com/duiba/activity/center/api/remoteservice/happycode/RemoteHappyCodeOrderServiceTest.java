package cn.com.duiba.activity.center.api.remoteservice.happycode;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.happy_code.HappyCodeOrderDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.common.util.DateUtil;
import cn.com.duiba.wolf.utils.DateUtils;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Created by hww on 2018/1/3
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class RemoteHappyCodeOrderServiceTest extends TransactionalTestCaseBase {


    @Autowired
    private RemoteHappyCodeOrderService remoteHappyCodeOrderService;

    @Test
    public void testFindWaitOrderByBasicIdsAndConsumerId(){
        List<HappyCodeOrderDto> list = remoteHappyCodeOrderService.findWaitOrderByBasicIdsAndConsumerId(1539361L,Lists.newArrayList(105L,21L,54L));
        System.out.println(list.size());
    }


    @Test
    public void testFindOrderByPhaseIdAndConsumerId() {
        Long consumerId1 = (long) (Math.random() * 1000);
        HappyCodeOrderDto order1 = getOrder(consumerId1);
        Long orderId = remoteHappyCodeOrderService.insert(order1);
        List<HappyCodeOrderDto> orders = remoteHappyCodeOrderService.findOrderByPhaseIdAndConsumerId(order1.getPhaseId(), order1.getConsumerId());
        Assert.assertTrue(orders.size() == 1);
        Assert.assertTrue(Objects.equals(orders.get(0).getId(), orderId));
    }

    @Test
    public void testFindNewestOrderByPhaseIdAndCount() {
        Long consumerId1 = (long) (Math.random() * 1000);
        HappyCodeOrderDto order1 = getOrder(consumerId1);
        order1.setGmtCreate(DateUtil.minutesAddOrSub(new Date(), -10));
        remoteHappyCodeOrderService.insert(order1);
        Long consumerId2 = (long) (Math.random() * 1000);
        Long orderId2 = remoteHappyCodeOrderService.insert(getOrder(consumerId2));
        List<HappyCodeOrderDto> orders = remoteHappyCodeOrderService.findNewestOrderByPhaseIdAndCount(order1.getPhaseId(), 1);
        Assert.assertTrue(orders.size() == 1);
        Assert.assertTrue(Objects.equals(orders.get(0).getId(), orderId2));
    }
    @Test
    public void testFindOrderByIds() {
        Long consumerId1 = (long) (Math.random() * 1000);
        HappyCodeOrderDto order1 = getOrder(consumerId1);
        Long orderId1 = remoteHappyCodeOrderService.insert(order1);
        Long orderId2 = remoteHappyCodeOrderService.insert(order1);
        List<HappyCodeOrderDto> orders = remoteHappyCodeOrderService.findOrderByIds(consumerId1, Lists.newArrayList(orderId1, orderId2));
        List<Long> orderIds = Lists.transform(orders, HappyCodeOrderDto::getId);
        Assert.assertTrue(orderIds.size() == 2);
        orderIds.remove(orderId1);
        orderIds.remove(orderId2);
        Assert.assertTrue(orderIds.size() == 0);
    }
    @Test
    public void testFindOrderById() {
        Long consumerId = (long) (Math.random() * 1000);
        Long orderId = remoteHappyCodeOrderService.insert(getOrder(consumerId));
        HappyCodeOrderDto order = remoteHappyCodeOrderService.findOrderById(orderId);
        Assert.assertTrue(Objects.equals(order.getConsumerId(), consumerId));
    }

    @Test
    public void testDoTakePrize() {
        Long consumerId = (long) (Math.random() * 1000);
        HappyCodeOrderDto order = getOrder(consumerId);
        order.setExchangeStatus(HappyCodeOrderDto.EXCHANGE_STATUS_WAIT);
        Long orderId = remoteHappyCodeOrderService.insert(order);
        int skip = remoteHappyCodeOrderService.doTakePrize(order.getConsumerId(), orderId);

        HappyCodeOrderDto order2 = remoteHappyCodeOrderService.findOrderById(orderId);
        Assert.assertTrue(skip == 1);
        Assert.assertTrue(order2.getExchangeStatus() == HappyCodeOrderDto.EXCHANGE_STATUS_DONE);
    }

    @Test
    public void testDelWinnerOrder() {
        Long consumerId = (long) (Math.random() * 1000);
        HappyCodeOrderDto order = getOrder(consumerId);
        Long orderId = remoteHappyCodeOrderService.insert(order);
        Integer skip = remoteHappyCodeOrderService.delWinnerOrder(order.getPhaseId(), consumerId, null, DateUtils.secondsAddOrSub(new Date(), 2));
        Assert.assertTrue(skip == 1);
        HappyCodeOrderDto orderResult = remoteHappyCodeOrderService.findOrderById(orderId);
        Assert.assertTrue(orderResult.getDeleted() == 1);
    }

    private HappyCodeOrderDto getOrder(Long consumerId) {
        HappyCodeOrderDto order = new HappyCodeOrderDto();
        Date now = new Date();
        order.setAppId(1L);
        order.setPhaseId(-1L);
        order.setConsumerId(consumerId);
        order.setDeleted(0);
        order.setExchangeStatus(HappyCodeOrderDto.EXCHANGE_STATUS_NONE);
        order.setGmtCreate(now);
        order.setGmtModified(now);
        order.setOrderStatus(HappyCodeOrderDto.STATUS_READY);
        order.setOrigin(HappyCodeOrderDto.ORDER_ORIGIN_PLUGIN);
        order.setPartnerUserId(consumerId.toString());
        order.setHappyCode("aaa");
        return order;
    }
}
