package cn.com.duiba.activity.center.api.remoteservice.bargain;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.bargain.BargainActivityInfoDto;
import cn.com.duiba.activity.center.api.dto.bargain.BargainRecordDto;
import cn.com.duiba.activity.center.api.enums.DeletedEnum;
import cn.com.duiba.activity.center.api.params.bargain.BargainActivityInfoParam;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.api.bo.page.Page;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> (<EMAIL>)
 * @date 2018/12/7 0007 11:03
 */
@Transactional(value = DsConstants.DATABASE_ACT_RECORD)
@Rollback(value = false)
public class RemoteBargainRecordServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteBargainRecordService remoteBargainRecordService;

    @Test
    public void selectByCidAndOrderId() {
        BargainRecordDto in = remoteBargainRecordService.selectByCidAndOrderId(6L,10L);
        System.out.println(JSONObject.toJSONString(in));
    }
}
