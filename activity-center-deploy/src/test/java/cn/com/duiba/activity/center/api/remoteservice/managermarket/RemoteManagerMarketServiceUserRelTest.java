package cn.com.duiba.activity.center.api.remoteservice.managermarket;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.managermarket.ManagerMarketBackendConfigDto;
import cn.com.duiba.activity.center.api.dto.managermarket.ManagerMarketStaffExtDto;
import cn.com.duiba.activity.center.api.dto.managermarket.ManagerMarketStaffUserRelDto;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.List;

/**
 * @Author: lufeng
 * @Description:
 * @Date: Created in 2020/8/10
 */
@Rollback(value = false)
public class RemoteManagerMarketServiceUserRelTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteManagerMarketService remoteManagerMarketService;

//    @Test
//    public void testInsertConfig(){
//        ManagerMarketBackendConfigDto dto = new ManagerMarketBackendConfigDto();
//        dto.setAppId(1L);
//        dto.setOpId(1L);
//        dto.setTitle("这是标题");
//        dto.setUnit("单位");
//        dto.setStaffExcelName("员工表格1.xls");
//        dto.setStaffExcelUrl("www.baidu.com");
//        dto.setOrganExcelName("机构表格1.xls");
//        dto.setOrganExcelUrl("www.taobao.com");
//        dto.setRankSwitch(true);
//        dto.setRankPeriod("1");
//        dto.setRankLatitude("1,2");
//        dto.setChannelType("1,2");
//        ManagerMarketTaskDto taskDto = new ManagerMarketTaskDto();
//        taskDto.setAppId(1L);
//        taskDto.setType(1);
//        taskDto.setName("任务名称");
//        taskDto.setContext("文本内容");
//        taskDto.setImage("www.baidu.com");
//        taskDto.setStaffExclusiveUrl("www.baidu.com");
//        taskDto.setActivityUrl("www.baidu.com");
//        taskDto.setShareImage("分享图片");
//        taskDto.setShareTitle("分享标题");
//        taskDto.setShareSubtitle("分享副标题");
//        taskDto.setWxFriendScore(1);
//        taskDto.setWxCircleScore(3);
//        taskDto.setUrlOpenedScore(5);
//        taskDto.setBindedScore(7);
//        taskDto.setLimitScore(100);
//        taskDto.setEndTime(new Date());
//        taskDto.setMustTaskSwitch(false);
//        taskDto.setToppingSwitch(true);
//        taskDto.setToppingTime(new Date());
//        List<ManagerMarketTaskDto> list = Lists.newArrayList();
//        list.add(taskDto);
//        dto.setManagerMarketTaskDtoList(list);
//        System.out.println(remoteManagerMarketService.saveConfig(dto));
//    }

    @Test
    public void testSelectConfig() {
        ManagerMarketBackendConfigDto dto = remoteManagerMarketService.getBackendConfig(18L);
        System.out.println("title :" + dto.getTitle());
    }

    @Test
    public void testInsertUserRel() {
        ManagerMarketStaffUserRelDto staffUserRelDto = new ManagerMarketStaffUserRelDto();
        staffUserRelDto.setStaffPhone("15757101387");
        staffUserRelDto.setStaffNumber("00111");
        staffUserRelDto.setConfigId(18L);
        staffUserRelDto.setChannelType(1);
        staffUserRelDto.setAppId(1L);
        staffUserRelDto.setUserId(5559999L);
        int i = remoteManagerMarketService.insertUserRel(staffUserRelDto);
        System.out.println("保存结果：" + i);
    }

    @Test
    public void testSelect() {
        ManagerMarketStaffUserRelDto byUserId = remoteManagerMarketService.getUserRelByUserId(18L, 5559999L);
        System.out.println("查询结果：" + JSON.toJSONString(byUserId));
    }

    @Test
    public void testFindByStaffNumber() {
        List<ManagerMarketStaffUserRelDto> byStaffNumber = remoteManagerMarketService.findByStaffNumber(18L, "00111");
        System.out.println("查询结果：" + JSON.toJSONString(byStaffNumber));
    }

    @Test
    public void testGetStaffExtByUserIdFromUserRel() {
        ManagerMarketStaffExtDto staffExtByUserIdFromUserRel = remoteManagerMarketService.getStaffExtByUserIdFromUserRel(18L, 5559999L);
        System.out.println("查询结果：" + JSON.toJSONString(staffExtByUserIdFromUserRel));
    }


}
