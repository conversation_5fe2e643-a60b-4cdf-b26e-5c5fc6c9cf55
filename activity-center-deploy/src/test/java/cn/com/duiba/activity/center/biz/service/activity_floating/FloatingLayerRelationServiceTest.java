package cn.com.duiba.activity.center.biz.service.activity_floating;

import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity_floating.FloatingLayerRelationDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;

import com.google.common.collect.Lists;

/** 
 * ClassName:FloatingLayerRelationServiceTest.java <br/>
 * <AUTHOR> 
 * @date 创建时间：2017年8月10日 上午11:15:05 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class FloatingLayerRelationServiceTest extends TransactionalTestCaseBase {
	/**
	 * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
	 */
	protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS_ACTIVITY;
	@Resource
	private FloatingLayerRelationService floatingLayerRelationService;
	
	@Before
	public void testInsert(){
		List<Long> list = Lists.newArrayList();
		list.add(1L);
		list.add(2L);
		list.add(3L);
		List<String> lists = Lists.newArrayList();
		lists.add("hdtool");
		floatingLayerRelationService.saveOrUpdateFloatingLayerRelation(1L, lists, list, 1L);
	}
	
	@Test
	public void testFindByFloatingId(){
		List<FloatingLayerRelationDto> list = floatingLayerRelationService.findByFloatingId(1L);
		Assert.assertNotNull(list);
	}
}
