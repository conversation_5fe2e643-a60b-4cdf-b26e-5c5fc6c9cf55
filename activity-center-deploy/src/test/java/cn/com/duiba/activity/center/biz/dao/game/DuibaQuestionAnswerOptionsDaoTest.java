package cn.com.duiba.activity.center.biz.dao.game;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.game.DuibaQuestionAnswerOptionsEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/1.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaQuestionAnswerOptionsDaoTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaQuestionAnswerOptionsDao duibaQuestionAnswerOptionsDao;

    private ThreadLocal<DuibaQuestionAnswerOptionsEntity> duibaQuestionAnswerOptionsDO=new ThreadLocal<>();

    @Before
    public void testInsert() {
        DuibaQuestionAnswerOptionsEntity e=new DuibaQuestionAnswerOptionsEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionAnswerOptionsDao.insert(e);
        duibaQuestionAnswerOptionsDO.set(e);
    }

    @Test
    public void testFindOptionsByQuestionId() {
        Assert.assertTrue(duibaQuestionAnswerOptionsDao.findOptionsByQuestionId(duibaQuestionAnswerOptionsDO.get().getDuibaQuestionAnswerId()).size()>0);
    }

    @Test
    public void testFind() {
        DuibaQuestionAnswerOptionsEntity e=duibaQuestionAnswerOptionsDO.get();
        DuibaQuestionAnswerOptionsEntity e1=duibaQuestionAnswerOptionsDao.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindById() {
        DuibaQuestionAnswerOptionsEntity e=duibaQuestionAnswerOptionsDO.get();
        DuibaQuestionAnswerOptionsEntity e1=duibaQuestionAnswerOptionsDao.findById(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testDelete() {
        DuibaQuestionAnswerOptionsEntity e=duibaQuestionAnswerOptionsDO.get();
        duibaQuestionAnswerOptionsDao.delete(Arrays.asList(e.getId()));
        DuibaQuestionAnswerOptionsEntity e1=duibaQuestionAnswerOptionsDao.findById(e.getId());
        Assert.assertTrue(e1.getDeleted());
    }

    @Test
    public void testUpdate() {
        DuibaQuestionAnswerOptionsEntity e=duibaQuestionAnswerOptionsDO.get();
        e.setDescription("test");
        duibaQuestionAnswerOptionsDao.update(e);
        Assert.assertTrue(duibaQuestionAnswerOptionsDao.find(e.getId()).getDescription().equals("test"));
    }



    private void assertDO(DuibaQuestionAnswerOptionsEntity e, DuibaQuestionAnswerOptionsEntity e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaQuestionAnswerOptionsEntity e, DuibaQuestionAnswerOptionsEntity e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","remaining","optionCount","newOptionCount","deleted","itemName"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

}
