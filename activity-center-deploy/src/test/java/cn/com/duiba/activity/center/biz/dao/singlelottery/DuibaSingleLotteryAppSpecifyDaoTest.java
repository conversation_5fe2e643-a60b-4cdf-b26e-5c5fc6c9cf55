package cn.com.duiba.activity.center.biz.dao.singlelottery;

/**
 * Created by yansen on 16/6/17.
 */

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.singlelottery.SingleLotteryAppSpecifyEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created by yansen on 16/5/20.
 */
@Transactional(DuibaSingleLotteryAppSpecifyDaoTest.THIS_DATABASE_SCHEMA)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaSingleLotteryAppSpecifyDaoTest extends TransactionalTestCaseBase {
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS;

    @Resource
    private DuibaSingleLotteryAppSpecifyDao duibaSingleLotteryAppSpecifyDao;

    private SingleLotteryAppSpecifyEntity e;

    @Before
    public void testInsert(){
        e= getInsertAbleAppDO();
        e.setRemaining(100);
        duibaSingleLotteryAppSpecifyDao.insertAppSpecify(e);
    }

    @Test
    public void testFindAllSpecifyByDuibaSingleLottery() {
        List<SingleLotteryAppSpecifyEntity> list= duibaSingleLotteryAppSpecifyDao.findAllSpecifyByDuibaSingleLottery(e.getDuibaSingleLotteryId());
        boolean isfind=false;
        for(SingleLotteryAppSpecifyEntity s:list){
            if(s.getId().equals(e.getId())){
                isfind=true;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindSpecifyById() {
        SingleLotteryAppSpecifyEntity e1= duibaSingleLotteryAppSpecifyDao.findSpecifyById(e.getId());
        assertAppDO(e,e1);
    }

    @Test
    public void testFindSpecifyForupdate() {
        SingleLotteryAppSpecifyEntity e1= duibaSingleLotteryAppSpecifyDao.findSpecifyForupdate(e.getId());
        assertAppDO(e,e1);
    }

    @Test
    public void testReduceSpecifyAppRemaining() {
        duibaSingleLotteryAppSpecifyDao.reduceSpecifyAppRemaining(e.getDuibaSingleLotteryId(),e.getAppId());
        SingleLotteryAppSpecifyEntity e1= duibaSingleLotteryAppSpecifyDao.findSpecifyById(e.getId());
        Assert.assertEquals(e.getRemaining(),(Integer)(e1.getRemaining()+1));
    }

    @Test
    public void testAddSpecifyAppRemaining() {
        duibaSingleLotteryAppSpecifyDao.addSpecifyAppRemaining(e.getDuibaSingleLotteryId(),e.getAppId());
        SingleLotteryAppSpecifyEntity e1= duibaSingleLotteryAppSpecifyDao.findSpecifyById(e.getId());
        Assert.assertEquals(e.getRemaining(),(Integer)(e1.getRemaining()-1));
    }

    @Test
    public void testAddSpecifyOrderCount() {
        duibaSingleLotteryAppSpecifyDao.addSpecifyOrderCount(e.getDuibaSingleLotteryId(),e.getAppId());
        SingleLotteryAppSpecifyEntity e1= duibaSingleLotteryAppSpecifyDao.findSpecifyById(e.getId());
        Assert.assertEquals(e.getOrderCount(),(Integer)(e1.getOrderCount()-1));
    }

    @Test
    public void testDeleteSpecify() {
        duibaSingleLotteryAppSpecifyDao.deleteSpecify(e.getId());
        Assert.assertNull(duibaSingleLotteryAppSpecifyDao.findSpecifyById(e.getId()));
    }

    @Test
    public void testInsertAppSpecify() {
        SingleLotteryAppSpecifyEntity e1= duibaSingleLotteryAppSpecifyDao.findSpecifyById(e.getId());
        assertAppDO(e,e1);
    }

    @Test
    public void testUpdateSpecifyRemaining() {
        duibaSingleLotteryAppSpecifyDao.updateSpecifyRemaining(e.getId(),42);
        SingleLotteryAppSpecifyEntity e1= duibaSingleLotteryAppSpecifyDao.findSpecifyById(e.getId());
        Assert.assertEquals(e1.getRemaining(),new Integer(42));
    }

    @Test
    public void testFindSpecifyByDuibaSingleLotteryAndApp() {
        Assert.assertEquals(duibaSingleLotteryAppSpecifyDao.findSpecifyByDuibaSingleLotteryAndApp(e.getDuibaSingleLotteryId(),e.getAppId()).getRemaining(),e.getRemaining());
    }

    private SingleLotteryAppSpecifyEntity getInsertAbleAppDO(){
        SingleLotteryAppSpecifyEntity e=new SingleLotteryAppSpecifyEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        return e;
    }

    private void assertAppDO(SingleLotteryAppSpecifyEntity e,SingleLotteryAppSpecifyEntity e1){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified"}));

        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }

}
