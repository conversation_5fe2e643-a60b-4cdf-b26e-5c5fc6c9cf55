package cn.com.duiba.activity.center.biz.remoteservice.quizz;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.quizz.DuibaQuizzBrickDto;
import cn.com.duiba.activity.center.api.remoteservice.quizz.RemoteDuibaQuizzBrickService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ)
@Ignore
public class RemoteDuibaQuizzBrickServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private RemoteDuibaQuizzBrickService duibaQuizzBrickService;
	
	private DuibaQuizzBrickDto info;
	
	private String[] exceptFields = new String[] { "gmtCreate", "gmtModified" };
	
	@Before
    public void insertTest(){
		info = TestUtils.createRandomBean(DuibaQuizzBrickDto.class);
		duibaQuizzBrickService.insert(info);
    }
	
	@Test
	public void findTest(){
		DuibaQuizzBrickDto infoTest = duibaQuizzBrickService.find(info.getId());
		TestUtils.assertEqualsReflect(infoTest, info, false, exceptFields);
	}

	@Test
    public void getBrickContentByIdTest(){
    	String content = duibaQuizzBrickService.getBrickContentById(info.getId());
    	Assert.assertEquals(content, info.getContent());
    }

	@Test
    public void findNoContentTest(){
		DuibaQuizzBrickDto infoTest = duibaQuizzBrickService.findNoContent(info.getId());
    	Assert.assertNotNull(infoTest);
    }

    @Test
	public void update4AdminTest(){
    	DuibaQuizzBrickDto test = TestUtils.createRandomBean(DuibaQuizzBrickDto.class);
    	duibaQuizzBrickService.update4Admin(info.getId(), test.getTitle(), test.getContent(), test.getMd5());
		String title = duibaQuizzBrickService.find(info.getId()).getTitle();
		Assert.assertTrue(title.equals(test.getTitle()));
	}

    @Test
	public void findByTitleTest(){
    	DuibaQuizzBrickDto infoTest = duibaQuizzBrickService.findByTitle(info.getTitle());
		TestUtils.assertEqualsReflect(infoTest, info, false, exceptFields);
	}
	
    @Test
	public void openTest(){
    	duibaQuizzBrickService.open(info.getId());
		int status = duibaQuizzBrickService.find(info.getId()).getStatus();
		Assert.assertTrue(status == 1);
	}
	
    @Test
	public void disableTest(){
    	duibaQuizzBrickService.disable(info.getId());
		int status = duibaQuizzBrickService.find(info.getId()).getStatus();
		Assert.assertTrue(status == 0);
	}
	
    @Test
	public void findPageTest(){
    	DuibaQuizzBrickDto test = TestUtils.createRandomBean(DuibaQuizzBrickDto.class);
    	test.setDeleted(false);
    	duibaQuizzBrickService.insert(test);
		Map<String, Object> queryMap = new HashMap<>();
		queryMap.put("offset", 0);
		queryMap.put("max", 10);
		List<DuibaQuizzBrickDto> list = duibaQuizzBrickService.findPage(queryMap);
		Assert.assertTrue(list.size() > 0);
	}
	
    @Test
	public void findPageCountTest(){
    	DuibaQuizzBrickDto test = TestUtils.createRandomBean(DuibaQuizzBrickDto.class);
    	test.setDeleted(false);
    	duibaQuizzBrickService.insert(test);
		long count = duibaQuizzBrickService.findPageCount();
		Assert.assertTrue(count > 0);
	}
	
    @Test
	public void findAllTest(){
    	DuibaQuizzBrickDto test = TestUtils.createRandomBean(DuibaQuizzBrickDto.class);
    	test.setStatus(1);
    	duibaQuizzBrickService.insert(test);
		List<DuibaQuizzBrickDto> list = duibaQuizzBrickService.findAll();
		Assert.assertTrue(list.size() > 0);
	}
}
