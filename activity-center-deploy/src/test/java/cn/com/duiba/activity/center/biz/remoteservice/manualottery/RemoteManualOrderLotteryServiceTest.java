package cn.com.duiba.activity.center.biz.remoteservice.manualottery;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.manual.ManualLotteryOrderDto;
import cn.com.duiba.activity.center.api.remoteservice.manual.RemoteManualOrderLotteryService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * 
 * ClassName: RemoteManualOrderLotteryServiceTest <br/>
 * date: 2016年12月1日 下午8:04:50 <br/>
 *
 * @version 
 * @since JDK 1.6
 */
@Transactional(DsConstants.DATABASE_CREDITS)
@Ignore
public class RemoteManualOrderLotteryServiceTest extends TransactionalTestCaseBase{

	@Autowired
	private RemoteManualOrderLotteryService manualOrderLotteryService;
	
	private ManualLotteryOrderDto info;
	
	private String[] exceptFields = new String[] { "gmtCreate", "gmtModified", "winDate", "shipDate" };
	
	@Before
	public void insertTest(){
		ManualLotteryOrderDto e = new ManualLotteryOrderDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		manualOrderLotteryService.insert(e);
		info = e;
	}
	
	/**
     * 查询用户参与活动次数
     */
	@Test
    public void countByConsumerIdAndOperatingActivityIdTest(){
    	int num = manualOrderLotteryService.countByConsumerIdAndOperatingActivityId(info.getConsumerId(), info.getOperatingActivityId());
    	Assert.assertTrue(num > 0);
    }

    /**
     * 查询用户在时间段内参与活动次数
     */
	@Test
    public void countByConsumerIdAndOperatingActivityIdAndDateTest(){
    	Calendar calendar = Calendar.getInstance();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	Date start = calendar.getTime();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, 1);
    	Date end = calendar.getTime();
    	int count = manualOrderLotteryService.countByConsumerIdAndOperatingActivityIdAndDate(info.getConsumerId(), info.getOperatingActivityId(), start, end);
    	Assert.assertTrue(count > 0);
    }

    /**
     * 查询用户免费参与活动次数
     */
	@Test
    public void countFreeByConsumerIdAndOperatingActivityIdTest(){
    	ManualLotteryOrderDto e = new ManualLotteryOrderDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setCredits(0L);
		manualOrderLotteryService.insert(e);
		int count = manualOrderLotteryService.countFreeByConsumerIdAndOperatingActivityId(e.getConsumerId(), e.getOperatingActivityId());
		Assert.assertTrue(count > 0);
    }

    /**
     * 查询用户在时间段内免费参与活动次数
     */
	@Test
    public void countFreeByConsumerIdAndOperatingActivityIdAndDateTest(){
    	ManualLotteryOrderDto e = new ManualLotteryOrderDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setCredits(0L);
		manualOrderLotteryService.insert(e);
		Calendar calendar = Calendar.getInstance();
    	calendar.setTime(e.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	Date start = calendar.getTime();
    	calendar.setTime(e.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, 1);
    	Date end = calendar.getTime();
    	int count = manualOrderLotteryService.countFreeByConsumerIdAndOperatingActivityIdAndDate(e.getConsumerId(), e.getOperatingActivityId(), start, end);
    	Assert.assertTrue(count > 0);
    }

    /**
     * 条件搜索开奖记录
     *
     * @param paramMap
     * @return
     */
	@Test
    public void findAllOpenManualLotteryByConditionTest(){
    	ManualLotteryOrderDto e = new ManualLotteryOrderDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setStatus(1);
		e.setExchangeStatus(1);
		manualOrderLotteryService.insert(e);
    	Map<String, Object> paramMap = new HashMap<>();
    	paramMap.put("operatingActivityId", e.getOperatingActivityId());
    	paramMap.put("partnerUserId", e.getPartnerUserId());
    	paramMap.put("phone", e.getPhone());
    	List<ManualLotteryOrderDto> list = manualOrderLotteryService.findAllOpenManualLotteryByCondition(paramMap);
    	Assert.assertTrue(list.size() > 0);
    }

    /**
     * 条件搜索开奖记录条数
     *
     * @param paramMap
     * @return
     */
	@Test
    public void findAllOpenManualLotteryCountTest(){
    	ManualLotteryOrderDto e = new ManualLotteryOrderDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setStatus(1);
		e.setExchangeStatus(1);
		manualOrderLotteryService.insert(e);
		Map<String, Object> map = new HashMap<>();
		map.put("operatingActivityId", e.getOperatingActivityId());
		long count = manualOrderLotteryService.findAllOpenManualLotteryCount(map);
		Assert.assertTrue(count > 0);
    }

    /**
     * 查询中奖清单
     *
     * @return
     */
	@Test
    public void findWardListTest(){
    	ManualLotteryOrderDto e = new ManualLotteryOrderDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setExchangeStatus(2);
		manualOrderLotteryService.insert(e);
		List<ManualLotteryOrderDto> list = manualOrderLotteryService.findWardList(e.getOperatingActivityId());
		Assert.assertTrue(list.size() > 0);
    }

    /**
     * 查询参与清单
     *
     * @param paramMap
     * @return
     */
	@Test
    public void findNoWardListTest(){
		ManualLotteryOrderDto e = new ManualLotteryOrderDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setExchangeStatus(1);
    	manualOrderLotteryService.insert(e);
    	Map<String, Object> map = new HashMap<>();
    	map.put("operatingActivityId", e.getOperatingActivityId());
    	map.put("partnerUserId", e.getPartnerUserId());
    	map.put("phone", e.getPhone());
    	map.put("offset", 0);
    	map.put("max", 10);
    	List<ManualLotteryOrderDto> list = manualOrderLotteryService.findNoWardList(map);
    	Assert.assertTrue(list.size() > 0);
    }

    /**
     * 查询参与清单count
     *
     * @param paramMap
     * @return
     */
	@Test
    public void findNoWardListCountTest(){
		ManualLotteryOrderDto e = new ManualLotteryOrderDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setExchangeStatus(1);
    	manualOrderLotteryService.insert(e);
    	Map<String, Object> map = new HashMap<>();
    	map.put("operatingActivityId", e.getOperatingActivityId());
    	map.put("partnerUserId", e.getPartnerUserId());
    	map.put("phone", e.getPhone());
    	long count = manualOrderLotteryService.findNoWardListCount(map);
    	Assert.assertTrue(count > 0);
    }

    /**
     * 根据ids查询待开奖的用户list
     *
     * @param paramMap
     * @return
     */
	@Test
    public void findStartOpenListByIdsTest(){
		ManualLotteryOrderDto e = new ManualLotteryOrderDto(info.getId());
		TestUtils.setRandomAttributesForBean(e, false);
    	e.setExchangeStatus(1);
    	manualOrderLotteryService.update(e);
    	List<Long> ids = new ArrayList<Long>();
    	Map<String, Object> map = new HashMap<>();
    	ids.add(e.getId());
    	map.put("ids", ids);
    	map.put("activityId", e.getOperatingActivityId());
    	List<ManualLotteryOrderDto> list = manualOrderLotteryService.findStartOpenListByIds(map);
    	Assert.assertTrue(list.size() > 0);
    }

    /**
     * 更新中奖用户状态
     *
     * @param id
     * @param activityId
     * @param date
     * @return
     */
	@Test
    public void updateAwardTest(){
    	manualOrderLotteryService.updateAward(info.getId(), info.getOperatingActivityId(), info.getWinDate());
    	int exchangeStatus = manualOrderLotteryService.find(info.getId()).getExchangeStatus();
    	Assert.assertTrue(exchangeStatus == 2);
    }

    /**
     * 随机抽一条
     *
     * @param paramMap
     * @return
     */
	@Test
    public void randomFindManualLotteryTest(){
		ManualLotteryOrderDto e = new ManualLotteryOrderDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
    	e.setStatus(1);
    	e.setExchangeStatus(1);
    	manualOrderLotteryService.insert(e);
    	Map<String, Object> map = new HashMap<>();
    	map.put("operatingActivityId", e.getOperatingActivityId());
    	map.put("randomInt", 0);
    	ManualLotteryOrderDto test = manualOrderLotteryService.randomFindManualLottery(map);
    	TestUtils.assertEqualsReflect(e, test, false, exceptFields);
    }

    /**
     * 更新未中奖用户
     *
     * @param paramMap
     */
	@Test
    public void updateNoAwardListTest(){
		ManualLotteryOrderDto e = new ManualLotteryOrderDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
    	e.setExchangeStatus(1);
    	manualOrderLotteryService.insert(e);
    	Map<String, Object> map = new HashMap<>();
    	map.put("date", e.getWinDate());
    	map.put("activityId", e.getOperatingActivityId());
    	manualOrderLotteryService.updateNoAwardList(map);
    	int exchangeStatus = manualOrderLotteryService.find(e.getId()).getExchangeStatus();
    	Assert.assertTrue(exchangeStatus == 3);    	
    }

    /**
     * 查询未中奖用户
     *
     * @param paramMap
     * @return
     */
	@Test
    public void findAllNoAwardListTest(){
		ManualLotteryOrderDto e = new ManualLotteryOrderDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
    	e.setExchangeStatus(1);
    	manualOrderLotteryService.insert(e);
    	Map<String, Object> map = new HashMap<>();
    	map.put("activityId", e.getOperatingActivityId());
    	List<ManualLotteryOrderDto> list = manualOrderLotteryService.findAllNoAwardList(map);
    	Assert.assertTrue(list.size() > 0);
    }

    /**
     * 查询所有ids
     *
     * @param ids
     * @return
     */
	@Test
    public void findAllByIdsTest(){
    	List<Long> ids = new ArrayList<Long>();
    	ids.add(info.getId());
    	List<ManualLotteryOrderDto> list = manualOrderLotteryService.findAllByIds(ids);
    	TestUtils.assertEqualsReflect(info, list.get(0), false, exceptFields);
    }

    /**
     * 根据订单号查询
     *
     * @param orderId
     * @return
     */
	@Test
    public void findByOrderIdTest(){
    	ManualLotteryOrderDto test = manualOrderLotteryService.findByOrderId(info.getOrderId());
    	TestUtils.assertEqualsReflect(info, test, false, exceptFields);
    }

	@Test
    public void findByAppAndDeveloperBizIdTest(){
    	ManualLotteryOrderDto test = manualOrderLotteryService.findByAppAndDeveloperBizId(info.getAppId(), info.getDeveloperBizId());
    	TestUtils.assertEqualsReflect(info, test, false, exceptFields);
    }

	@Test
    public void updateTest(){
		ManualLotteryOrderDto e = new ManualLotteryOrderDto(info.getId());
		TestUtils.setRandomAttributesForBean(e, false);
    	manualOrderLotteryService.update(e);
    	ManualLotteryOrderDto test = manualOrderLotteryService.find(e.getId());
    	TestUtils.assertEqualsReflect(e, test, false, exceptFields);
    }

	@Test
    public void findTest(){
    	ManualLotteryOrderDto test = manualOrderLotteryService.find(info.getId());
    	TestUtils.assertEqualsReflect(info, test, false, exceptFields);
    }
}
