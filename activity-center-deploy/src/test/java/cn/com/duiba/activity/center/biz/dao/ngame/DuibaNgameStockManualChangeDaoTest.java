package cn.com.duiba.activity.center.biz.dao.ngame;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.ngame.DuibaNgameStockManualChangeEntity;

@Transactional(DsConstants.DATABASE_NGAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaNgameStockManualChangeDaoTest extends TransactionalTestCaseBase{

	@Resource
	private DuibaNgameStockManualChangeDao duibaNgameStockManualChangeDao;

	@Test
	public void findByStockIdTest() {
		List<DuibaNgameStockManualChangeEntity> list = duibaNgameStockManualChangeDao.findByStockId(31l);
		System.out.println(list.size()+",,,,");
	}
	
	@Test
	public void addBatchTest() {
		List<DuibaNgameStockManualChangeEntity> list = new ArrayList<DuibaNgameStockManualChangeEntity>();
		DuibaNgameStockManualChangeEntity c = new DuibaNgameStockManualChangeEntity();
		c.setGameStockId(31l);
		c.setOptionId(33l);
		c.setChangeKind(1);
		c.setChangeQuantity(2);
		c.setBeforeStock(8);
		c.setAfterStock(10);
		list.add(c);
		c = new DuibaNgameStockManualChangeEntity();
		c.setGameStockId(31l);
		c.setOptionId(33l);
		c.setChangeKind(1);
		c.setChangeQuantity(1);
		c.setBeforeStock(10);
		c.setAfterStock(11);
		list.add(c);
		duibaNgameStockManualChangeDao.addBatch(list);
	}
	
	@Test
	public void addTest() {
		DuibaNgameStockManualChangeEntity c = new DuibaNgameStockManualChangeEntity();
		c.setGameStockId(31l);
		c.setOptionId(33l);
		c.setChangeKind(0);
		c.setChangeQuantity(3);
		c.setBeforeStock(11);
		c.setAfterStock(8);
		duibaNgameStockManualChangeDao.add(c);
	}
}
