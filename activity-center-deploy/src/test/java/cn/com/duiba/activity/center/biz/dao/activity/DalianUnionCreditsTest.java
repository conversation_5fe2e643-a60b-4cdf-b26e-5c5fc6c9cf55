package cn.com.duiba.activity.center.biz.dao.activity;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.cctv.TaskUnitDto;
import cn.com.duiba.activity.center.api.remoteservice.cctv.RemoteTaskUnitService;
import cn.com.duiba.activity.center.biz.remoteservice.impl.cctv.RemoteTaskUnitServiceImpl;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

public class DalianUnionCreditsTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteTaskUnitService remoteTaskUnitService;

    @Test
    public void batchInsert() {
        TaskUnitDto dto = new TaskUnitDto();
        dto.setAppId(19224L);
        dto.setConsumerId(100960247L);
        dto.setTaskType("dalian_unionPay_task");
        dto.setScopeTag(20210910);
        dto.setTaskScope(0);
        remoteTaskUnitService.batchInsert(Collections.singletonList(dto));
    }


    @Test
    public void findByRecordId() {
        TaskUnitDto record3 = remoteTaskUnitService.findRecordById(3L);
        TaskUnitDto record1 = remoteTaskUnitService.findRecordById(1L);
        System.out.println(record1.isDeleted()+"    "+record3.isDeleted());
    }
}
