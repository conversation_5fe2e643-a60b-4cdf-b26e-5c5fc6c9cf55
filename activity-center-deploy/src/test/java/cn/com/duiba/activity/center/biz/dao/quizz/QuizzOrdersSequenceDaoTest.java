package cn.com.duiba.activity.center.biz.dao.quizz;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;

@Transactional(DsConstants.DATABASE_QUIZZ_CONSUMER)
public class QuizzOrdersSequenceDaoTest extends TransactionalTestCaseBase{

	@Autowired
	private QuizzOrdersSequenceDao quizzOrdersSequenceDao;
	
	@Test
	public void getId() {
		long num = quizzOrdersSequenceDao.getId();
		Assert.assertTrue(num > 0);
	}
}
