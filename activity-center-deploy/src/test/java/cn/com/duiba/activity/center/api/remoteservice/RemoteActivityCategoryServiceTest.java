package cn.com.duiba.activity.center.api.remoteservice;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/6/13.
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class RemoteActivityCategoryServiceTest extends TransactionalTestCaseBase {
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS_ACTIVITY;
    @Resource
    private RemoteActivityCategoryService remoteActivityCategoryService;
    @Test
    public void testfindAll() {
    	remoteActivityCategoryService.findById(1L);
    }
    
    @Test
    public void testfindAllActivitiesByAppCategory() {
    	remoteActivityCategoryService.findAllActivitiesByAppCategory(1L, 1L, 0, 20);
    }
    
    @Test
    public void testisCategoryEnable() {
        remoteActivityCategoryService.isCategoryEnable(1L, 13L);
    }
}
