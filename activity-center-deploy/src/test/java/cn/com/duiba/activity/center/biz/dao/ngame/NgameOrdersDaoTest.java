package cn.com.duiba.activity.center.biz.dao.ngame;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;

@Transactional(DsConstants.DATABASE_NGAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class NgameOrdersDaoTest extends TransactionalTestCaseBase {

	@Resource
	private NgameOrdersDao ngameOrdersDao;

	@Test
	public void findTest() {
		System.out.println(ngameOrdersDao.find(17l).getOperatingActivityId());
	}
	
	@Test
	public void findOverdueOrderTest() {
		System.out.println(ngameOrdersDao.findOverdueOrder().size());
	}
}
