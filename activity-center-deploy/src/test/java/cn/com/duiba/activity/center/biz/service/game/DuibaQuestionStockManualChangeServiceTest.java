package cn.com.duiba.activity.center.biz.service.game;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionStockManualChangeDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/2.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaQuestionStockManualChangeServiceTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaQuestionStockManualChangeService duibaQuestionStockManualChangeService;

    @Test
    public void testAddBatch() {
        DuibaQuestionStockManualChangeDto duibaQuestionStockManualChangeDto =new DuibaQuestionStockManualChangeDto();
        TestUtils.setRandomAttributesForBean(duibaQuestionStockManualChangeDto,false);
        List<DuibaQuestionStockManualChangeDto> list=new ArrayList<>();
        list.add(duibaQuestionStockManualChangeDto);
        duibaQuestionStockManualChangeService.addBatch(list);
    }

    @Test
    public void testAdd() {
        DuibaQuestionStockManualChangeDto duibaQuestionStockManualChangeDto =new DuibaQuestionStockManualChangeDto();
        TestUtils.setRandomAttributesForBean(duibaQuestionStockManualChangeDto,false);
        duibaQuestionStockManualChangeService.add(duibaQuestionStockManualChangeDto);
    }

    @Test
    public void testFindByStockId() {
        DuibaQuestionStockManualChangeDto duibaQuestionStockManualChangeDto =new DuibaQuestionStockManualChangeDto();
        TestUtils.setRandomAttributesForBean(duibaQuestionStockManualChangeDto,false);
        duibaQuestionStockManualChangeService.add(duibaQuestionStockManualChangeDto);
        Assert.assertNotNull(duibaQuestionStockManualChangeService.findByStockId(duibaQuestionStockManualChangeDto.getQuestionStockId()));
    }

}
