package cn.com.duiba.activity.center.biz.dao.quizz;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.quizz.DuibaQuizzOptionsEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ)
public class DuibaQuizzOptionsDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaQuizzOptionsDao duibaQuizzOptionsDao;
	
	private DuibaQuizzOptionsEntity info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaQuizzOptionsEntity.class);
		duibaQuizzOptionsDao.insert(info);
	}
	
	@Test
	public void findTest(){
		Assert.assertNotNull(duibaQuizzOptionsDao.find(info.getId()));
	}
	
	@Test
	public void findOptionsByQuizzIdTest(){
		DuibaQuizzOptionsEntity e = TestUtils.createRandomBean(DuibaQuizzOptionsEntity.class);
		e.setDeleted(false);
		duibaQuizzOptionsDao.insert(e);
		List<DuibaQuizzOptionsEntity> list = duibaQuizzOptionsDao.findOptionsByQuizzId(e.getDuibaQuizzId());
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void deleteTest(){
		List<Long> ids = new ArrayList<Long>();
		ids.add(info.getId());
		duibaQuizzOptionsDao.delete(ids);
		DuibaQuizzOptionsEntity e = duibaQuizzOptionsDao.find(info.getId());
		Assert.assertNull(e);
    }
     
	@Test
	public void updateInfoFormTest(){
		DuibaQuizzOptionsEntity e = TestUtils.createRandomBean(DuibaQuizzOptionsEntity.class);
		e.setId(info.getId());
		duibaQuizzOptionsDao.updateInfoForm(e);
		DuibaQuizzOptionsEntity e1 = duibaQuizzOptionsDao.find(info.getId());
		Assert.assertEquals(e.getDescription(), e1.getDescription());
    }

//	@Test 字段不对
//    public int updateRemainingByIdTest(Long id, Integer remaining){
//    	 
//    }
}
