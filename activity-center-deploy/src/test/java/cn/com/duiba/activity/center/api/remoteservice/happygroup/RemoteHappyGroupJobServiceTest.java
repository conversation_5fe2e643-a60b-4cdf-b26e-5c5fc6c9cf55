package cn.com.duiba.activity.center.api.remoteservice.happygroup;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupBaseConfigDto;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupConfigDto;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/3/5 11:41
 * @description:
 */
@Rollback(value = false)
public class RemoteHappyGroupJobServiceTest extends TransactionalTestCaseBase {

    @Autowired
    RemoteHappyGroupJobService remoteHappyGroupJobService;

    @Test
    public void testExpireGroup() {
        remoteHappyGroupJobService.expireGroup();
    }
}
