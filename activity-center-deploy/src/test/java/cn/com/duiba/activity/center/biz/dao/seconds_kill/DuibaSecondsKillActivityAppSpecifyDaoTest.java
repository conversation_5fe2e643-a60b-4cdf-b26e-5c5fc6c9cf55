package cn.com.duiba.activity.center.biz.dao.seconds_kill;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.seconds_kill.DuibaSecondsKillActivityAppSpecifyEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_CREDITS)
public class DuibaSecondsKillActivityAppSpecifyDaoTest extends TransactionalTestCaseBase{
	
	@Autowired
	private DuibaSecondsKillActivityAppSpecifyDao duibaSecondsKillActivityAppSpecifyDao;
	
	private DuibaSecondsKillActivityAppSpecifyEntity info;
	
	private String[] exceptFields = new String[] { "gmtCreate", "gmtModified" };
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaSecondsKillActivityAppSpecifyEntity.class);
		duibaSecondsKillActivityAppSpecifyDao.insert(info);
	}
	
	@Test
	public void findByDuibaSecondsKillActivityIdTest(){
		List<DuibaSecondsKillActivityAppSpecifyEntity> list = duibaSecondsKillActivityAppSpecifyDao.findByDuibaSecondsKillActivityId(info.getDuibaSecondsKillActivityId());
		Assert.assertTrue(list.size() > 0);
	}
	
	@Test
	public void deleteTest(){
		DuibaSecondsKillActivityAppSpecifyEntity test = TestUtils.createRandomBean(DuibaSecondsKillActivityAppSpecifyEntity.class);
		duibaSecondsKillActivityAppSpecifyDao.insert(test);
		duibaSecondsKillActivityAppSpecifyDao.delete(test.getId());
		DuibaSecondsKillActivityAppSpecifyEntity e = duibaSecondsKillActivityAppSpecifyDao.find(test.getId());
		Assert.assertNull(e);
	}
	
	@Test
	public void findTest(){
		DuibaSecondsKillActivityAppSpecifyEntity test = duibaSecondsKillActivityAppSpecifyDao.find(info.getId());
		TestUtils.assertEqualsReflect(info, test, false, exceptFields);
	}
	
	@Test
	public void findByDuibaSecondsKillActivityAndAppTest(){
		DuibaSecondsKillActivityAppSpecifyEntity test = duibaSecondsKillActivityAppSpecifyDao.findByDuibaSecondsKillActivityAndApp(info.getDuibaSecondsKillActivityId(), info.getAppId());
		TestUtils.assertEqualsReflect(info, test, false, exceptFields);
	}
	
	@Test
	public void findByDuibaActivityIdsAndAppTest(){
		List<Long> activityIds = new ArrayList<Long>();
		activityIds.add(info.getDuibaSecondsKillActivityId());
		List<DuibaSecondsKillActivityAppSpecifyEntity> list = duibaSecondsKillActivityAppSpecifyDao.findByDuibaActivityIdsAndApp(activityIds, info.getAppId());
		Assert.assertTrue(list.size() > 0);
	}
	
}
