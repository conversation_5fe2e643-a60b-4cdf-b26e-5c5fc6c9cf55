package cn.com.duiba.activity.center.biz.remoteservice.seconds_kill;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.seconds_kill.DuibaSecondsKillActivityAppSpecifyDto;
import cn.com.duiba.activity.center.api.remoteservice.seconds_kill.RemoteDuibaSecondsKillActivityAppSpecifyService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_CREDITS)
@Ignore
public class RemoteDuibaSecondsKillActivityAppSpecifyServiceTest extends TransactionalTestCaseBase{
	
	@Autowired
	private RemoteDuibaSecondsKillActivityAppSpecifyService duibaSecondsKillActivityAppSpecifyService;
	
	private DuibaSecondsKillActivityAppSpecifyDto info;
	
	private String[] exceptFields = new String[] { "gmtCreate", "gmtModified" };
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaSecondsKillActivityAppSpecifyDto.class);
		duibaSecondsKillActivityAppSpecifyService.insert(info);
	}
	
	@Test
	public void findByDuibaActivityAndAppTest(){
		DuibaSecondsKillActivityAppSpecifyDto test = duibaSecondsKillActivityAppSpecifyService.findByDuibaActivityAndApp(info.getDuibaSecondsKillActivityId(), info.getAppId());
		TestUtils.assertEqualsReflect(info, test, false, exceptFields);
	}

	@Test
	public void findByDuibaSecondsKillActivityIdTest(){
		List<DuibaSecondsKillActivityAppSpecifyDto> list = duibaSecondsKillActivityAppSpecifyService.findByDuibaSecondsKillActivityId(info.getDuibaSecondsKillActivityId());
		Assert.assertTrue(list.size() > 0);
	}
	
	@Test
	public void deleteTest(){
		DuibaSecondsKillActivityAppSpecifyDto test = TestUtils.createRandomBean(DuibaSecondsKillActivityAppSpecifyDto.class);
		duibaSecondsKillActivityAppSpecifyService.insert(test);
		duibaSecondsKillActivityAppSpecifyService.delete(test.getId());
		DuibaSecondsKillActivityAppSpecifyDto e = duibaSecondsKillActivityAppSpecifyService.find(test.getId());
		Assert.assertNull(e);
	}
	
	@Test
	public void findTest(){
		DuibaSecondsKillActivityAppSpecifyDto test = duibaSecondsKillActivityAppSpecifyService.find(info.getId());
		TestUtils.assertEqualsReflect(info, test, false, exceptFields);
	}
	
	@Test
	public void findByDuibaSecondsKillActivityAndAppTest(){
		DuibaSecondsKillActivityAppSpecifyDto test = duibaSecondsKillActivityAppSpecifyService.findByDuibaSecondsKillActivityAndApp(info.getDuibaSecondsKillActivityId(), info.getAppId());
		TestUtils.assertEqualsReflect(info, test, false, exceptFields);
	}
	
	@Test
	public void findByDuibaActivityIdsAndAppTest(){
		List<Long> activityIds = new ArrayList<Long>();
		activityIds.add(info.getDuibaSecondsKillActivityId());
		List<DuibaSecondsKillActivityAppSpecifyDto> list = duibaSecondsKillActivityAppSpecifyService.findByDuibaActivityIdsAndApp(activityIds, info.getAppId());
		Assert.assertTrue(list.size() > 0);
	}
	
}
