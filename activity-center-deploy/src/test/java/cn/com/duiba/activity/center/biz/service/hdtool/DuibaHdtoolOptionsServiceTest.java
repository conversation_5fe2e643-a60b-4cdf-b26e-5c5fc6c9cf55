package cn.com.duiba.activity.center.biz.service.hdtool;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.hdtool.DuibaHdtoolOptionsDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/25.
 */
@Transactional(DsConstants.DATABASE_PRIZE_CONF)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaHdtoolOptionsServiceTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaHdtoolOptionsService duibaHdtoolOptionsService;

    private DuibaHdtoolOptionsDto duibaHdtoolOptionsDto;

    @Before
    public void prepareData() {
        DuibaHdtoolOptionsDto e=new DuibaHdtoolOptionsDto(true);
        e.setRolling(1);
        TestUtils.setRandomAttributesForBean(e,false);
        duibaHdtoolOptionsService.insertHdtoolOption(e);
        duibaHdtoolOptionsDto =e;
    }

    @Test
    public void testFindByHdtoolId() {
        Assert.assertNotNull(duibaHdtoolOptionsService.findByHdtoolId(duibaHdtoolOptionsDto.getDuibaHdtoolId()));
    }

    @Test
    public void testAddRemainingById() {
        DuibaHdtoolOptionsDto e= duibaHdtoolOptionsDto;
        duibaHdtoolOptionsService.addRemainingById(e.getId(),1);
        Assert.assertTrue(duibaHdtoolOptionsService.findOptionById(e.getId()).getRemaining().equals(e.getRemaining()+1));
    }

    @Test
    public void testSubRemainingById() {
        DuibaHdtoolOptionsDto e= duibaHdtoolOptionsDto;
        duibaHdtoolOptionsService.subRemainingById(e.getId(),1);
        Assert.assertTrue(duibaHdtoolOptionsService.findOptionById(e.getId()).getRemaining().equals(e.getRemaining()-1));
    }

    @Test
    public void testUpdateRemainingById() {
        DuibaHdtoolOptionsDto e= duibaHdtoolOptionsDto;
        duibaHdtoolOptionsService.updateRemainingById(e.getId(),42);
        Assert.assertTrue(duibaHdtoolOptionsService.findOptionById(e.getId()).getRemaining().equals(42));
    }

    @Test
    public void testfindRemaingForupdate() {
        DuibaHdtoolOptionsDto e= duibaHdtoolOptionsDto;
        Assert.assertTrue( duibaHdtoolOptionsService.findRemaingForupdate(e.getId()).equals(e.getRemaining()));
    }

    @Test
    public void testFindOptionsByDuibaHdtoolId() {
        DuibaHdtoolOptionsDto duibaHdtoolOptionsDto =new DuibaHdtoolOptionsDto(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-1L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsService.insertHdtoolOption(duibaHdtoolOptionsDto);
        Assert.assertTrue(duibaHdtoolOptionsService.findOptionsByDuibaHdtoolId(-1L).size()>0);
    }

    @Test
    public void testCountOptionsByHdtoolId() {
        DuibaHdtoolOptionsDto duibaHdtoolOptionsDto =new DuibaHdtoolOptionsDto(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-2L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsService.insertHdtoolOption(duibaHdtoolOptionsDto);
        Assert.assertTrue(duibaHdtoolOptionsService.countOptionsByHdtoolId(-2L)>0);
    }

    @Test
    public void testFindOptionById() {
        DuibaHdtoolOptionsDto duibaHdtoolOptionsDto =new DuibaHdtoolOptionsDto(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-3L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsService.insertHdtoolOption(duibaHdtoolOptionsDto);
        assertOptionDO(duibaHdtoolOptionsDto,duibaHdtoolOptionsService.findOptionById(duibaHdtoolOptionsDto.getId()));
    }

    @Test
    public void testFindOptionByIdForupdate() {
        DuibaHdtoolOptionsDto duibaHdtoolOptionsDto =new DuibaHdtoolOptionsDto(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-4L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsService.insertHdtoolOption(duibaHdtoolOptionsDto);
        assertOptionDO(duibaHdtoolOptionsDto,duibaHdtoolOptionsService.findOptionByIdForupdate(duibaHdtoolOptionsDto.getId()));
    }

    @Test
    public void testFindHasUserdHdIds() {//item在另外项目下
        DuibaHdtoolOptionsDto duibaHdtoolOptionsDto =new DuibaHdtoolOptionsDto(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-5L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsService.insertHdtoolOption(duibaHdtoolOptionsDto);
        duibaHdtoolOptionsService.findHasUserdHdIds(duibaHdtoolOptionsDto.getItemId());
    }


    @Test
    public void testDecrementOptionRemaining() {
        DuibaHdtoolOptionsDto duibaHdtoolOptionsDto =new DuibaHdtoolOptionsDto(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-5L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsService.insertHdtoolOption(duibaHdtoolOptionsDto);
        duibaHdtoolOptionsService.decrementOptionRemaining(duibaHdtoolOptionsDto.getId());
        Assert.assertEquals(duibaHdtoolOptionsDto.getRemaining(),(Integer)(duibaHdtoolOptionsService.findOptionById(duibaHdtoolOptionsDto.getId()).getRemaining()+1));
    }

    @Test
    public void testIncrementOptionRemaining() {
        DuibaHdtoolOptionsDto duibaHdtoolOptionsDto =new DuibaHdtoolOptionsDto(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-5L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsService.insertHdtoolOption(duibaHdtoolOptionsDto);
        duibaHdtoolOptionsService.incrementOptionRemaining(duibaHdtoolOptionsDto.getId());
        Assert.assertEquals(duibaHdtoolOptionsDto.getRemaining(),(Integer)(duibaHdtoolOptionsService.findOptionById(duibaHdtoolOptionsDto.getId()).getRemaining()-1));
    }

    @Test
    public void testDeleteOptions() {
        DuibaHdtoolOptionsDto duibaHdtoolOptionsDto =new DuibaHdtoolOptionsDto(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-5L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsService.insertHdtoolOption(duibaHdtoolOptionsDto);
        duibaHdtoolOptionsService.deleteOptions(Arrays.asList(duibaHdtoolOptionsDto.getId()));
        Assert.assertTrue(duibaHdtoolOptionsService.findOptionById(duibaHdtoolOptionsDto.getId()).getDeleted());
    }

    @Test
    public void testUpdateHdtoolPrize() {
        DuibaHdtoolOptionsDto duibaHdtoolOptionsDto =new DuibaHdtoolOptionsDto(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-5L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsService.insertHdtoolOption(duibaHdtoolOptionsDto);
        duibaHdtoolOptionsService.incrementOptionRemaining(duibaHdtoolOptionsDto.getId());
        duibaHdtoolOptionsDto.setRate("123");
        duibaHdtoolOptionsService.updateHdtoolPrize(duibaHdtoolOptionsDto);
        Assert.assertEquals(duibaHdtoolOptionsDto.getRate(),duibaHdtoolOptionsService.findOptionById(duibaHdtoolOptionsDto.getId()).getRate());

    }

    @Test
    public void testInsertHdtoolOption() {
        DuibaHdtoolOptionsDto duibaHdtoolOptionsDto =new DuibaHdtoolOptionsDto(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-5L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsService.insertHdtoolOption(duibaHdtoolOptionsDto);
    }

    @Test
    public void testUpdateHdtoolOption() {
        DuibaHdtoolOptionsDto duibaHdtoolOptionsDto =new DuibaHdtoolOptionsDto(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-5L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsService.insertHdtoolOption(duibaHdtoolOptionsDto);
        DuibaHdtoolOptionsDto e=new DuibaHdtoolOptionsDto(duibaHdtoolOptionsDto.getId());
        TestUtils.setRandomAttributesForBean(e,false);
        e.setRolling(1);
        duibaHdtoolOptionsService.updateHdtoolOption(e);

        assertOptionDO(e,duibaHdtoolOptionsService.findOptionById(e.getId()));
    }


    private void assertOptionDO(DuibaHdtoolOptionsDto e, DuibaHdtoolOptionsDto e1){
        assertOptionDO(e,e1,null);
    }

    private void assertOptionDO(DuibaHdtoolOptionsDto e, DuibaHdtoolOptionsDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","valid","addrlimit","itemName"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

}
