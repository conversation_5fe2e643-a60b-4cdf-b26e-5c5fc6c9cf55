package cn.com.duiba.activity.center.api.remoteservice.lotterysquare;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.lotterysquare.LotterySquareBonusRecordDto;
import cn.com.duiba.activity.center.api.enums.LSBonusTypeEnum;
import cn.com.duiba.activity.center.api.enums.LSExchangeStatusEnum;
import cn.com.duiba.activity.center.api.enums.LSPrizeTypeEnum;
import cn.com.duiba.activity.center.api.params.LotterySquareRecordQueryParam;
import cn.com.duiba.api.bo.page.Page;
import cn.com.duiba.wolf.utils.TestUtils;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2018/12/5
 */
public class LotterySquareRecordTest extends TransactionalTestCaseBase {
    private static final Logger logger = LoggerFactory.getLogger(LotterySquareRecordTest.class);

    @Autowired
    private RemoteLotterySquareRecordService lotterySquareRecordService;

    @Test
    public void testCount(){
        LotterySquareRecordQueryParam param = new LotterySquareRecordQueryParam();
        param.setConsumerId(1539361L);
        param.setActivityId(6L);
        param.setPrizeType(LSPrizeTypeEnum.PRIZE_TYPE_ACCMULATE);
        param.setPrizeLevel(1);
        System.out.println(lotterySquareRecordService.countBonusRecordByParam(param));
    }

    @Test
    public void testInsert(){
        LotterySquareBonusRecordDto lotterySquareConfigEntity =  TestUtils.createRandomBean(LotterySquareBonusRecordDto.class);
        lotterySquareConfigEntity.setBonusType(LSBonusTypeEnum.BONUS_TYPE_CREDITS);
        lotterySquareConfigEntity.setPrizeType(LSPrizeTypeEnum.PRIZE_TYPE_ACCMULATE);
        lotterySquareConfigEntity.setPrizeLevel(1);
        lotterySquareConfigEntity.setExchangeStatus(LSExchangeStatusEnum.EXCHANGE_STATUS_DEALING);

        lotterySquareRecordService.insert(lotterySquareConfigEntity);
    }

    @Test
    public void testUpdate(){
        LotterySquareBonusRecordDto lotterySquareConfigEntity =  TestUtils.createRandomBean(LotterySquareBonusRecordDto.class);
        lotterySquareConfigEntity.setBonusType(LSBonusTypeEnum.BONUS_TYPE_CREDITS);
        lotterySquareConfigEntity.setPrizeType(LSPrizeTypeEnum.PRIZE_TYPE_ACCMULATE);
        lotterySquareConfigEntity.setPrizeLevel(1);
        lotterySquareConfigEntity.setExchangeStatus(LSExchangeStatusEnum.EXCHANGE_STATUS_DEALING);
        lotterySquareConfigEntity.setId(1L);
        lotterySquareRecordService.update(lotterySquareConfigEntity);
    }

    @Test
    public void testfind(){
        LotterySquareRecordQueryParam param = new LotterySquareRecordQueryParam();
        param.setRead(false);
        param.setConsumerId(1L);
        param.setActivityId(25L);
        param.setPageNo(1);
        param.setPageSize(100);
        Page<LotterySquareBonusRecordDto> result = lotterySquareRecordService.selectBonusListRecordByPage(param);
        Long resultNum = lotterySquareRecordService.getConsumerTotalBonus(1L,2000637L);
        Long resultNum2 = lotterySquareRecordService.getTotalSendBonus(1L);
        logger.info(JSON.toJSONString(result));
        logger.info(JSON.toJSONString(resultNum));
        logger.info(JSON.toJSONString(resultNum2));
    }
}
