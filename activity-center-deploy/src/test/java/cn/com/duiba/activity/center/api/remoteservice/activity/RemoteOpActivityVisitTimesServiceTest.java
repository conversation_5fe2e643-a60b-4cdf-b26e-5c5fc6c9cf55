package cn.com.duiba.activity.center.api.remoteservice.activity;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by sty on 11/22/17.
 */
public class RemoteOpActivityVisitTimesServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteOpActivityVisitTimesService remoteOpActivityVisitTimesService;

    @Test
    public void testFindByActivityId() {

        remoteOpActivityVisitTimesService.findByOpActivityId(1L);
        remoteOpActivityVisitTimesService.findByOpActivityId(99L);
    }

    @Test
    public void testFindByActivityIds() {
        remoteOpActivityVisitTimesService.findByOpActivityIds(Lists.newArrayList(1L));
        remoteOpActivityVisitTimesService.findByOpActivityIds(Lists.newArrayList(1L,2L,3L));
    }
}
