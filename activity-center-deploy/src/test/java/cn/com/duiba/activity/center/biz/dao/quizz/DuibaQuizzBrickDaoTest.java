package cn.com.duiba.activity.center.biz.dao.quizz;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.quizz.DuibaQuizzBrickEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * 
 * ClassName: DuibaQuizzBrickDaoTest <br/>
 * date: 2016年12月1日 下午8:03:49 <br/>
 *
 * @version 
 * @since JDK 1.6
 */
@Transactional(DsConstants.DATABASE_QUIZZ)
public class DuibaQuizzBrickDaoTest extends TransactionalTestCaseBase{
	
	@Autowired
	private DuibaQuizzBrickDao duibaQuizzBrickDao;
	
	private DuibaQuizzBrickEntity info;
	
	private String[] exceptFields = new String[] { "gmtCreate", "gmtModified" };
	
	@Before
    public void insertTest(){
		info = TestUtils.createRandomBean(DuibaQuizzBrickEntity.class);
    	duibaQuizzBrickDao.insert(info);
    }
	
	@Test
	public void findTest(){
		DuibaQuizzBrickEntity infoTest = duibaQuizzBrickDao.find(info.getId());
		TestUtils.assertEqualsReflect(infoTest, info, false, exceptFields);
	}

	@Test
    public void getBrickContentByIdTest(){
    	String content = duibaQuizzBrickDao.getBrickContentById(info.getId());
    	Assert.assertEquals(content, info.getContent());
    }

	@Test
    public void findNoContentTest(){
    	DuibaQuizzBrickEntity infoTest = duibaQuizzBrickDao.findNoContent(info.getId());
    	Assert.assertNotNull(infoTest);
    }

    @Test
	public void update4AdminTest(){
    	DuibaQuizzBrickEntity test = TestUtils.createRandomBean(DuibaQuizzBrickEntity.class);
		duibaQuizzBrickDao.update4Admin(info.getId(), test.getTitle(), test.getContent(), test.getMd5());
		String title = duibaQuizzBrickDao.find(info.getId()).getTitle();
		Assert.assertTrue(title.equals(test.getTitle()));
	}

    @Test
	public void findByTitleTest(){
		DuibaQuizzBrickEntity infoTest = duibaQuizzBrickDao.findByTitle(info.getTitle());
		TestUtils.assertEqualsReflect(infoTest, info, false, exceptFields);
	}
	
    @Test
	public void openTest(){
		duibaQuizzBrickDao.open(info.getId());
		int status = duibaQuizzBrickDao.find(info.getId()).getStatus();
		Assert.assertTrue(status == 1);
	}
	
    @Test
	public void disableTest(){
		duibaQuizzBrickDao.disable(info.getId());
		int status = duibaQuizzBrickDao.find(info.getId()).getStatus();
		Assert.assertTrue(status == 0);
	}
	
    @Test
	public void findPageTest(){
    	DuibaQuizzBrickEntity test = TestUtils.createRandomBean(DuibaQuizzBrickEntity.class);
    	test.setDeleted(false);
    	duibaQuizzBrickDao.insert(test);
		Map<String, Object> queryMap = new HashMap<>();
		queryMap.put("offset", 0);
		queryMap.put("max", 10);
		List<DuibaQuizzBrickEntity> list = duibaQuizzBrickDao.findPage(queryMap);
		Assert.assertTrue(list.size() > 0);
	}
	
    @Test
	public void findPageCountTest(){
    	DuibaQuizzBrickEntity test = TestUtils.createRandomBean(DuibaQuizzBrickEntity.class);
    	test.setDeleted(false);
    	duibaQuizzBrickDao.insert(test);
		long count = duibaQuizzBrickDao.findPageCount();
		Assert.assertTrue(count > 0);
	}
	
    @Test
	public void findAllTest(){
    	DuibaQuizzBrickEntity test = TestUtils.createRandomBean(DuibaQuizzBrickEntity.class);
    	test.setStatus(1);
    	duibaQuizzBrickDao.insert(test);
		List<DuibaQuizzBrickEntity> list = duibaQuizzBrickDao.findAll();
		Assert.assertTrue(list.size() > 0);
	}
}
