package cn.com.duiba.activity.center.biz.remoteservice.activity;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.remoteservice.equity.RemoteEquityVerificationRecordService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.apache.commons.lang.ObjectUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by lk on 2020/8/16.
 */
@Transactional(DsConstants.DATABASE_ACT_RECORD)
public class RemoteEquityVerificationServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteEquityVerificationRecordService remoteEquityVerificationService;

    @Test
    public void del() {
        Integer integer = remoteEquityVerificationService.deleteRecordById(1L);
        Assert.assertTrue(ObjectUtils.equals(integer,1));
    }


}
