package cn.com.duiba.activity.center.biz.service.game;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionAnswerOrdersExtraDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/3.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaQuestionAnswerOrdersExtraServiceTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaQuestionAnswerOrdersExtraService duibaQuestionAnswerOrdersExtraService;

    private ThreadLocal<DuibaQuestionAnswerOrdersExtraDto> duibaQuestionAnswerOrdersExtraDO=new ThreadLocal<>();

    @Test
    public void testInsert() {
        DuibaQuestionAnswerOrdersExtraDto e=new DuibaQuestionAnswerOrdersExtraDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionAnswerOrdersExtraService.insert(e);
        duibaQuestionAnswerOrdersExtraDO.set(e);
    }

    @Test
    public void testFind() {
        DuibaQuestionAnswerOrdersExtraDto e=new DuibaQuestionAnswerOrdersExtraDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionAnswerOrdersExtraService.insert(e);
        DuibaQuestionAnswerOrdersExtraDto e1=duibaQuestionAnswerOrdersExtraService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testUpdate() {
        DuibaQuestionAnswerOrdersExtraDto e=new DuibaQuestionAnswerOrdersExtraDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionAnswerOrdersExtraService.insert(e);
        duibaQuestionAnswerOrdersExtraService.update(e.getId(),"test",20);
        DuibaQuestionAnswerOrdersExtraDto e1=duibaQuestionAnswerOrdersExtraService.find(e.getId());
        Assert.assertTrue(e1.getRightCount().equals(20));
    }

    @Test
    public void testFindByQuestionOrderId() {
        DuibaQuestionAnswerOrdersExtraDto e=new DuibaQuestionAnswerOrdersExtraDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionAnswerOrdersExtraService.insert(e);
        Assert.assertNotNull(duibaQuestionAnswerOrdersExtraService.findByQuestionOrderId(e.getQuestionOrdersId()));
    }

    private void assertDO(DuibaQuestionAnswerOrdersExtraDto e, DuibaQuestionAnswerOrdersExtraDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaQuestionAnswerOrdersExtraDto e, DuibaQuestionAnswerOrdersExtraDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

}
