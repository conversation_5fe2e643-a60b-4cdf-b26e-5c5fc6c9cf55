package cn.com.duiba.activity.center.api.remoteservice.shuqi;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.shuqipk.PkTeamMemberInfoDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.boot.exception.BizException;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 18/7/17 21:31
 * @description:
 */
@Transactional(DsConstants.DATABASE_CUSTOM_RECORD)
@Rollback(value = false)
public class RemotePkTeamMemberInfoServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemotePkTeamMemberInfoService remotePkTeamMemberInfoService;

	@Test
	public void testInsert() throws BizException{
		PkTeamMemberInfoDto pkTeamMemberInfoDto = new PkTeamMemberInfoDto();
		pkTeamMemberInfoDto.setTeamId(373L);
		pkTeamMemberInfoDto.setConsumerId(128L);
		pkTeamMemberInfoDto.setAppId(1L);
		pkTeamMemberInfoDto.setActivityId(12431L);
		Integer result = remotePkTeamMemberInfoService.insert(pkTeamMemberInfoDto);
		System.out.println(result);
		Assert.assertNotNull(result);
	}

	@Test
	public void testGetByConsumerId(){
		PkTeamMemberInfoDto pkTeamMemberInfoDto = remotePkTeamMemberInfoService.getByConsumerId(111L);
		System.out.println(JSON.toJSON(pkTeamMemberInfoDto));
		Assert.assertNotNull(pkTeamMemberInfoDto);
	}

	@Test
	public void testGetListByTeamId(){
		List<PkTeamMemberInfoDto> pkTeamMemberInfoDtoList = remotePkTeamMemberInfoService.getListByTeamId(3L);
		System.out.println(JSON.toJSON(pkTeamMemberInfoDtoList));
		Assert.assertNotNull(pkTeamMemberInfoDtoList);
	}

	@Test
	public void testGetListByTeamIds(){
		List<PkTeamMemberInfoDto> pkTeamMemberInfoDtoList = remotePkTeamMemberInfoService.getListByTeamIds(Arrays.asList(3L, 4L));
		System.out.println(JSON.toJSON(pkTeamMemberInfoDtoList));
		Assert.assertNotNull(pkTeamMemberInfoDtoList);
	}
}
