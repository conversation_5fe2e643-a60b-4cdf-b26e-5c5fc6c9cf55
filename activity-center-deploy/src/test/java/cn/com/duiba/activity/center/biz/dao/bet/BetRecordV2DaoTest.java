package cn.com.duiba.activity.center.biz.dao.bet;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity_order.ActivityOrderDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.betv2.BetRecordV2Dao;
import cn.com.duiba.activity.center.biz.entity.betv2.BetRecordV2Entity;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 * @date 2018/07/31
 */
@Transactional(DsConstants.DATABASE_ACT_RECORD)
public class BetRecordV2DaoTest extends TransactionalTestCaseBase {
    @Autowired
    private BetRecordV2Dao betRecordV2Dao;

    @Test
    public void shouldRight() {
        BetRecordV2Entity entity = betRecordV2Dao.findByBetIdAndConsumerId(52L, 1539361L);

        assertThat(entity).isNotNull();
        Integer count = betRecordV2Dao.updateExchangeStatusByOrderNum(entity.getOrderNum(), ActivityOrderDto.ExchangeSuccess);
        assertThat(count).isEqualTo(1);
    }

    @Test
    public void getTotalBetCredits() {
        System.out.println(betRecordV2Dao.sumBetCreditsByBetIdAndOptionIdsAndAppId(1L, 494L, Arrays.asList(989L, 990L)));
    }
}
