package cn.com.duiba.activity.center.biz.remoteservice.activity_floating;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity_floating.FloatingLayerDto;
import cn.com.duiba.activity.center.api.remoteservice.activity_floating.RemoteActivityFloatingLayerService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.dubbo.DubboResult;

/** 
 * ClassName:RemoteActivityFloatingLayerServiceTest.java <br/>
 * <AUTHOR> 
 * @date 创建时间：2017年8月10日 下午2:58:00 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class RemoteActivityFloatingLayerServiceTest extends TransactionalTestCaseBase {
	@Autowired
	private RemoteActivityFloatingLayerService remoteActivityFloatingLayerService;
	@Test
	public void testfFndConPluginFirstJoin() {
		DubboResult<FloatingLayerDto> dubboResult = remoteActivityFloatingLayerService.findFloatingLayerInfo(1L, "hdtool", 0, false);
		Assert.assertEquals(true, dubboResult.isSuccess());
		Assert.assertNotEquals(null, dubboResult.getResult());
	}
	
	@Test
	public void testfFndConPluginFirstJoin1() {
		DubboResult<FloatingLayerDto> dubboResult = remoteActivityFloatingLayerService.findFloatingLayerInfo(1L, "hdtool", 0, true);
		Assert.assertEquals(true, dubboResult.isSuccess());
		Assert.assertNotEquals(null, dubboResult.getResult());
	}
	
	@Test
	public void testfFndConPluginFirstJoin2() {
		DubboResult<FloatingLayerDto> dubboResult = remoteActivityFloatingLayerService.findFloatingLayerInfo(1L, "hdtool", 1, true);
		Assert.assertEquals(true, dubboResult.isSuccess());
		Assert.assertNotEquals(null, dubboResult.getResult());
	}
}
