package cn.com.duiba.activity.center.biz.remoteservice.sync;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteOperatingActivityOptionsService;
import cn.com.duiba.api.bo.activity.SyncMsgData;
import com.alibaba.fastjson.JSON;
import org.junit.Test;

import javax.annotation.Resource;

public class MerchantSyncTest extends TransactionalTestCaseBase {
    @Resource
    private RemoteOperatingActivityOptionsService remoteOperatingActivityOptionsService;

    @Test
    public void hdtoolTest() {
        SyncMsgData syncMsgData = JSON.parseObject("{\"appId\":19441,\"appItemId\":185158159275879,\"newMerchantCode\":\"DBJF_800\",\"operatingActivityId\":176481766365571,\"originalMerchantCode\":\"DBJF_600\",\"rocketMqTag\":\"common\"}", SyncMsgData.class);
        remoteOperatingActivityOptionsService.syncMerchantCoding(syncMsgData);
    }
}
