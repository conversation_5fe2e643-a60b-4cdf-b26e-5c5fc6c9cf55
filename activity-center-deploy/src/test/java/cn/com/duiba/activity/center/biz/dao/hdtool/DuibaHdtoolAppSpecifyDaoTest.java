package cn.com.duiba.activity.center.biz.dao.hdtool;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.hdtool.HdtoolAppSpecifyEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/24.
 */
@Transactional(DsConstants.DATABASE_CREDITS)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaHdtoolAppSpecifyDaoTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaHdtoolAppSpecifyDao duibaHdtoolAppSpecifyDao;

    private HdtoolAppSpecifyEntity hdtoolAppSpecifyEntity;

    @Before
    public void testInsert(){
        HdtoolAppSpecifyEntity e=new HdtoolAppSpecifyEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        duibaHdtoolAppSpecifyDao.insertSpecify(e);
        hdtoolAppSpecifyEntity = e;
        Assert.assertNotNull(e.getId());
    }

    @Test
    public void testFindAllSpecifyByHdToolId() {
        Assert.assertTrue(duibaHdtoolAppSpecifyDao.findAllSpecifyByHdToolId(hdtoolAppSpecifyEntity.getDuibaHdtoolId()).size()>0);
    }

    @Test
    public void testFindSpecifyByHdToolIdsAndApp() {
        List<Long> duibaHdToolIds = Collections.singletonList(hdtoolAppSpecifyEntity.getDuibaHdtoolId());

        Assert.assertTrue(duibaHdtoolAppSpecifyDao.findSpecifyByHdToolIdsAndApp(duibaHdToolIds, hdtoolAppSpecifyEntity.getAppId()).size()>0);
    }

    @Test
    public void testFindSpecifyByHdToolIdAndApp() {
        Assert.assertNotNull(duibaHdtoolAppSpecifyDao.findSpecifyByHdToolIdAndApp(hdtoolAppSpecifyEntity.getDuibaHdtoolId(),hdtoolAppSpecifyEntity.getAppId()));
    }

    @Test
    public void testFindSpecifyById() {
        Assert.assertNotNull(duibaHdtoolAppSpecifyDao.findSpecifyById(hdtoolAppSpecifyEntity.getId()));
    }

    @Test
    public void testFindAllSpecifyMap() {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("hdtoolType", hdtoolAppSpecifyEntity.getHdtoolType());
        queryMap.put("appId", hdtoolAppSpecifyEntity.getAppId());
        Assert.assertNotNull(duibaHdtoolAppSpecifyDao.findAllSpecifyMap(queryMap).size() > 0);
    }

    @Test
    public void testFindAllSpecifyByCredordCount() {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("hdtoolType", hdtoolAppSpecifyEntity.getHdtoolType());
        queryMap.put("appId", hdtoolAppSpecifyEntity.getAppId());
        Assert.assertNotNull(duibaHdtoolAppSpecifyDao.findAllSpecifyByCredordCount(queryMap) > 0);
    }

    @Test
    public void testDeleteSpecifyById() {
        duibaHdtoolAppSpecifyDao.deleteSpecifyById(hdtoolAppSpecifyEntity.getId());
        Assert.assertNull(duibaHdtoolAppSpecifyDao.findSpecifyById(hdtoolAppSpecifyEntity.getId()));
    }

}
