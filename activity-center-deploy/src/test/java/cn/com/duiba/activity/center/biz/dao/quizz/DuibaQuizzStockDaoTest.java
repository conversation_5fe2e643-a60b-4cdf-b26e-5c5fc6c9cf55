package cn.com.duiba.activity.center.biz.dao.quizz;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.quizz.DuibaQuizzStockEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ)
public class DuibaQuizzStockDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaQuizzStockDao duibaQuizzStockDao;
	
	private DuibaQuizzStockEntity info;
	
	@Before
	public void addTest(){
		info = TestUtils.createRandomBean(DuibaQuizzStockEntity.class);
		info.setStock(100);
		duibaQuizzStockDao.add(info);
	}
	
	@Test
	public void subStockTest(){
		int subNumber = 30;
		duibaQuizzStockDao.subStock(info.getId(), subNumber);
		
		int stock = duibaQuizzStockDao.findById(info.getId()).getStock();
		Assert.assertTrue(stock == (info.getStock() - subNumber));
	}

	@Test
    public void addStockTest(){
    	int addNumber = 30;
    	duibaQuizzStockDao.addStock(info.getId(), addNumber);
    	int stock = duibaQuizzStockDao.findById(info.getId()).getStock();
    	Assert.assertTrue(stock == (info.getStock() + addNumber));
    }

	@Test
    public void findRemainingTest(){
    	DuibaQuizzStockEntity e = duibaQuizzStockDao.findRemaining(info.getQuizzOptionId());
    	Assert.assertNotNull(e);
    }

	@Test
    public void findByQuizzOptionIdTest(){
    	DuibaQuizzStockEntity e = duibaQuizzStockDao.findByQuizzOptionId(info.getQuizzOptionId());
    	Assert.assertNotNull(e);
    }

	@Test
	public void findByQuizzOptionIdsTest(){
		List<Long> list = new ArrayList<Long>();
		list.add(info.getQuizzOptionId());
		List<DuibaQuizzStockEntity> test = duibaQuizzStockDao.findByQuizzOptionIds(list);
		Assert.assertTrue(test.size() > 0);
    }

	@Test
    public void updateStockAddTest(){
		int stockAdd = 30;
    	duibaQuizzStockDao.addStock(info.getId(), stockAdd);
    	int stock = duibaQuizzStockDao.findById(info.getId()).getStock();
    	Assert.assertTrue(stock == (info.getStock() + stockAdd));
    }
	
	@Test
	public void updateStockSubTest(){
		int stockSub = 30;
		duibaQuizzStockDao.subStock(info.getId(), stockSub);
		int stock = duibaQuizzStockDao.findById(info.getId()).getStock();
		Assert.assertTrue(stock == (info.getStock() - stockSub));
    }

	@Test
	public void addBatchTest(){
		DuibaQuizzStockEntity e1 = TestUtils.createRandomBean(DuibaQuizzStockEntity.class);
		DuibaQuizzStockEntity e2 = TestUtils.createRandomBean(DuibaQuizzStockEntity.class);
		List<DuibaQuizzStockEntity> list = new ArrayList<DuibaQuizzStockEntity>();
		list.add(e1);
		list.add(e2);
		duibaQuizzStockDao.addBatch(list);
		
		Assert.assertNotNull(duibaQuizzStockDao.findById(e1.getId()));
		Assert.assertNotNull(duibaQuizzStockDao.findById(e2.getId()));
    }
}
