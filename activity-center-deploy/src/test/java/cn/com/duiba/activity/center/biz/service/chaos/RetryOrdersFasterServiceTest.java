package cn.com.duiba.activity.center.biz.service.chaos;

import java.util.Date;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.chaos.RetryOrdersFasterDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;
/**
 * Created by yansen on 16/6/3.
 */
@Transactional(RetryOrdersFasterServiceTest.THIS_DATABASE_SCHEMA)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class RetryOrdersFasterServiceTest extends TransactionalTestCaseBase {

    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS_ACTIVITY;


    @Resource
    private RetryOrdersFasterService retryOrdersFasterService;

    @Test
    public void testInsert() {
        RetryOrdersFasterDto r=new RetryOrdersFasterDto(true);
        TestUtils.setRandomAttributesForBean(r,false);
        retryOrdersFasterService.insert(r);
    }

    @Test
    public void testDeleteByOrderId() {
        RetryOrdersFasterDto r=new RetryOrdersFasterDto(true);
        TestUtils.setRandomAttributesForBean(r,false);
        retryOrdersFasterService.insert(r);
        Assert.assertNotNull(retryOrdersFasterService.findByOrderId(r.getOrderId()));
        retryOrdersFasterService.deleteByOrderId(r.getOrderId());
        Assert.assertNull(retryOrdersFasterService.findByOrderId(r.getOrderId()));
    }

    @Test
    public void testFindByOrderId() {
        RetryOrdersFasterDto r=new RetryOrdersFasterDto(true);
        TestUtils.setRandomAttributesForBean(r,false);
        retryOrdersFasterService.insert(r);
        Assert.assertNotNull(retryOrdersFasterService.findByOrderId(r.getOrderId()));

    }

    @Test
    public void testFindEndtimeRetryOrder() {
        RetryOrdersFasterDto r=new RetryOrdersFasterDto(true);
        TestUtils.setRandomAttributesForBean(r,false);
        r.setEndTime(new Date(System.currentTimeMillis()+1000000l));
        retryOrdersFasterService.insert(r);
        Assert.assertTrue(retryOrdersFasterService.findEndtimeRetryOrder().size()>0);
    }
    
}
