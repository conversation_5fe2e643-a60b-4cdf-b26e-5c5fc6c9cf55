package cn.com.duiba.activity.center.biz.dao.ngame;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.ngame.NgameStockEntity;

@Transactional(DsConstants.DATABASE_NGAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class NgameStockDaoTest extends TransactionalTestCaseBase {

	@Resource
	private NgameStockDao ngameStockDao;

	@Test
	public void findRemainingTest() {
		System.out.println(ngameStockDao.findRemaining(68l).getId());
	}
	
	@Test
	public void subStockTest() {
		System.out.println(ngameStockDao.subStock(66l,1));
	}
	
	@Test
	public void addStockTest() {
		System.out.println(ngameStockDao.addStock(66l,1));
	}

	@Test
	public void findByGameOptionIdTest() {
		System.out.println(ngameStockDao.findByGameOptionId(3l).getStock());
	}

	@Test
	public void findByGameOptionIdsTest() {
		List<Long> ids = new ArrayList<Long>();
		ids.add(3l);
		ids.add(4l);
		System.out.println(ngameStockDao.findByGameOptionIds(ids).size());
	}

	@Test
	public void updateStockTest() {
		NgameStockEntity s = ngameStockDao.findByGameOptionId(3l);
		s.setStock(-89);
		ngameStockDao.updateStock(s);
	}

	@Test
	public void addTest() {
		NgameStockEntity s = new NgameStockEntity();
		s.setGameOptionId(-1l);
		s.setStock(-1);
		ngameStockDao.add(s);
	}

	@Test
	public void addBatchTest() {
		List<NgameStockEntity> list = new ArrayList<NgameStockEntity>();
		NgameStockEntity s = new NgameStockEntity();
		s.setGameOptionId(-2l);
		s.setStock(-2);
		list.add(s);
		s = new NgameStockEntity();
		s.setGameOptionId(-100l);
		s.setStock(-100);
		list.add(s);
		ngameStockDao.addBatch(list);
	}
}
