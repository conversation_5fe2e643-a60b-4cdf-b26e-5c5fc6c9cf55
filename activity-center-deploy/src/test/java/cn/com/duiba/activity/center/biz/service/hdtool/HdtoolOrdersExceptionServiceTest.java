package cn.com.duiba.activity.center.biz.service.hdtool;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.PaginationDto;
import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolOrdersDto;
import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolOrdersExceptionDto;
import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolOrdersExceptionQueryDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.hdtool.HdtoolOrdersExceptionDao;
import cn.com.duiba.activity.center.biz.entity.hdtool.HdtoolOrdersExceptionEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * Created by zzy on 2017/3/15.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class HdtoolOrdersExceptionServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private HdtoolOrdersExceptionDao hdtoolOrdersExceptionDao;

    @Autowired
    private HdtoolOrdersExceptionService hdtoolOrdersExceptionService;

    @Test
    public void testInsert() {
        HdtoolOrdersExceptionEntity hdtoolOrdersExceptionEntity = new HdtoolOrdersExceptionEntity();
        hdtoolOrdersExceptionEntity.setId(9990L);
        hdtoolOrdersExceptionEntity.setAppId(2L);
        hdtoolOrdersExceptionEntity.setConsumerId(505L);
        hdtoolOrdersExceptionEntity.setCredits(1L);
        hdtoolOrdersExceptionEntity.setAppItemId(505L);
        hdtoolOrdersExceptionEntity.setDeveloperBizId("123");
        hdtoolOrdersExceptionEntity.setDuibaHdtoolId(123L);
        hdtoolOrdersExceptionEntity.setError4Admin("abc");
        hdtoolOrdersExceptionEntity.setError4Consumer("abc");
        hdtoolOrdersExceptionEntity.setError4Developer("abc");
        hdtoolOrdersExceptionEntity.setExchangeStatus(HdtoolOrdersExceptionEntity.ExchangeStatusWait);
        hdtoolOrdersExceptionEntity.setFacePrice("2.0");
        hdtoolOrdersExceptionEntity.setPartnerUserId("1");
        hdtoolOrdersExceptionEntity.setHdtoolType(7);
        hdtoolOrdersExceptionEntity.setIp("127.0.0.1");
        hdtoolOrdersExceptionEntity.setStatus(HdtoolOrdersDto.StatusCreate);
        hdtoolOrdersExceptionEntity.setSourceOrderId(123L);
        hdtoolOrdersExceptionEntity.setPrizeType("qq");
        hdtoolOrdersExceptionEntity.setPrizeName("qbi");
        hdtoolOrdersExceptionEntity.setOperatingActivityId(1234L);
        hdtoolOrdersExceptionEntity.setItemId(123L);
        int ret = hdtoolOrdersExceptionDao.insert(hdtoolOrdersExceptionEntity);
        Assert.assertEquals(1, ret);
    }

    @Test
    public void testUpdateExchangeStatusToFail() {
        testInsert();
        int ret = hdtoolOrdersExceptionDao.updateExchangeStatusToFail(9990L, "修改为失败", "", "");
        Assert.assertEquals(1, ret);
        HdtoolOrdersExceptionEntity hdtoolOrdersExceptionEntity = hdtoolOrdersExceptionDao.find(9990L);
        Assert.assertNotNull(hdtoolOrdersExceptionEntity);
        Assert.assertEquals("修改为失败", hdtoolOrdersExceptionEntity.getError4Admin());
        Assert.assertEquals(Integer.valueOf(HdtoolOrdersExceptionEntity.ExchangeStatusFail), hdtoolOrdersExceptionEntity.getExchangeStatus());
    }

    @Test
    public void testUpdateExchangeStatusToOverdue() {
        testInsert();
        int ret = hdtoolOrdersExceptionDao.updateExchangeStatusToOverdue(9990L, "修改为过期失效", "", "");
        Assert.assertEquals(1, ret);
        HdtoolOrdersExceptionEntity hdtoolOrdersExceptionEntity = hdtoolOrdersExceptionDao.find(9990L);
        Assert.assertNotNull(hdtoolOrdersExceptionEntity);
        Assert.assertEquals("修改为过期失效", hdtoolOrdersExceptionEntity.getError4Admin());
        Assert.assertEquals(Integer.valueOf(HdtoolOrdersExceptionEntity.ExchangeStatusOverdue), hdtoolOrdersExceptionEntity.getExchangeStatus());
    }

    @Test
    public void testDoTakePrize() {
        testInsert();
        int ret = hdtoolOrdersExceptionDao.doTakePrize(9990L);
        Assert.assertEquals(1, ret);
        HdtoolOrdersExceptionEntity hdtoolOrdersExceptionEntity = hdtoolOrdersExceptionDao.find(9990L);
        Assert.assertNotNull(hdtoolOrdersExceptionEntity);
        Assert.assertEquals(Integer.valueOf(HdtoolOrdersExceptionEntity.ExchangeStatusSuccess), hdtoolOrdersExceptionEntity.getExchangeStatus());
    }

    @Test
    public void testRollbackTakePrize() {
        HdtoolOrdersExceptionEntity hdtoolOrdersExceptionEntity = new HdtoolOrdersExceptionEntity();
        hdtoolOrdersExceptionEntity.setId(8880L);
        hdtoolOrdersExceptionEntity.setAppId(2L);
        hdtoolOrdersExceptionEntity.setConsumerId(505L);
        hdtoolOrdersExceptionEntity.setCredits(1L);
        hdtoolOrdersExceptionEntity.setAppItemId(505L);
        hdtoolOrdersExceptionEntity.setDeveloperBizId("123");
        hdtoolOrdersExceptionEntity.setDuibaHdtoolId(123L);
        hdtoolOrdersExceptionEntity.setError4Admin("abc");
        hdtoolOrdersExceptionEntity.setError4Consumer("abc");
        hdtoolOrdersExceptionEntity.setError4Developer("abc");
        hdtoolOrdersExceptionEntity.setExchangeStatus(HdtoolOrdersExceptionEntity.ExchangeStatusSuccess);
        hdtoolOrdersExceptionEntity.setFacePrice("2.0");
        hdtoolOrdersExceptionEntity.setPartnerUserId("1");
        hdtoolOrdersExceptionEntity.setHdtoolType(7);
        hdtoolOrdersExceptionEntity.setIp("127.0.0.1");
        hdtoolOrdersExceptionEntity.setStatus(HdtoolOrdersDto.StatusCreate);
        hdtoolOrdersExceptionEntity.setSourceOrderId(123L);
        hdtoolOrdersExceptionEntity.setPrizeType("qq");
        hdtoolOrdersExceptionEntity.setPrizeName("qbi");
        hdtoolOrdersExceptionEntity.setOperatingActivityId(1234L);
        hdtoolOrdersExceptionEntity.setItemId(123L);
        int ret = hdtoolOrdersExceptionDao.insert(hdtoolOrdersExceptionEntity);
        Assert.assertEquals(1, ret);
        ret = hdtoolOrdersExceptionDao.rollbackTakePrize(8880L);
        Assert.assertEquals(1, ret);
        hdtoolOrdersExceptionEntity = hdtoolOrdersExceptionDao.find(8880L);
        Assert.assertNotNull(hdtoolOrdersExceptionEntity);
        Assert.assertEquals(Integer.valueOf(HdtoolOrdersExceptionEntity.ExchangeStatusWait), hdtoolOrdersExceptionEntity.getExchangeStatus());
    }

    @Test
    public void testUpdateMainOrderId() {
        testInsert();
        int ret = hdtoolOrdersExceptionDao.updateMainOrderId(9990L, 1234L, "1234");
        Assert.assertEquals(1, ret);
        HdtoolOrdersExceptionEntity hdtoolOrdersExceptionEntity = hdtoolOrdersExceptionDao.find(9990L);
        Assert.assertNotNull(hdtoolOrdersExceptionEntity);
        Assert.assertEquals(Long.valueOf(1234L), hdtoolOrdersExceptionEntity.getMainOrderId());
        Assert.assertEquals("1234", hdtoolOrdersExceptionEntity.getMainOrderNum());
    }

    @Test
    public void testSelectList() throws InterruptedException {
        HdtoolOrdersExceptionEntity hdtoolOrdersExceptionEntity1 = new HdtoolOrdersExceptionEntity();
        hdtoolOrdersExceptionEntity1.setId(1110L);
        hdtoolOrdersExceptionEntity1.setAppId(5250L);
        hdtoolOrdersExceptionEntity1.setConsumerId(505L);
        hdtoolOrdersExceptionEntity1.setCredits(1L);
        hdtoolOrdersExceptionEntity1.setAppItemId(505L);
        hdtoolOrdersExceptionEntity1.setDeveloperBizId("123");
        hdtoolOrdersExceptionEntity1.setDuibaHdtoolId(123L);
        hdtoolOrdersExceptionEntity1.setError4Admin("abc");
        hdtoolOrdersExceptionEntity1.setError4Consumer("abc");
        hdtoolOrdersExceptionEntity1.setError4Developer("abc");
        hdtoolOrdersExceptionEntity1.setExchangeStatus(HdtoolOrdersExceptionEntity.ExchangeStatusFail);
        hdtoolOrdersExceptionEntity1.setFacePrice("2.0");
        hdtoolOrdersExceptionEntity1.setPartnerUserId("1");
        hdtoolOrdersExceptionEntity1.setHdtoolType(7);
        hdtoolOrdersExceptionEntity1.setIp("127.0.0.1");
        hdtoolOrdersExceptionEntity1.setStatus(HdtoolOrdersDto.StatusCreate);
        hdtoolOrdersExceptionEntity1.setSourceOrderId(123L);
        hdtoolOrdersExceptionEntity1.setPrizeType("qq");
        hdtoolOrdersExceptionEntity1.setPrizeName("qbi");
        hdtoolOrdersExceptionEntity1.setPrizeId(7400L);
        hdtoolOrdersExceptionEntity1.setOperatingActivityId(1234L);
        hdtoolOrdersExceptionEntity1.setItemId(123L);
        int ret1 = hdtoolOrdersExceptionDao.insert(hdtoolOrdersExceptionEntity1);
        Assert.assertEquals(1, ret1);

        Thread.sleep(1000L);

        HdtoolOrdersExceptionEntity hdtoolOrdersExceptionEntity2 = new HdtoolOrdersExceptionEntity();
        hdtoolOrdersExceptionEntity2.setId(2220L);
        hdtoolOrdersExceptionEntity2.setAppId(5250L);
        hdtoolOrdersExceptionEntity2.setConsumerId(505L);
        hdtoolOrdersExceptionEntity2.setCredits(2L);
        hdtoolOrdersExceptionEntity2.setAppItemId(505L);
        hdtoolOrdersExceptionEntity2.setDeveloperBizId("123");
        hdtoolOrdersExceptionEntity2.setDuibaHdtoolId(123L);
        hdtoolOrdersExceptionEntity2.setError4Admin("abc");
        hdtoolOrdersExceptionEntity2.setError4Consumer("abc");
        hdtoolOrdersExceptionEntity2.setError4Developer("abc");
        hdtoolOrdersExceptionEntity2.setExchangeStatus(HdtoolOrdersExceptionEntity.ExchangeStatusWait);
        hdtoolOrdersExceptionEntity2.setFacePrice("2.0");
        hdtoolOrdersExceptionEntity2.setPartnerUserId("1");
        hdtoolOrdersExceptionEntity2.setHdtoolType(7);
        hdtoolOrdersExceptionEntity2.setIp("127.0.0.1");
        hdtoolOrdersExceptionEntity2.setStatus(HdtoolOrdersDto.StatusCreate);
        hdtoolOrdersExceptionEntity2.setSourceOrderId(123L);
        hdtoolOrdersExceptionEntity2.setPrizeType("qq");
        hdtoolOrdersExceptionEntity2.setPrizeName("qbi");
        hdtoolOrdersExceptionEntity2.setPrizeId(7400L);
        hdtoolOrdersExceptionEntity2.setOperatingActivityId(1234L);
        hdtoolOrdersExceptionEntity2.setItemId(123L);
        int ret2 = hdtoolOrdersExceptionDao.insert(hdtoolOrdersExceptionEntity2);
        Assert.assertEquals(1, ret2);

        Thread.sleep(1000L);

        HdtoolOrdersExceptionEntity hdtoolOrdersExceptionEntity3 = new HdtoolOrdersExceptionEntity();
        hdtoolOrdersExceptionEntity3.setId(3330L);
        hdtoolOrdersExceptionEntity3.setAppId(5250L);
        hdtoolOrdersExceptionEntity3.setConsumerId(505L);
        hdtoolOrdersExceptionEntity3.setCredits(3L);
        hdtoolOrdersExceptionEntity3.setAppItemId(505L);
        hdtoolOrdersExceptionEntity3.setDeveloperBizId("123");
        hdtoolOrdersExceptionEntity3.setDuibaHdtoolId(123L);
        hdtoolOrdersExceptionEntity3.setError4Admin("abc");
        hdtoolOrdersExceptionEntity3.setError4Consumer("abc");
        hdtoolOrdersExceptionEntity3.setError4Developer("abc");
        hdtoolOrdersExceptionEntity3.setExchangeStatus(HdtoolOrdersExceptionEntity.ExchangeStatusOverdue);
        hdtoolOrdersExceptionEntity3.setFacePrice("2.0");
        hdtoolOrdersExceptionEntity3.setPartnerUserId("1");
        hdtoolOrdersExceptionEntity3.setHdtoolType(7);
        hdtoolOrdersExceptionEntity3.setIp("127.0.0.1");
        hdtoolOrdersExceptionEntity3.setStatus(HdtoolOrdersDto.StatusCreate);
        hdtoolOrdersExceptionEntity3.setSourceOrderId(123L);
        hdtoolOrdersExceptionEntity3.setPrizeType("qq");
        hdtoolOrdersExceptionEntity3.setPrizeName("qbi");
        hdtoolOrdersExceptionEntity3.setPrizeId(7400L);
        hdtoolOrdersExceptionEntity3.setOperatingActivityId(1234L);
        hdtoolOrdersExceptionEntity3.setItemId(123L);
        int ret3 = hdtoolOrdersExceptionDao.insert(hdtoolOrdersExceptionEntity3);
        Assert.assertEquals(1, ret3);

        Thread.sleep(1000L);

        HdtoolOrdersExceptionQueryDto hdtoolOrdersExceptionQueryDto = new HdtoolOrdersExceptionQueryDto();
        hdtoolOrdersExceptionQueryDto.setActivityId(1234L);
        hdtoolOrdersExceptionQueryDto.setAppId(5250L);
        hdtoolOrdersExceptionQueryDto.setBegin(DateUtils.daysAddOrSub(new Date(), -1));
        hdtoolOrdersExceptionQueryDto.setEnd(new Date());
        hdtoolOrdersExceptionQueryDto.setPageSize(2);
        hdtoolOrdersExceptionQueryDto.setRowId(1);
        PaginationDto<HdtoolOrdersExceptionDto> paginationDto = hdtoolOrdersExceptionService.findList(hdtoolOrdersExceptionQueryDto);
        Assert.assertNotNull(paginationDto);
        Assert.assertEquals(Long.valueOf(3), paginationDto.getTotalCount());
        Assert.assertEquals(2, paginationDto.getRows().size());
        Assert.assertEquals(Long.valueOf(3), paginationDto.getRows().get(0).getCredits());


        HdtoolOrdersExceptionQueryDto hdtoolOrdersExceptionQueryDto2 = new HdtoolOrdersExceptionQueryDto();
        hdtoolOrdersExceptionQueryDto2.setActivityId(1234L);
        hdtoolOrdersExceptionQueryDto2.setAppId(5250L);
        hdtoolOrdersExceptionQueryDto2.setBegin(DateUtils.daysAddOrSub(new Date(), -1));
        hdtoolOrdersExceptionQueryDto2.setEnd(new Date());
        hdtoolOrdersExceptionQueryDto2.setPageSize(2);
        hdtoolOrdersExceptionQueryDto2.setRowId(1);
        hdtoolOrdersExceptionQueryDto2.setExchangeStatus(HdtoolOrdersExceptionEntity.ExchangeStatusOverdue);
        PaginationDto<HdtoolOrdersExceptionDto> paginationDto2 = hdtoolOrdersExceptionService.findList(hdtoolOrdersExceptionQueryDto2);
        Assert.assertNotNull(paginationDto2);
        Assert.assertEquals(Long.valueOf(1), paginationDto2.getTotalCount());
        Assert.assertEquals(1, paginationDto2.getRows().size());
        Assert.assertEquals(Long.valueOf(3), paginationDto2.getRows().get(0).getCredits());


        HdtoolOrdersExceptionQueryDto hdtoolOrdersExceptionQueryDto3 = new HdtoolOrdersExceptionQueryDto();
        hdtoolOrdersExceptionQueryDto3.setActivityId(1234L);
        hdtoolOrdersExceptionQueryDto3.setAppId(5250L);
        hdtoolOrdersExceptionQueryDto3.setBegin(DateUtils.daysAddOrSub(new Date(), -1));
        hdtoolOrdersExceptionQueryDto3.setEnd(new Date());
        hdtoolOrdersExceptionQueryDto3.setPageSize(2);
        hdtoolOrdersExceptionQueryDto3.setRowId(1);
        hdtoolOrdersExceptionQueryDto3.setPartnerUserId("1");
        PaginationDto<HdtoolOrdersExceptionDto> paginationDto3 = hdtoolOrdersExceptionService.findList(hdtoolOrdersExceptionQueryDto3);
        Assert.assertNotNull(paginationDto3);
        Assert.assertEquals(Long.valueOf(3), paginationDto3.getTotalCount());
        Assert.assertEquals(2, paginationDto3.getRows().size());
        Assert.assertEquals(Long.valueOf(3), paginationDto3.getRows().get(0).getCredits());

        HdtoolOrdersExceptionQueryDto hdtoolOrdersExceptionQueryDto4 = new HdtoolOrdersExceptionQueryDto();
        hdtoolOrdersExceptionQueryDto4.setActivityId(1234L);
        hdtoolOrdersExceptionQueryDto4.setAppId(5250L);
        hdtoolOrdersExceptionQueryDto4.setBegin(DateUtils.daysAddOrSub(new Date(), -1));
        hdtoolOrdersExceptionQueryDto4.setEnd(new Date());
        hdtoolOrdersExceptionQueryDto4.setPageSize(2);
        hdtoolOrdersExceptionQueryDto4.setRowId(1);
        hdtoolOrdersExceptionQueryDto4.setPrizeId(7400L);
        PaginationDto<HdtoolOrdersExceptionDto> paginationDto4 = hdtoolOrdersExceptionService.findList(hdtoolOrdersExceptionQueryDto4);
        Assert.assertNotNull(paginationDto4);
        Assert.assertEquals(Long.valueOf(3), paginationDto4.getTotalCount());
        Assert.assertEquals(2, paginationDto4.getRows().size());
        Assert.assertEquals(Long.valueOf(3), paginationDto4.getRows().get(0).getCredits());
    }

}
