package cn.com.duiba.activity.center.biz.remoteservice.quizz;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.quizz.QuizzOrdersDto;
import cn.com.duiba.activity.center.api.remoteservice.quizz.RemoteQuizzOrderTextChangeService;
import cn.com.duiba.activity.center.api.remoteservice.quizz.RemoteQuizzOrdersSequenceService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ_CONSUMER)
@Ignore
public class RemoteQuizzOrderTextChangeServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private RemoteQuizzOrderTextChangeService quizzOrderTextChangeService;
	
	@Autowired
	private RemoteQuizzOrdersSequenceService quizzOrdersSequenceService;
	
	private QuizzOrdersDto info;
	
	@Before
	public void insertTest(){
		QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		quizzOrderTextChangeService.insert(e);
		info = e;
	}

	@Test
    public void updateDeveloperBizIdTest(){
    	QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		
		quizzOrderTextChangeService.updateDeveloperBizId(info.getConsumerId(), info.getId(), e.getDeveloperBizId());
		
		String developerBizId = quizzOrderTextChangeService.find(info.getConsumerId(), info.getId()).getDeveloperBizId();
		Assert.assertTrue(developerBizId.equals(e.getDeveloperBizId()));
    }

	@Test
    public void updateMainOrderIdTest(){
    	QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		
		quizzOrderTextChangeService.updateMainOrderId(info.getConsumerId(), info.getId(), e.getMainOrderId(), e.getMainOrderNum());
		
		long mainOrderId = quizzOrderTextChangeService.find(info.getConsumerId(), info.getId()).getMainOrderId();
		Assert.assertTrue(mainOrderId == e.getMainOrderId());
    }
}
