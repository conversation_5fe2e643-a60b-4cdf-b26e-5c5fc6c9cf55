package cn.com.duiba.activity.center.biz.dao.cus;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.enums.CusBoxStatusEnum;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.cus.CusUserInviteCountEntity;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 * @date 2018/05/25
 */
@Transactional(DsConstants.DATABASE_DUIBA_CUSTOM)
public class CusUserInviteCountDaoTest extends TransactionalTestCaseBase {
    @Autowired
    private CusUserInviteCountDao cusUserInviteCountDao;
    private CusUserInviteCountEntity cusUserInviteCountEntity;

    @Before
    public void setup() {
        cusUserInviteCountEntity = new CusUserInviteCountEntity();

        cusUserInviteCountEntity.setConsumerId(1L);
        cusUserInviteCountEntity.setOperatingActivityId(1L);
        cusUserInviteCountEntity.setTimes(0);

        cusUserInviteCountEntity.setId(cusUserInviteCountDao.insert(cusUserInviteCountEntity));
    }

    @Test
    public void shouldInsert() {
        assertThat(cusUserInviteCountEntity.getId()).isNotNull().isGreaterThan(0);
    }

    @Test
    public void shouldUpdate() {
        cusUserInviteCountEntity.setBox1(CusBoxStatusEnum.OPENED.getCode());
        cusUserInviteCountEntity.setTimes(2);

        assertThat(cusUserInviteCountDao.updateByConsumerId(cusUserInviteCountEntity)).isEqualTo(1);
    }

    @Test
    public void shouldFindByConsumerId() {
        CusUserInviteCountEntity entity = cusUserInviteCountDao.findByConsumerId(cusUserInviteCountEntity.getConsumerId());

        assertThat(entity).isNotNull();
    }
}
