package cn.com.duiba.activity.center.biz.dao.seedredpacket;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.enums.SeedRedPacketLandTaskTypeEnum;
import cn.com.duiba.activity.center.biz.entity.seedredpacket.SeedRedPacketTaskEntity;
import cn.com.duiba.activity.center.biz.service.seedredpacket.SeedRedPacketUserMarkManage;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created by hww on 2019/1/25 10:49 AM.
 */
//@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class SeedRedPacketTaskDaoTest extends TransactionalTestCaseBase {

    private Long activityId = 1L;

    @Autowired
    private SeedRedPacketTaskDao taskDao;
    @Autowired
    private SeedRedPacketUserMarkManage seedRedPacketUserMarkManage;

    @Test
    public void test(){
        seedRedPacketUserMarkManage.batchClearMark();
    }

    @Test
    public void testBatchInsert() {
        List<SeedRedPacketTaskEntity> insert = Lists.newArrayList();
        for (long i = 1; i < 9; i++) {
            SeedRedPacketTaskEntity temp = new SeedRedPacketTaskEntity();
            temp.setLandId(Long.valueOf(i));
            temp.setTaskType(SeedRedPacketLandTaskTypeEnum.NONE.getCode());
            temp.setTaskTarget(0);
            temp.setActivityId(activityId);
            insert.add(temp);
        }
        boolean suc = taskDao.batchInsert(insert);
        Assert.assertTrue(suc);
    }

    @Test
    public void testFindByActivityId() {
        testBatchInsert();
        List<SeedRedPacketTaskEntity> list = taskDao.findTasksByActivityId(activityId);
        Assert.assertTrue(CollectionUtils.isNotEmpty(list) && list.size() == 8);
    }

    @Test
    public void testFindByActivityIdAndLandId() {
        testBatchInsert();
        SeedRedPacketTaskEntity task = taskDao.findTaskByActivityIdAndLandId(activityId, 1L);
        Assert.assertTrue(task != null && task.getLandId() == 1);
    }

    @Test
    public void testBatchUpdate() {
//        testBatchInsert();
        List<SeedRedPacketTaskEntity> list = taskDao.findTasksByActivityId(activityId);
        list.forEach(t -> {
            t.setTaskType(2);
            t.setTaskTarget(2);
        });
        boolean suc = taskDao.batchUpdate(list, activityId);
        Assert.assertTrue(suc);

        List<SeedRedPacketTaskEntity> list2 = taskDao.findTasksByActivityId(activityId);
        list2.forEach(t -> {
            Assert.assertTrue(t.getTaskTarget() == 2);
            Assert.assertTrue(t.getTaskType() == 2);
        });
    }
}
