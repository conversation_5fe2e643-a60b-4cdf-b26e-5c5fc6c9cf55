package cn.com.duiba.activity.center.biz.dao.manualottery;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.manuallottery.ManualOrderLotteryDao;
import cn.com.duiba.activity.center.biz.entity.manual.ManualLotteryOrderEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_CREDITS)
public class ManualOrderLotteryDaoTest extends TransactionalTestCaseBase{

	@Autowired
	private ManualOrderLotteryDao manualOrderLotteryDao;
	
	private ManualLotteryOrderEntity info;
	
	private String[] exceptFields = new String[] { "gmtCreate", "gmtModified", "winDate", "shipDate" };
	
	@Before
	public void insertTest(){
		ManualLotteryOrderEntity e = new ManualLotteryOrderEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		info = e;
		manualOrderLotteryDao.insert(info);
	}
	
	/**
     * 查询用户参与活动次数
     */
	@Test
    public void countByConsumerIdAndOperatingActivityIdTest(){
    	int num = manualOrderLotteryDao.countByConsumerIdAndOperatingActivityId(info.getConsumerId(), info.getOperatingActivityId());
    	Assert.assertTrue(num > 0);
    }

    /**
     * 查询用户在时间段内参与活动次数
     */
	@Test
    public void countByConsumerIdAndOperatingActivityIdAndDateTest(){
    	Calendar calendar = Calendar.getInstance();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	Date start = calendar.getTime();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, 1);
    	Date end = calendar.getTime();
    	int count = manualOrderLotteryDao.countByConsumerIdAndOperatingActivityIdAndDate(info.getConsumerId(), info.getOperatingActivityId(), start, end);
    	Assert.assertTrue(count > 0);
    }

    /**
     * 查询用户免费参与活动次数
     */
	@Test
    public void countFreeByConsumerIdAndOperatingActivityIdTest(){
    	ManualLotteryOrderEntity e = new ManualLotteryOrderEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setCredits(0L);
		manualOrderLotteryDao.insert(e);
		int count = manualOrderLotteryDao.countFreeByConsumerIdAndOperatingActivityId(e.getConsumerId(), e.getOperatingActivityId());
		Assert.assertTrue(count > 0);
    }

    /**
     * 查询用户在时间段内免费参与活动次数
     */
	@Test
    public void countFreeByConsumerIdAndOperatingActivityIdAndDateTest(){
    	ManualLotteryOrderEntity e = new ManualLotteryOrderEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setCredits(0L);
		manualOrderLotteryDao.insert(e);
		Calendar calendar = Calendar.getInstance();
    	calendar.setTime(e.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	Date start = calendar.getTime();
    	calendar.setTime(e.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, 1);
    	Date end = calendar.getTime();
    	int count = manualOrderLotteryDao.countFreeByConsumerIdAndOperatingActivityIdAndDate(e.getConsumerId(), e.getOperatingActivityId(), start, end);
    	Assert.assertTrue(count > 0);
    }

    /**
     * 条件搜索开奖记录
     *
     * @param paramMap
     * @return
     */
	@Test
    public void findAllOpenManualLotteryByConditionTest(){
    	ManualLotteryOrderEntity e = new ManualLotteryOrderEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setStatus(1);
		e.setExchangeStatus(1);
		manualOrderLotteryDao.insert(e);
    	Map<String, Object> paramMap = new HashMap<>();
    	paramMap.put("operatingActivityId", e.getOperatingActivityId());
    	paramMap.put("partnerUserId", e.getPartnerUserId());
    	paramMap.put("phone", e.getPhone());
    	List<ManualLotteryOrderEntity> list = manualOrderLotteryDao.findAllOpenManualLotteryByCondition(paramMap);
    	Assert.assertTrue(list.size() > 0);
    }

    /**
     * 条件搜索开奖记录条数
     *
     * @param paramMap
     * @return
     */
	@Test
    public void findAllOpenManualLotteryCountTest(){
    	ManualLotteryOrderEntity e = new ManualLotteryOrderEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setStatus(1);
		e.setExchangeStatus(1);
		manualOrderLotteryDao.insert(e);
		Map<String, Object> map = new HashMap<>();
		map.put("operatingActivityId", e.getOperatingActivityId());
		long count = manualOrderLotteryDao.findAllOpenManualLotteryCount(map);
		Assert.assertTrue(count > 0);
    }

    /**
     * 查询中奖清单
     *
     * @return
     */
	@Test
    public void findWardListTest(){
    	ManualLotteryOrderEntity e = new ManualLotteryOrderEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setExchangeStatus(2);
		manualOrderLotteryDao.insert(e);
		List<ManualLotteryOrderEntity> list = manualOrderLotteryDao.findWardList(e.getOperatingActivityId());
		Assert.assertTrue(list.size() > 0);
    }

    /**
     * 查询参与清单
     *
     * @param paramMap
     * @return
     */
	@Test
    public void findNoWardListTest(){
		ManualLotteryOrderEntity e = new ManualLotteryOrderEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setExchangeStatus(1);
    	manualOrderLotteryDao.insert(e);
    	Map<String, Object> map = new HashMap<>();
    	map.put("operatingActivityId", e.getOperatingActivityId());
    	map.put("partnerUserId", e.getPartnerUserId());
    	map.put("phone", e.getPhone());
    	map.put("offset", 0);
    	map.put("max", 10);
    	List<ManualLotteryOrderEntity> list = manualOrderLotteryDao.findNoWardList(map);
    	Assert.assertTrue(list.size() > 0);
    }

    /**
     * 查询参与清单count
     *
     * @param paramMap
     * @return
     */
	@Test
    public void findNoWardListCountTest(){
		ManualLotteryOrderEntity e = new ManualLotteryOrderEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setExchangeStatus(1);
    	manualOrderLotteryDao.insert(e);
    	Map<String, Object> map = new HashMap<>();
    	map.put("operatingActivityId", e.getOperatingActivityId());
    	map.put("partnerUserId", e.getPartnerUserId());
    	map.put("phone", e.getPhone());
    	long count = manualOrderLotteryDao.findNoWardListCount(map);
    	Assert.assertTrue(count > 0);
    }

    /**
     * 根据ids查询待开奖的用户list
     *
     * @param paramMap
     * @return
     */
	@Test
    public void findStartOpenListByIdsTest(){
		ManualLotteryOrderEntity e = new ManualLotteryOrderEntity(info.getId());
		TestUtils.setRandomAttributesForBean(e, false);
    	e.setExchangeStatus(1);
    	manualOrderLotteryDao.update(e);
    	List<Long> ids = new ArrayList<Long>();
    	Map<String, Object> map = new HashMap<>();
    	ids.add(e.getId());
    	map.put("ids", ids);
    	map.put("activityId", e.getOperatingActivityId());
    	List<ManualLotteryOrderEntity> list = manualOrderLotteryDao.findStartOpenListByIds(map);
    	Assert.assertTrue(list.size() > 0);
    }

    /**
     * 更新中奖用户状态
     *
     * @param id
     * @param activityId
     * @param date
     * @return
     */
	@Test
    public void updateAwardTest(){
    	manualOrderLotteryDao.updateAward(info.getId(), info.getOperatingActivityId(), info.getWinDate());
    	int exchangeStatus = manualOrderLotteryDao.find(info.getId()).getExchangeStatus();
    	Assert.assertTrue(exchangeStatus == 2);
    }

    /**
     * 随机抽一条
     *
     * @param paramMap
     * @return
     */
	@Test
    public void randomFindManualLotteryTest(){
		ManualLotteryOrderEntity e = new ManualLotteryOrderEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
    	e.setStatus(1);
    	e.setExchangeStatus(1);
    	manualOrderLotteryDao.insert(e);
    	Map<String, Object> map = new HashMap<>();
    	map.put("operatingActivityId", e.getOperatingActivityId());
    	map.put("randomInt", 0);
    	ManualLotteryOrderEntity test = manualOrderLotteryDao.randomFindManualLottery(map);
    	TestUtils.assertEqualsReflect(e, test, false, exceptFields);
    }

    /**
     * 更新未中奖用户
     *
     * @param paramMap
     */
	@Test
    public void updateNoAwardListTest(){
		ManualLotteryOrderEntity e = new ManualLotteryOrderEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
    	e.setExchangeStatus(1);
    	manualOrderLotteryDao.insert(e);
    	Map<String, Object> map = new HashMap<>();
    	map.put("date", e.getWinDate());
    	map.put("activityId", e.getOperatingActivityId());
    	manualOrderLotteryDao.updateNoAwardList(map);
    	int exchangeStatus = manualOrderLotteryDao.find(e.getId()).getExchangeStatus();
    	Assert.assertTrue(exchangeStatus == 3);    	
    }

    /**
     * 查询未中奖用户
     *
     * @param paramMap
     * @return
     */
	@Test
    public void findAllNoAwardListTest(){
		ManualLotteryOrderEntity e = new ManualLotteryOrderEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
    	e.setExchangeStatus(1);
    	manualOrderLotteryDao.insert(e);
    	Map<String, Object> map = new HashMap<>();
    	map.put("activityId", e.getOperatingActivityId());
    	List<ManualLotteryOrderEntity> list = manualOrderLotteryDao.findAllNoAwardList(map);
    	Assert.assertTrue(list.size() > 0);
    }

    /**
     * 查询所有ids
     *
     * @param ids
     * @return
     */
	@Test
    public void findAllByIdsTest(){
    	List<Long> ids = new ArrayList<Long>();
    	ids.add(info.getId());
    	List<ManualLotteryOrderEntity> list = manualOrderLotteryDao.findAllByIds(ids);
    	TestUtils.assertEqualsReflect(info, list.get(0), false, exceptFields);
    }

    /**
     * 根据订单号查询
     *
     * @param orderId
     * @return
     */
	@Test
    public void findByOrderIdTest(){
    	ManualLotteryOrderEntity test = manualOrderLotteryDao.findByOrderId(info.getOrderId());
    	TestUtils.assertEqualsReflect(info, test, false, exceptFields);
    }

	@Test
    public void findByAppAndDeveloperBizIdTest(){
    	ManualLotteryOrderEntity test = manualOrderLotteryDao.findByAppAndDeveloperBizId(info.getAppId(), info.getDeveloperBizId());
    	TestUtils.assertEqualsReflect(info, test, false, exceptFields);
    }

	@Test
    public void updateTest(){
		ManualLotteryOrderEntity e = new ManualLotteryOrderEntity(info.getId());
		TestUtils.setRandomAttributesForBean(e, false);
    	manualOrderLotteryDao.update(e);
    	ManualLotteryOrderEntity test = manualOrderLotteryDao.find(e.getId());
    	TestUtils.assertEqualsReflect(e, test, false, exceptFields);
    }

	@Test
    public void findTest(){
    	ManualLotteryOrderEntity test = manualOrderLotteryDao.find(info.getId());
    	TestUtils.assertEqualsReflect(info, test, false, exceptFields);
    }
}
