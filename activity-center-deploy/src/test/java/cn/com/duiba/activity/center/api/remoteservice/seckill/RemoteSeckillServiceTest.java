package cn.com.duiba.activity.center.api.remoteservice.seckill;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.seckill.SeckillStockDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.service.direct.DeveloperActBlackService;
import cn.com.duiba.wolf.dubbo.DubboResult;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by xutao on 2018/4/12.
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class RemoteSeckillServiceTest extends TransactionalTestCaseBase {
    @Autowired
    RemoteSeckillService remoteSeckillService;

    @Autowired
    DeveloperActBlackService developerActBlackService;

    @Test
    public void batchGetStock(){
        List<Long> list = Lists.newArrayList();
        list.add(5281L);
        list.add(5282L);
        DubboResult<List<SeckillStockDto>> dubboResult = remoteSeckillService.batchGetStock(1L, list);

        List<Long> newList = Lists.newArrayList();
        newList.add(5281L);
        newList.add(5250L);
        remoteSeckillService.batchGetStock(1L, newList);
        System.out.println(dubboResult.getResult());
    }

    @Test
    public void queryIdsByActivityType(){
        Set<Integer> activityTypeList = new HashSet<>();
        activityTypeList.add(41);
        activityTypeList.add(2);
        activityTypeList.add(31);
        Map<Integer,Set<Long>> map = developerActBlackService.queryIdsByActivityType(1L, activityTypeList);
        System.out.println(map);

        Set<Integer> list = new HashSet<>();
        list.add(31);
        list.add(6);
        Map<Integer,Set<Long>> newMap = developerActBlackService.queryIdsByActivityType(1L, list);
        System.out.println(newMap);
    }
}
