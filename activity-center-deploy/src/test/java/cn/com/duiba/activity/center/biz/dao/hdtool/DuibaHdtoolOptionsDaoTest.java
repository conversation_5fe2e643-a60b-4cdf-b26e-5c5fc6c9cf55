package cn.com.duiba.activity.center.biz.dao.hdtool;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.hdtool.DuibaHdtoolOptionsEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/25.
 */
@Transactional(DsConstants.DATABASE_PRIZE_CONF)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaHdtoolOptionsDaoTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaHdtoolOptionsDao duibaHdtoolOptionsDao;

    private DuibaHdtoolOptionsEntity duibaHdtoolOptionsDto;

    @Before
    public void prepareData() {
        DuibaHdtoolOptionsEntity e=new DuibaHdtoolOptionsEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setRolling(1);
        duibaHdtoolOptionsDao.insertHdtoolOption(e);
        duibaHdtoolOptionsDto =e;
    }

    @Test
    public void testFindByHdtoolId() {
        Assert.assertNotNull(duibaHdtoolOptionsDao.findByHdtoolId(duibaHdtoolOptionsDto.getDuibaHdtoolId()));
    }

    @Test
    public void testAddRemainingById() {
        DuibaHdtoolOptionsEntity e= duibaHdtoolOptionsDto;
        duibaHdtoolOptionsDao.addRemainingById(e.getId(),1);
        Assert.assertTrue(duibaHdtoolOptionsDao.findOptionById(e.getId()).getRemaining().equals(e.getRemaining()+1));
    }

    @Test
    public void testSubRemainingById() {
        DuibaHdtoolOptionsEntity e= duibaHdtoolOptionsDto;
        duibaHdtoolOptionsDao.subRemainingById(e.getId(),1);
        Assert.assertTrue(duibaHdtoolOptionsDao.findOptionById(e.getId()).getRemaining().equals(e.getRemaining()-1));
    }

    @Test
    public void testUpdateRemainingById() {
        DuibaHdtoolOptionsEntity e= duibaHdtoolOptionsDto;
        duibaHdtoolOptionsDao.updateRemainingById(e.getId(),42);
        Assert.assertTrue(duibaHdtoolOptionsDao.findOptionById(e.getId()).getRemaining().equals(42));
    }

    @Test
    public void testfindRemaingForupdate() {
        DuibaHdtoolOptionsEntity e= duibaHdtoolOptionsDto;
        Assert.assertTrue( duibaHdtoolOptionsDao.findRemaingForupdate(e.getId()).equals(e.getRemaining()));
    }

    @Test
    public void testFindOptionsByDuibaHdtoolId() {
        DuibaHdtoolOptionsEntity duibaHdtoolOptionsDto =new DuibaHdtoolOptionsEntity(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-8L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsDao.insertHdtoolOption(duibaHdtoolOptionsDto);
        Assert.assertTrue(duibaHdtoolOptionsDao.findOptionsByDuibaHdtoolId(-8L).size()>0);
    }

    @Test
    public void testCountOptionsByHdtoolId() {
        DuibaHdtoolOptionsEntity duibaHdtoolOptionsDto =new DuibaHdtoolOptionsEntity(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-8L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsDao.insertHdtoolOption(duibaHdtoolOptionsDto);
        Assert.assertTrue(duibaHdtoolOptionsDao.countOptionsByHdtoolId(-8L)>0);
    }

    @Test
    public void testFindOptionById() {
        DuibaHdtoolOptionsEntity duibaHdtoolOptionsDto =new DuibaHdtoolOptionsEntity(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-8L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsDao.insertHdtoolOption(duibaHdtoolOptionsDto);
        assertOptionDO(duibaHdtoolOptionsDto,duibaHdtoolOptionsDao.findOptionById(duibaHdtoolOptionsDto.getId()));
    }

    @Test
    public void testFindOptionByIdForupdate() {
        DuibaHdtoolOptionsEntity duibaHdtoolOptionsDto =new DuibaHdtoolOptionsEntity(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-8L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsDao.insertHdtoolOption(duibaHdtoolOptionsDto);
        assertOptionDO(duibaHdtoolOptionsDto,duibaHdtoolOptionsDao.findOptionByIdForupdate(duibaHdtoolOptionsDto.getId()));
    }

    @Test
    public void testFindHasUserdHdIds() {//item在另外项目下
        DuibaHdtoolOptionsEntity duibaHdtoolOptionsDto =new DuibaHdtoolOptionsEntity(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-8L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsDao.insertHdtoolOption(duibaHdtoolOptionsDto);
        duibaHdtoolOptionsDao.findHasUserdHdIds(duibaHdtoolOptionsDto.getItemId());
    }

    @Test
    public void testDecrementOptionRemaining() {
        DuibaHdtoolOptionsEntity duibaHdtoolOptionsDto =new DuibaHdtoolOptionsEntity(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-8L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsDao.insertHdtoolOption(duibaHdtoolOptionsDto);
        duibaHdtoolOptionsDao.decrementOptionRemaining(duibaHdtoolOptionsDto.getId());
        Assert.assertEquals(duibaHdtoolOptionsDto.getRemaining(),(Integer)(duibaHdtoolOptionsDao.findOptionById(duibaHdtoolOptionsDto.getId()).getRemaining()+1));
    }

    @Test
    public void testIncrementOptionRemaining() {
        DuibaHdtoolOptionsEntity duibaHdtoolOptionsDto =new DuibaHdtoolOptionsEntity(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(1L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsDao.insertHdtoolOption(duibaHdtoolOptionsDto);
        duibaHdtoolOptionsDao.incrementOptionRemaining(duibaHdtoolOptionsDto.getId());
        Assert.assertEquals(duibaHdtoolOptionsDto.getRemaining(),(Integer)(duibaHdtoolOptionsDao.findOptionById(duibaHdtoolOptionsDto.getId()).getRemaining()-1));
    }

    @Test
    public void testDeleteOptions() {
        DuibaHdtoolOptionsEntity duibaHdtoolOptionsDto =new DuibaHdtoolOptionsEntity(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-8L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsDao.insertHdtoolOption(duibaHdtoolOptionsDto);
        duibaHdtoolOptionsDao.deleteOptions(Arrays.asList(duibaHdtoolOptionsDto.getId()));
        Assert.assertTrue(duibaHdtoolOptionsDao.findOptionById(duibaHdtoolOptionsDto.getId()).getDeleted());
    }

    @Test
    public void testUpdateHdtoolPrize() {
        DuibaHdtoolOptionsEntity duibaHdtoolOptionsDto =new DuibaHdtoolOptionsEntity(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-8L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsDao.insertHdtoolOption(duibaHdtoolOptionsDto);
        duibaHdtoolOptionsDao.incrementOptionRemaining(duibaHdtoolOptionsDto.getId());
        duibaHdtoolOptionsDto.setRate("123");
        duibaHdtoolOptionsDao.updateHdtoolPrize(duibaHdtoolOptionsDto);
        Assert.assertEquals(duibaHdtoolOptionsDto.getRate(),duibaHdtoolOptionsDao.findOptionById(duibaHdtoolOptionsDto.getId()).getRate());

    }

    @Test
    public void testInsertHdtoolOption() {
        DuibaHdtoolOptionsEntity duibaHdtoolOptionsDto =new DuibaHdtoolOptionsEntity(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(1L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsDao.insertHdtoolOption(duibaHdtoolOptionsDto);
    }

    @Test
    public void testUpdateHdtoolOption() {
        DuibaHdtoolOptionsEntity duibaHdtoolOptionsDto =new DuibaHdtoolOptionsEntity(true);
        TestUtils.setRandomAttributesForBean(duibaHdtoolOptionsDto,false);
        duibaHdtoolOptionsDto.setDuibaHdtoolId(-8L);
        duibaHdtoolOptionsDto.setRolling(1);
        duibaHdtoolOptionsDao.insertHdtoolOption(duibaHdtoolOptionsDto);
        DuibaHdtoolOptionsEntity e=new DuibaHdtoolOptionsEntity(duibaHdtoolOptionsDto.getId());
        TestUtils.setRandomAttributesForBean(e,false);
        e.setRolling(1);
        duibaHdtoolOptionsDao.updateHdtoolOption(e);

        assertOptionDO(e,duibaHdtoolOptionsDao.findOptionById(e.getId()));
    }

    private void assertOptionDO(DuibaHdtoolOptionsEntity e, DuibaHdtoolOptionsEntity e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","valid","addrlimit","itemName"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

    private void assertOptionDO(DuibaHdtoolOptionsEntity e, DuibaHdtoolOptionsEntity e1){
        assertOptionDO(e,e1,null);
    }


}
