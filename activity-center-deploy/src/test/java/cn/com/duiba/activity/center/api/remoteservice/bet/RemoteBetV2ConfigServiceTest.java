package cn.com.duiba.activity.center.api.remoteservice.bet;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.bet.BetActGroupDto;
import cn.com.duiba.activity.center.api.remoteservice.betv2.RemoteBetV2ConfigService;
import cn.com.duiba.activity.center.api.tool.RedisKeySpace;
import cn.com.duiba.activity.center.biz.constant.PkConstant;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.service.betopenprize.impl.BetOpenPrizeForAddBalanceHandler;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.kvtable.service.api.enums.ActCenterHBaseKeyEnum;
import cn.com.duiba.kvtable.service.api.remoteservice.hbase.RemoteHbApiKvNoHotspotService;
import cn.com.duiba.wolf.utils.DateUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description:
 * @date 2019/2/281:58 PM
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class RemoteBetV2ConfigServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteBetV2ConfigService remoteBetV2ConfigService;

    @Autowired
    private RemoteHbApiKvNoHotspotService remoteHbApiKvNoHotspotService;

    @Resource(name = "redisTemplate")
    private RedisTemplate<String,Date> redisTemplate;

    @Autowired
    private BetOpenPrizeForAddBalanceHandler betOpenPrizeForAddBalanceHandler;


    @Before
    public void init(){
        BetActGroupDto betActGroupDto = new BetActGroupDto();
        betActGroupDto.setId(8888888L);
        betActGroupDto.setAppId(9876L);
        String pkUnitAwardTag = RedisKeySpace.K145.toString() + "_" + betActGroupDto.getId() + "_" + betActGroupDto.getAppId();
        redisTemplate.opsForHash().put(pkUnitAwardTag, 123456L,DateUtils.getSecondDate("2019-01-02 22:11:22"));
        String prizeTotalKey = ActCenterHBaseKeyEnum.K04.toString()+PkConstant.PRIZE_BET_AMOUNT+betActGroupDto.getId()+"_"+betActGroupDto.getAppId();
        remoteHbApiKvNoHotspotService.increaseByKey(prizeTotalKey,1000L);

    }


    @Test
    public void accessAccountTag(){
        BetActGroupDto betActGroupDto = new BetActGroupDto();
        betActGroupDto.setGmtCreate(DateUtils.getSecondDate("2019-09-02 22:11:22"));
        Assert.assertTrue(remoteBetV2ConfigService.accessAccountTag(betActGroupDto));
    }

    @Test
    public void beLongTagUserCheck(){
        BetActGroupDto betActGroupDto = new BetActGroupDto();
        betActGroupDto.setId(8888888L);
        betActGroupDto.setAppId(9876L);
        betActGroupDto.setBonusAmount(100);
        betActGroupDto.setBonusSizeLimit(10);
        betActGroupDto.setGmtCreate(DateUtils.getSecondDate("2019-09-02 22:11:22"));
        try {
            String pkUnitAwardTag = RedisKeySpace.K145.toString() + "_" + betActGroupDto.getId() + "_" + betActGroupDto.getAppId();

            Assert.assertTrue(remoteBetV2ConfigService.beLongTagUserCheck(true,pkUnitAwardTag,123456L,betActGroupDto));
        } catch (BizException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void openPrizePocket(){
        betOpenPrizeForAddBalanceHandler.openPrizePocket(441L, 47758L);
    }




}
