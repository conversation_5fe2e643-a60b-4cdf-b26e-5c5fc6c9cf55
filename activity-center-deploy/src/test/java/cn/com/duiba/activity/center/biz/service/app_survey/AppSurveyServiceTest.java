package cn.com.duiba.activity.center.biz.service.app_survey;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.app_survey.AppSurveyConfigDto;
import cn.com.duiba.activity.center.api.dto.app_survey.AppSurveyOptionDto;
import cn.com.duiba.activity.center.api.dto.app_survey.AppSurveyQuestionDto;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.wolf.utils.DateUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;

public class AppSurveyServiceTest extends TransactionalTestCaseBase {

    @Resource
    private AppSurveyService appSurveyService;

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Test
    public void findSurveyConfigTest() throws BizException {
        AppSurveyConfigDto surveyConfig = appSurveyService.findSurveyConfigByOpId(204732830531071L);
        log.info("[app_survey] surveyConfig = {}", JSON.toJSONString(surveyConfig));
    }


    @Test
    public void editSurveyConfigTest() throws BizException {
        String dataStr = "{\"appId\":19441,\"bannerImage\":\"//yun.duiba.com.cn/saas/images/marketing/rockSign/banner.png\",\"endTime\":1666368000000,\"gmtCreate\":1658734398000,\"gmtModified\":1658734398000,\"id\":7,\"interfaceConfig\":\"\",\"opId\":204732830531071,\"operationSwitch\":0,\"questionConfigList\":[{\"appId\":19441,\"gmtCreate\":1658734399000,\"gmtModified\":1658734399000,\"id\":3,\"optionConfigList\":[{\"gmtCreate\":1658734399000,\"gmtModified\":1658734399000,\"id\":1,\"optionContent\":\"汉堡\",\"optionType\":1,\"questionId\":3},{\"gmtCreate\":1658734399000,\"gmtModified\":1658734399000,\"id\":2,\"optionContent\":\"羊肉串\",\"optionType\":1,\"questionId\":3},{\"optionContent\":\"土豆泥\",\"optionType\":1,\"questionId\":3},{\"gmtCreate\":1658734399000,\"gmtModified\":1658734399000,\"id\":4,\"optionContent\":\"炸鸡翅\",\"optionType\":1,\"questionId\":3}],\"questionDescription\":\"最喜爱的食物\",\"questionRequired\":1,\"questionType\":1,\"surveyId\":7},{\"appId\":19441,\"gmtCreate\":1658734399000,\"gmtModified\":1658734399000,\"id\":4,\"optionConfigList\":[{\"gmtCreate\":1658734399000,\"gmtModified\":1658734399000,\"id\":5,\"optionContent\":\"阿甘正传\",\"optionType\":2,\"questionId\":4},{\"gmtCreate\":1658734399000,\"gmtModified\":1658734399000,\"id\":6,\"optionContent\":\"肖申克的救赎\",\"optionType\":2,\"questionId\":4},{\"gmtCreate\":1658734399000,\"gmtModified\":1658734399000,\"id\":7,\"optionContent\":\"新龙门客栈\",\"optionType\":2,\"questionId\":4},{\"gmtCreate\":1658734399000,\"gmtModified\":1658734399000,\"id\":8,\"optionContent\":\"变形金刚2\",\"optionType\":2,\"questionId\":4}],\"questionDescription\":\"喜爱的电影\",\"questionRequired\":1,\"questionType\":2,\"surveyId\":7}],\"smallImage\":\"//yun.duiba.com.cn/saas/images/marketing/rockSign/banner.png\",\"startTime\":1658332800000,\"surveyRule\":\"绝不意气用事！！\",\"surveyTitle\":\"测试问卷123\"}";
        AppSurveyConfigDto surveyConfigDto = JSON.parseObject(dataStr, AppSurveyConfigDto.class);

        log.info("surveyConfigDto = {}", JSON.toJSONString(surveyConfigDto));

        appSurveyService.editSurveyConfig(surveyConfigDto, false);
    }

    @Test
    public void saveSurveyConfigTest() throws BizException {
        AppSurveyConfigDto surveyConfig = new AppSurveyConfigDto();
        surveyConfig.setAppId(19441L);
        surveyConfig.setSurveyTitle("测试问卷");
        surveyConfig.setSurveyRule("1……2……3……");
        surveyConfig.setStartTime(DateUtils.getDayStartTime("2022-07-21 00:00:00"));
        surveyConfig.setEndTime(DateUtils.getDayEndTime("2022-10-21 00:00:00"));
        surveyConfig.setSmallImage("//yun.duiba.com.cn/saas/images/marketing/rockSign/banner.png");
        surveyConfig.setBannerImage("//yun.duiba.com.cn/saas/images/marketing/rockSign/banner.png");
        surveyConfig.setInterfaceConfig("");
        surveyConfig.setOperationSwitch(0);
        List<AppSurveyQuestionDto> qList = Lists.newArrayList();
        surveyConfig.setQuestionConfigList(qList);

        AppSurveyQuestionDto q1 = new AppSurveyQuestionDto();
        q1.setAppId(19441L);
        q1.setQuestionType(1);
        q1.setQuestionDescription("最喜爱的食物");
        q1.setQuestionRequired(1);
        List<AppSurveyOptionDto> opList = Lists.newArrayList();
        q1.setOptionConfigList(opList);
        qList.add(q1);

        AppSurveyOptionDto op1 = new AppSurveyOptionDto();
        op1.setOptionType(1);
        op1.setOptionContent("汉堡");
        opList.add(op1);

        AppSurveyOptionDto op2 = new AppSurveyOptionDto();
        op2.setOptionType(1);
        op2.setOptionContent("薯条");
        opList.add(op2);

        AppSurveyOptionDto op3 = new AppSurveyOptionDto();
        op3.setOptionType(1);
        op3.setOptionContent("蛋挞");
        opList.add(op3);

        AppSurveyOptionDto op4 = new AppSurveyOptionDto();
        op4.setOptionType(1);
        op4.setOptionContent("炸鸡翅");
        opList.add(op4);




        AppSurveyQuestionDto q2 = new AppSurveyQuestionDto();
        q2.setAppId(19441L);
        q2.setQuestionType(2);
        q2.setQuestionDescription("喜爱的电影");
        q2.setQuestionRequired(1);
        List<AppSurveyOptionDto> _opList = Lists.newArrayList();
        q2.setOptionConfigList(_opList);
        qList.add(q2);

        AppSurveyOptionDto op5 = new AppSurveyOptionDto();
        op5.setOptionType(2);
        op5.setOptionContent("阿甘正传");
        _opList.add(op5);

        AppSurveyOptionDto op6 = new AppSurveyOptionDto();
        op6.setOptionType(2);
        op6.setOptionContent("肖申克的救赎");
        _opList.add(op6);

        AppSurveyOptionDto op7 = new AppSurveyOptionDto();
        op7.setOptionType(2);
        op7.setOptionContent("新龙门客栈");
        _opList.add(op7);

        AppSurveyOptionDto op8 = new AppSurveyOptionDto();
        op8.setOptionType(2);
        op8.setOptionContent("变形金刚");
        _opList.add(op8);

        log.info("[survey] surveyConfig = {}", JSON.toJSONString(surveyConfig));
        appSurveyService.saveSurveyConfig(surveyConfig,false);
    }
}
