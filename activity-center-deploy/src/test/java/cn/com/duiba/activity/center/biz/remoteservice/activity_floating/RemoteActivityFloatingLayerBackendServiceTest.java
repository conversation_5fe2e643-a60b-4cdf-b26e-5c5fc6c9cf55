package cn.com.duiba.activity.center.biz.remoteservice.activity_floating;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity_floating.AbTestDto;
import cn.com.duiba.activity.center.api.remoteservice.activity_floating.RemoteActivityFloatingLayerBackendService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.activity_floating.AbTestDao;
import cn.com.duiba.activity.center.biz.entity.activity_floating.AbTestEntity;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by hww on 2017/8/21.
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class RemoteActivityFloatingLayerBackendServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteActivityFloatingLayerBackendService remoteActivityFloatingLayerBackendService;

    @Autowired
    private AbTestDao abTestDao;

    @Test
    public void testFindAbTestOpen() {
        AbTestEntity entity = insertDto();
        AbTestDto dto = new AbTestDto();
        dto.setRelationId(entity.getRelationId());
        dto.setRelationType(entity.getRelationType());
        dto.setAbTesType(entity.getAbTesType());
        AbTestDto result = remoteActivityFloatingLayerBackendService.findAbTestOpen(dto).getResult();
        Assert.assertEquals(entity.getRelationId(), result.getRelationId());
        Assert.assertEquals(entity.getRelationType(), result.getRelationType());
        Assert.assertEquals(entity.getAbTesType(), result.getAbTesType());
    }

    private AbTestEntity insertDto() {
        AbTestEntity entity = new AbTestEntity();
        entity.setAbTesType(AbTestDto.TYPEFLOAT);
        entity.setRelationId(-1L);
        entity.setRelationType(AbTestDto.RELATION_TYPE_HDTOOL);
        entity.setAdminId(-1L);
        entity.setState(1);
        abTestDao.insert(entity);
        return entity;
    }
}
