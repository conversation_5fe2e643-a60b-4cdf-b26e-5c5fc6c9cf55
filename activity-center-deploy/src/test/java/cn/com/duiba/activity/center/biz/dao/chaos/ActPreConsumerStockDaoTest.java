package cn.com.duiba.activity.center.biz.dao.chaos;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.chaos.ActPreConsumeStockEntity;
import cn.com.duiba.wolf.utils.TestUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * Created by yansen on 16/5/25.
 */
@Transactional(ActPreConsumerStockDaoTest.THIS_DATABASE_SCHEMA)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class ActPreConsumerStockDaoTest extends TransactionalTestCaseBase {
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS_ACTIVITY;

    @Resource
    private ActPreConsumerStockDao actPreConsumerStockDao;


    @Test
    public void testInsert() {
        ActPreConsumeStockEntity e=new ActPreConsumeStockEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        actPreConsumerStockDao.insert(e);
    }

    @Test
    public void testFindPreConsumerByBizPay() {
        ActPreConsumeStockEntity e=new ActPreConsumeStockEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setAction("pay");
        actPreConsumerStockDao.insert(e);
        Assert.assertNotNull(actPreConsumerStockDao.findPreConsumerByBizPay(e.getBizId(),e.getBizSource()));
    }
    
}
