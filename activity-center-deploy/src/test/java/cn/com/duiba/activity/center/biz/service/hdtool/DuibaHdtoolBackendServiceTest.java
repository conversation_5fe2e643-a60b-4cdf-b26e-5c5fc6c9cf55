package cn.com.duiba.activity.center.biz.service.hdtool;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;

/**
 * Created by yansen on 16/5/24.
 */
@Transactional(DsConstants.DATABASE_CREDITS)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaHdtoolBackendServiceTest extends TransactionalTestCaseBase {

	@Resource
	private DuibaHdtoolBackendService duibaHdtoolBackendService;


	@Test
	public void testFindDuibaHdToolsList() {
		Map<String,Object> params=new HashMap<>();
		params.put("max",10);
		params.put("offset",0);
		params.put("extend","adsf");
		Assert.assertTrue(duibaHdtoolBackendService.findDuibaHdToolsList(params).size()==0);
	}

	@Test
	public void testCountDuibaHdToolsList() {
		Map<String,Object> params=new HashMap<>();
		params.put("extend","adsf");
		Assert.assertTrue(duibaHdtoolBackendService.countDuibaHdToolsList(params)==0);
	}
}
