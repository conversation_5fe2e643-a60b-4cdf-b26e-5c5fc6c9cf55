package cn.com.duiba.activity.center.biz.dao.bet;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.remoteservice.betv2.RemoteBetV2RecordService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.service.betopenprize.impl.BetOpenPrizeForAddBalanceHandler;
import cn.com.duiba.activity.center.biz.service.betopenprize.impl.BetOpenPrizeForItemHandler;
import cn.com.duiba.kvtable.service.api.dto.HbaseKvDto;
import cn.com.duiba.kvtable.service.api.enums.ActCenterHBaseKeyEnum;
import cn.com.duiba.kvtable.service.api.enums.HbaseKeySpaceEnum;
import cn.com.duiba.kvtable.service.api.params.HbaseVKeyParam;
import cn.com.duiba.kvtable.service.api.params.hbapi.HbKStrVParam;
import cn.com.duiba.kvtable.service.api.remoteservice.hbase.RemoteHbConsisHashKvService;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2019/11/1
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class BetOpenPrizeTest  extends TransactionalTestCaseBase {
    @Autowired
    private BetOpenPrizeForItemHandler betOpenPrizeForItemHandler;
    @Autowired
    private BetOpenPrizeForAddBalanceHandler betOpenPrizeForAddBalanceHandler;
    @Autowired
    private RemoteBetV2RecordService remoteBetV2RecordService;
    @Autowired
    private RemoteHbConsisHashKvService remoteHbConsisHashKvService;


    @Test
    public void testOpenPrize() {
        betOpenPrizeForItemHandler.handleOpenPrize(562L, 1L);
    }

    @Test
    public void testOpenPrize4RedPacket() {
        betOpenPrizeForAddBalanceHandler.handleOpenPrize(546L, 1L);
        Assert.assertTrue(Objects.equals(remoteHbConsisHashKvService.getStringByKey("k03_ACH_K10_161_1539361"),"true"));
        Assert.assertTrue(Objects.equals(remoteHbConsisHashKvService.batchFindStrVBykeys(Arrays.asList("k03_ACH_K10_161_1539361")).get(0).getValue(),"true"));
    }

    @Test
    public void testHbaseKey(){
//        Assert.assertTrue(Objects.equals(remoteHbConsisHashKvService.getStringByKey("k03_ACH_K10_161_1539361"),"true"));
//        Assert.assertTrue(Objects.equals(remoteHbConsisHashKvService.batchFindStrVBykeys(Arrays.asList("k03_ACH_K10_161_1539361")).get(0).getValue(),"true"));


        System.out.println("k03_ACH_K10_161_1539361");
        System.out.println(JSON.toJSON(remoteHbConsisHashKvService.batchFindStrVBykeys(Arrays.asList("k03_ACH_K10_161_1539361"))));
    }

    @Test
    public void list() {
        remoteBetV2RecordService.listByActGroupId(1L, 1L, 0, 99999);
    }

    @Test
    public void test() {
        List<HbKStrVParam> batchUpsertKey = new ArrayList<>();
        HbaseVKeyParam hbaseVKeyParam = new HbaseVKeyParam();
        hbaseVKeyParam.setConsumerId(1539361L);
        hbaseVKeyParam.setVkey(buildPKH5RedPacketHbaseKey(161L));
        hbaseVKeyParam.setKeySpaceEnum(HbaseKeySpaceEnum.K03);
        String key = hbaseVKeyParam.buildVkey();

        HbKStrVParam param = new HbKStrVParam();
        param.setKey(key);
        param.setValue(String.valueOf(400));
        batchUpsertKey.add(param);
        remoteHbConsisHashKvService.batchUpsertStrV(batchUpsertKey);


        HbaseKvDto hbaseKvDto = new HbaseKvDto();
        hbaseKvDto.setRowKey(key);
        hbaseKvDto.setsValue("400");
        remoteHbConsisHashKvService.insertKvDto(hbaseKvDto);


        System.out.println(remoteHbConsisHashKvService.getStringByKey(key));
        System.out.println(JSON.toJSONString(remoteHbConsisHashKvService.batchFindStrVBykeys(Arrays.asList(key))));

    }

    private String buildPKH5RedPacketHbaseKey(Long actGroupId) {
        return ActCenterHBaseKeyEnum.K10.toString() + actGroupId;
    }
}
