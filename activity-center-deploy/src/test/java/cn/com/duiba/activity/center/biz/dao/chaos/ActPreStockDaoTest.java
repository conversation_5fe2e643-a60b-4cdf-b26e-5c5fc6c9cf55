package cn.com.duiba.activity.center.biz.dao.chaos;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.chaos.ActPreStockEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/22.
 */
@Transactional(ActPreStockDaoTest.THIS_DATABASE_SCHEMA)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class ActPreStockDaoTest extends TransactionalTestCaseBase{

    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS_ACTIVITY;

    @Resource
    private ActPreStockDao actPreStockDao;

    private ActPreStockEntity actPreStockDO;

    @Before
    public void testInsert() {
        ActPreStockEntity e=new ActPreStockEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(0);
        actPreStockDao.insert(e);
        actPreStockDO=e;
    }

    @Test
    public void testFindByLock() {
        ActPreStockEntity e=actPreStockDO;
        ActPreStockEntity e1=actPreStockDao.findByLock(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindPreStockByApp() {
        ActPreStockEntity e=actPreStockDO;
        ActPreStockEntity e1=actPreStockDao.findPreStockByApp(e.getRelationPrizeId(),e.getRelationType(),e.getAppId());
        assertDO(e,e1);
    }

    @Test
    public void testFindPreStockByShare() {
        ActPreStockEntity e=new ActPreStockEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(0);
        e.setAppId(0l);
        actPreStockDao.insert(e);
        ActPreStockEntity e1=actPreStockDao.findPreStockByShare(e.getRelationPrizeId(),e.getRelationType());
        assertDO(e,e1);
    }

    @Test
    public void testDecrementRemaining() {
        actPreStockDao.decrementRemaining(actPreStockDO.getId());
        Assert.assertTrue(actPreStockDao.findByLock(actPreStockDO.getId()).getPrizeQuantity().equals(actPreStockDO.getPrizeQuantity()-1));
    }

    @Test
    public void testIncrementRemaining() {
        actPreStockDao.incrementRemaining(actPreStockDO.getId());
        Assert.assertTrue(actPreStockDao.findByLock(actPreStockDO.getId()).getPrizeQuantity().equals(actPreStockDO.getPrizeQuantity()+1));
    }

    @Test
    public void testAddRemainingById() {
        actPreStockDao.addRemainingById(actPreStockDO.getId(),1l);
        Assert.assertTrue(actPreStockDao.findByLock(actPreStockDO.getId()).getPrizeQuantity().equals(actPreStockDO.getPrizeQuantity()+1));

    }

    @Test
    public void testSubRemainingById() {
        actPreStockDao.subRemainingById(actPreStockDO.getId(),1l);
        Assert.assertTrue(actPreStockDao.findByLock(actPreStockDO.getId()).getPrizeQuantity().equals(actPreStockDO.getPrizeQuantity()-1));
    }

    @Test
    public void testUpdate() {
        ActPreStockEntity e=new ActPreStockEntity(actPreStockDO.getId());
        TestUtils.setRandomAttributesForBean(e,false);
        actPreStockDao.update(e);
        ActPreStockEntity e1=actPreStockDao.findByLock(e.getId());
        assertDO(e,e1);

    }

    @Test
    public void testDeleteActStock() {
        ActPreStockEntity e=actPreStockDO;
        actPreStockDao.deleteActStock(e.getRelationConfigId(),e.getRelationType());
        Assert.assertNull(actPreStockDao.findByLock(e.getId()));
    }

    @Test
    public void testDeleteActPrizeStock() {
        ActPreStockEntity e=actPreStockDO;
        actPreStockDao.deleteActPrizeStock(e.getRelationConfigId(),e.getRelationPrizeId(),e.getRelationType());
        Assert.assertNull(actPreStockDao.findByLock(e.getId()));
    }

    @Test
    public void testDeleteActStockAppId() {
        ActPreStockEntity e=actPreStockDO;
        actPreStockDao.deleteActStockAppId(e.getRelationPrizeId(),e.getRelationType(),e.getAppId());
    }

    @Test
    public void testFindActStockByConfigId() {
        ActPreStockEntity e=actPreStockDO;
        Assert.assertTrue(actPreStockDao.findActStockByConfigId(e.getRelationConfigId(),e.getRelationType()).size()>0);
    }

    private void assertDO(ActPreStockEntity e, ActPreStockEntity e1){
        assertDO(e,e1,null);
    }

    private void assertDO(ActPreStockEntity e, ActPreStockEntity e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }

}
