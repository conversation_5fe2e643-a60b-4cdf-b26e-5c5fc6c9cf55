package cn.com.duiba.activity.center.api.remoteservice.richman;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.richman.RichManPieceDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2020-08-26.
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class RemoteRichManPieceServiceTest extends TransactionalTestCaseBase {


    @Autowired
    private RemoteRichManPieceService remoteRichManPieceService;


    @Test
    public void testPiece() {
        Long operatingActivityId = 1L;
        Long consumerId = 1L;
        RichManPieceDto piece = remoteRichManPieceService.findOrInit(operatingActivityId, consumerId);
        Assert.assertNotNull(piece);
        Integer creditsCost = piece.getCreditsCost();
        Assert.assertEquals(0, (int) creditsCost);
        int delta = 5;
        Integer newCreditsCost = remoteRichManPieceService.increaseCreditsCost(piece.getId(), delta + creditsCost);
        Assert.assertEquals(newCreditsCost.longValue(), creditsCost + delta);
        Integer currentPoint = piece.getCurrentPoint();
        Assert.assertEquals(1, (int) currentPoint);
        int location = 5;
        Integer newLocation = remoteRichManPieceService.changeLocationById(piece.getId(), location);
        Assert.assertEquals(5, (int) newLocation);
        RichManPieceDto currentPiece = remoteRichManPieceService.findOrInit(operatingActivityId, consumerId);
        Assert.assertEquals(newLocation.longValue(), (int) currentPiece.getCurrentPoint());
    }

}
