package cn.com.duiba.activity.center.api.remoteservice.plugin;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity_brick.PopupActivityBrickDto;
import cn.com.duiba.activity.center.api.dto.plugin.ActivityPluginDto;
import cn.com.duiba.activity.center.api.dto.plugin.ActivityPluginParticipateStatusDto;
import cn.com.duiba.activity.center.api.enums.PopupTimeAreaEnum;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.service.activity_brick.PopupActivityBrickService;
import cn.com.duiba.developer.center.api.utils.MD5;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Created by hww on 2017/9/12.
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class RemoteActivityPluginServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteActivityPluginService remoteActivityPluginService;
    @Autowired
    private PopupActivityBrickService popupActivityBrickService;

    @Test
    public void testFindMd5ById() {
        PopupActivityBrickDto insert = popupActivityBrickService.insert(getBrick());
        String md5 = remoteActivityPluginService.findMd5ById(insert.getId()).getResult();
        Assert.assertEquals(md5, insert.getMd5());
    }

    @Test
    public void testFindBrickByIdNoCache() {
        PopupActivityBrickDto insert = popupActivityBrickService.insert(getBrick());
        PopupActivityBrickDto customHdToolSkinDTO = remoteActivityPluginService.findBrickByIdNoCache(insert.getId()).getResult();
        Assert.assertEquals(customHdToolSkinDTO.getMd5(), insert.getMd5());
        Assert.assertEquals(customHdToolSkinDTO.getContent(), insert.getContent());
    }

    private PopupActivityBrickDto getBrick(){
        PopupActivityBrickDto brick4insert = new PopupActivityBrickDto();
        brick4insert.setName("NAME");
        brick4insert.setContent("CONTENT");
        brick4insert.setStatus(PopupActivityBrickDto.STATUS_OFF);
        brick4insert.setMd5(MD5.md5(brick4insert.getContent()));
        brick4insert.setAppId(0L);
        brick4insert.setPosition(0);
        brick4insert.setType(PopupActivityBrickDto.TYPE_CUSTOM_HDTOOL);
        return brick4insert;
    }

    @Test
    public void testAddAppMarkForPopup() {
        Long appId1 = 1L;
        Long appId2 = 2L;
        Long appId3 = 3L;
        remoteActivityPluginService.addAppMarkForPopup(appId1);
        remoteActivityPluginService.addAppMarkForPopup(appId2);
        remoteActivityPluginService.addAppMarkForPopup(appId3);
        Set<Long> appIds = remoteActivityPluginService.findAppMarkForPopup(PopupTimeAreaEnum.getEnumByDate(new Date()));
        Assert.assertEquals(appIds.size(), 3);
        Assert.assertTrue(appIds.contains(appId1));
        Assert.assertTrue(appIds.contains(appId2));
        Assert.assertTrue(appIds.contains(appId3));

    }

    @Test
    public void testFindByIds(){
        List<Long> ids = new ArrayList<>();
        ids.add(1031L);
        ids.add(1032l);
        List<ActivityPluginDto> activityPluginDtoList = remoteActivityPluginService.findByIds(ids);
        System.out.println(JSON.toJSONString(activityPluginDtoList));
    }

    @Test
    public void testCheckUsedPlugins(){
        List<Long> ids = new ArrayList<>();
        ids.add(1033L);
        ids.add(1032l);
        ids.add(1034L);
        List<ActivityPluginParticipateStatusDto> result = remoteActivityPluginService.checkUsedPlugins(ids, 100040205L);
        System.out.println(JSON.toJSONString(result));
    }
}
