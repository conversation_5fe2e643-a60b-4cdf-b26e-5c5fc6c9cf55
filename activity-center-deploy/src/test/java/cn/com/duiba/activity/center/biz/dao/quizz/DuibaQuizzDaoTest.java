package cn.com.duiba.activity.center.biz.dao.quizz;

import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.AddActivityEntity;
import cn.com.duiba.activity.center.biz.entity.quizz.DuibaQuizzEntity;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by x<PERSON><PERSON><PERSON><PERSON> on 16/6/13.
 */
@Transactional(DsConstants.DATABASE_QUIZZ)
public class DuibaQuizzDaoTest extends QuizzBaseTest {

    @Resource
    private DuibaQuizzDao duibaQuizzDao;

    private long          id;

    @Before
    public void insertTest() {

        DuibaQuizzEntity duibaQuizzEntity = new DuibaQuizzEntity();
        id = 1233333331;
        duibaQuizzEntity.setId(id);
        duibaQuizzEntity.setTitle("title");
        duibaQuizzEntity.setStatus(1);
        duibaQuizzEntity.setCreditsPrice(12L);
        duibaQuizzEntity.setLimitCount(10);
        duibaQuizzEntity.setFreeScope("scope");
        duibaQuizzEntity.setDuibaQuizzBrickId(12L);
        duibaQuizzEntity.setRule("rule");
        duibaQuizzEntity.setBanner("banner");
        duibaQuizzEntity.setSmallImage("smalll image");
        duibaQuizzEntity.setWhiteImage("while image");
        duibaQuizzEntity.setLogo("logo");
        duibaQuizzEntity.setRecommendImage("recommend iamge");
        duibaQuizzEntity.setSwitches(12);
        duibaQuizzEntity.setAutoOffDate(new Date());
        duibaQuizzEntity.setDeleted(false);
        duibaQuizzDao.insert(duibaQuizzEntity);

    }
    
    @Test
    public void findTest() {
        DuibaQuizzEntity duibaQuizzEntity = duibaQuizzDao.find(id);
        Assert.assertNotNull(duibaQuizzEntity);
    }

    @Test
    public void findAutoOffTest() {

        List<DuibaQuizzEntity> autoOff = duibaQuizzDao.findAutoOff();

        Assert.assertNotNull(autoOff);
    }

    @Test
    public void findAllByIdsTest() {
        ArrayList<Long> longs = Lists.newArrayList(id);
        List<DuibaQuizzEntity> allByIds = duibaQuizzDao.findAllByIds(longs);
        Assert.assertNotNull(allByIds);
    }

    @Test
    public void findAllQuizzTest() {
        List<AddActivityEntity> allQuizz = duibaQuizzDao.findAllQuizz();
        Assert.assertNotNull(allQuizz);
    }

    @Test
    public void findByPageTest() {
        List<DuibaQuizzEntity> byPage = duibaQuizzDao.findByPage(1, 1);
        Assert.assertNotNull(byPage);
    }

    @Test
    public void findByPage1Test() {
        Map<String, Object> params = new HashMap<>();
        params.put("offset", 1);
        params.put("max", 1);
        List<DuibaQuizzEntity> byPage = duibaQuizzDao.findByPage(params);
        Assert.assertNotNull(byPage);
    }

    @Test
    public void updateAutoOffDateNullTest() {
        int i = duibaQuizzDao.updateAutoOffDateNull(id);
        Assert.assertEquals(1, i);
    }

    @Test
    public void findpageCountTest() {
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        Long pageCount = duibaQuizzDao.findPageCount(params);
        Assert.assertNotNull(pageCount);
    }

    @Test
    public void updateTest() {
        DuibaQuizzEntity duibaQuizzEntity = new DuibaQuizzEntity();
        String newTitle = "title-new";
        duibaQuizzEntity.setId(id);
        duibaQuizzEntity.setTitle(newTitle);
        duibaQuizzDao.update(duibaQuizzEntity);

        DuibaQuizzEntity duibaQuizzEntity1 = duibaQuizzDao.find(id);
        Assert.assertNotNull(duibaQuizzEntity1);
        Assert.assertEquals(newTitle, duibaQuizzEntity1.getTitle());
    }

    @Test
    public void deleteTest() {
        int delete = duibaQuizzDao.delete(id);
        Assert.assertEquals(1,delete);
    }

    @Test
    public void updateSwitchesTest() {

        duibaQuizzDao.updateSwitches(id, 2L);
        DuibaQuizzEntity duibaQuizzEntity1 = duibaQuizzDao.find(id);
        Assert.assertNotNull(duibaQuizzEntity1);
        Assert.assertEquals(2, duibaQuizzEntity1.getSwitches().intValue());
    }

    @Test
    public void updateInfoFormTest() {

        DuibaQuizzEntity duibaQuizzEntity = new DuibaQuizzEntity();
        String newTitle = "title-new";
        duibaQuizzEntity.setId(id);
        duibaQuizzEntity.setTitle(newTitle);
        duibaQuizzEntity.setStatus(2);
        duibaQuizzEntity.setCreditsPrice(13L);
        duibaQuizzEntity.setLimitCount(11);
        duibaQuizzEntity.setLimitScope("limit-scope");
        duibaQuizzEntity.setFreeScope("scope-new");
        duibaQuizzEntity.setDuibaQuizzBrickId(13L);
        duibaQuizzEntity.setRule("rule-new");
        duibaQuizzEntity.setBanner("banner-new");
        duibaQuizzEntity.setSmallImage("smalll image-new");
        duibaQuizzEntity.setWhiteImage("while image-new");
        duibaQuizzEntity.setLogo("logo-new");
        duibaQuizzEntity.setRecommendImage("recommend iamge-new");
        duibaQuizzEntity.setSwitches(13);
        duibaQuizzEntity.setAutoOffDate(new Date());
        duibaQuizzEntity.setDeleted(false);

        duibaQuizzDao.updateInfoForm(duibaQuizzEntity);

        DuibaQuizzEntity duibaQuizzEntity1 = duibaQuizzDao.find(id);
        Assert.assertNotNull(duibaQuizzEntity1);
        Assert.assertEquals(newTitle, duibaQuizzEntity1.getTitle());

    }
}
