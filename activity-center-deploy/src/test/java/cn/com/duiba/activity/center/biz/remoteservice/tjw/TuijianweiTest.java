package cn.com.duiba.activity.center.biz.remoteservice.tjw;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.params.RecommendParams;
import cn.com.duiba.activity.center.api.remoteservice.recommend.RemoteRecommendService;

/**
 * Created by sty on 2018/6/29.
 */
public class TuijianweiTest extends TransactionalTestCaseBase {

  @Autowired
  private RemoteRecommendService remoteRecommendService;
  @Test
  public void testRecommends(){

    RecommendParams params = new RecommendParams();
    params.setAppId(1L);
    params.setRequestType("activity");
    params.setOperatingActivityId(1L);
    remoteRecommendService.findRecommends(params);
  }

}
