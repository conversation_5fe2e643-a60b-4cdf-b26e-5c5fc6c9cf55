package cn.com.duiba.activity.center.api.remoteservice.openredpacket;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.openredpacket.OpenRedPacketHelpRecordDto;
import cn.com.duiba.activity.center.api.enums.YesOrNoEnum;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/5/9 12:00
 * @description:
 */
public class RemoteOpenRedPacketHelpRecordServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteOpenRedPacketHelpRecordService remoteOpenRedPacketHelpRecordService;

    @Test
    public void testAdd(){
        OpenRedPacketHelpRecordDto entity = new OpenRedPacketHelpRecordDto();
        entity.setAppId(1L);
        entity.setConfigId(22L);
        entity.setOpenRecordId(2L);
        entity.setConsumerId(5L);
        entity.setPartnerUserId("test5");
        entity.setOpenAmount(3L);
        entity.setOpenType(YesOrNoEnum.NO.getCode());
        int recordId = remoteOpenRedPacketHelpRecordService.add(entity);
        System.out.println(recordId);
        Assert.assertTrue(recordId > 0);
    }

    @Test
    public void testGetByRecordId(){
        List<OpenRedPacketHelpRecordDto> records = remoteOpenRedPacketHelpRecordService.getByRecordId(2L);
        System.out.println(JSON.toJSON(records));
        Assert.assertNotNull(records);
    }

    @Test
    public void testGetByCidAndOpenType(){
        List<OpenRedPacketHelpRecordDto> records = remoteOpenRedPacketHelpRecordService.getByCidAndOpenType(2L, 22L, 0);
        System.out.println(JSON.toJSON(records));
        Assert.assertNotNull(records);
    }
}
