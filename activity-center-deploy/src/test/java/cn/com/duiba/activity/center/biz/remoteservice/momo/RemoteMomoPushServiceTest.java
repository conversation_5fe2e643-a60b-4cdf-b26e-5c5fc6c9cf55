package cn.com.duiba.activity.center.biz.remoteservice.momo;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.remoteservice.momo.RemoteMomoPushService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2018/8/17
 */
@Transactional(DsConstants.DATABASE_DUIBA_CUSTOM)
public class RemoteMomoPushServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteMomoPushService remoteMomoPushService;

    @Test
    public void getMinAndMaxConsumerId() {
//        System.out.println(remoteMomoPushService.getMinAndMaxConsumerId());
    }

}
