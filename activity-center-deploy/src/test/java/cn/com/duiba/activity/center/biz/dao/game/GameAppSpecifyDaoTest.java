package cn.com.duiba.activity.center.biz.dao.game;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.game.GameAppSpecifyEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by yansen on 16/5/26.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class GameAppSpecifyDaoTest extends TransactionalTestCaseBase {

    @Resource
    private GameAppSpecifyDao gameAppSpecifyDao;

    private GameAppSpecifyEntity gameAppSpecifyDO;

    @Before
    public void testAdd(){
        GameAppSpecifyEntity e=new GameAppSpecifyEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        gameAppSpecifyDao.add(e);
        gameAppSpecifyDO=e;
    }

    @Test
    public void testFindByGameConfigAndAppId() {
        GameAppSpecifyEntity e=gameAppSpecifyDO;
        GameAppSpecifyEntity e1=gameAppSpecifyDao.findByGameConfigAndAppId(e.getAppId(),e.getGameConfigDuibaId());
        assertDO(e,e1);
    }

    @Test
    public void testDelete() {
        GameAppSpecifyEntity e=gameAppSpecifyDO;
        GameAppSpecifyEntity e1=gameAppSpecifyDao.findByGameConfigAndAppId(e.getAppId(),e.getGameConfigDuibaId());
        assertDO(e,e1);
        gameAppSpecifyDao.delete(e1.getId());
        Assert.assertNull(gameAppSpecifyDao.findByGameConfigAndAppId(e.getAppId(),e.getGameConfigDuibaId()));
    }

    @Test
    public void testAddBatch() {
        GameAppSpecifyEntity e=new GameAppSpecifyEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        List<GameAppSpecifyEntity> list=new ArrayList<>();
        list.add(e);
        gameAppSpecifyDao.addBatch(list);
    }

    @Test
    public void testFindByGameId() {
        GameAppSpecifyEntity e=new GameAppSpecifyEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        gameAppSpecifyDao.add(e);
        Assert.assertTrue(gameAppSpecifyDao.findByGameId(e.getGameConfigDuibaId()).size()>0);
    }

    private void assertDO(GameAppSpecifyEntity e, GameAppSpecifyEntity e1){
        assertDO(e,e1,null);
    }

    private void assertDO(GameAppSpecifyEntity e, GameAppSpecifyEntity e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","id","deleted"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

}
