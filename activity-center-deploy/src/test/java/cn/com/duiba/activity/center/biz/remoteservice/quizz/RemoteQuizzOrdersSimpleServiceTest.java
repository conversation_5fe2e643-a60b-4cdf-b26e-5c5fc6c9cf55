package cn.com.duiba.activity.center.biz.remoteservice.quizz;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.quizz.QuizzOrdersDto;
import cn.com.duiba.activity.center.api.remoteservice.quizz.RemoteQuizzOrdersSequenceService;
import cn.com.duiba.activity.center.api.remoteservice.quizz.RemoteQuizzOrdersSimpleService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ_CONSUMER)
@Ignore
public class RemoteQuizzOrdersSimpleServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private RemoteQuizzOrdersSimpleService quizzOrdersSimpleService;
	
	@Autowired
	private RemoteQuizzOrdersSequenceService quizzOrdersSequenceService;
	
	private QuizzOrdersDto info;
	
	@Before
	public void insertTest(){
		QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		quizzOrdersSimpleService.insert(e, quizzOrdersSequenceService.getId().getId());
		info = e;
	}
	
	@Test
	public void countByConsumerIdAndOperatingActivityIdTest(){
		QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setStatus(1);
		quizzOrdersSimpleService.insert(e, quizzOrdersSequenceService.getId().getId());
		int count = quizzOrdersSimpleService.countByConsumerIdAndOperatingActivityId(e.getConsumerId(), e.getOperatingActivityId());
		Assert.assertTrue(count > 0);
	}

	@Test
    public void countByConsumerIdAndOperatingActivityIdAndDateTest() throws Exception{
		QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setStatus(1);
		quizzOrdersSimpleService.insert(e, quizzOrdersSequenceService.getId().getId());
		Calendar calendar = Calendar.getInstance();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	Date start = calendar.getTime();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, 1);
    	Date end = calendar.getTime();
    	int count = quizzOrdersSimpleService.countByConsumerIdAndOperatingActivityIdAndDate(e.getConsumerId(), e.getOperatingActivityId(), start, end);
    	Assert.assertTrue(count > 0);
    }
	
	@Test
    public void countFreeByConsumerIdAndOperatingActivityIdAndDateTest() throws Exception{
		QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setCredits(0L);
		quizzOrdersSimpleService.insert(e, quizzOrdersSequenceService.getId().getId());
		Calendar calendar = Calendar.getInstance();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	Date start = calendar.getTime();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, 1);
    	Date end = calendar.getTime();
    	int count = quizzOrdersSimpleService.countFreeByConsumerIdAndOperatingActivityIdAndDate(e.getConsumerId(), e.getOperatingActivityId(), start, end);
    	Assert.assertTrue(count > 0);
    }

	@Test
    public void countFreeByConsumerIdAndOperatingActivityIdTest(){
		QuizzOrdersDto e = new QuizzOrdersDto(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setCredits(0L);
		quizzOrdersSimpleService.insert(e, quizzOrdersSequenceService.getId().getId());
    	int count = quizzOrdersSimpleService.countFreeByConsumerIdAndOperatingActivityId(e.getConsumerId(), e.getOperatingActivityId());
    	Assert.assertTrue(count > 0);
    }

	@Test
    public void findTest(){
		QuizzOrdersDto test = quizzOrdersSimpleService.find(info.getConsumerId(), info.getId());
		Assert.assertNotNull(test);
    }

	@Test
    public void countByConsumerIdAndPrizeIdTest(){
		int count = quizzOrdersSimpleService.countByConsumerIdAndPrizeId(info.getConsumerId(), info.getOperatingActivityId(), info.getPrizeId());
		Assert.assertTrue(count > 0);
    }

	@Test
    public void findByIdsTest(){
    	List<Long> ids = new ArrayList<Long>();
    	ids.add(info.getId());
    	List<QuizzOrdersDto> list = quizzOrdersSimpleService.findByIds(info.getConsumerId(), ids);
    	Assert.assertTrue(list.size() > 0);
    }
}
