package cn.com.duiba.activity.center.biz.service.guess;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.guess.DuibaGuessSelectionsDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class DuibaGuessSelectionsServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaGuessSelectionsService duibaGuessSelectionsService;
	
	private DuibaGuessSelectionsDto info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaGuessSelectionsDto.class);
		duibaGuessSelectionsService.insert(info);
	}
	
	@Test
	public void findTest(){
		DuibaGuessSelectionsDto e = duibaGuessSelectionsService.find(info.getId());
		Assert.assertNotNull(e);
	}

	@Test
	public void update4AdminTest(){
		DuibaGuessSelectionsDto e = TestUtils.createRandomBean(DuibaGuessSelectionsDto.class);
		duibaGuessSelectionsService.update4Admin(info.getId(), e.getDuibaGuessId(), e.getPosition(), e.getContent());
		String content = duibaGuessSelectionsService.find(info.getId()).getContent();
		Assert.assertTrue(content.equals(e.getContent()));
	}
	
	@Test
	public void findAllByGuessIdTest(){
		DuibaGuessSelectionsDto e = TestUtils.createRandomBean(DuibaGuessSelectionsDto.class);
		e.setDeleted(false);
		duibaGuessSelectionsService.insert(e);
		List<DuibaGuessSelectionsDto> list = duibaGuessSelectionsService.findAllByGuessId(e.getDuibaGuessId());
		Assert.assertTrue(list.size() > 0);
	}
	
	@Test
	public void deleteTest(){
		List<Long> ids = new ArrayList<Long>();
		ids.add(info.getId());
		duibaGuessSelectionsService.delete(ids);
		boolean deleted = duibaGuessSelectionsService.find(info.getId()).getDeleted();
		Assert.assertTrue(deleted);
	}

}
