package cn.com.duiba.activity.center.biz.dao.game;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.game.GameOptionsDuibaEntity;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by hwq on 16/5/26.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class GameOptionsDuibaDaoTest extends TransactionalTestCaseBase {

    @Resource
    private GameOptionsDuibaDao gameOptionsDuibaDao;

    private GameOptionsDuibaEntity entity;

//    @Before
//    public void testAdd(){
//        GameOptionsDuibaEntity e=new GameOptionsDuibaEntity();
//        TestUtils.setRandomAttributesForBean(e,false);
//        e.setDeleted(false);
//        gameOptionsDuibaDao.(e);
//        entity=e;
//    }

    @Test
    public void testFind() {
        gameOptionsDuibaDao.find(1L);
    }

    @Test
    public void testFindByAutoOpen() {
        gameOptionsDuibaDao.findByAutoOpen(1L, true);
    }

    @Test
    public void testFindByGameId() {
        gameOptionsDuibaDao.findByGameId(1L);
    }

    @Test
    public void testFindByGameIds() {
        List<Long> ids = new ArrayList<>();
        ids.add(1L);
        gameOptionsDuibaDao.findByGameIds(ids);
    }

}
