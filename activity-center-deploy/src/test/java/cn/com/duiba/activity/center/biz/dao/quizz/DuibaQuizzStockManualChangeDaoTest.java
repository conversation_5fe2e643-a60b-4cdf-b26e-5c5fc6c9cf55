package cn.com.duiba.activity.center.biz.dao.quizz;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.quizz.DuibaQuizzStockManualChangeEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ)
public class DuibaQuizzStockManualChangeDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaQuizzStockManualChangeDao duibaQuizzStockManualChangeDao;
	
	private DuibaQuizzStockManualChangeEntity info;
	
	@Before
	public void addTest(){
		info = TestUtils.createRandomBean(DuibaQuizzStockManualChangeEntity.class);
		duibaQuizzStockManualChangeDao.add(info);
	}
	
	@Test
	public void findByStockIdTest(){
		List<DuibaQuizzStockManualChangeEntity> list = duibaQuizzStockManualChangeDao.findByStockId(info.getQuizzStockId());
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void addBatchTest(){
		List<DuibaQuizzStockManualChangeEntity> list = new ArrayList<DuibaQuizzStockManualChangeEntity>();
		DuibaQuizzStockManualChangeEntity e1 = TestUtils.createRandomBean(DuibaQuizzStockManualChangeEntity.class);
		list.add(e1);
		duibaQuizzStockManualChangeDao.addBatch(list);
		List<DuibaQuizzStockManualChangeEntity> entityList = duibaQuizzStockManualChangeDao.findByStockId(e1.getQuizzStockId());
		Assert.assertTrue(entityList.size() > 0);
	}

    
}
