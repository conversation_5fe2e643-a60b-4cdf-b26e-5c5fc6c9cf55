package cn.com.duiba.activity.center.api.remoteservice.game;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionAnswerOptionsDto;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.service.exception.BusinessException;
import org.junit.Test;
import org.springframework.test.annotation.Rollback;

import javax.annotation.Resource;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/2/19 14:43
 * @description:
 */
@Rollback(value = false)
public class RemoteDuibaQuestionStockServiceTest extends TransactionalTestCaseBase {

	@Resource
	private RemoteDuibaQuestionStockService remoteDuibaQuestionStockService;

	@Test
	public void testConsumeStock() throws BusinessException{
		remoteDuibaQuestionStockService.consumeStock(1156L, "110");
	}

	@Test
	public void testConsumeStockApi() throws BizException{
		remoteDuibaQuestionStockService.consumeStockApi(1156L, "111");
	}

	@Test
	public void testPaybackStock() throws BusinessException{
		remoteDuibaQuestionStockService.paybackStock("111");
	}

	@Test
	public void testPaybackStockApi() throws BizException{
		remoteDuibaQuestionStockService.paybackStockApi("110");
	}

	@Test
	public void testUpdateStockByOptions() throws BusinessException{
		DuibaQuestionAnswerOptionsDto duibaQuestionAnswerOptionsDto = new DuibaQuestionAnswerOptionsDto();
		duibaQuestionAnswerOptionsDto.setId(1000L);
		duibaQuestionAnswerOptionsDto.setNewOptionCount(5);
		remoteDuibaQuestionStockService.updateStockByOptions(duibaQuestionAnswerOptionsDto, new DuibaQuestionAnswerOptionsDto());
	}

	@Test
	public void testUpdateStockByOptionsApi() throws BizException{
		DuibaQuestionAnswerOptionsDto duibaQuestionAnswerOptionsDto = new DuibaQuestionAnswerOptionsDto();
		duibaQuestionAnswerOptionsDto.setId(1001L);
		duibaQuestionAnswerOptionsDto.setNewOptionCount(1);
		remoteDuibaQuestionStockService.updateStockByOptionsApi(duibaQuestionAnswerOptionsDto, new DuibaQuestionAnswerOptionsDto());
	}
}
