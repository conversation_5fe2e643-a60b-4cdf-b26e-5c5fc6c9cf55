package cn.com.duiba.activity.center.biz.dao.guess;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.AddActivityEntity;
import cn.com.duiba.activity.center.biz.entity.guess.DuibaGuessEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class DuibaGuessDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaGuessDao duibaGuessDao;
	
	private DuibaGuessEntity info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaGuessEntity.class);
		info.setLimitScope(10);
		info.setFreeScope(10);
		info.setIsAccurate(10);
		duibaGuessDao.insert(info);
	}
	
	@Test
	public void findTest(){
		DuibaGuessEntity e = duibaGuessDao.find(info.getId());
		Assert.assertNotNull(e);
	}
	
	@Test
	public void findByPageTest(){
		DuibaGuessEntity e = TestUtils.createRandomBean(DuibaGuessEntity.class);
		e.setDeleted(false);
		e.setLimitScope(10);
		e.setFreeScope(10);
		e.setIsAccurate(10);
		duibaGuessDao.insert(e);
		List<DuibaGuessEntity> list = duibaGuessDao.findByPage(0, 10, e.getTitle(), Integer.parseInt(e.getId() + ""));
		Assert.assertTrue(list.size() > 0);
	}
	
	@Test
	public void findPageCountTest(){
		DuibaGuessEntity e = TestUtils.createRandomBean(DuibaGuessEntity.class);
		e.setDeleted(false);
		e.setLimitScope(10);
		e.setFreeScope(10);
		e.setIsAccurate(10);
		duibaGuessDao.insert(e);
		long num = duibaGuessDao.findPageCount(e.getTitle(), Integer.parseInt(e.getId() + ""));
		Assert.assertTrue(num > 0);
	}
	
	@Test
	public void updateStatusTest(){
		DuibaGuessEntity e = TestUtils.createRandomBean(DuibaGuessEntity.class);
		duibaGuessDao.updateStatus(info.getId(), e.getStatus());
		int status = duibaGuessDao.find(info.getId()).getStatus();
		Assert.assertTrue(status == e.getStatus());
	}
	
	@Test
	public void deleteTest(){
		duibaGuessDao.delete(info.getId());
		boolean deleted = duibaGuessDao.find(info.getId()).getDeleted();
		Assert.assertTrue(deleted);
	}
	
	@Test
	public void updateInfoFormTest(){
		DuibaGuessEntity e = TestUtils.createRandomBean(DuibaGuessEntity.class);
		e.setId(info.getId());
		e.setLimitScope(10);
		e.setFreeScope(10);
		e.setIsAccurate(10);
		duibaGuessDao.updateInfoForm(e);
		String logo = duibaGuessDao.find(info.getId()).getLogo();
		Assert.assertTrue(logo.equals(e.getLogo()));
	}
	
	@Test
	public void updateAutoOffDateNullTest(){
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		DuibaGuessEntity e = TestUtils.createRandomBean(DuibaGuessEntity.class);
		duibaGuessDao.updateAutoOffDateNull(e.getAutoOffDate(), info.getId());
		Date autoOffDate = duibaGuessDao.find(info.getId()).getAutoOffDate();
		Assert.assertTrue(format.format(autoOffDate).equals(format.format(e.getAutoOffDate())));
	}
	
	@Test
	public void updateSwitchesTest(){
		DuibaGuessEntity e = TestUtils.createRandomBean(DuibaGuessEntity.class);
		duibaGuessDao.updateSwitches(info.getId(), e.getSwitches());
		int switches = duibaGuessDao.find(info.getId()).getSwitches();
		Assert.assertTrue(switches == e.getSwitches());
	}
	
	/**
	 * 修改开奖后的主表信息
	 * updateOpenWinning:(这里用一句话描述这个方法的作用). <br/>
	 *
	 * <AUTHOR>
	 * @param id
	 * @since JDK 1.6
	 */
	@Test
	public void updateOpenWinningTest(){
		DuibaGuessEntity e = TestUtils.createRandomBean(DuibaGuessEntity.class);
		e.setLimitScope(10);
		e.setFreeScope(10);
		e.setIsAccurate(10);
		duibaGuessDao.updateOpenWinning(info.getId(), e.getLuckNum(), e.getRightSelectionId(), e.getIsAccurate());
		String luckNum = duibaGuessDao.find(info.getId()).getLuckNum();
		Assert.assertTrue(luckNum.equals(e.getLuckNum()));
	}

	@Test
	public void findAllByIdsTest(){
		List<Long> ids = new ArrayList<Long>();
		ids.add(info.getId());
		List<DuibaGuessEntity> list = duibaGuessDao.findAllByIds(ids);
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void findAllGuessTest(){
		DuibaGuessEntity e = TestUtils.createRandomBean(DuibaGuessEntity.class);
		e.setDeleted(false);
		e.setStatus(1);
		e.setLimitScope(10);
		e.setFreeScope(10);
		e.setIsAccurate(10);
		duibaGuessDao.insert(e);
		List<AddActivityEntity> list = duibaGuessDao.findAllGuess(e.getId());
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void findAutoOffTest(){
		DuibaGuessEntity e = TestUtils.createRandomBean(DuibaGuessEntity.class);
		e.setDeleted(false);
		e.setStatus(1);
		Calendar calendar = Calendar.getInstance();
    	calendar.setTime(e.getAutoOffDate());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	Date start = calendar.getTime();
    	e.setAutoOffDate(start);
    	e.setLimitScope(10);
		e.setFreeScope(10);
		e.setIsAccurate(10);
		duibaGuessDao.insert(e);
		List<DuibaGuessEntity> list = duibaGuessDao.findAutoOff();
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void updateOpenPrizeTest(){
		DuibaGuessEntity e = TestUtils.createRandomBean(DuibaGuessEntity.class);
		e.setIsOpenPrize(false);
		e.setStatus(2);
		e.setLimitScope(10);
		e.setFreeScope(10);
		e.setIsAccurate(10);
		duibaGuessDao.insert(e);
		duibaGuessDao.updateOpenPrize(e.getId());
		boolean isOpenPrize = duibaGuessDao.find(e.getId()).getIsOpenPrize();
		Assert.assertTrue(isOpenPrize);
	}

	@Test
	public void testFindTagById(){
		String tag = duibaGuessDao.findTagById(info.getId());
		Assert.assertEquals(tag, info.getTag());
	}

	@Test
	public void testUpdateTagById(){
		String tag = "afsd";
		duibaGuessDao.updateTagById(info.getId(),tag);
		String tag1 = duibaGuessDao.findTagById(info.getId());
		Assert.assertEquals(tag, tag1);
	}
}
