package cn.com.duiba.activity.center.api.remoteservice.creditsfarm;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmCropDetailDto;
import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmNutritionRecordDto;
import cn.com.duiba.api.enums.AccountActionTypeEnum;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * created by liugq in 2019/08/16
 **/
public class RemoteCreditsFarmNutritionServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteCreditsFarmNutritionRecordService remoteCreditsFarmNutritionService;
    @Autowired
    private RemoteCreditsFarmCropDetailService remoteCreditsFarmCropDetailService;

    @Test
    public void testUse(){
        CreditsFarmNutritionRecordDto nutritionRecordDto = new CreditsFarmNutritionRecordDto();
        nutritionRecordDto.setAppId(1L);
        nutritionRecordDto.setConsumerId(2000012L);
        nutritionRecordDto.setPartnerUserId("5");
        nutritionRecordDto.setActionType(AccountActionTypeEnum.ACTION_OUT.getCode());
        nutritionRecordDto.setActId(28L);
        nutritionRecordDto.setActionDesc("消耗");
        nutritionRecordDto.setChangeAmount(1);
        CreditsFarmCropDetailDto cropDetailDto = remoteCreditsFarmCropDetailService.findById(206L);
        remoteCreditsFarmNutritionService.useNutrition(cropDetailDto,nutritionRecordDto);
    }

    @Test
    public void testAdd(){
        for (int i = 0 ;i< 100; i++) {
            CreditsFarmNutritionRecordDto nutritionRecordDto = new CreditsFarmNutritionRecordDto();
            nutritionRecordDto.setActId(22L);
            nutritionRecordDto.setConsumerId(1539361l);
            nutritionRecordDto.setPartnerUserId("1");
            nutritionRecordDto.setAppId(1L);
            nutritionRecordDto.setChangeAmount(3);
            nutritionRecordDto.setActionType(AccountActionTypeEnum.ACTION_IN.getCode());
            nutritionRecordDto.setTaskType(1);
            nutritionRecordDto.setActionDesc("DANCE");
            remoteCreditsFarmNutritionService.addNutrition(nutritionRecordDto);
        }
    }

   }

