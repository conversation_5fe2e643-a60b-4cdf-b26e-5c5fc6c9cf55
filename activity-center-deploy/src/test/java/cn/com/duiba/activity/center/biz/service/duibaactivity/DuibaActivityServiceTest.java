package cn.com.duiba.activity.center.biz.service.duibaactivity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.duibaactivity.DuibaActivityDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/30.
 */
@Transactional(DuibaActivityServiceTest.THIS_DATABASE_SCHEMA)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaActivityServiceTest extends TransactionalTestCaseBase {
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS;

    @Resource
    private DuibaActivityService duibaActivityService;

    private DuibaActivityDto duibaActivityDO;

    @Before
    public void testInsert() {
        DuibaActivityDto e=new DuibaActivityDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        duibaActivityService.insert(e);
        duibaActivityDO=e;
    }

    @Test
    public void testFind() {
        DuibaActivityDto e=duibaActivityDO;
        DuibaActivityDto e1=duibaActivityService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testUpdate() {
        DuibaActivityDto e=new DuibaActivityDto(duibaActivityDO.getId());
        TestUtils.setRandomAttributesForBean(e,false);
        duibaActivityService.update(e);
        DuibaActivityDto e1=duibaActivityService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindPage() {
        Map<String,Object> params=new HashMap<>();
        params.put("max",10);
        params.put("offset",0);
        params.put("id",duibaActivityDO.getId());
        Assert.assertTrue(duibaActivityService.findPage(params).size()>0);
    }

    @Test
    public void testFindDuibaActivity() {
        Map<String,Object> params=new HashMap<>();
        params.put("max",10);
        params.put("offset",0);
        params.put("activityId",duibaActivityDO.getId());
        Assert.assertTrue(duibaActivityService.findDuibaActivity(params).size()>0);
    }

    @Test
    public void testUpdateAutoOffDateNull() {
        duibaActivityService.updateAutoOffDateNull(duibaActivityDO.getId());
        Assert.assertNull(duibaActivityService.find(duibaActivityDO.getId()).getAutoOffDate());
    }

    @Test
    public void testFindPageCount() {
        Map<String,Object> params=new HashMap<>();
        params.put("max",10);
        params.put("offset",0);
        params.put("activityId",duibaActivityDO.getId());
        Assert.assertTrue(duibaActivityService.findPageCount(params)>0);
    }

    @Test
    public void testGetCountDuibaActivity() {
        Map<String,Object> params=new HashMap<>();
        params.put("max",10);
        params.put("offset",0);
        params.put("activityId",duibaActivityDO.getId());
        Assert.assertTrue(duibaActivityService.getCountDuibaActivity(params)>0);
    }

    @Test
    public void testFindAllDuibaActivity() {
        Assert.assertTrue(duibaActivityService.findAllDuibaActivity(duibaActivityDO.getId()).size()>0);
    }

    @Test
    public void testFindAllByIds() {
        Assert.assertTrue(duibaActivityService.findAllByIds(Arrays.asList(duibaActivityDO.getId())).size()>0);
    }

    @Test
    public void testfindAutoOff() {
        DuibaActivityDto e=new DuibaActivityDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setAutoOffDate(new Date(System.currentTimeMillis()-10000l));
        e.setStatus(1);
        duibaActivityService.insert(e);
        Assert.assertTrue(duibaActivityService.findAutoOff().size()>0);
    }

    private void assertDO(DuibaActivityDto e, DuibaActivityDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaActivityDto e, DuibaActivityDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }

}
