package cn.com.duiba.activity.center.api.remoteservice.shuqi;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.BizResultDto;
import cn.com.duiba.activity.center.api.dto.shuqipk.PkTeamInfoDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.api.bo.page.PageQuery;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: zhen<PERSON>jian<PERSON>
 * @date: 18/7/17 21:31
 * @description:
 */
@Transactional(DsConstants.DATABASE_CUSTOM_RECORD)
@Rollback(value = false)
public class RemotePkTeamInfoServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemotePkTeamInfoService remotePkTeamInfoService;

    @Test
    public void testInsert() throws Exception{
        PkTeamInfoDto pkTeamInfoDto = new PkTeamInfoDto();
        pkTeamInfoDto.setAppId(1L);
        pkTeamInfoDto.setActivityId(12431L);
        pkTeamInfoDto.setMemberNum(1);
//        int i = 0;
//        while (i < 100) {
            pkTeamInfoDto.setTeamName("zjh测试31");
//            i++;
//            Thread.sleep(1000L);
            remotePkTeamInfoService.insert(pkTeamInfoDto, 125L);
//        }
    }

    @Test
    public void testTeamNameExist(){
        String name = "单元测试name1";
        Boolean result = remotePkTeamInfoService.teamNameExist(name);
        Assert.isTrue(result, "名称不存在");
    }

    @Test
    public void testBatchUpdateReadValue(){
        List<PkTeamInfoDto> list = new ArrayList<>();
        PkTeamInfoDto pkTeamInfoDto = new PkTeamInfoDto();
        pkTeamInfoDto.setId(2L);
        pkTeamInfoDto.setTotalReadValue(1L);
        list.add(pkTeamInfoDto);

        PkTeamInfoDto pkTeamInfoDto2 = new PkTeamInfoDto();
        pkTeamInfoDto2.setId(3L);
        pkTeamInfoDto2.setTotalReadValue(2L);
        list.add(pkTeamInfoDto2);

        BizResultDto bizResultDto = remotePkTeamInfoService.batchUpdateReadValue(list);
        Assert.isTrue(bizResultDto.isSuccess(), "更新失败");
    }

    @Test
    public void testGetById(){
        PkTeamInfoDto pkTeamInfoDto = remotePkTeamInfoService.getById(2L);
        System.out.println(JSON.toJSON(pkTeamInfoDto));
        Assert.notNull(pkTeamInfoDto, "空");
    }

    @Test
    public void testGetTeamPage(){
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNo(1);
        pageQuery.setPageSize(10);
        List<PkTeamInfoDto> list = remotePkTeamInfoService.getTeamPage(pageQuery);
        System.out.println(JSON.toJSON(list));
        Assert.notEmpty(list, "空");
    }

    @Test
    public void testGetListByIds(){
        List<Long > teamIdList = Arrays.asList(1L,2L);
        List<PkTeamInfoDto> pkTeamInfoDtoList = remotePkTeamInfoService.getListByIds(teamIdList);
        System.out.println(JSON.toJSON(pkTeamInfoDtoList));
        Assert.notEmpty(pkTeamInfoDtoList, "空");
    }
}

