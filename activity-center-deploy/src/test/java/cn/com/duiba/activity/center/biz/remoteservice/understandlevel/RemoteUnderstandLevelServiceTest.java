package cn.com.duiba.activity.center.biz.remoteservice.understandlevel;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.understandlevel.UnderstandDegreeDto;
import cn.com.duiba.activity.center.api.dto.understandlevel.UnderstandLevelAssistRecordDto;
import cn.com.duiba.activity.center.api.dto.understandlevel.UnderstandLevelConfigDto;
import cn.com.duiba.activity.center.api.dto.understandlevel.UnderstandOptionDto;
import cn.com.duiba.activity.center.api.dto.understandlevel.UnderstandPrizeDto;
import cn.com.duiba.activity.center.api.remoteservice.understandlevel.RemoteUnderstandLevelService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.understandlevel.UnderstandLevelPrizeDao;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-03-12
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
@Rollback(false)
public class RemoteUnderstandLevelServiceTest extends TransactionalTestCaseBase {


    @Autowired
    private RemoteUnderstandLevelService remoteUnderstandLevelService;
    @Autowired
    private UnderstandLevelPrizeDao understandLevelPrizeDao;


    @Test
    public void saveOrUpdateAllConfigTest() {
        UnderstandLevelConfigDto req = new UnderstandLevelConfigDto();
        req.setId(5L);
        req.setOpId(1L);
        req.setAppId(1L);
        req.setActTitle("活动标题更新");
        req.setActRule("活动规则");
        req.setEndTime(new Date());
        req.setBannerImage("入口图地址");
        req.setSmallImage("缩略图地址");
        req.setActCredits(5);

        req.setQuestionTitle("题目文案");
        req.setQuestionAssistTitle("分享好友题目文案");

        UnderstandOptionDto op1 = new UnderstandOptionDto();
        op1.setId(null);
        op1.setOptionIndex(1);
        op1.setOptionName("选项名称1更新");
        op1.setOptionImage("选项地址1");

        UnderstandOptionDto op2 = new UnderstandOptionDto();
        op2.setId(null);
        op2.setOptionIndex(2);
        op2.setOptionName("选项名称2");
        op2.setOptionImage("选项地址2");

        UnderstandOptionDto op3 = new UnderstandOptionDto();
        op3.setId(null);
        op3.setOptionIndex(3);
        op3.setOptionName("选项名称3");
        op3.setOptionImage("选项地址3");

        List<UnderstandOptionDto> optionVOList = Lists.newArrayList(op1, op2, op3);
        req.setOptionList(optionVOList);

        req.setSelectNum(2);

        UnderstandDegreeDto co1 = new UnderstandDegreeDto();
        co1.setGuessNum(1);
        co1.setDegree(33);
        co1.setTextList(Lists.newArrayList("一般默契", "一般般默契"));

        UnderstandDegreeDto co2 = new UnderstandDegreeDto();
        co2.setGuessNum(2);
        co2.setDegree(100);
        co2.setTextList(Lists.newArrayList("很默契", "非常默契"));

        List<UnderstandDegreeDto> configVOList = Lists.newArrayList(co1, co2);
        req.setDegreeList(configVOList);
        req.setStartButtonText("开始按钮文案");
        req.setLogoSwitch(1);
        req.setLogoImage("企业logo图地址");
        req.setInterfaceConfig("界面配置");
        req.setShareLinkImage("分享图标地址");
        req.setShareLinkTitle("分享标题");
        req.setShareLinkSubTitle("分享副标题");
        req.setSharePosterSwitch(1);
        req.setSharePosterTitle("海报标题");
        req.setSharePosterImage("海报背景");

        UnderstandPrizeDto prize1 = new UnderstandPrizeDto();
        prize1.setId(null);
        prize1.setAppItemId(123455L);
        prize1.setPrizeType("virtual");
        prize1.setPrizeName("商品名称更新");
        prize1.setPrizeImage("商品图片地址");
        prize1.setPrizeTitle("奖品信息");
        prize1.setPrizeValue("20");
        prize1.setPrizeStock(100);
        prize1.setPrizeRate(22);
        prize1.setPrizeQualifyNum(3);
        prize1.setHasLimit(1);
        prize1.setLimitNum(2);
        prize1.setShowSwitch(1);

        req.setPrizeList(Lists.newArrayList(prize1));

        remoteUnderstandLevelService.saveOrUpdateAllConfig(req);
    }


    @Test
    public void assistTest() {
        UnderstandLevelAssistRecordDto updateOne = new UnderstandLevelAssistRecordDto();
        updateOne.setId(1L);
        updateOne.setExchangeStatus(2);
        remoteUnderstandLevelService.updateAssistRecordById(updateOne);
    }


    @Test
    public void prizeTest() {
        understandLevelPrizeDao.deleteNoUse(1L, Lists.newArrayList(1L, 2L));
    }

}
