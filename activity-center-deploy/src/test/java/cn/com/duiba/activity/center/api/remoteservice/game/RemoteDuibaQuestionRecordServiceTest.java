package cn.com.duiba.activity.center.api.remoteservice.game;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionRecordDto;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.service.exception.BusinessException;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.test.annotation.Rollback;

import javax.annotation.Resource;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/2/19 14:43
 * @description:
 */
@Rollback(value = false)
public class RemoteDuibaQuestionRecordServiceTest extends TransactionalTestCaseBase {

	@Resource
	private RemoteDuibaQuestionRecordService remoteDuibaQuestionRecordService;

	@Test
	public void testInsert_manager() throws BusinessException{
		DuibaQuestionRecordDto duibaQuestionRecordDto = new DuibaQuestionRecordDto();
		duibaQuestionRecordDto.setBankId(200L);
		duibaQuestionRecordDto.setName("测试111");
		duibaQuestionRecordDto.setRightAnswer(2);
		duibaQuestionRecordDto.setOption1("A");
		duibaQuestionRecordDto.setOption2("B");
		duibaQuestionRecordDto.setOption3("C");
		duibaQuestionRecordDto.setOption4("D");
		duibaQuestionRecordDto.setType("text");
		DuibaQuestionRecordDto result = remoteDuibaQuestionRecordService.insert_manager(duibaQuestionRecordDto);
		System.out.println(JSON.toJSON(result));
		Assert.assertNotNull(result);
	}

	@Test
	public void testInsert_managerApi() throws BizException{
		DuibaQuestionRecordDto duibaQuestionRecordDto = new DuibaQuestionRecordDto();
		duibaQuestionRecordDto.setBankId(200L);
		duibaQuestionRecordDto.setName("测试112");
		duibaQuestionRecordDto.setRightAnswer(2);
		duibaQuestionRecordDto.setOption1("A");
		duibaQuestionRecordDto.setOption2("B");
		duibaQuestionRecordDto.setOption3("C");
		duibaQuestionRecordDto.setOption4("D");
		duibaQuestionRecordDto.setType("text");
		DuibaQuestionRecordDto result = remoteDuibaQuestionRecordService.insertManagerApi(duibaQuestionRecordDto);
		System.out.println(JSON.toJSON(result));
		Assert.assertNotNull(result);
	}

	@Test
	public void testDelete_manager() throws BusinessException{
		remoteDuibaQuestionRecordService.delete_manager(241L);
	}

	@Test
	public void testDelete_managerApi() throws BizException{
		remoteDuibaQuestionRecordService.deleteManagerApi(240L);
	}
}
