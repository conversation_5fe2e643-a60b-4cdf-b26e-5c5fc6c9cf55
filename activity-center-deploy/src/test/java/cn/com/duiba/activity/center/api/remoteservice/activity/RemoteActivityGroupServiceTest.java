package cn.com.duiba.activity.center.api.remoteservice.activity;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.ActivityGroupIntegrityDto;
import cn.com.duiba.activity.center.api.dto.ActivityKeyDto;
import cn.com.duiba.activity.center.api.dto.activity.ActivityGroupDto;
import cn.com.duiba.activity.center.api.dto.activity.ActivityGroupRelationDto;
import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.params.ActivityGroupParam;
import cn.com.duiba.activity.center.api.params.PageParams;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.hdtool.DuibaHdtoolDao;
import cn.com.duiba.activity.center.biz.entity.hdtool.DuibaHdtoolEntity;
import cn.com.duiba.activity.center.biz.entity.littlegame.LittleGameEntity;
import cn.com.duiba.activity.center.biz.service.littlegame.LittleGameService;
import cn.com.duiba.wolf.dubbo.DubboResult;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by xiaoxuda on 2017/2/9.
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
@Rollback(value= false)
public class RemoteActivityGroupServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteActivityGroupService remoteActivityGroupService;
    @Resource
    private DuibaHdtoolDao duibaHdtoolDao;
    @Autowired
    private LittleGameService littleGameService;

    public List<ActivityGroupRelationDto> prepareActivityData() {
        List<ActivityGroupRelationDto> list = Lists.newArrayList();
        DuibaHdtoolEntity duibaHdtoolEntity = duibaHdtoolDao.find(6202L);
        duibaHdtoolEntity.setId(null);
        duibaHdtoolEntity.setType(OperatingActivityDto.hdToolTypeSet.asList().get(0));
        duibaHdtoolDao.insert(duibaHdtoolEntity);
        ActivityGroupRelationDto relationDto = new ActivityGroupRelationDto();
        relationDto.setActivityType(duibaHdtoolEntity.getType());
        relationDto.setActivityId(duibaHdtoolEntity.getId());
        relationDto.setActivityTitle(duibaHdtoolEntity.getTitle());
        list.add(relationDto);

        duibaHdtoolEntity.setId(null);
        duibaHdtoolEntity.setType(OperatingActivityDto.hdToolTypeSet.asList().get(1));
        duibaHdtoolDao.insert(duibaHdtoolEntity);
        relationDto = new ActivityGroupRelationDto();
        relationDto.setActivityType(duibaHdtoolEntity.getType());
        relationDto.setActivityId(duibaHdtoolEntity.getId());
        relationDto.setActivityTitle(duibaHdtoolEntity.getTitle());
        list.add(relationDto);

        duibaHdtoolEntity.setId(null);
        duibaHdtoolEntity.setType(OperatingActivityDto.hdToolTypeSet.asList().get(2));
        duibaHdtoolDao.insert(duibaHdtoolEntity);
        relationDto = new ActivityGroupRelationDto();
        relationDto.setActivityType(duibaHdtoolEntity.getType());
        relationDto.setActivityId(duibaHdtoolEntity.getId());
        relationDto.setActivityTitle(duibaHdtoolEntity.getTitle());
        list.add(relationDto);

        LittleGameEntity entity = littleGameService.selectById(1L);
        entity.setId(null);
        littleGameService.insert(entity);
        relationDto = new ActivityGroupRelationDto();
        relationDto.setActivityType(OperatingActivityDto.TypeDuibaLittleGame);
        relationDto.setActivityId(entity.getId());
        relationDto.setActivityTitle(entity.getLittleGameTitle());
        list.add(relationDto);

        return list;
    }

    public Long prepareData() {
        DuibaHdtoolEntity duibaHdtoolEntity = duibaHdtoolDao.find(6202L);
        ActivityGroupDto activityGroupDto = new ActivityGroupDto();
        activityGroupDto.setTitle("测试活动组");
        ActivityGroupRelationDto activityGroupRelationDto = new ActivityGroupRelationDto();
        activityGroupRelationDto.setActivityType(duibaHdtoolEntity.getType());
        activityGroupRelationDto.setActivityId(duibaHdtoolEntity.getId());
        List<ActivityGroupRelationDto> list = Lists.newArrayList();
        list.add(activityGroupRelationDto);
        ActivityGroupIntegrityDto activityGroupIntegrityDto = new ActivityGroupIntegrityDto(activityGroupDto, list);
        return remoteActivityGroupService.addActivityGroup(activityGroupIntegrityDto).getResult();

    }

    @Test
    public void test() {
        List<ActivityGroupRelationDto> activityGroupRelationDtos = prepareActivityData();
        Long groupId = prepareData();

        PageParams pageParams = new PageParams();
        DubboResult<List<ActivityGroupDto>> result = remoteActivityGroupService.findActivityGroupByPage(pageParams);
        Assert.assertNotNull(result.getResult());
        Assert.assertFalse(result.getResult().isEmpty());

        DubboResult<ActivityGroupIntegrityDto> result1 = remoteActivityGroupService.findById(groupId);
        Assert.assertNotNull(result1.getResult());
        Assert.assertNotNull(result1.getResult().getActivityGroupRelationDtoList());

        ActivityGroupRelationDto relationDto1 = result1.getResult().getActivityGroupRelationDtoList().get(0);
        DubboResult<ActivityGroupRelationDto> result2 = remoteActivityGroupService.findByGroupIdAndTypeAndActivityId(
                result1.getResult().getActivityGroupDto().getId(),
                relationDto1.getActivityType(), relationDto1.getActivityId());
        Assert.assertNotNull(result2.getResult());

        DubboResult<Integer> result3 = remoteActivityGroupService.countGroup();
        Assert.assertTrue(result3.getResult()>0);

        ActivityGroupIntegrityDto activityGroupIntegrityDto = result1.getResult();
        List<ActivityGroupRelationDto> list = activityGroupIntegrityDto.getActivityGroupRelationDtoList();
        list = list == null ? new ArrayList<ActivityGroupRelationDto>() : list;
        activityGroupIntegrityDto.setActivityGroupRelationDtoList(list);
        list.add(activityGroupRelationDtos.get(0));
        list.add(activityGroupRelationDtos.get(1));
        list.add(activityGroupRelationDtos.get(3));
        DubboResult<Integer> result4 = remoteActivityGroupService.updateById(activityGroupIntegrityDto);
        Assert.assertEquals(result4.getResult(),new Integer(1));

        DubboResult<List<ActivityGroupRelationDto>> result5 = remoteActivityGroupService.
                canInsertOrUpdateToRelation(groupId,activityGroupRelationDtos);
        Assert.assertFalse(result5.isSuccess());

        activityGroupIntegrityDto.setActivityGroupRelationDtoList(list.subList(0,1));
        remoteActivityGroupService.updateById(activityGroupIntegrityDto);
        Assert.assertEquals(result4.getResult(),new Integer(1));

        DubboResult<Integer> result6 = remoteActivityGroupService.deleteById(groupId);
        Assert.assertEquals(result6.getResult(),new Integer(1));
    }

    @Test
    public void findActivityKeyDtosByGroupIdTest(){
        ActivityGroupParam param = new ActivityGroupParam();
        param.setAppId(1L);
        param.setGroupId(59L);
        param.setDeveloperId(1L);
        DubboResult<List<ActivityKeyDto>> dubboResult = remoteActivityGroupService.
                findActivityKeyDtosByGroupId(param);
        Assert.assertTrue(dubboResult.isSuccess());
    }

    @Test
    public void findActiveActivityOrderByIdDescWithLimitTest(){
        DubboResult<List<ActivityKeyDto>> ret = remoteActivityGroupService.findActiveActivityOrderByIdDescWithLimit(1L,20);
        Assert.assertTrue(ret.getResult().size()>0);
    }
}
