package cn.com.duiba.activity.center.biz.service.duibaactivity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.duibaactivity.DuibaActivityAppSpecifyDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/23.
 */
@Transactional(DuibaActivityAppSpecifyServiceTest.THIS_DATABASE_SCHEMA)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaActivityAppSpecifyServiceTest  extends TransactionalTestCaseBase {
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS;

    @Resource
    private DuibaActivityAppSpecifyService duibaActivityAppSpecifyService;

    private DuibaActivityAppSpecifyDto dto;

    @Before
    public void testInsert() {
        DuibaActivityAppSpecifyDto e=new DuibaActivityAppSpecifyDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        duibaActivityAppSpecifyService.insert(e);
        dto = e;
    }

    @Test
    public void testFind() {
        DuibaActivityAppSpecifyDto e1=duibaActivityAppSpecifyService.find(dto.getId());
        assertDO(e1,dto);
    }

    @Test
    public void testFindDuiBaActivitySpecifyDO() {
        Assert.assertTrue(duibaActivityAppSpecifyService.findDuiBaActivitySpecifyDO(dto.getDuibaActivityId()).size()>0);
    }

    @Test
    public void testDelete() {
        duibaActivityAppSpecifyService.delete(dto.getId());
        Assert.assertNull(duibaActivityAppSpecifyService.find(dto.getId()));
    }

    @Test
    public void testFindByDuibaActivityAndApp() {
        Assert.assertNotNull(duibaActivityAppSpecifyService.findByDuibaActivityAndApp(dto.getDuibaActivityId(),dto.getAppId()));
        duibaActivityAppSpecifyService.delete(dto.getId());
        Assert.assertNull(duibaActivityAppSpecifyService.findByDuibaActivityAndApp(dto.getDuibaActivityId(),dto.getAppId()));
    }

    @Test
    public void testFindByDuibaActivitysAndApp() {
        Assert.assertTrue(!duibaActivityAppSpecifyService.findByDuibaActivitysAndApp(Collections.singletonList(dto.getDuibaActivityId()),dto.getAppId()).isEmpty());
        duibaActivityAppSpecifyService.delete(dto.getId());
        Assert.assertNull(duibaActivityAppSpecifyService.findByDuibaActivityAndApp(dto.getDuibaActivityId(),dto.getAppId()));
    }

    private void assertDO(DuibaActivityAppSpecifyDto e, DuibaActivityAppSpecifyDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaActivityAppSpecifyDto e, DuibaActivityAppSpecifyDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }
}
