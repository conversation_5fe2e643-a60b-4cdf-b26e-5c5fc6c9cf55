package cn.com.duiba.activity.center.biz.service.game;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.ActivityExtraInfoEntity;
import cn.com.duiba.activity.center.biz.entity.AddActivityEntity;
import cn.com.duiba.activity.center.biz.entity.game.GameConfigDuibaEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created by yansen on 16/5/23.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class GameConfigDuibaServiceTest extends TransactionalTestCaseBase {

    @Resource
    private GameConfigDuibaService gameConfigDuibaService;

    private GameConfigDuibaEntity gameConfigDuibaDO;


    @Before
    public void testAdd() {
        GameConfigDuibaEntity e=new GameConfigDuibaEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        gameConfigDuibaService.add(e);
        gameConfigDuibaDO=e;
    }

    @Test
    public void testFind() {
        GameConfigDuibaEntity e=gameConfigDuibaDO;
        GameConfigDuibaEntity e1=gameConfigDuibaService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testUpdateOpenPrize() {
        GameConfigDuibaEntity e=new GameConfigDuibaEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        gameConfigDuibaService.add(e);
        gameConfigDuibaService.updateOpenPrize(e.getId());
        GameConfigDuibaEntity e1=gameConfigDuibaService.find(e.getId());

    }

    @Test
    public void testFindAllGame() {
        GameConfigDuibaEntity e=new GameConfigDuibaEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        e.setStatus(2);
        gameConfigDuibaService.add(e);
        List<AddActivityEntity> list=gameConfigDuibaService.findAllGame(null);//实际查询中没用到appid
        boolean isfind=false;
        for(AddActivityEntity a:list){
            if(a.getId().equals(e.getId())){
                isfind=true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindAutoOff() {
        GameConfigDuibaEntity e = new GameConfigDuibaEntity(true);
        TestUtils.setRandomAttributesForBean(e, false);
        e.setDeleted(false);
        e.setStatus(1);
        e.setAutoOffDate(new Date(System.currentTimeMillis() - 100000l));
        gameConfigDuibaService.add(e);
        List<GameConfigDuibaEntity> list = gameConfigDuibaService.findAutoOff();
        boolean isfind = false;
        for (GameConfigDuibaEntity g : list) {
            if (g.getId().equals(e.getId())) {
                assertDO(e, g);
                isfind = true;
                break;
            }
        }
        Assert.assertTrue(isfind);
    }


    @Test
    public void testUpdateStatus() {
        GameConfigDuibaEntity e = gameConfigDuibaDO;
        gameConfigDuibaService.updateStatus(e.getId(),42);
        GameConfigDuibaEntity e1=gameConfigDuibaService.find(e.getId());
        Assert.assertEquals((int)e1.getStatus(),42);
    }

    @Test
    public void testFindByIds() {
        GameConfigDuibaEntity e = gameConfigDuibaDO;
        List<GameConfigDuibaEntity> list=gameConfigDuibaService.findByIds(Arrays.asList(e.getId()));
        assertDO(e,list.get(0));
    }

    @Test
    public void testFindExtraInfoById() {
        GameConfigDuibaEntity e = gameConfigDuibaDO;
        ActivityExtraInfoEntity vo=gameConfigDuibaService.findExtraInfoById(e.getId());
        Assert.assertEquals(e.getFreeRule(),vo.getFreeRule());
    }

    @Test
    public void testFindByPage() {
        GameConfigDuibaEntity e = gameConfigDuibaDO;
        Map<String,Object> params=new HashMap<>();
        params.put("offset",0);
        params.put("max",20);
        params.put("activityName",e.getTitle());
        List<GameConfigDuibaEntity> list=gameConfigDuibaService.findByPage(params);
        boolean isfind=false;
        for(GameConfigDuibaEntity g:list){
            if(g.getId().equals(e.getId())){
                assertDO(e,g);
                isfind=true;
            }
        }
        Assert.assertTrue(isfind);
    }

    @Test
    public void testFindOpenPrizeForUpdate() {
        GameConfigDuibaEntity e = new GameConfigDuibaEntity(true);
        TestUtils.setRandomAttributesForBean(e, false);
        e.setDeleted(false);
        e.setIsOpenPrize(false);
        gameConfigDuibaService.add(e);
        GameConfigDuibaEntity e1=gameConfigDuibaService.findOpenPrizeForUpdate(e.getId());
        Assert.assertNotNull(e1);
    }

    @Test
    public void testCount() {
        Assert.assertTrue(gameConfigDuibaService.count()>0);
    }

    @Test
    public void testFindByPageCount() {
        Map<String,Object> params=new HashMap<>();

        params.put("activityName",gameConfigDuibaDO.getTitle());
        Assert.assertTrue(gameConfigDuibaService.findByPageCount(params)>0);

    }

    @Test
    public void testFindNotDeleted() {
        GameConfigDuibaEntity e = gameConfigDuibaDO;
        GameConfigDuibaEntity e1=gameConfigDuibaService.findNotDeleted(e.getId());
        assertDO(e,e1);
    }


    @Test
    public void testDelete() {
        gameConfigDuibaService.delete(gameConfigDuibaDO.getId());
        Assert.assertTrue(gameConfigDuibaService.find(gameConfigDuibaDO.getId()).getDeleted());
    }

    @Test
    public void testUpdate() {
        GameConfigDuibaEntity e = new GameConfigDuibaEntity(gameConfigDuibaDO.getId());
        TestUtils.setRandomAttributesForBean(e,false);
        gameConfigDuibaService.update(e);
        GameConfigDuibaEntity e1=gameConfigDuibaService.find(e.getId());
        Assert.assertEquals(e.getBannerImage(),e1.getBannerImage());
    }

    @Test
    public void testUpdateSwitch() {
        GameConfigDuibaEntity e = gameConfigDuibaDO;
        e.setSwitches(42);
        gameConfigDuibaService.updateSwitch(e);
        GameConfigDuibaEntity e1=gameConfigDuibaService.find(e.getId());
        Assert.assertEquals((int)e1.getSwitches(),42);
    }


    private void assertDO(GameConfigDuibaEntity e, GameConfigDuibaEntity e1){
        assertDO(e,e1,null);
    }

    private void assertDO(GameConfigDuibaEntity e, GameConfigDuibaEntity e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","optionsJson","openSpecify","openBlack","openFreeRule"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

}
