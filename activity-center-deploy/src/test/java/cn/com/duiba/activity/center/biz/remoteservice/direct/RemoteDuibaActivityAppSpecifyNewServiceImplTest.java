package cn.com.duiba.activity.center.biz.remoteservice.direct;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.direct.AppQuantityDto;
import cn.com.duiba.activity.center.api.dto.direct.DuibaActivityAppSpecifyNewDto;
import cn.com.duiba.activity.center.api.remoteservice.direct.RemoteDuibaActivityAppSpecifyNewService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.dubbo.DubboResult;
import com.alibaba.fastjson.JSONObject;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static cn.com.duiba.activity.center.api.dto.direct.DuibaActivityAppSpecifyNewDto.BARGAIN_ACTIVITY;

/**
 * Created by su<PERSON><PERSON> on 16/7/20.
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class RemoteDuibaActivityAppSpecifyNewServiceImplTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteDuibaActivityAppSpecifyNewService remoteDuibaActivityAppSpecifyNewService;

    private DuibaActivityAppSpecifyNewDto dto;

    @Before
    public void testInsertAppSpecify() throws Exception {
        dto = new DuibaActivityAppSpecifyNewDto();
        dto.setAppId(-1l);
        dto.setHdType(null);
        dto.setRelationId(10l);
        dto.setRelationType(DuibaActivityAppSpecifyNewDto.ACTIVITY_GUESS);
        DubboResult result = remoteDuibaActivityAppSpecifyNewService.insertAppSpecify(dto);
        Long xxId = (Long) result.getResult();
        dto.setId(xxId);
    }

    @Test
    public void testIsNotAppSpecifyByActivityIdAndAppIdAndActivityType() throws Exception {

        DubboResult result = remoteDuibaActivityAppSpecifyNewService.isNotAppSpecifyByActivityIdAndAppIdAndActivityType(dto.getRelationId(), dto.getAppId(), dto.getRelationType());

//        Preconditions.checkArgument(true == result.getResult());
    }

    @Test
    public void testFindAppSpecifyByActivityIdAndAppIdAndActivityType() throws Exception {
        DuibaActivityAppSpecifyNewDto d = remoteDuibaActivityAppSpecifyNewService.findAppSpecifyByActivityIdAndAppIdAndActivityType(dto.getRelationId(), dto.getAppId(), dto.getRelationType()).getResult();
        Assert.assertNotNull(d);
        remoteDuibaActivityAppSpecifyNewService.deleteAppSpecifyById(dto.getId());

        d = remoteDuibaActivityAppSpecifyNewService.findAppSpecifyByActivityIdAndAppIdAndActivityType(dto.getRelationId(), dto.getAppId(), dto.getRelationType()).getResult();
        Assert.assertNull(d);

    }

    @Test
    public void testFindAppSpecifyByActivityIdAndActivityType() throws Exception {
        DubboResult<List<DuibaActivityAppSpecifyNewDto>> result = remoteDuibaActivityAppSpecifyNewService.findAppSpecifyByActivityIdAndActivityType(dto.getRelationId(), dto.getRelationType());

        Assert.assertTrue(!result.getResult().isEmpty());
    }

    @Test
    public void testFindAppSpecifyById() throws Exception {
        DubboResult result = remoteDuibaActivityAppSpecifyNewService.findAppSpecifyById(dto.getId());

        DuibaActivityAppSpecifyNewDto dto = (DuibaActivityAppSpecifyNewDto) result.getResult();
        Assert.assertEquals(dto.getId(), this.dto.getId());
    }

    @Test
    public void testFindAllAppIdByActivityIdsAndRelationType() {
        DuibaActivityAppSpecifyNewDto dto = new DuibaActivityAppSpecifyNewDto();

        long appId1 = (long) (Math.random() * 100000L);
        long appId2 = (long) (Math.random() * 100000L);

        dto.setRelationId(-1L);
        dto.setRelationType(DuibaActivityAppSpecifyNewDto.ACTIVITY_PLUGIN);

        dto.setAppId(appId1);
        remoteDuibaActivityAppSpecifyNewService.insertAppSpecify(dto);
        dto.setAppId(appId2);
        remoteDuibaActivityAppSpecifyNewService.insertAppSpecify(dto);

        List<Long> activityIds = Lists.newArrayList(-1L);
        List<Long> appIds = remoteDuibaActivityAppSpecifyNewService.findAllAppIdByActivityIdsAndRelationType(activityIds, DuibaActivityAppSpecifyNewDto.ACTIVITY_PLUGIN);
        Assert.assertTrue(appIds.size() == 2);
        Assert.assertTrue(appIds.contains(appId1));
        Assert.assertTrue(appIds.contains(appId2));

        //入参活动id为空集合，接口返回null
        List<Long> appIdsNULL = remoteDuibaActivityAppSpecifyNewService.findAllAppIdByActivityIdsAndRelationType(Lists.newArrayList(), DuibaActivityAppSpecifyNewDto.ACTIVITY_PLUGIN);
        Assert.assertTrue(appIdsNULL == null);

    }

    @Test
    public void countByRelationIds() throws Exception {
        List<AppQuantityDto> result = remoteDuibaActivityAppSpecifyNewService.countByRelationIds(Arrays.asList(1L,2L,3L,4L,14713L), BARGAIN_ACTIVITY);

        System.out.println(JSONObject.toJSONString(result));
    }
}
