package cn.com.duiba.activity.center.api.remoteservice.happygroup;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.guessredpacket.GuessRedPacketDto;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupBaseConfigDto;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupConfigDto;
import cn.com.duiba.activity.center.api.params.GuessRedPacketParams;
import cn.com.duiba.activity.center.api.remoteservice.guessredpacket.RemoteGuessRedPacketService;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/3/5 11:41
 * @description:
 */
@Rollback(value = false)
public class RemoteHappyGroupAppServiceTest extends TransactionalTestCaseBase {

    @Autowired
    RemoteHappyGroupAppService remoteHappyGroupAppService;

    @Test
    public void testGetById() {
        HappyGroupConfigDto happyGroupConfigDto = remoteHappyGroupAppService.getById(1L);
        System.out.println(JSON.toJSON(happyGroupConfigDto));
        Assert.assertNotNull(happyGroupConfigDto);
    }

    @Test
    public void testGetBaseConfigById() {
        HappyGroupBaseConfigDto happyGroupBaseConfigDto = remoteHappyGroupAppService.getBaseConfigById(1L);
        System.out.println(JSON.toJSON(happyGroupBaseConfigDto));
        Assert.assertNotNull(happyGroupBaseConfigDto);
    }
}
