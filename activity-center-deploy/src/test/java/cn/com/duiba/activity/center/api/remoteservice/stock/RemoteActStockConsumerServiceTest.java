package cn.com.duiba.activity.center.api.remoteservice.stock;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.service.exception.BusinessException;
import org.junit.Test;
import org.springframework.test.annotation.Rollback;

import javax.annotation.Resource;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/2/20 19:42
 * @description:
 */
@Rollback(value = false)
public class RemoteActStockConsumerServiceTest extends TransactionalTestCaseBase {

	@Resource
	private RemoteActStockConsumerService remoteActStockConsumerService;

	@Test
	public void testConsumeActStock() throws BusinessException{
		remoteActStockConsumerService.consumeActStock(1157L, "101", "plugin", 1L);
	}

	@Test
	public void testConsumeActStockApi() throws BizException{
		remoteActStockConsumerService.consumeActStockApi(1157L, "102", "plugin", 1L);
	}

	@Test
	public void testPaybackActStock() throws BusinessException{
		remoteActStockConsumerService.paybackActStock("102", "plugin");
	}

	@Test
	public void testPaybackActStockApi() throws BizException{
		remoteActStockConsumerService.paybackActStockApi("101", "plugin");
	}
}
