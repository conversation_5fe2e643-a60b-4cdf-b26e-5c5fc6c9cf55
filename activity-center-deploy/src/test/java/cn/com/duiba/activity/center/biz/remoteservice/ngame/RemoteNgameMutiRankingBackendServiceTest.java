package cn.com.duiba.activity.center.biz.remoteservice.ngame;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.remoteservice.ngame.RemoteNgameMutiRankingBackendService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * Created by sty on 12/9/17.
 */
@Transactional(DsConstants.DATABASE_NGAME)
public class RemoteNgameMutiRankingBackendServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteNgameMutiRankingBackendService remoteNgameMutiRankingBackendService;

    @Test
    public void testFindByBaseConfigId(){

        Assert.assertEquals(remoteNgameMutiRankingBackendService.insert("test", Lists.newArrayList()),false);

    }

    @Test
    public void testFindhasRelationNgameIdByNgameIds(){
        System.out.println((new Date()).getTime());
        Assert.assertEquals(remoteNgameMutiRankingBackendService.findhasRelationNgameIdByNgameIds( Lists.newArrayList(1L,2L,3L)).size()>0,true);
        System.out.println((new Date()).getTime());
    }
}
