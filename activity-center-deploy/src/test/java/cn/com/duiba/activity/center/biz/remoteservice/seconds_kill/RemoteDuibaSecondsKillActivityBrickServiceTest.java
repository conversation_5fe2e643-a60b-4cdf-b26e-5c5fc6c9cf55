package cn.com.duiba.activity.center.biz.remoteservice.seconds_kill;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.seconds_kill.DuibaSecondsKillActivityBrickDto;
import cn.com.duiba.activity.center.api.remoteservice.seconds_kill.RemoteDuibaSecondsKillActivityBrickService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * 
 * ClassName: RemoteDuibaSecondsKillActivityBrickServiceTest <br/>
 * date: 2016年12月1日 下午8:05:56 <br/>
 *
 * @version 
 * @since JDK 1.6
 */
@Transactional(DsConstants.DATABASE_CREDITS)
@Ignore
public class RemoteDuibaSecondsKillActivityBrickServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private RemoteDuibaSecondsKillActivityBrickService duibaSecondsKillActivityBrickService;
	
	private DuibaSecondsKillActivityBrickDto info;
	
	private String[] exceptFields = new String[] { "gmtCreate", "gmtModified" };
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaSecondsKillActivityBrickDto.class);
		duibaSecondsKillActivityBrickService.insert(info);
	}
	
	@Test
	public void findPageTest(){
		DuibaSecondsKillActivityBrickDto test = TestUtils.createRandomBean(DuibaSecondsKillActivityBrickDto.class);
		test.setDeleted(false);
		duibaSecondsKillActivityBrickService.insert(test);
		Map<String, Object> map = new HashMap<>();
		map.put("offset", 0);
		map.put("max", 10);
		List<DuibaSecondsKillActivityBrickDto> list = duibaSecondsKillActivityBrickService.findPage(map);
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void findPageCountTest(){
		DuibaSecondsKillActivityBrickDto test = TestUtils.createRandomBean(DuibaSecondsKillActivityBrickDto.class);
		test.setDeleted(false);
		duibaSecondsKillActivityBrickService.insert(test);
		long count = duibaSecondsKillActivityBrickService.findPageCount();
		Assert.assertTrue(count > 0);
	}

	@Test
	public void findTest(){
		DuibaSecondsKillActivityBrickDto test = duibaSecondsKillActivityBrickService.find(info.getId());
		TestUtils.assertEqualsReflect(info, test, false, exceptFields);
	}

	@Test
	public void updateTest(){
		DuibaSecondsKillActivityBrickDto test = TestUtils.createRandomBean(DuibaSecondsKillActivityBrickDto.class);
		test.setId(info.getId());
		duibaSecondsKillActivityBrickService.update(test);
		DuibaSecondsKillActivityBrickDto e = duibaSecondsKillActivityBrickService.find(info.getId());
		TestUtils.assertEqualsReflect(e, test, false, exceptFields);
	}
	
	@Test
	public void openTest(){
		duibaSecondsKillActivityBrickService.open(info.getId());
		int status = duibaSecondsKillActivityBrickService.find(info.getId()).getStatus();
		Assert.assertTrue(status == 1);
	}
	
	@Test
	public void disableTest(){
		duibaSecondsKillActivityBrickService.disable(info.getId());
		int status = duibaSecondsKillActivityBrickService.find(info.getId()).getStatus();
		Assert.assertTrue(status == 0);
	}
	
	@Test
	public void findByTitleTest(){
		DuibaSecondsKillActivityBrickDto test = TestUtils.createRandomBean(DuibaSecondsKillActivityBrickDto.class);
		test.setDeleted(false);
		duibaSecondsKillActivityBrickService.insert(test);
		DuibaSecondsKillActivityBrickDto e = duibaSecondsKillActivityBrickService.findByTitle(test.getTitle());
		Assert.assertNotNull(e);
	}
	
	@Test
	public void findAllEnableTest(){
		DuibaSecondsKillActivityBrickDto test = TestUtils.createRandomBean(DuibaSecondsKillActivityBrickDto.class);
		test.setDeleted(false);
		test.setStatus(1);
		duibaSecondsKillActivityBrickService.insert(test);
		List<DuibaSecondsKillActivityBrickDto> list = duibaSecondsKillActivityBrickService.findAllEnable();
		Assert.assertTrue(list.size() > 0);
	}
}
