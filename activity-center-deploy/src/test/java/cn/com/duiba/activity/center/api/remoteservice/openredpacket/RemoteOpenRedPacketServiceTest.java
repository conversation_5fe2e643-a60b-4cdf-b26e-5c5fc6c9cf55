package cn.com.duiba.activity.center.api.remoteservice.openredpacket;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.openredpacket.OpenRedPacketConfigDto;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * @Author: lufeng
 * @Description:
 * @Date: Created in 2019/5/6
 */
public class RemoteOpenRedPacketServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteOpenRedPacketConfigService remoteOpenRedPacketConfigService;

    @Test
    public void testGet() {
        OpenRedPacketConfigDto dto = remoteOpenRedPacketConfigService.queryOrpConfig(3L);
        if (dto == null) {
            System.out.println("=========结果为空");
        } else {
            System.out.println(dto.toString());
        }
    }

    @Test
    public void testAdd() {
        OpenRedPacketConfigDto dto = new OpenRedPacketConfigDto();
        dto.setAppId(5L);
        dto.setBudgetWarnSwitch(1);
        dto.setBudgetWarnThreshold(1000L);
        dto.setBulletScreenStatus(1);
        dto.setBannerImage("www.baidu.com");
        dto.setChannelType(1);
        dto.setStartTime(new Date());
        dto.setEndTime(new Date());
        dto.setInterfaceConfigJson("{}");
        dto.setMessageNoticePhone("150");
        dto.setParticipateConditionType(1);
        dto.setQrCode("二维码");
        dto.setRedPacketJson("[\n" +
                "            {\n" +
                "                \"orderId\":1,\n" +
                "                \"amountMoney\":500,\n" +
                "                \"needFriendsNum\":5\n" +
                "            }\n" +
                "        ]");
        dto.setRedPacketNum(5);
        dto.setShareTitle("分享标题");
        dto.setShareSubTitle("分享副标题");
        dto.setSharePic("分享图片");
        dto.setTitle("标题");
        dto.setRule("规则");
        dto.setTotalBudget(10000000L);
        Long id = remoteOpenRedPacketConfigService.createOrpConfig(dto);
        System.out.println("======id=" + id);
    }

    @Test
    public void testUpdate() {
        OpenRedPacketConfigDto dto = new OpenRedPacketConfigDto();
        dto.setId(1L);
        dto.setBudgetWarnSwitch(3);
        dto.setBudgetWarnThreshold(1000L);
        dto.setBulletScreenStatus(1);
        dto.setBannerImage("www.baidu.com");
        dto.setChannelType(1);
        dto.setStartTime(new Date());
        dto.setEndTime(new Date());
        dto.setInterfaceConfigJson("{}");
        dto.setMessageNoticePhone("150");
        dto.setParticipateConditionType(1);
        dto.setQrCode("二维码");
        dto.setRedPacketJson("{}");
        dto.setRedPacketNum(5);
        dto.setShareTitle("分享标题");
        dto.setShareSubTitle("分享副标题");
        dto.setSharePic("分享图片");
        dto.setTitle("标题");
        dto.setRule("规则");
        dto.setTotalBudget(10000000L);
        remoteOpenRedPacketConfigService.updateOrpConfig(dto);
    }

    @Test
    public void testEndActivity(){
        remoteOpenRedPacketConfigService.endActivity(7L);
    }
}
