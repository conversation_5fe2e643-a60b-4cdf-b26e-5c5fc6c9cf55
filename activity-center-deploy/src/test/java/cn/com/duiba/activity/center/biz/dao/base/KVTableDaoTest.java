package cn.com.duiba.activity.center.biz.dao.base;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.base.KVTableEntity;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by <PERSON><PERSON><PERSON>yong on 2017/1/6.
 */
@Transactional(DsConstants.DATABASE_CKVTABLE)
public class KVTableDaoTest extends TransactionalTestCaseBase {
	@Autowired
	private KVTableDao kvTableDao;

	@Test
	public void testInsert() {
		KVTableEntity kvTableEntity = new KVTableEntity();
		kvTableEntity.setConsumerId(1L);
		kvTableEntity.setIntValue(1L);
		kvTableEntity.setSpace("s");
		kvTableEntity.setKey("key");
		kvTableEntity.setStringValue("string");
		KVTableEntity result = kvTableDao.insert(kvTableEntity);
		Assert.assertTrue(result.getId() != null);
	}

	@Test
	public void testSelect() {
		KVTableEntity kvTableEntity = new KVTableEntity();
		kvTableEntity.setConsumerId(1L);
		kvTableEntity.setIntValue(1L);
		kvTableEntity.setSpace("s");
		kvTableEntity.setKey("key");
		kvTableEntity.setStringValue("string");
		KVTableEntity result = kvTableDao.insert(kvTableEntity);
		KVTableEntity selectResult = kvTableDao.select(result.getId(), kvTableEntity.getConsumerId());
		Assert.assertEquals(result.getId(), selectResult.getId());
		Assert.assertEquals(result.getConsumerId(), selectResult.getConsumerId());
		Assert.assertEquals(result.getSpace(), selectResult.getSpace());
		Assert.assertEquals(result.getKey(), selectResult.getKey());
		Assert.assertEquals(result.getIntValue(), selectResult.getIntValue());
		Assert.assertEquals(result.getStringValue(), selectResult.getStringValue());
	}

	@Test
	public void testSelectByCondition() {
		KVTableEntity kvTableEntity = new KVTableEntity();
		kvTableEntity.setConsumerId(1L);
		kvTableEntity.setIntValue(1L);
		kvTableEntity.setSpace("s");
		kvTableEntity.setKey("key");
		kvTableEntity.setStringValue("string");
		KVTableEntity result = kvTableDao.insert(kvTableEntity);
		KVTableEntity selectResult = kvTableDao.selectByCondition(kvTableEntity.getKey(), kvTableEntity.getConsumerId(), kvTableEntity.getSpace());
		Assert.assertEquals(result.getId(), selectResult.getId());
		Assert.assertEquals(result.getConsumerId(), selectResult.getConsumerId());
		Assert.assertEquals(result.getSpace(), selectResult.getSpace());
		Assert.assertEquals(result.getKey(), selectResult.getKey());
		Assert.assertEquals(result.getIntValue(), selectResult.getIntValue());
		Assert.assertEquals(result.getStringValue(), selectResult.getStringValue());
	}

	@Test
	public void testUpdate() {
		KVTableEntity kvTableEntity = new KVTableEntity();
		kvTableEntity.setConsumerId(1L);
		kvTableEntity.setIntValue(1L);
		kvTableEntity.setSpace("s");
		kvTableEntity.setKey("key");
		kvTableEntity.setStringValue("string");
		KVTableEntity result = kvTableDao.insert(kvTableEntity);
		result.setStringValue("string2");
		result.setIntValue(2L);
		int count = kvTableDao.update(result);
		Assert.assertEquals(1, count);
		KVTableEntity selectResult = kvTableDao.select(result.getId(), kvTableEntity.getConsumerId());
		Assert.assertEquals(result.getId(), selectResult.getId());
		Assert.assertEquals(result.getConsumerId(), selectResult.getConsumerId());
		Assert.assertEquals(result.getSpace(), selectResult.getSpace());
		Assert.assertEquals(result.getKey(), selectResult.getKey());
		Assert.assertEquals(result.getIntValue(), selectResult.getIntValue());
		Assert.assertEquals(result.getStringValue(), selectResult.getStringValue());
	}

	@Test
	public void testDelete() {
		KVTableEntity kvTableEntity = new KVTableEntity();
		kvTableEntity.setConsumerId(1L);
		kvTableEntity.setIntValue(1L);
		kvTableEntity.setSpace("s");
		kvTableEntity.setKey("key");
		kvTableEntity.setStringValue("string");
		KVTableEntity result = kvTableDao.insert(kvTableEntity);
		int count = kvTableDao.delete(result.getId(), result.getConsumerId());
		Assert.assertEquals(1, count);
		KVTableEntity selectResult = kvTableDao.select(result.getId(), result.getConsumerId());
		Assert.assertEquals(null, selectResult);
	}

}
