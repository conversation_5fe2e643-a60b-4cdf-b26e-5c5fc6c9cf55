package cn.com.duiba.activity.center.biz.dao.guess;

import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.guess.GuessOrdersExtraEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class GuessOrdersExtraDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private GuessOrdersExtraDao GuessOrdersExtraDao;
	
	private GuessOrdersExtraEntity info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(GuessOrdersExtraEntity.class);
		GuessOrdersExtraDao.insert(info);
	}
	
	@Test
	public void findTest(){
		GuessOrdersExtraEntity e = GuessOrdersExtraDao.find(info.getId());
		Assert.assertNotNull(e);
	}

	@Test
    public void updateOrderIdTest(){
    	GuessOrdersExtraEntity e = TestUtils.createRandomBean(GuessOrdersExtraEntity.class);
    	GuessOrdersExtraDao.updateOrderId(info.getId(), e.getMainOrderId(), e.getMainOrderNum());
    	long mainOrderId = GuessOrdersExtraDao.find(info.getId()).getMainOrderId();
    	Assert.assertTrue(mainOrderId == e.getMainOrderId());
    }

	@Test
    public void findWiningPrizeIdsTest(){
    	GuessOrdersExtraEntity e = TestUtils.createRandomBean(GuessOrdersExtraEntity.class);
    	e.setMainOrderId(200L);
    	GuessOrdersExtraDao.insert(e);
    	List<Long> list = GuessOrdersExtraDao.findWiningPrizeIds(e.getConsumerId(), e.getDuibaGuessId());
    	Assert.assertTrue(list.size() > 0);
    }

	@Test
    public void updateNotPrizeTest(){
    	GuessOrdersExtraEntity e = TestUtils.createRandomBean(GuessOrdersExtraEntity.class);
    	GuessOrdersExtraDao.insert(e);
    	GuessOrdersExtraDao.updateNotPrize(e.getId());
    	Long appItemId = GuessOrdersExtraDao.find(e.getId()).getAppItemId();
    	Assert.assertNull(appItemId);
    }

}
