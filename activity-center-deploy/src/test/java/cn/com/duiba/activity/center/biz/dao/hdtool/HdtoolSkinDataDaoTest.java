package cn.com.duiba.activity.center.biz.dao.hdtool;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.hdtool.HdtoolSkinEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/7/6.
 */
@Transactional(DsConstants.DATABASE_HDTOOL_CONF)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class HdtoolSkinDataDaoTest extends TransactionalTestCaseBase {
    
    @Autowired
    private HdtoolSkinDataDao hdtoolSkinDataDao;

    @Test
    public void testInsert() throws Exception {
        HdtoolSkinEntity e=new HdtoolSkinEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        hdtoolSkinDataDao.insert(e);
    }

    @Test
    public void testUpdateDataJson() throws Exception {
        HdtoolSkinEntity e=new HdtoolSkinEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        hdtoolSkinDataDao.insert(e);
        Assert.assertTrue(hdtoolSkinDataDao.updateDataJson(e.getHdtoolId(),e.getType(),"test")>0);
    }

    @Test
    public void testSelectJsonByHdtoolIdAndType() throws Exception {
        HdtoolSkinEntity e=new HdtoolSkinEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        hdtoolSkinDataDao.insert(e);
        Assert.assertNotNull(hdtoolSkinDataDao.selectJsonByHdtoolIdAndType(e.getHdtoolId(),e.getType()));
    }

    @Test
    public void testSelectByHdtoolIdAndType() throws Exception {
        HdtoolSkinEntity e=new HdtoolSkinEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        hdtoolSkinDataDao.insert(e);
        Assert.assertNotNull(hdtoolSkinDataDao.selectByHdtoolIdAndType(e.getHdtoolId(),e.getType()));
    }
}
