package cn.com.duiba.activity.center.biz.service.guess;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.guess.DuibaGuessOptionsDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class DuibaGuessOptionsServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaGuessOptionsService duibaGuessOptionsService;
	
	private DuibaGuessOptionsDto info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaGuessOptionsDto.class);
		duibaGuessOptionsService.insert(info);
	}
	
	@Test
	public void findByIdTest(){
		DuibaGuessOptionsDto e = duibaGuessOptionsService.findById(info.getId());
		Assert.assertNotNull(e);
	}
	
	@Test
	public void findByGuessIdTest(){
		DuibaGuessOptionsDto e = TestUtils.createRandomBean(DuibaGuessOptionsDto.class);
		e.setDeleted(false);
		duibaGuessOptionsService.insert(e);
		List<DuibaGuessOptionsDto> list = duibaGuessOptionsService.findByGuessId(e.getDuibaGuessId());
		Assert.assertTrue(list.size() > 0);
	}
	
	@Test
	public void findByAutoOpenTest(){
		DuibaGuessOptionsDto e = TestUtils.createRandomBean(DuibaGuessOptionsDto.class);
		e.setDeleted(false);
		e.setAutoOpen(1);
		duibaGuessOptionsService.insert(e);
		List<DuibaGuessOptionsDto> list = duibaGuessOptionsService.findByAutoOpen(e.getDuibaGuessId(), true);
		Assert.assertTrue(list.size() > 0);
	}
	
	@Test
	public void findByAutoOpenAscTest(){
		DuibaGuessOptionsDto e = TestUtils.createRandomBean(DuibaGuessOptionsDto.class);
		e.setDeleted(false);
		e.setAutoOpen(1);
		duibaGuessOptionsService.insert(e);
		List<DuibaGuessOptionsDto> list = duibaGuessOptionsService.findByAutoOpen(e.getDuibaGuessId(), true);
		Assert.assertTrue(list.size() > 0);
	}

	//from manager
	@Test
	public void deleteTest(){
		List<Long> ids = new ArrayList<Long>();
		ids.add(info.getId());
		duibaGuessOptionsService.delete(ids);
		boolean deleted = duibaGuessOptionsService.findById(info.getId()).getDeleted();
		Assert.assertTrue(deleted);
	}
	
	@Test
	public void updateInfoFormTest(){
		DuibaGuessOptionsDto e = TestUtils.createRandomBean(DuibaGuessOptionsDto.class);
		e.setId(info.getId());
		duibaGuessOptionsService.updateInfoForm(e);
		String description = duibaGuessOptionsService.findById(info.getId()).getDescription();
		Assert.assertTrue(description.equals(e.getDescription()));
	}

	/**
	 * 更新库存
	 */
	@Test
	public void updateRemainingByIdTest(){
		DuibaGuessOptionsDto e = TestUtils.createRandomBean(DuibaGuessOptionsDto.class);
		duibaGuessOptionsService.updateRemainingById(info.getId(), e.getRemaining());
		int remaining = duibaGuessOptionsService.findById(info.getId()).getRemaining();
		Assert.assertTrue(remaining == e.getRemaining());
	}
}
