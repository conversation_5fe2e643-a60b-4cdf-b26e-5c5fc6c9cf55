package cn.com.duiba.activity.center.biz.adapter;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.ActivityCommonDto;
import cn.com.duiba.activity.center.api.enums.ActivityTypeEnum;
import cn.com.duiba.activity.center.biz.adapter.activity.ActivityAdapter;
import cn.com.duiba.activity.center.biz.adapter.activity.ActivityAdapterManager;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created by xiaoxuda on 2017/3/24.
 */
public class ActivityAdapterTest extends TransactionalTestCaseBase {
    @Autowired
    private ActivityAdapterManager activityAdapterManager;

    @Test
    public void filterAppDirectTest() {
        List<ActivityCommonDto> list = Lists.newArrayList();
        ActivityCommonDto activityCommonDto = new ActivityCommonDto();
        activityCommonDto.setActivityId(1L);
        activityCommonDto.setSwitches(1 << ActivityCommonDto.SWITCHES_APP_DIRECT);
        list.add(activityCommonDto);
        ActivityCommonDto activityCommonDto2 = new ActivityCommonDto();
        activityCommonDto2.setActivityId(3L);
        activityCommonDto2.setSwitches(0);
        list.add(activityCommonDto2);

        List<ActivityCommonDto> filterResult = Lists.newArrayList();
        for (ActivityTypeEnum type : ActivityTypeEnum.values()) {
            try {
                ActivityAdapter adapter = activityAdapterManager.getActivityAdapterByType(type);
                filterResult = adapter.appDirectFilter(1L, list);
                Assert.assertTrue(filterResult.size() >= 1);
            } catch (Exception e) {
                continue;
            }

        }
    }

    @Test
    public void filterDevBlackTest() {
        List<ActivityCommonDto> list = Lists.newArrayList();
        ActivityCommonDto activityCommonDto = new ActivityCommonDto();
        activityCommonDto.setActivityId(1L);
        activityCommonDto.setSwitches(1 << ActivityCommonDto.SWITCHES_DEV_BLACKLIST);
        list.add(activityCommonDto);
        ActivityCommonDto activityCommonDto2 = new ActivityCommonDto();
        activityCommonDto2.setActivityId(3L);
        activityCommonDto2.setSwitches(0);
        list.add(activityCommonDto2);

        List<ActivityCommonDto> filterResult = Lists.newArrayList();
        for (ActivityTypeEnum type : ActivityTypeEnum.values()) {
            try {
                ActivityAdapter adapter = activityAdapterManager.getActivityAdapterByType(type);
                filterResult = adapter.devActBlackFilter(1L, list);
                Assert.assertTrue(filterResult.size() >= 1);
            } catch (Exception e) {
                continue;
            }

        }
    }

    @Test
    public void getOneActivityCommonTest(){
        ActivityCommonDto dto = activityAdapterManager.getActivityAdapterByType(ActivityTypeEnum.APP_SINGLE_LOTTERY)
                .getOneActivityCommon(1L);
        Assert.assertNotNull(dto);
        dto = activityAdapterManager.getActivityAdapterByType(ActivityTypeEnum.MANUAL_LOTTERY)
                .getOneActivityCommon(1L);
        Assert.assertNotNull(dto);

        dto = activityAdapterManager.getActivityAdapterByType(ActivityTypeEnum.SECONDS_ACTIVITY)
                .getOneActivityCommon(1L);
        Assert.assertNotNull(dto);
    }

    @Test
    public void getOneActivityCommonByIdsTest(){
        List<ActivityCommonDto> dtos = activityAdapterManager.getActivityAdapterByType(ActivityTypeEnum.APP_SINGLE_LOTTERY)
                .getActivityCommonByIds(Lists.newArrayList(1L));
        Assert.assertNotNull(dtos);
        dtos = activityAdapterManager.getActivityAdapterByType(ActivityTypeEnum.MANUAL_LOTTERY)
                .getActivityCommonByIds(Lists.newArrayList(1L));
        Assert.assertNotNull(dtos);
        dtos = activityAdapterManager.getActivityAdapterByType(ActivityTypeEnum.SECONDS_ACTIVITY)
                .getActivityCommonByIds(Lists.newArrayList(1L));
        Assert.assertNotNull(dtos);

    }

}
