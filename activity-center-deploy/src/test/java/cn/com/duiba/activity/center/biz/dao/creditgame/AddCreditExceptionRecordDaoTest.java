package cn.com.duiba.activity.center.biz.dao.creditgame;

import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.creditgame.AddCreditExceptionRecordEntity;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 积分游戏DAO单元测试
 * <AUTHOR>
 * @since 2016-09-05
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)
public class AddCreditExceptionRecordDaoTest extends CreditGameDaoTestCaseBase<AddCreditExceptionRecordEntity> {


    @Autowired
    private AddCreditExceptionRecordDao addCreditExceptionRecordDao;

    @Override
    protected AddCreditExceptionRecordEntity genEntity(){
        AddCreditExceptionRecordEntity addCreditExceptionRecordEntity=new AddCreditExceptionRecordEntity();
        addCreditExceptionRecordEntity.setGmtModified(new Date());
        addCreditExceptionRecordEntity.setGmtCreate(new Date());
        addCreditExceptionRecordEntity.setOrderNum("123l");
        addCreditExceptionRecordEntity.setAddCreditError("error msg");
        addCreditExceptionRecordEntity.setAddCreditNum(100l);
        addCreditExceptionRecordEntity.setAddCreditReason("reason");
        addCreditExceptionRecordEntity.setAddCreditType(1l);
        addCreditExceptionRecordEntity.setCloseReason("close reason");
        addCreditExceptionRecordEntity.setAppId(123l);
        addCreditExceptionRecordEntity.setIsClose((byte)0);
        addCreditExceptionRecordEntity.setAppId(1L);
        addCreditExceptionRecordEntity.setExceptionType((byte)0);

        return addCreditExceptionRecordEntity;
    }
    @Test
    public void testInsert(){
        doTestInsert(addCreditExceptionRecordDao);
    }


    @Test
    public void testQueryById(){
        doTestQueryById(addCreditExceptionRecordDao);
    }

    /*
    @Test
    public void testQuery(){
        doTestQuery(addCreditExceptionRecordDao);
    }
    */

    @Test
    public void testUpdate(){
        doTestUpdate(addCreditExceptionRecordDao, new PreUpdateHandler<AddCreditExceptionRecordEntity>() {
            @Override
            public void preHandle(AddCreditExceptionRecordEntity addCreditExceptionRecordEntity) {
                addCreditExceptionRecordEntity.setGmtModified(new Date());
            }
        });
    }


    @Test
    public void testDelete(){
        doTestDelete(addCreditExceptionRecordDao);
    }
}
