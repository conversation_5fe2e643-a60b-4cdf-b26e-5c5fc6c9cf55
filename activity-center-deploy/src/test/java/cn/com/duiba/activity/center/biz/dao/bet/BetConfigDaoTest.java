package cn.com.duiba.activity.center.biz.dao.bet;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.enums.AttributionTypeEnum;
import cn.com.duiba.activity.center.api.enums.ConfigStatusEnum;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.bet.BetConfigEntity;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 * @date 2018/05/02
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class BetConfigDaoTest extends TransactionalTestCaseBase {
    @Autowired
    private BetConfigDao betConfigDao;
    private BetConfigEntity betConfigEntity;

    @Before
    public void setup() {
        betConfigEntity = new BetConfigEntity();
        betConfigEntity.setCreditsValue(100);
        betConfigEntity.setConfigStatus(ConfigStatusEnum.OPEN.getCode());
        betConfigEntity.setEndTime(new Date(System.currentTimeMillis()));
        betConfigEntity.setShareExp(2L);
        betConfigEntity.setTitle("test");
        betConfigEntity.setAttributionType(AttributionTypeEnum.DEVELOPER.getCode());
        betConfigEntity.setPluginId(1L);
        betConfigEntity.setPrizeLimit(20L);
        betConfigEntity.setBonusAmount(0);
        betConfigEntity.setId(betConfigDao.insert(betConfigEntity));
    }

    @Test
    public void shouldInsertCorrectly() {
        assertThat(betConfigEntity.getId()).isNotNull().isGreaterThan(0);
    }

    @Test
    public void shouldUpdateCorrectly() {
        betConfigEntity.setTitle("test2");
        betConfigEntity.setCreditsValue(200);
        betConfigEntity.setShareExp(20L);
        betConfigEntity.setEndTime(new Date(System.currentTimeMillis()));
        betConfigEntity.setPluginId(2L);
        betConfigEntity.setPrizeLimit(30L);

        Integer count = betConfigDao.update(betConfigEntity);
        assertThat(count).isEqualTo(1);
    }

    @Test
    public void shouldListCorrectly() {
        List<BetConfigEntity> betConfigEntityList = betConfigDao.list(null,1, 1);

        assertThat(betConfigEntityList).isNotNull();
        assertThat(betConfigEntityList.size()).isEqualTo(1);
    }

    @Test
    public void shouldFindCorrectly() {
        BetConfigEntity entity = betConfigDao.findById(betConfigEntity.getId());

        assertThat(entity).isNotNull();
        assertThat(entity.getId()).isEqualTo(betConfigEntity.getId());
        assertThat(entity.getTitle()).isEqualTo(betConfigEntity.getTitle());
        assertThat(entity.getCreditsValue()).isEqualTo(betConfigEntity.getCreditsValue());
    }

    @Test
    public void shouldListByEndTimeCorrectly() {
        List<BetConfigEntity> betConfigEntityList = betConfigDao.listByEndTime();

        assertThat(betConfigEntityList).isNotNull();
        assertThat(betConfigEntityList.size()).isGreaterThan(0);
    }

    @Test
    public void shouldCount() {
        assertThat(betConfigDao.countByDuiBaAttriType()).isGreaterThan(0);
    }

    @Test
    //首页列表
    public void shouldListByIds() {
        List<BetConfigEntity> betConfigEntityList = betConfigDao.listByIds(Arrays.asList(betConfigEntity.getId(), 0L));

        assertThat(betConfigEntityList).isNotNull();
        assertThat(betConfigEntityList.size()).isGreaterThan(0);
    }

    @Test
    //往日战绩
    public void shouldListByBetIds() {
        List<BetConfigEntity> betConfigEntityList = betConfigDao.listByBetIds(Arrays.asList(betConfigEntity.getId(), 0L));

        assertThat(betConfigEntityList).isNotNull();
        assertThat(betConfigEntityList.size()).isGreaterThan(0);
    }
}
