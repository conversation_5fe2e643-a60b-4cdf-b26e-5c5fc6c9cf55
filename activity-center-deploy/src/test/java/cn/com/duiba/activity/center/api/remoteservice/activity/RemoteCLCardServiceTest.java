package cn.com.duiba.activity.center.api.remoteservice.activity;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity.CLCardConfigDto;
import cn.com.duiba.activity.center.api.dto.activity.CLRewardDto;
import cn.com.duiba.activity.center.api.dto.activity.CLRuleDto;
import cn.com.duiba.activity.center.api.enums.CLRewardRuleMatchEnum;
import cn.com.duiba.activity.center.api.enums.RewardTypeEnum;
import cn.com.duiba.activity.center.api.params.CLCardPageQueryParam;
import cn.com.duiba.activity.center.api.params.GrantCLCardDecideParam;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.stock.service.api.remoteservice.RemoteStockBackendService;
import cn.com.duiba.wolf.utils.BeanUtils;

/**
 * Created by xiaoxuda on 2017/10/26.
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class RemoteCLCardServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteCLCardService remoteCLCardService;
    @Autowired
    private RemoteStockBackendService remoteStockBackendService;

    private CLCardConfigDto prepareData(){
        CLCardConfigDto dto = new CLCardConfigDto();
        dto.setClName("集卡中iphone");
        CLRuleDto rule = new CLRuleDto();
        rule.setName("爱心卡");
        rule.setImg("http://yun.duiba.com.cn/1.jpg");
        rule.setTitle("爱心卡");
        rule.setSelImg("http://yun.duiba.com.cn/2.jpg");
        rule.setId(10000L);
        CLRuleDto rule2 = BeanUtils.copy(rule, CLRuleDto.class);
        rule2.setName("节约卡");
        rule2.setId(10001L);
        dto.setRules(Lists.newArrayList(rule, rule2));
        //设置奖项
        CLRewardDto clReward = new CLRewardDto();
        clReward.setName("一等奖");
        clReward.setAmountsLimit(true);
        clReward.setGrade(1);
        clReward.setLimitCount(1L);
        clReward.setMatchType(CLRewardRuleMatchEnum.ALL_MATCH);
        clReward.setPrizeImg("http://yun.duiba.com.cn/3.jpg");
        clReward.setPrizeName("iphone");
        clReward.setRdType(RewardTypeEnum.GOODS);
        clReward.setRlType("object");
        clReward.setRlId(1L);
        clReward.setStockLimit(true);
        clReward.setStock(1L);
        CLRewardDto.Entry entry = new CLRewardDto.Entry();
        entry.setKey(rule.getId().toString());
        entry.setVal(1);
        CLRewardDto.Entry entry2 = new CLRewardDto.Entry();
        entry2.setKey(rule2.getId().toString());
        entry2.setVal(1);
        clReward.setMatchs(Lists.newArrayList(entry));
        CLRewardDto clReward2 = BeanUtils.copy(clReward, CLRewardDto.class);
        clReward2.setName("二等奖");
        clReward2.setGrade(2);
        clReward2.setRlId(2L);
        clReward2.setMatchs(Lists.newArrayList(entry2));
        CLRewardDto clReward3 = BeanUtils.copy(clReward, CLRewardDto.class);
        clReward3.setName("3等奖");
        clReward3.setGrade(3);
        clReward3.setRlId(3L);
        clReward3.setMatchType(CLRewardRuleMatchEnum.RANDOM_MATCH);
        clReward3.setEntryCount(1);
        clReward3.setAutoReward(false);
        dto.setRewards(Lists.newArrayList(clReward, clReward2));

        Long id = remoteCLCardService.insert(dto);
        dto.setId(id);
        return dto;
    }

    @Test
    public void testFind(){
        CLCardConfigDto config = prepareData();
        config = remoteCLCardService.findById(config.getId());
        Assert.assertNotNull(config);

        config.setExtra(new JSONObject());
        config.setClName("新集卡活动");
        remoteCLCardService.updateById(config);
        config = remoteCLCardService.findById(config.getId());
        Assert.assertNotNull(config.getExtra());

        CLCardPageQueryParam pageQueryParam = new CLCardPageQueryParam();
        pageQueryParam.setName("集卡");
        List<CLCardConfigDto> list = remoteCLCardService.findByPage(pageQueryParam);
        Assert.assertTrue(list.size()>0);
        pageQueryParam.setId(config.getId());
        list = remoteCLCardService.findByPage(pageQueryParam);
        Assert.assertTrue(list.size()==1);
        Assert.assertTrue(remoteCLCardService.count(pageQueryParam) > 0);


        //Assert.assertTrue(remoteCLCardService.deleteById(config.getId()));
    }

    @Test
    public void grantCardDecideTest(){
        CLCardConfigDto config = prepareData();
        CLRewardDto no1 = config.getRewards().get(0);
        CLRewardDto no2 = config.getRewards().get(1);
        //设置库存
        if(Boolean.TRUE.equals(no1.getStockLimit())) {
            Long stockId = remoteStockBackendService.newStock(config.getId(), no1.getStock()).getResult();
            no1.setStockId(stockId);
        }
        if(Boolean.TRUE.equals(no2.getStockLimit())) {
            Long stockId = remoteStockBackendService.newStock(config.getId(), no2.getStock()).getResult();
            no2.setStockId(stockId);
        }
        remoteCLCardService.updateById(config);

        GrantCLCardDecideParam param = new GrantCLCardDecideParam();
        param.setConsumerId(1L);
        param.setClCardConfigId(config.getId());
        List<CLRewardDto.Entry> cards = new ArrayList<>();
        param.setCards(cards);

        //一等奖
        param.setCardId(Long.parseLong(no1.getMatchs().get(0).getKey()));
        Assert.assertTrue(remoteCLCardService.grantCLCardDecide(param));
        //一等奖库存不足
        param.setCardId(Long.parseLong(no1.getMatchs().get(0).getKey()));
        Assert.assertFalse(remoteCLCardService.grantCLCardDecide(param));
        //一等奖超出限额
        cards.add(no1.getMatchs().get(0));
        param.setCardId(Long.parseLong(no1.getMatchs().get(0).getKey()));
        Assert.assertFalse(remoteCLCardService.grantCLCardDecide(param));

        //二等奖,领奖超出限额
        param.setCardId(Long.parseLong(no2.getMatchs().get(0).getKey()));
        Assert.assertFalse(remoteCLCardService.grantCLCardDecide(param));

        //二等奖领奖
        param.getCards().clear();
        param.setCardId(Long.parseLong(no2.getMatchs().get(0).getKey()));
        Assert.assertTrue(remoteCLCardService.grantCLCardDecide(param));
    }
}
