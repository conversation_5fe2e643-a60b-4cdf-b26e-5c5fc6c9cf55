package cn.com.duiba.activity.center.biz.service.guess;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import cn.com.duiba.wolf.utils.DateUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.guess.GuessOrdersDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.guess.GuessOrdersDao;
import cn.com.duiba.activity.center.biz.entity.guess.GuessOrdersEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class GuessOrdersServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private GuessOrdersDao guessOrdersDao;
	@Autowired
	private GuessOrdersService guessOrdersService;
	
	private GuessOrdersEntity info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(GuessOrdersEntity.class);
		info.setOrderStatus(10);
		info.setExchangeStatus(10);
		info.setIsGivePrize(10);
		guessOrdersDao.insert(info);
	}
	
	@Test
	public void findWinByGuessIdAndPrizeIdTest(){
		List<GuessOrdersDto> list = guessOrdersService.findWinByGuessIdAndPrizeId(info.getDuibaGuessId(), info.getPrizeId());
		Assert.assertTrue(list.size() > 0);
	}

    /**
     * 精确匹配获取中奖名单
     * @param duibaGuessId
     * @param num
     * @return
     */
	@Test
    public void findWinOrderByExactMatchTest(){
		GuessOrdersEntity e = TestUtils.createRandomBean(GuessOrdersEntity.class);
    	e.setOrderStatus(2);
    	e.setExchangeStatus(1);
		e.setIsGivePrize(10);
		guessOrdersDao.insert(e);
    	List<String> winIds = new ArrayList<String>();
    	winIds.add(e.getGuessRandomNum());
    	List<GuessOrdersDto> list = guessOrdersService.findWinOrderByExactMatch(e.getDuibaGuessId(), winIds, e.getGuessData());
    	Assert.assertTrue(list.size() > 0);
    }
    /**
     * 模糊匹配获取中奖名单
     * @param duibaGuessId
     * @param num
     * @return
     */
	@Test
    public void findWinOrderByVagueMatchTest(){
		GuessOrdersEntity e = TestUtils.createRandomBean(GuessOrdersEntity.class);
    	e.setOrderStatus(2);
    	e.setExchangeStatus(1);
    	e.setGuessRandomNum("100");
		e.setIsGivePrize(10);
		guessOrdersDao.insert(e);
    	List<GuessOrdersDto> list = guessOrdersService.findWinOrderByVagueMatch(e.getDuibaGuessId(), 10, "10", e.getGuessData());
    	Assert.assertTrue(list.size() > 0);
    }

    //from manager
	@Test
    public void findExpireOrderTest(){
		GuessOrdersEntity e = TestUtils.createRandomBean(GuessOrdersEntity.class);
    	e.setOrderStatus(2);
    	e.setExchangeStatus(2);
		e.setIsGivePrize(10);
    	Calendar calendar = Calendar.getInstance();
    	calendar.setTime(e.getPrizeOverdueDate());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	Date start = calendar.getTime();
    	e.setPrizeOverdueDate(start);
    	guessOrdersDao.insert(e);
    	List<GuessOrdersDto> list = guessOrdersService.findExpireOrder();
    	Assert.assertTrue(list.size() > 0);
    }

    /**
     * 精确匹配获取中奖名单
     * @param duibaGuessId
     * @param num
     * @return
     */
	@Test
    public void findWinOrderByExactMatch2Test(){
		GuessOrdersEntity e = TestUtils.createRandomBean(GuessOrdersEntity.class);
    	e.setOrderStatus(2);
		e.setExchangeStatus(10);
		e.setIsGivePrize(10);
		guessOrdersDao.insert(e);
    	List<String> winIds = new ArrayList<String>();
    	winIds.add(e.getGuessRandomNum());
    	List<GuessOrdersDto> list = guessOrdersService.findWinOrderByExactMatch(e.getDuibaGuessId(), winIds, e.getGuessData(), 0, 10, e.getConsumerId());
    	Assert.assertTrue(list.size() > 0);
    }
	
    /**
     * 模糊匹配获取中奖名单
     * @param duibaGuessId
     * @param num
     * @return
     */
	@Test
    public void findWinOrderByVagueMatch2Test(){
		GuessOrdersEntity e = TestUtils.createRandomBean(GuessOrdersEntity.class);
    	e.setOrderStatus(2);
    	e.setExchangeStatus(1);
    	e.setGuessRandomNum("100");
		e.setIsGivePrize(10);
		guessOrdersDao.insert(e);
    	List<GuessOrdersDto> list = guessOrdersService.findWinOrderByVagueMatch(e.getDuibaGuessId(), 10, "10", e.getGuessData(), 0, 10, e.getConsumerId());
    	Assert.assertTrue(list.size() > 0);
    }

	@Test
	public void findAllWinOrder(){
		Long guessId = 99999999L;
		String guessData = "1";
		Long consumerId = 88888888L;
		Date createTime = new Date();
		Integer size = 5;
		//创建50条consumerId递增的订单
		for (int i = 0; i < 50; i++) {
			GuessOrdersEntity e = TestUtils.createRandomBean(GuessOrdersEntity.class);
			e.setId(null);
			e.setDuibaGuessId(guessId);
			e.setGuessData(guessData);
			e.setOrderStatus(2);
			e.setExchangeStatus(1);
			e.setConsumerId(consumerId++);
			e.setGmtCreate(createTime);
			guessOrdersDao.insert(e);
		}
		List<Long> ids1 = guessOrdersDao.findAllWinOrderId(guessId, guessData, 88888888L, DateUtils.daysAddOrSub(createTime, -1), size);
		Assert.assertTrue(ids1.size() == 5);
		List<Long> ids2 = guessOrdersDao.findAllWinOrderId(guessId, guessData + "1", 88888888L, DateUtils.daysAddOrSub(createTime, -1), size);
		Assert.assertTrue(ids2.size() == 0);
	}

	@Test
	public void findByIds(){
		Long guessId = 99999999L;
		String guessData = "1";
		Long consumerId = 88888888L;
		Date createTime = new Date();
		Integer size = 49;
		//创建50条consumerId递增的订单
		for (int i = 0; i < 50; i++) {
			GuessOrdersEntity e = TestUtils.createRandomBean(GuessOrdersEntity.class);
			e.setId(null);
			e.setDuibaGuessId(guessId);
			e.setGuessData(guessData);
			e.setOrderStatus(2);
			e.setExchangeStatus(1);
			e.setConsumerId(consumerId++);
			e.setGmtCreate(createTime);
			guessOrdersDao.insert(e);
		}
		List<Long> ids = guessOrdersDao.findAllWinOrderId(guessId, guessData, 88888888L, DateUtils.daysAddOrSub(createTime, -1), size);
		List<Long> subList = ids.subList(0, 15);
		List<GuessOrdersEntity> guessOrderList = guessOrdersDao.findByIds(subList);
		Assert.assertTrue(guessOrderList.size() == 15);
	}


}
