package cn.com.duiba.activity.center.api.remoteservice.activity;

import static org.junit.Assert.assertTrue;

import cn.com.duiba.activity.center.api.dto.ActivityDto;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//@Ignore
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
@Rollback(value= false)
public class RemoteOperatingActivityServiceNewTest extends TransactionalTestCaseBase {

	@Autowired
	private RemoteOperatingActivityServiceNew remoteOperatingActivityServiceNew;

	@Test
	public void testIncreaseJoinNum() {
		try {
			for (int i = 0; i < 100000; i++) {
				remoteOperatingActivityServiceNew.increaseJoinNum(1L);
			}
			Thread.sleep(3000);
		} catch (InterruptedException e) {
			
		}
		assertTrue(true);
	}

	@Test
	public void testFindActivityList(){
		Map<String,Object> maps= Maps.newHashMap();
		maps.put("start",0);
		maps.put("pageSize",50);
		remoteOperatingActivityServiceNew.findActivityList(maps);
	}
	@Test
	public void testClearAutoOffDate() {
		remoteOperatingActivityServiceNew.clearAutoOffDate(getSingleLottery());
		remoteOperatingActivityServiceNew.clearAutoOffDate(getMmanualLottery());
	}

	@Test
	public void testFindAllAliveDuibaActivityByAppId() {
		List<OperatingActivityDto> operatingActivityDtos = remoteOperatingActivityServiceNew.findAllAliveDuibaActivityByAppId(1L);
		Assert.assertTrue(operatingActivityDtos.size() > 0);
	}

	private OperatingActivityDto getSingleLottery() {
		OperatingActivityDto operatingActivityDto = new OperatingActivityDto();
		operatingActivityDto.setId(70710L);
		operatingActivityDto.setActivityId(2940L);
		operatingActivityDto.setType(3);
		operatingActivityDto.setStatus(3);
		return operatingActivityDto;
	}

	private OperatingActivityDto getMmanualLottery() {
		OperatingActivityDto operatingActivityDto = new OperatingActivityDto();
		operatingActivityDto.setId(70700L);
		operatingActivityDto.setActivityId(1149L);
		operatingActivityDto.setType(5);
		operatingActivityDto.setStatus(3);
		return operatingActivityDto;
	}

    @Test
    public void findActivityListByOperationIds() {
        Map<String, Object> param = new HashMap<>();
        param.put("operatingActivityIds", Arrays.asList(82102));
        param.put("appId", 1);
        List<ActivityDto> operatingActivityDtos =
                remoteOperatingActivityServiceNew.findActivityListByOperationIds(param);

        System.out.println(operatingActivityDtos.size());
    }

	@Test
	public void countActivityListByOperationIds() {
		Map<String, Object> param = new HashMap<>();
		param.put("operatingActivityIds", Arrays.asList(82102));
		param.put("appId", 1);
		Integer total =
				remoteOperatingActivityServiceNew.countActivityByOperationIds(param);

		System.out.println(total);
	}
}
