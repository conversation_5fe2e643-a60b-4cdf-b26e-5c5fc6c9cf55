package cn.com.duiba.activity.center.biz.remoteservice.lotterysquare;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.remoteservice.lotterysquare.RemoteLotterySquareGradientRewardService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by Liugq on 2019/1/2.
 */
public class RemoteLotterySquareGradientRewardServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteLotterySquareGradientRewardService remoteLotterySquareGradientRewardService;

    @Test
    public void testFindByInviteCount(){
        System.out.println(remoteLotterySquareGradientRewardService.findByInviteCount(6L,1).getBonusAmount());
    }
}
