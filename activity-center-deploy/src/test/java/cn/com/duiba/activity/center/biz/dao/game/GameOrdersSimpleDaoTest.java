package cn.com.duiba.activity.center.biz.dao.game;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.DeveloperActivityStatisticsEntity;
import cn.com.duiba.activity.center.biz.entity.game.GameOrdersEntity;
import cn.com.duiba.activity.center.biz.entity.game.GameOrdersSimpleEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by ZQian on 2016/8/3.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class GameOrdersSimpleDaoTest extends TransactionalTestCaseBase {

    @Resource
    private GameOrdersSimpleDao gameOrdersSimpleDao;

    private ThreadLocal<GameOrdersEntity> gameOrdersEntity=new ThreadLocal<>();

    @Before
    public void testInsert(){
        GameOrdersEntity e=new GameOrdersEntity();
        TestUtils.setRandomAttributesForBean(e,true);
        e.setCredits(0L);
        e.setStatus(2);
        gameOrdersSimpleDao.insert(e);
        gameOrdersEntity.set(e);
    }

    @Test
    public void testFind(){
        GameOrdersEntity e1 = gameOrdersEntity.get();
        GameOrdersEntity e2 = gameOrdersSimpleDao.find(e1.getId());
        assertDO(e1, e2,new String[]{"prizeType","prizeFacePrice"});
    }


    @Test
    public void testFindByIds(){
        GameOrdersEntity e = gameOrdersEntity.get();
        Assert.assertTrue(gameOrdersSimpleDao.findByIds(Arrays.asList(e.getId())).size()>0);
    }


    private void assertDO(GameOrdersEntity e, GameOrdersEntity e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","questionType","prizeOverdueDate"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }
}
