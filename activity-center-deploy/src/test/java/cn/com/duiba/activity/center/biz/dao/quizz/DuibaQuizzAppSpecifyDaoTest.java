package cn.com.duiba.activity.center.biz.dao.quizz;

import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.quizz.DuibaQuizzAppSpecifyEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ)
public class DuibaQuizzAppSpecifyDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaQuizzAppSpecifyDao duibaQuizzAppSpecifyDao;
	
	private DuibaQuizzAppSpecifyEntity info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaQuizzAppSpecifyEntity.class);
		duibaQuizzAppSpecifyDao.insert(info);
	}
	
	@Test
	public void findTest(){
		DuibaQuizzAppSpecifyEntity e = duibaQuizzAppSpecifyDao.find(info.getId());
		Assert.assertNotNull(e);
	}
	
	@Test
	public void findByDuibaQuizzIdTest(){
		List<DuibaQuizzAppSpecifyEntity> list = duibaQuizzAppSpecifyDao.findByDuibaQuizzId(info.getDuibaQuizzId());
		Assert.assertTrue(list.size() > 0);
	}
	
	@Test
	public void deleteTest(){
		DuibaQuizzAppSpecifyEntity e = TestUtils.createRandomBean(DuibaQuizzAppSpecifyEntity.class);
		duibaQuizzAppSpecifyDao.insert(e);

		duibaQuizzAppSpecifyDao.delete(e.getId());
		
		DuibaQuizzAppSpecifyEntity e1 = duibaQuizzAppSpecifyDao.find(e.getId());
		Assert.assertNull(e1);
	}
	
	@Test
	public void findByDuibaQuizzAndAppTest(){
		DuibaQuizzAppSpecifyEntity e = duibaQuizzAppSpecifyDao.findByDuibaQuizzAndApp(info.getDuibaQuizzId(), info.getAppId());
		Assert.assertNotNull(e);
	}
	
	@Test
	public void findByQuizzIdAndAppIdTest(){
		DuibaQuizzAppSpecifyEntity e = duibaQuizzAppSpecifyDao.findByDuibaQuizzAndApp(info.getDuibaQuizzId(), info.getAppId());
		Assert.assertNotNull(e);
	}
}
