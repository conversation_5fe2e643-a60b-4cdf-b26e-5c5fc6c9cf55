package cn.com.duiba.activity.center.biz.remoteservice.optionrank;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.ngame.NgameMutiRankingRecordDto;
import cn.com.duiba.activity.center.api.dto.optionrank.OptionRankRecordDto;
import cn.com.duiba.activity.center.api.remoteservice.ngame.RemoteNgameMutiRankingRecordService;
import cn.com.duiba.activity.center.api.remoteservice.optionrank.RemoteOptionRankRecordService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by sty on 12/10/17.
 */
@Transactional(DsConstants.DATABASE_DUIBA_OPTION)
public class RemoteOptionRankRecordServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteOptionRankRecordService remoteOptionRankRecordService;

    @Test
    public void insert(){
        OptionRankRecordDto record=new OptionRankRecordDto();
        record.setAppId(1L);
        record.setConsumerId(1L);
        record.setBaseConfigId(1L);
        record.setTotalNum(1L);
        Assert.assertNotNull(remoteOptionRankRecordService.insert(record));
        OptionRankRecordDto record1=new OptionRankRecordDto();
        record1.setAppId(1L);
        record1.setConsumerId(2L);
        record1.setBaseConfigId(1L);
        Assert.assertNotNull(remoteOptionRankRecordService.insert(record1));
    }

    @Test
    public void update(){
        insert();
        OptionRankRecordDto record=new OptionRankRecordDto();
        record.setAppId(1L);
        record.setConsumerId(1L);
        record.setBaseConfigId(1L);
        record.setTotalNum(3L);
        Assert.assertEquals(remoteOptionRankRecordService.updateTotalNum(record)>0,true);
    }

    @Test
    public void find(){
        insert();
        OptionRankRecordDto record=new OptionRankRecordDto();
        record.setAppId(1L);
        record.setConsumerId(1L);
        record.setBaseConfigId(1L);
        Assert.assertNotNull(remoteOptionRankRecordService.findByCidAndAppIdAndConfigId(record));
    }
}
