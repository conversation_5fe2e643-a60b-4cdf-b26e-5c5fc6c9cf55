package cn.com.duiba.activity.center.biz.dao.bet;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.enums.BetResultStatusEnum;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.bet.BetResultEntity;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 * @date 2018/05/10
 */
@Transactional(DsConstants.DATABASE_BET_ACTIVITY)
public class BetResultDaoTest extends TransactionalTestCaseBase {
    @Autowired
    private BetResultDao betResultDao;
    private BetResultEntity betResultEntity;

    @Before
    public void setup() {
        betResultEntity = new BetResultEntity();

        betResultEntity.setBetId(1L);
        betResultEntity.setBetTimes(2);
        betResultEntity.setOptionId(1L);
        betResultEntity.setOptionImg("https://img.duiba.com.cn");
        betResultEntity.setOptionName("option_name");
        betResultEntity.setRank(1);
        betResultEntity.setResultStatus(BetResultStatusEnum.BET_PROCESS.getCode());

        betResultEntity.setId(betResultDao.insert(betResultEntity));
    }

    @Test
    public void shouldInsertCorrectly() {
        assertThat(betResultEntity.getId()).isNotNull().isGreaterThan(0);
    }

    @Test
    public void whenInvokeThenShouldIncreaseBetTimes() {
        Integer count = betResultDao.increaseBetTimes(betResultEntity.getBetId(), betResultEntity.getOptionId());

        assertThat(count).isEqualTo(1);
    }

    @Test
    public void shouldCountByBetIdAndOptionId() {
        Integer count = betResultDao.countByBetIdAndOptionId(betResultEntity.getBetId(), betResultEntity.getOptionId());

        assertThat(count).isGreaterThan(0);
    }

    @Test
    public void shouldUpdateResultStatus() {
        Integer count = betResultDao.updateResultStatus(betResultEntity.getBetId(), BetResultStatusEnum.CAL_PROCESS.getCode());

        assertThat(count).isGreaterThan(0);
    }

    @Test
    public void shouldListByBetId() {
        List<BetResultEntity> betResultEntityList = betResultDao.listByBetId(betResultEntity.getBetId());

        assertThat(betResultEntityList).isNotNull();
        assertThat(betResultEntityList.size()).isGreaterThan(0);
    }

    @Test
    public void shouldListByBetIds() {
        List<Long> ids = Arrays.asList(betResultEntity.getBetId(), 0L, 2L);
        List<BetResultEntity> betResultEntityList = betResultDao.listByBetIds(ids);

        assertThat(betResultEntityList).isNotNull();
        assertThat(betResultEntityList.size()).isGreaterThan(0);
    }

//    @Test
//    public void shouldBatchUpdate() {
//        BetResultEntity entity = new BetResultEntity();
//        entity.setId(0L);
//        entity.setResultStatus(BetResultStatusEnum.CAL_SUCCESS.getCode());
//        entity.setRank(2);
//        entity.setOptionName("test111");
//        entity.setOptionImg("https://hahahah.com");
//        Integer count = betResultDao.batchUpdate(Arrays.asList(entity, betResultEntity));
//
//        assertThat(count).isGreaterThan(0);
//    }

    @Test
    public void shouldBatchInsert() {
        List<BetResultEntity> betResultEntityList = betResultDao.batchInsert(Arrays.asList(betResultEntity));

        assertThat(betResultEntityList).isNotNull();
        assertThat(betResultEntityList.get(0).getId()).isGreaterThan(0);
    }
}
