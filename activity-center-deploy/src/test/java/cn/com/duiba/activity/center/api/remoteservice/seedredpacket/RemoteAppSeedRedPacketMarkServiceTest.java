package cn.com.duiba.activity.center.api.remoteservice.seedredpacket;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.seedredpacket.SeedRedPacketUserMarkDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 18/7/17 21:31
 * @description:
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class RemoteAppSeedRedPacketMarkServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteAppSeedRedPacketMarkService remoteAppSeedRedPacketMarkService;

    @Test
    public void testAdd(){
        SeedRedPacketUserMarkDto seedRedPacketUserMarkDto = new SeedRedPacketUserMarkDto();
        seedRedPacketUserMarkDto.setActivityId(1L);
        seedRedPacketUserMarkDto.setAppId(1L);
        seedRedPacketUserMarkDto.setConsumerId(100110236L);
        remoteAppSeedRedPacketMarkService.add(seedRedPacketUserMarkDto);
    }
}
