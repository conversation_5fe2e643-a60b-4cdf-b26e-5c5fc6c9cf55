package cn.com.duiba.activity.center.api.remoteservice.guessredpacket;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.guessredpacket.NewGuessRedPacketInfoDto;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.List;

/**
 * @description: 猜红包测试类
 * @author: Zhengwei
 * @date: 2018-09-11 13:48
 */
public class RemoteGuessRedPacketInfoServiceTest extends TransactionalTestCaseBase {
    @Autowired
    RemoteGuessRedPacketInfoService remoteGuessRedPacketInfoService;

    @Test
    @Rollback(false)
    public void insert() {
        NewGuessRedPacketInfoDto dto = new NewGuessRedPacketInfoDto();
        dto.setAppId(1L);
        dto.setActivityId(81784L);
        dto.setConsumerId(1539361L);
        dto.setAmount(360L);
        dto.setBonusMax(360L);
        dto.setBonusMin(360L);
        dto.setRedPacketId(710802427060289L);
        Long id = remoteGuessRedPacketInfoService.insert(dto);
        System.out.println(id);
    }

    @Test
    public void getConsumerPacket() {
        Long appId = 1L;
        Long activityId = 81784L;
        Long consumerId = 1539361L;
        List<NewGuessRedPacketInfoDto> result = remoteGuessRedPacketInfoService.getConsumerPacket(appId, activityId, consumerId);
        System.out.print(JSON.toJSONString(result));
    }
}
