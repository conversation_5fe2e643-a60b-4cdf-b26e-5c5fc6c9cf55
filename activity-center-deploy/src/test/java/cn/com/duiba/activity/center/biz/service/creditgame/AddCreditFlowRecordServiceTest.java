package cn.com.duiba.activity.center.biz.service.creditgame;

import cn.com.duiba.activity.center.biz.dao.creditgame.PreUpdateHandler;
import cn.com.duiba.activity.center.biz.entity.creditgame.AddCreditFlowRecordEntity;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * 积分游戏DAO单元测试
 * <AUTHOR>
 * @since 2016-09-05
 */
public class AddCreditFlowRecordServiceTest extends CreditGameServiceTestCaseBase<AddCreditFlowRecordEntity> {


    @Autowired
    private AddCreditFlowRecordService addCreditFlowRecordService;

    @Override
    protected AddCreditFlowRecordEntity genEntity(){
        AddCreditFlowRecordEntity entity=new AddCreditFlowRecordEntity();
        entity.setGmtModified(new Date());
        entity.setAddCreditType(1l);
        entity.setAddCreditReason("add credit reason");
        entity.setOrderNum("123l");
        entity.setAddCreditNum(100l);
        entity.setAddCreditStatus((byte)0);
        entity.setGmtCreate(new Date());
        entity.setAppId(123l);
        return entity;
    }
    @Test
    public void testInsert(){
        doTestInsert(addCreditFlowRecordService);
    }

    @Test
    public void testQueryById(){
        doTestQueryById(addCreditFlowRecordService);
    }

    /*
    @Test
    public void testQuery(){
        doTestQuery(addCreditFlowRecordService);
    }
*/
    @Test
    public void testUpdate(){
        doTestUpdate(addCreditFlowRecordService, new PreUpdateHandler<AddCreditFlowRecordEntity>() {
            @Override
            public void preHandle(AddCreditFlowRecordEntity addCreditFlowRecordEntity) {
                addCreditFlowRecordEntity.setGmtModified(new Date());
            }
        });
    }


    @Test
    public void testDelete(){
        doTestDelete(addCreditFlowRecordService);
    }
}
