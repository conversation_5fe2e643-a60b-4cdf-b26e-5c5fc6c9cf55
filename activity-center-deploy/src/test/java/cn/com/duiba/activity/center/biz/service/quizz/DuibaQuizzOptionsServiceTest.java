package cn.com.duiba.activity.center.biz.service.quizz;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.quizz.DuibaQuizzOptionsDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ)
public class DuibaQuizzOptionsServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaQuizzOptionsService duibaQuizzOptionsService;
	
	private DuibaQuizzOptionsDto info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaQuizzOptionsDto.class);
		duibaQuizzOptionsService.insert(info);
	}
	
	@Test
	public void findTest(){
		Assert.assertNotNull(duibaQuizzOptionsService.find(info.getId()));
	}
	
	@Test
	public void findOptionsByQuizzIdTest(){
		DuibaQuizzOptionsDto e = TestUtils.createRandomBean(DuibaQuizzOptionsDto.class);
		e.setDeleted(false);
		duibaQuizzOptionsService.insert(e);
		List<DuibaQuizzOptionsDto> list = duibaQuizzOptionsService.findOptionsByQuizzId(e.getDuibaQuizzId());
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void deleteTest(){
		List<Long> ids = new ArrayList<Long>();
		ids.add(info.getId());
		duibaQuizzOptionsService.delete(ids);
		DuibaQuizzOptionsDto e = duibaQuizzOptionsService.find(info.getId());
		Assert.assertNull(e);
    }
     
	@Test
	public void updateInfoFormTest(){
		DuibaQuizzOptionsDto e = TestUtils.createRandomBean(DuibaQuizzOptionsDto.class);
		e.setId(info.getId());
		duibaQuizzOptionsService.updateInfoForm(e);
		DuibaQuizzOptionsDto e1 = duibaQuizzOptionsService.find(info.getId());
		Assert.assertEquals(e.getDescription(), e1.getDescription());
    }

//	@Test 字段不对
//    public int updateRemainingByIdTest(Long id, Integer remaining){
//    	 
//    }
}
