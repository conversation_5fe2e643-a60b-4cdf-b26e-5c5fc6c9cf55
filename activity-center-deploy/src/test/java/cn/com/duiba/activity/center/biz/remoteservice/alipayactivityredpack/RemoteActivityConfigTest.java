package cn.com.duiba.activity.center.biz.remoteservice.alipayactivityredpack;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.alipayactivityredpack.AlipayActivityConfigDto;
import cn.com.duiba.activity.center.api.enums.alipayactivityredpack.RedpackActivityStatusEnum;
import cn.com.duiba.activity.center.api.params.alipayactivityredpack.AlipayActivityConfigPageParam;
import cn.com.duiba.activity.center.api.params.alipayactivityredpack.AlipayActivityConfigParam;
import cn.com.duiba.activity.center.api.remoteservice.alipayactivityredpack.RemoteAlipayActivityConfigService;
import cn.com.duiba.api.bo.page.Page;
import cn.com.duiba.wolf.utils.DateUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RemoteActivityConfigTest.java
 * @Description
 * @createTime 2022年11月20日 21:43:00
 */
public class RemoteActivityConfigTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteAlipayActivityConfigService alipayActivityConfig;



    @Test
    public void test(){



        for (int i=255;i<257;i++){
            AlipayActivityConfigParam alipayActivityConfigParam=new AlipayActivityConfigParam();
            alipayActivityConfigParam.setRedpackActivityId(i+"");
            alipayActivityConfigParam.setRedpackActivityDescription("ceshi");
            alipayActivityConfigParam.setRedpackActivityName(i+"");
            alipayActivityConfigParam.setRedpackActivityCardTotal("50");
            alipayActivityConfigParam.setRedpackActivityRemark(i+"");
            alipayActivityConfigParam.setRedpackActivityStatus(0);
            alipayActivityConfigParam.setRedpackActivitySubject("福建兑吧");
            alipayActivityConfigParam.setRedpackActivityEndTime(DateUtils.hoursAddOrSub(new Date(), -5));
            alipayActivityConfigParam.setRedpackActivityPeriodEndTime(DateUtils.hoursAddOrSub(new Date(), -5));
            alipayActivityConfig.createActivity(alipayActivityConfigParam);
        }

        AlipayActivityConfigPageParam alipayActivityConfigPageParam = new AlipayActivityConfigPageParam();
        alipayActivityConfigPageParam.setPageNo(1);
        alipayActivityConfigPageParam.setPageSize(5);
        alipayActivityConfigPageParam.setRedpackActivityName("2");
        Page<AlipayActivityConfigDto> page = alipayActivityConfig.pageByParams(alipayActivityConfigPageParam);


        alipayActivityConfig.updateActivityStatus("2", RedpackActivityStatusEnum.REDPACK_ACTIVITY_STATUS_OVERDUE);


    }


    @Test
    public void updateActivityStatusTest(){
        alipayActivityConfig.updateActivityStatus("29", RedpackActivityStatusEnum.REDPACK_ACTIVITY_STATUS_OVERDUE);

    }

    @Test
    public void activityListByOverdueTest(){
        List<AlipayActivityConfigDto> list = alipayActivityConfig.activityListByOverdue();
        System.out.println(list);
        System.out.println("----");
    }

}
