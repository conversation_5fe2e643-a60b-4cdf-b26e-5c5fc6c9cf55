package cn.com.duiba.activity.center.api.remoteservice.miningmachine;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.miningmachine.MiningMachineDto;
import cn.com.duiba.activity.center.api.dto.scraperedpacket.MiniProgramConfig;
import cn.com.duiba.activity.center.api.dto.scraperedpacket.OfficialAccountConfig;
import cn.com.duiba.activity.center.api.dto.scraperedpacket.ScrapeRedPacketDto;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * @program: activity-all
 * @description: 挖矿机配置接口测试类
 * @author: Simba
 * @create: 2019-05-13 16:32
 **/
public class RemoteMiningMachineServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteMiningMachineService remoteMiningMachineServiceTest;

    private MiningMachineDto getDto() {
        MiningMachineDto entity = new MiningMachineDto();
        entity.setAppId(0L);
        entity.setTitle("标题");
        entity.setRule("规则");
        entity.setStartTime(new Date());
        entity.setEndTime(new Date());
        entity.setBannerImage("banner");
        entity.setSmallImage("缩略图");
        entity.setOperatingActivityId(0L);
        entity.setMiningExpend(100L);
        entity.setPkExpend(100L);
        entity.setRecoveryRate(99);
        entity.setShowCredits(1);
        entity.setDataJson("dataJson");
        return entity;
    }

    @Test
    public void testInert() {
        MiningMachineDto dto = getDto();
        MiningMachineDto miningMachineDto = remoteMiningMachineServiceTest.insert(dto, 2);
        Assert.assertNotNull(miningMachineDto.getId());
        Assert.assertNotNull(miningMachineDto.getOperatingActivityId());
    }

    @Test
    public void testUpdate() {
        MiningMachineDto dto = remoteMiningMachineServiceTest.insert(getDto(), 2);
        dto.setShowCredits(2);
        int result = remoteMiningMachineServiceTest.update(dto, 2);
        Assert.assertNotEquals(0, result);
    }

    @Test
    public void testFindById() {
        MiningMachineDto miningMachineDto = remoteMiningMachineServiceTest.insert(getDto(), 2);
        MiningMachineDto result = remoteMiningMachineServiceTest.findById(miningMachineDto.getId());
        Assert.assertNotNull(result);
    }

    @Test
    public void testFindByOperatingActivityId() {
        MiningMachineDto miningMachineDto = remoteMiningMachineServiceTest.insert(getDto(), 2);
        MiningMachineDto result = remoteMiningMachineServiceTest.findByOperatingActivityId(miningMachineDto.getOperatingActivityId());
        Assert.assertNotNull(result);
    }

    @Test
    public void testDeleteById() {
        MiningMachineDto miningMachineDto = remoteMiningMachineServiceTest.insert(getDto(), 2);
        int result = remoteMiningMachineServiceTest.deleteById(miningMachineDto.getId());
        Assert.assertNotEquals(0, result);
    }

}

