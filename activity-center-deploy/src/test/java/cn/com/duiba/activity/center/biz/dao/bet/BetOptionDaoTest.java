package cn.com.duiba.activity.center.biz.dao.bet;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.bet.BetOptionEntity;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 * @date 2018/05/02
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class BetOptionDaoTest extends TransactionalTestCaseBase {
    @Autowired
    private BetOptionDao betOptionDao;

    private BetOptionEntity betOptionEntity;
    private BetOptionEntity betOptionEntity2;

    @Before
    public void setup() {
        betOptionEntity = new BetOptionEntity();

        betOptionEntity.setOptionImg("https://duiba.com.cn");
        betOptionEntity.setOptionName("test");
        betOptionEntity.setBetId(1L);

        betOptionEntity2 = new BetOptionEntity();
        betOptionEntity2.setOptionName("test2");
        betOptionEntity2.setOptionImg("https://dui88.com.cn");
        betOptionEntity2.setBetId(2L);

        List<BetOptionEntity> list = betOptionDao.batchInsert(Arrays.asList(betOptionEntity, betOptionEntity2));
        betOptionEntity.setId(list.get(0).getId());
        betOptionEntity2.setId(list.get(1).getId());
    }


    @Test
    public void shouldBatchInsert() {
        assertThat(betOptionEntity.getId()).isGreaterThan(0);
        assertThat(betOptionEntity2.getId()).isGreaterThan(0);
    }

    @Test
    public void shouldBatchUpdateCorrectly() {
        Integer count = betOptionDao.batchUpdate(Arrays.asList(betOptionEntity2, betOptionEntity));

        assertThat(count).isEqualTo(2);
    }

    @Test
    public void shouldListByBetId() {
        List<BetOptionEntity> betOptionEntityList = betOptionDao.listByBetId(betOptionEntity.getBetId());

        assertThat(betOptionEntityList).isNotNull();
        assertThat(betOptionEntityList.size()).isGreaterThan(0);
    }

    @Test
    public void shouldListByBetIds() {
        List<Long> ids = Arrays.asList(betOptionEntity2.getBetId(), betOptionEntity.getBetId());

        List<BetOptionEntity> betOptionEntityList = betOptionDao.listByBetIds(ids);

        assertThat(betOptionEntityList).isNotNull();
        assertThat(betOptionEntityList.size()).isGreaterThan(0);
    }

    @Test
    public void shouldFindById() {
        assertThat(betOptionDao.findById(betOptionEntity.getId())).isNotNull();
    }
}
