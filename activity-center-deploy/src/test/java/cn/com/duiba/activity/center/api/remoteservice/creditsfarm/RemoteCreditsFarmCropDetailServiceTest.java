package cn.com.duiba.activity.center.api.remoteservice.creditsfarm;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmCropDetailDto;
import cn.com.duiba.activity.center.api.enums.creditsfarm.CropStatusEnum;
import cn.com.duiba.wolf.utils.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * created by liugq in 2019/08/16
 **/
public class RemoteCreditsFarmCropDetailServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteCreditsFarmCropDetailService remoteCreditsFarmCropDetailService;
    @Autowired
    private RemoteCreditsFarmUserSeedRecordService remoteCreditsFarmUserSeedRecordService;

    @Test
    public void testInitCropDetail(){
        CreditsFarmCropDetailDto cropDetailDto = new CreditsFarmCropDetailDto();
        cropDetailDto.setActId(1L);
        cropDetailDto.setAppId(1L);
        cropDetailDto.setConsumerId(1539361L);
        cropDetailDto.setPartnerUserId("1");
        cropDetailDto.setCropStatus(CropStatusEnum.CROP_STATUS_INIT.getCode());
        cropDetailDto.setCredits(10);
        cropDetailDto.setCreditsOrderNum("test_order_num1");
        cropDetailDto.setMatureTime(DateUtils.minutesAddOrSub(new Date(), 5));
        cropDetailDto.setSeedId(1l);
        cropDetailDto.setZoneNo(1);
        Long id = remoteCreditsFarmCropDetailService.insert(cropDetailDto);
        Assert.assertTrue(null !=id && id > 0);
    }

    @Test
    public void gain(){
        try {
            CreditsFarmCropDetailDto cropDetailDto = remoteCreditsFarmCropDetailService.findById(148L);
            int a =  remoteCreditsFarmCropDetailService.gain(cropDetailDto);
           System.out.println(a);
        } catch (Exception e){
           System.out.println(e);
        }
    }

    @Test
    public void testCount(){
        int result = remoteCreditsFarmCropDetailService.countByCondition(1539361L,22L,null);
        System.out.println(result);
    }

    @Test
    public void testSpeed(){
        remoteCreditsFarmCropDetailService.speed(1539361L,1L, 1200L);
    }

    @Test
    public void plantSuccess(){
        remoteCreditsFarmCropDetailService.dealAfterSubCreditsCallBack("1081517358897040140", true);
    }

    @Test
    public void testFind(){
        System.out.println(remoteCreditsFarmUserSeedRecordService.findByUnikey(1L,1L,1L));
    }
}

