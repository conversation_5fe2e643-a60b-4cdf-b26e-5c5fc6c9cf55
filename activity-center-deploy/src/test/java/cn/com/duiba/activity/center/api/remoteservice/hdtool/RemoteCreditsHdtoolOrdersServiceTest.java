package cn.com.duiba.activity.center.api.remoteservice.hdtool;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.utils.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * Created by zzy on 2017/8/15.
 */
@Transactional(DsConstants.DATABASE_CREDITS_HDTOOL)
public class RemoteCreditsHdtoolOrdersServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteCreditsHdtoolOrdersService remoteCreditsHdtoolOrdersService;

    @Test
    public void testCountByCidOaIdCreate() {
        Date now = new Date();
        DubboResult<Integer> dubboResult = remoteCreditsHdtoolOrdersService.countByCidOaIdCreate(1L, 1L, DateUtils.changeByDay(now, 30), now);
        Assert.assertTrue(dubboResult.isSuccess());
        Assert.assertTrue(dubboResult.getResult().intValue() >= 0);
    }

}
