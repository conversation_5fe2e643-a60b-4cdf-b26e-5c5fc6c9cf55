package cn.com.duiba.activity.center.biz.dao.singlelottery;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.singlelottery.AppSingleLotteryEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/20.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class SingleLotteryDaoTest extends TransactionalTestCaseBase{
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS;

    @Resource
    private SingleLotteryDao singleLotteryDao;

    private AppSingleLotteryEntity appSingleLotteryDO;

    @Before
    public void testInsert() {
        AppSingleLotteryEntity e=new AppSingleLotteryEntity(true);
        TestUtils.setRandomAttributesForBean(e,false);
        singleLotteryDao.insert(e);
        appSingleLotteryDO=e;
    }

    @Test
    public void testFind() {
        AppSingleLotteryEntity e=appSingleLotteryDO;
        AppSingleLotteryEntity e1=singleLotteryDao.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindForupdate() {
        AppSingleLotteryEntity e=appSingleLotteryDO;
        AppSingleLotteryEntity e1=singleLotteryDao.findForupdate(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindAllByIds() {
        AppSingleLotteryEntity e=appSingleLotteryDO;
        assertDO(e,singleLotteryDao.findAllByIds(Arrays.asList(e.getId())).get(0),new String[]{"vipLimits","vipLimitType"});
    }

    @Test
    public void testAddMainAppItemRemainingById() {
        singleLotteryDao.addMainAppItemRemainingById(appSingleLotteryDO.getId(),1);
        Assert.assertTrue(singleLotteryDao.find(appSingleLotteryDO.getId()).getMainAppItemRemaining().equals(appSingleLotteryDO.getMainAppItemRemaining()+1));
    }

    @Test
    public void testSubMainAppItemRemainingById() {
        singleLotteryDao.subMainAppItemRemainingById(appSingleLotteryDO.getId(),1);
        Assert.assertTrue(singleLotteryDao.find(appSingleLotteryDO.getId()).getMainAppItemRemaining().equals(appSingleLotteryDO.getMainAppItemRemaining()-1));
    }

    @Test
    public void testFindRemaingForupdate() {
        Assert.assertEquals(appSingleLotteryDO.getMainAppItemRemaining(),singleLotteryDao.findRemaingForupdate(appSingleLotteryDO.getId()));
    }

    @Test
    public void testUpdate() {
        AppSingleLotteryEntity e=new AppSingleLotteryEntity(appSingleLotteryDO.getId());
        TestUtils.setRandomAttributesForBean(e,false);
        Assert.assertTrue(singleLotteryDao.update(e)>0);
    }

    @Test
    public void testReduceMainAppItemRemaining() {
        singleLotteryDao.reduceMainAppItemRemaining(appSingleLotteryDO.getId());
        Assert.assertTrue(singleLotteryDao.find(appSingleLotteryDO.getId()).getMainAppItemRemaining().equals(appSingleLotteryDO.getMainAppItemRemaining()-1));
    }

    @Test
    public void testAddMainAppItemRemaining() {
        singleLotteryDao.addMainAppItemRemaining(appSingleLotteryDO.getId());
        Assert.assertTrue(singleLotteryDao.find(appSingleLotteryDO.getId()).getMainAppItemRemaining().equals(appSingleLotteryDO.getMainAppItemRemaining()+1));
    }

    @Test
    public void testUpdateForDevEdit() {
        AppSingleLotteryEntity e=new AppSingleLotteryEntity(appSingleLotteryDO.getId());
        TestUtils.setRandomAttributesForBean(e,false);
        Assert.assertTrue(singleLotteryDao.updateForDevEdit(e)>0);
    }

    private void assertDO(AppSingleLotteryEntity e, AppSingleLotteryEntity e1){
        assertDO(e,e1,null);
    }

    private void assertDO(AppSingleLotteryEntity e, AppSingleLotteryEntity e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","exchangeLimit"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }

}
