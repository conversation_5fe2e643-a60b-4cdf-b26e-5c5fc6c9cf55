package cn.com.duiba.activity.center.biz.service.seconds_kill;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.quizz.AddActivityDto;
import cn.com.duiba.activity.center.api.dto.seconds_kill.DuibaSecondsKillActivityDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.service.activity.OperatingActivityService;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * 
 * ClassName: DuibaSecondsKillActivityServiceTest <br/>
 * date: 2016年12月1日 下午8:10:17 <br/>
 *
 * @version 
 * @since JDK 1.6
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class DuibaSecondsKillActivityServiceTest extends TransactionalTestCaseBase {
	@Autowired
	private DuibaSecondsKillActivityService duibaSecondsKillActivityService;
	
	@Autowired
	private OperatingActivityService operatingActivityService;
	
	private DuibaSecondsKillActivityDto info;
	
	private String[] exceptFields = new String[] { "gmtCreate", "gmtModified", "autoOffDate", "autoOnDate","tag", };
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaSecondsKillActivityDto.class);
		duibaSecondsKillActivityService.insert(info);
	}
	
	@Test
	public void findTest(){
		DuibaSecondsKillActivityDto test = duibaSecondsKillActivityService.find(info.getId());
		TestUtils.assertEqualsReflect(info, test, false, exceptFields);
	}

	@Test
	public void findAllDuibaSecondKillByAppIdTest(){
		DuibaSecondsKillActivityDto t1 = TestUtils.createRandomBean(DuibaSecondsKillActivityDto.class);
		t1.setDeleted(false);
		t1.setStatus(1);
		duibaSecondsKillActivityService.insert(t1);
		OperatingActivityDto t2 = TestUtils.createRandomBean(OperatingActivityDto.class);
		t2.setActivityId(t1.getId());
		t2.setDeleted(false);
		t2.setType(30);
		operatingActivityService.insert(t2);
		List<AddActivityDto> list = duibaSecondsKillActivityService.findAllDuibaSecondKillByAppId(t2.getAppId());
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void findAllByIdsTest(){
		List<Long> ids = new ArrayList<Long>();
		ids.add(info.getId());
		List<DuibaSecondsKillActivityDto> list = duibaSecondsKillActivityService.findAllByIds(ids);
		TestUtils.assertEqualsReflect(info, list.get(0), false, exceptFields);
	}

	@Test
	public void findAutoOffTest(){
		DuibaSecondsKillActivityDto e = TestUtils.createRandomBean(DuibaSecondsKillActivityDto.class);
		e.setDeleted(false);
		e.setStatus(1);
		Calendar calendar = Calendar.getInstance();
    	calendar.setTime(new Date());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	e.setAutoOffDate(calendar.getTime());
		duibaSecondsKillActivityService.insert(e);
		List<DuibaSecondsKillActivityDto> list = duibaSecondsKillActivityService.findAutoOff();
    	Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void changeStatusTest(){
		DuibaSecondsKillActivityDto e = TestUtils.createRandomBean(DuibaSecondsKillActivityDto.class);
		duibaSecondsKillActivityService.changeStatus(info.getId(), e.getStatus());
		int status = duibaSecondsKillActivityService.find(info.getId()).getStatus();
		Assert.assertTrue(status == e.getStatus());
	}

	@Test
	public void updateAutoOffDateNullTest(){
		duibaSecondsKillActivityService.updateAutoOffDateNull(info.getId());
		DuibaSecondsKillActivityDto e = duibaSecondsKillActivityService.find(info.getId());
		Assert.assertNull(e.getAutoOffDate());
		Assert.assertNull(e.getAutoOnDate());
	}

	@Test
	public void findByPageTest(){
		DuibaSecondsKillActivityDto e = TestUtils.createRandomBean(DuibaSecondsKillActivityDto.class);
		e.setDeleted(false);
		duibaSecondsKillActivityService.insert(e);
		Map<String, Object> map = new HashMap<>();
		map.put("title", e.getTitle());
		map.put("id", e.getId());
		map.put("offset", 0);
		map.put("max", 10);
		List<DuibaSecondsKillActivityDto> list = duibaSecondsKillActivityService.findByPage(map);
		TestUtils.assertEqualsReflect(e, list.get(0), false, exceptFields);
	}

	@Test
	public void countTest(){
		DuibaSecondsKillActivityDto e = TestUtils.createRandomBean(DuibaSecondsKillActivityDto.class);
		e.setDeleted(false);
		duibaSecondsKillActivityService.insert(e);
		Map<String, Object> map = new HashMap<>();
		map.put("title", e.getTitle());
		map.put("id", e.getId());
		int count = duibaSecondsKillActivityService.count(map);
		Assert.assertTrue(count > 0);
	}

	@Test
	public void deleteByIdTest(){
		duibaSecondsKillActivityService.deleteById(info.getId());
		DuibaSecondsKillActivityDto e = duibaSecondsKillActivityService.find(info.getId());
		Assert.assertTrue(e.getDeleted());
	}

	@Test
	public void updateSwitchesTest(){
		DuibaSecondsKillActivityDto e = TestUtils.createRandomBean(DuibaSecondsKillActivityDto.class);
		duibaSecondsKillActivityService.updateSwitches(info.getId(), e.getSwitches());
		long switches = duibaSecondsKillActivityService.find(info.getId()).getSwitches();
		Assert.assertTrue(switches == e.getSwitches());
	}

	@Test
	public void updateTest(){
		DuibaSecondsKillActivityDto e = TestUtils.createRandomBean(DuibaSecondsKillActivityDto.class);
		e.setId(info.getId());
		duibaSecondsKillActivityService.update(e);
		DuibaSecondsKillActivityDto test = duibaSecondsKillActivityService.find(info.getId());
		TestUtils.assertEqualsReflect(e, test, false, exceptFields);
	}
}
