package cn.com.duiba.activity.center.biz.service.impl;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duibabiz.component.domain.DomainService;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/** 
 * ClassName:ActivityPluginService.java <br/>
 * <AUTHOR> 
 * @date 创建时间：2016年10月12日 下午5:11:05 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class DomainServiceTest extends TransactionalTestCaseBase {

	@Resource
	private DomainService domainService;


	@Test
	public void testDomainService(){
		Assert.assertNotNull(domainService.getSystemDomain(1L));
		Assert.assertNotNull(domainService.getSystemDomain(333L));
	}

}
