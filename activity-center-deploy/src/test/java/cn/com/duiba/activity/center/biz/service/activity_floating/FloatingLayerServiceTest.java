package cn.com.duiba.activity.center.biz.service.activity_floating;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity_floating.FloatingLayerDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

import com.google.common.collect.Maps;

/** 
 * ClassName:FloatingLayerServiceTest.java <br/>
 * <AUTHOR> 
 * @date 创建时间：2017年8月10日 上午11:34:34 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class FloatingLayerServiceTest extends TransactionalTestCaseBase {
	/**
	 * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
	 */
	protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_ACT_COM_CONF;
	@Resource
	private FloatingLayerService floatingLayerService;
	private FloatingLayerDto dto;
	
	@Before
	public void testInsert(){
		dto = new FloatingLayerDto();
		TestUtils.setRandomAttributesForBean(dto, false);
		dto.setId(1L);
		floatingLayerService.saveOrUpdateFloatingLayer(dto);
	}
	
	@Test
	public void testFindFloatingLayerList(){
		Map<String, Object> paramMap = Maps.newHashMap();
		List<FloatingLayerDto> list = floatingLayerService.findFloatingLayerList(paramMap);
		Assert.assertNotNull(list);
	}
	
	@Test
	public void testFindById(){
		FloatingLayerDto dtos = floatingLayerService.findById(1L);
		Assert.assertNotNull(dtos);
	}
	@Test
	public void testFindFloatingLayerInfo(){
		FloatingLayerDto list = floatingLayerService.findFloatingLayerInfo(dto.getId(), "hdtool");
		Assert.assertNotNull(list);
	}
	@Test
	public void testFindFloatingLayerCount(){
		Integer id = floatingLayerService.findFloatingLayerCount();
		Assert.assertNotNull(id);
	}
}
