package cn.com.duiba.activity.center.biz.service.plugin;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.plugin.ActivityPluginDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

/** 
 * ClassName:ActivityPluginService.java <br/>
 * <AUTHOR> 
 * @date 创建时间：2016年10月12日 下午5:11:05 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class ActivityPluginServiceTest extends TransactionalTestCaseBase {

	/**
	 * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
	 */
	protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS_ACTIVITY;
	@Resource
	private ActivityPluginService activityPluginService;
	private ActivityPluginDto dto;
	
	@Before
	public void testInsert(){
		ActivityPluginDto activityPluginDto =new ActivityPluginDto();
		TestUtils.setRandomAttributesForBean(activityPluginDto, false);
		activityPluginDto.setSignType(2);
		activityPluginDto.setStatus(1);
		activityPluginDto.setTriggerTpye(1);
		activityPluginDto.setLimitScope(1);
		activityPluginDto.setExitDays(10);

		dto =activityPluginService.createActivityPluginInfo(activityPluginDto);
	}
	
	@Test
	public void testupdateActivityPluginInfo(){
		ActivityPluginDto activityPluginDto =new ActivityPluginDto();
		activityPluginDto.setId(dto.getId());
		activityPluginDto.setTitle("asdf");
		activityPluginDto.setActivityRelate("21");
		activityPluginService.updateActivityPluginInfo(activityPluginDto);
		ActivityPluginDto ent=activityPluginService.findById(dto.getId());
		Assert.assertEquals("asdf", ent.getTitle());
	}

	@Test
	public void testOpenStatus(){
		activityPluginService.OpenStatus(dto.getId(), ActivityPluginDto.STATUS_CLOSE_SHOW);
	}
	
	@Test
	public void testfindActivityPluginInfoList(){
		Map<String, Object> paramMap =new HashMap<>();
		paramMap.put("offset", 0);
		paramMap.put("max", 10);
		List<ActivityPluginDto> list =activityPluginService.findActivityPluginInfoList(paramMap);
		Assert.assertTrue(list.size() > 0);
	}
	
	@Test
	public void testfindPageCount(){
		Map<String, Object> paramMap =new HashMap<>();
		long count = activityPluginService.findPageCount(paramMap);
		Assert.assertTrue(count > 0);
	}
}
