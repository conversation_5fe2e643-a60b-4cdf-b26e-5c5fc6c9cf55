package cn.com.duiba.activity.center.biz.dao.carousel;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.carousel.CarouselActivityRecordDto;
import cn.com.duiba.activity.center.api.dto.carousel.CarouselItemRecordDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author: xuwei
 * @Date: 2019/8/14 20:32
 */
@Transactional(DsConstants.DATABASE_ACT_RECORD)
public class CarouselTest extends TransactionalTestCaseBase {
    @Autowired
    private CarouselActivityRecordDao carouselActivityRecordDao;
    @Autowired
    private CarouselItemRecordDao carouselItemRecordDao;

    @Test
    public void testInsert() {
        CarouselActivityRecordDto activityRecordDto = new CarouselActivityRecordDto();
        activityRecordDto.setAppId(1L);
        activityRecordDto.setActivityId(1L);
        activityRecordDto.setActivityType(ActivityUniformityTypeEnum.HappyCode.getCode());
        activityRecordDto.setPrizeName("prize");
        activityRecordDto.setConsumerName("123456");
        carouselActivityRecordDao.insert(activityRecordDto);

        CarouselItemRecordDto itemRecordDto = new CarouselItemRecordDto();
        itemRecordDto.setAppId(1L);
        itemRecordDto.setAppItemId(1L);
        itemRecordDto.setExchangeType(0);
        itemRecordDto.setConsumerName("123456");
        carouselItemRecordDao.insert(itemRecordDto);
    }
}
