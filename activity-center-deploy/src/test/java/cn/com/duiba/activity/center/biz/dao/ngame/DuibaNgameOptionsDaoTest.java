package cn.com.duiba.activity.center.biz.dao.ngame;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.ngame.DuibaNgameOptionsEntity;

@Transactional(DsConstants.DATABASE_NGAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaNgameOptionsDaoTest extends TransactionalTestCaseBase {

	@Resource
	private DuibaNgameOptionsDao duibaNgameOptionsDao;

	@Test
	public void findByGameIdTest() {
		List<DuibaNgameOptionsEntity> list = duibaNgameOptionsDao.findByGameId(23l);
		System.out.println(list.size()+",,,,");
	}
	
	@Test
	public void addBatchTest() {
		List<DuibaNgameOptionsEntity> list = new ArrayList<DuibaNgameOptionsEntity>();
		DuibaNgameOptionsEntity duibaNgameOptionsDto = new DuibaNgameOptionsEntity();
		duibaNgameOptionsDto.setDuibaGameId(24l);
		duibaNgameOptionsDto.setPayload(1);
		duibaNgameOptionsDto.setAutoOpen(false);
		list.add(duibaNgameOptionsDto);
		duibaNgameOptionsDto = new DuibaNgameOptionsEntity();
		duibaNgameOptionsDto.setDuibaGameId(24l);
		duibaNgameOptionsDto.setPayload(2);
		duibaNgameOptionsDto.setAutoOpen(true);
		list.add(duibaNgameOptionsDto);
		duibaNgameOptionsDao.addBatch(list);
	}
	
	@Test
	public void addTest() {
		DuibaNgameOptionsEntity duibaNgameOptionsDto = new DuibaNgameOptionsEntity();
		duibaNgameOptionsDto.setDuibaGameId(24l);
		duibaNgameOptionsDto.setPayload(3);
		duibaNgameOptionsDto.setAutoOpen(false);
		duibaNgameOptionsDao.add(duibaNgameOptionsDto);
	}
	
	@Test
	public void deleteTest() {
		duibaNgameOptionsDao.delete(38l);
	}
	
	@Test
	public void updateTest() {
		DuibaNgameOptionsEntity duibaNgameOptionsDto = duibaNgameOptionsDao.find(38l);
		duibaNgameOptionsDto.setPayload(4);
		duibaNgameOptionsDao.update(duibaNgameOptionsDto);
	}
	
	@Test
	public void countBigPrizeByGameIdTest() {
		System.out.println(duibaNgameOptionsDao.countBigPrizeByGameId(24l));
	}
}
