package cn.com.duiba.activity.center.biz.service.singlelottery;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.singlelottery.AppSingleLotteryDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/20.
 */
@Transactional(DsConstants.DATABASE_CREDITS)
public class SingleLotteryServiceTest extends TransactionalTestCaseBase{
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS;

    @Resource
    private SingleLotteryService singleLotteryService;

    private AppSingleLotteryDto appSingleLotteryDO;

    @Before
    public void testInsert() {
        AppSingleLotteryDto e=new AppSingleLotteryDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        singleLotteryService.insert(e);
        appSingleLotteryDO=e;
    }

    @Test
    public void testFind() {
        AppSingleLotteryDto e=appSingleLotteryDO;
        AppSingleLotteryDto e1=singleLotteryService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindForupdate() {
        AppSingleLotteryDto e=appSingleLotteryDO;
        AppSingleLotteryDto e1=singleLotteryService.findForupdate(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindAllByIds() {
        AppSingleLotteryDto e=appSingleLotteryDO;
        assertDO(e,singleLotteryService.findAllByIds(Arrays.asList(e.getId())).get(0),new String[]{"vipLimits","vipLimitType"});
    }

    @Test
    public void testAddMainAppItemRemainingById() {
        singleLotteryService.addMainAppItemRemainingById(appSingleLotteryDO.getId(),1);
        Assert.assertTrue(singleLotteryService.find(appSingleLotteryDO.getId()).getMainAppItemRemaining().equals(appSingleLotteryDO.getMainAppItemRemaining()+1));
    }

    @Test
    public void testSubMainAppItemRemainingById() {
        singleLotteryService.subMainAppItemRemainingById(appSingleLotteryDO.getId(),1);
        Assert.assertTrue(singleLotteryService.find(appSingleLotteryDO.getId()).getMainAppItemRemaining().equals(appSingleLotteryDO.getMainAppItemRemaining()-1));
    }

    @Test
    public void testFindRemaingForupdate() {
        Assert.assertEquals(appSingleLotteryDO.getMainAppItemRemaining(),singleLotteryService.findRemaingForupdate(appSingleLotteryDO.getId()));
    }

    @Test
    public void testUpdate() {
        AppSingleLotteryDto e=new AppSingleLotteryDto(appSingleLotteryDO.getId());
        TestUtils.setRandomAttributesForBean(e,false);
        Assert.assertTrue(singleLotteryService.update(e)>0);
    }

    @Test
    public void testReduceMainAppItemRemaining() {
        singleLotteryService.reduceMainAppItemRemaining(appSingleLotteryDO.getId());
        Assert.assertTrue(singleLotteryService.find(appSingleLotteryDO.getId()).getMainAppItemRemaining().equals(appSingleLotteryDO.getMainAppItemRemaining()-1));
    }

    @Test
    public void testAddMainAppItemRemaining() {
        singleLotteryService.addMainAppItemRemaining(appSingleLotteryDO.getId());
        Assert.assertTrue(singleLotteryService.find(appSingleLotteryDO.getId()).getMainAppItemRemaining().equals(appSingleLotteryDO.getMainAppItemRemaining()+1));
    }

    @Test
    public void testUpdateForDevEdit() {
        AppSingleLotteryDto e=new AppSingleLotteryDto(appSingleLotteryDO.getId());
        TestUtils.setRandomAttributesForBean(e,false);
        Assert.assertTrue(singleLotteryService.updateForDevEdit(e)>0);
    }
    @Test
    public void testFindAutoOff() {
        AppSingleLotteryDto e=new AppSingleLotteryDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setAutoOffDate(new Date());
        singleLotteryService.insert(e);
        Assert.assertTrue(singleLotteryService.doAutoOffDateScan()==0);
    }

    private void assertDO(AppSingleLotteryDto e, AppSingleLotteryDto e1){
        assertDO(e,e1,null);
    }


    private void assertDO(AppSingleLotteryDto e, AppSingleLotteryDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","exchangeLimit"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()),DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()),DateUtils.getDayStr( e1.getGmtModified()));
    }
}
