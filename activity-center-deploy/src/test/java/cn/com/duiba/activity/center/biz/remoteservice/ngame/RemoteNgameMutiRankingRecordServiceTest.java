package cn.com.duiba.activity.center.biz.remoteservice.ngame;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.ngame.NgameMutiRankingRecordDto;
import cn.com.duiba.activity.center.api.remoteservice.ngame.RemoteNgameMutiRankingRecordService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by sty on 12/10/17.
 */
@Transactional(DsConstants.DATABASE_NGAME)
public class RemoteNgameMutiRankingRecordServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteNgameMutiRankingRecordService remoteNgameMutiRankingRecordService;

    @Test
    public void insert(){
        NgameMutiRankingRecordDto record=new NgameMutiRankingRecordDto();
        record.setAppId(1L);
        record.setConsumerId(1L);
        record.setConfigBaseInfoId(1L);
        record.setTotalScore(1L);
        Assert.assertNotNull(remoteNgameMutiRankingRecordService.insert(record));
        NgameMutiRankingRecordDto record1=new NgameMutiRankingRecordDto();
        record1.setAppId(1L);
        record1.setConsumerId(2L);
        record1.setConfigBaseInfoId(1L);
        Assert.assertNotNull(remoteNgameMutiRankingRecordService.insert(record1));
    }

    @Test
    public void update(){
        insert();
        NgameMutiRankingRecordDto record=new NgameMutiRankingRecordDto();
        record.setAppId(1L);
        record.setConsumerId(1L);
        record.setConfigBaseInfoId(1L);
        record.setTotalScore(3L);
        Assert.assertEquals(remoteNgameMutiRankingRecordService.updateTotalScore(record)>0,true);
    }

    @Test
    public void find(){
        insert();
        NgameMutiRankingRecordDto record=new NgameMutiRankingRecordDto();
        record.setAppId(1L);
        record.setConsumerId(1L);
        record.setConfigBaseInfoId(1L);
        Assert.assertNotNull(remoteNgameMutiRankingRecordService.findByCidAndAppIdAndConfigId(record));
    }
}
