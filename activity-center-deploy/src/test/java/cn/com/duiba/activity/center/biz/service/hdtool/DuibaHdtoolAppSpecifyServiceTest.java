package cn.com.duiba.activity.center.biz.service.hdtool;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolAppSpecifyDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created by yansen on 16/5/24.
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaHdtoolAppSpecifyServiceTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaHdtoolAppSpecifyService duibaHdtoolAppSpecifyService;

    private HdtoolAppSpecifyDto hdtoolAppSpecifyDto;

    @Before
    public void testInsert(){
        HdtoolAppSpecifyDto e=new HdtoolAppSpecifyDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        duibaHdtoolAppSpecifyService.insertSpecify(e);
        hdtoolAppSpecifyDto = e;
        Assert.assertNotNull(e.getId());
    }

    @Test
    public void testFindAllSpecifyByHdToolId() {
        Assert.assertTrue(duibaHdtoolAppSpecifyService.findAllSpecifyByHdToolId(hdtoolAppSpecifyDto.getDuibaHdtoolId()).size()>0);
    }

    @Test
    public void testFindSpecifyByHdToolIdsAndApp() {
        List<Long> duibaHdToolIds = Collections.singletonList(hdtoolAppSpecifyDto.getDuibaHdtoolId());

        Assert.assertTrue(duibaHdtoolAppSpecifyService.findSpecifyByHdToolIdsAndApp(duibaHdToolIds, hdtoolAppSpecifyDto.getAppId()).size()>0);
    }

    @Test
    public void testFindSpecifyByHdToolIdAndApp() {
        Assert.assertNotNull(duibaHdtoolAppSpecifyService.findSpecifyByHdToolIdAndApp(hdtoolAppSpecifyDto.getDuibaHdtoolId(), hdtoolAppSpecifyDto.getAppId()));
        duibaHdtoolAppSpecifyService.deleteSpecifyById(hdtoolAppSpecifyDto.getId());
        Assert.assertNull(duibaHdtoolAppSpecifyService.findSpecifyByHdToolIdAndApp(hdtoolAppSpecifyDto.getDuibaHdtoolId(), hdtoolAppSpecifyDto.getAppId()));
    }

    @Test
    public void testFindSpecifyById() {
        Assert.assertNotNull(duibaHdtoolAppSpecifyService.findSpecifyById(hdtoolAppSpecifyDto.getId()));
    }

    @Test
    public void testFindAllSpecifyMap() {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("hdtoolType", hdtoolAppSpecifyDto.getHdtoolType());
        queryMap.put("appId", hdtoolAppSpecifyDto.getAppId());
        Assert.assertNotNull(duibaHdtoolAppSpecifyService.findAllSpecifyMap(queryMap).size() > 0);
    }

    @Test
    public void testFindAllSpecifyByCredordCount() {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("hdtoolType", hdtoolAppSpecifyDto.getHdtoolType());
        queryMap.put("appId", hdtoolAppSpecifyDto.getAppId());
        Assert.assertNotNull(duibaHdtoolAppSpecifyService.findAllSpecifyByCredordCount(queryMap) > 0);
    }

    @Test
    public void testDeleteSpecifyById() {
        duibaHdtoolAppSpecifyService.deleteSpecifyById(hdtoolAppSpecifyDto.getId());
        Assert.assertNull(duibaHdtoolAppSpecifyService.findSpecifyById(hdtoolAppSpecifyDto.getId()));
    }

}
