package cn.com.duiba.activity.center.biz.remoteservice.activity;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.bet.BetConfigDto;
import cn.com.duiba.activity.center.api.enums.KVSpaceEnum;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteActivityConsumerLimitService;
import cn.com.duiba.activity.center.api.remoteservice.bet.RemoteBetConfigService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.utils.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by zzy on 2017/2/16.
 */
@Transactional(DsConstants.DATABASE_CKVTABLE)
public class RemoteActivityConsumerLimitServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteActivityConsumerLimitService remoteActivityConsumerLimitService;


    @Autowired
    private RemoteBetConfigService remoteBetConfigService;

    @Test
    public void testfFndConPluginFirstJoin() {
        DubboResult<String> dubboResult = remoteActivityConsumerLimitService.findConPluginFirstJoin(1L, "abcde-test1", KVSpaceEnum.KV_SPACE_AW.getSpace());
        Assert.assertEquals(true, dubboResult.isSuccess());
        Assert.assertEquals(null, dubboResult.getResult());
    }

    @Test
    public void testSetConPluginFirstJoin() {
        DubboResult<Boolean> dubboResult = remoteActivityConsumerLimitService.setConPluginFirstJoin(1L, "abcde-test2","test-value2", KVSpaceEnum.KV_SPACE_AW.getSpace());
        Assert.assertEquals(true, dubboResult.isSuccess());
        Assert.assertEquals(true, dubboResult.getResult().booleanValue());
        DubboResult<String> dubboResult2 = remoteActivityConsumerLimitService.findConPluginFirstJoin(1L, "abcde-test2", KVSpaceEnum.KV_SPACE_AW.getSpace());
        Assert.assertEquals(true, dubboResult2.isSuccess());
        Assert.assertEquals("test-value2", dubboResult2.getResult());
    }


    @Test
    public void updateEndTime() {
        BetConfigDto betConfigDto = new BetConfigDto();
        betConfigDto.setEndTime(DateUtils.getSecondDate("2019-10-10 12:12:12"));
        betConfigDto.setId(401L);
        try {
            remoteBetConfigService.updateEndTime(betConfigDto);
        } catch (BizException e) {
            e.printStackTrace();
        }
    }
}
