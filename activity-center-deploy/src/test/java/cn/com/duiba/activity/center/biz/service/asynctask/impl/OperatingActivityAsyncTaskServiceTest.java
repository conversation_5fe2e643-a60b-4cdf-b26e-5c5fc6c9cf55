package cn.com.duiba.activity.center.biz.service.asynctask.impl;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.service.asynctask.OperatingActivityAsyncTaskService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by sty on 11/23/17.
 */
@Rollback(false)
public class OperatingActivityAsyncTaskServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private OperatingActivityAsyncTaskService operatingActivityAsyncTaskService;

    @Test
    public void testIncreaseJoinNum(){
        operatingActivityAsyncTaskService.increaseJoinNum(1L);
        operatingActivityAsyncTaskService.increaseJoinNum(1L);
        operatingActivityAsyncTaskService.increaseJoinNum(3L);
        operatingActivityAsyncTaskService.increaseJoinNum(1L);
        operatingActivityAsyncTaskService.increaseJoinNum(5L);

    }
}
