package cn.com.duiba.activity.center.api.remoteservice.quizz;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.quizz.DuibaQuizzOptionsDto;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.service.exception.BusinessException;
import org.junit.Test;
import org.springframework.test.annotation.Rollback;

import javax.annotation.Resource;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/2/19 14:43
 * @description:
 */
@Rollback(value = false)
public class RemoteQuizzStockServiceTest extends TransactionalTestCaseBase {

	@Resource
	private RemoteQuizzStockService remoteQuizzStockService;

	@Test
	public void testUpdateStockByOptions() throws BusinessException{
		DuibaQuizzOptionsDto duibaQuizzOptionsDto = new DuibaQuizzOptionsDto();
		duibaQuizzOptionsDto.setId(600L);
		duibaQuizzOptionsDto.setNewOptionCount(2);
		remoteQuizzStockService.updateStockByOptions(duibaQuizzOptionsDto, new DuibaQuizzOptionsDto());
	}

	@Test
	public void testUpdateStockByOptionsApi() throws BizException{
		DuibaQuizzOptionsDto duibaQuizzOptionsDto = new DuibaQuizzOptionsDto();
		duibaQuizzOptionsDto.setId(600L);
		duibaQuizzOptionsDto.setNewOptionCount(1);
		remoteQuizzStockService.updateStockByOptionsApi(duibaQuizzOptionsDto, new DuibaQuizzOptionsDto());
	}
}
