package cn.com.duiba.activity.center.biz.dao.scraperedpacket;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.entity.scraperedpacket.ScrapeRedPacketDetailEntity;
import com.alibaba.fastjson.JSONObject;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

public class ScrapeRedPacketDetailDaoTest extends TransactionalTestCaseBase {

    @Autowired
    private ScrapeRedPacketDetailDao scrapeRedPacketDetailDao;

    @Test
    public void testInsertAndFind() {
        Long id = scrapeRedPacketDetailDao.insert(newEntity());
        Assert.assertTrue(id > 0);
        ScrapeRedPacketDetailEntity entity = scrapeRedPacketDetailDao.findById(1L);
        Assert.assertNotNull(entity);
        System.out.println(JSONObject.toJSON(entity));

    }

    private ScrapeRedPacketDetailEntity newEntity() {
        ScrapeRedPacketDetailEntity entity = new ScrapeRedPacketDetailEntity();
        entity.setActivityId(0L);
        entity.setAppId(0L);
        entity.setConsumerId(0L);
        entity.setRedPacketLoc(0);
        entity.setRedPacketCode("");
        entity.setInviteFriends(0);
        entity.setAllFriends(0);
        entity.setAmountReceived(0);
        entity.setDetailStatus(0);
        entity.setMultipleCard("1.1");
        entity.setGmtCreate(new Date());
        entity.setGmtModified(new Date());
        return entity;
    }
}
