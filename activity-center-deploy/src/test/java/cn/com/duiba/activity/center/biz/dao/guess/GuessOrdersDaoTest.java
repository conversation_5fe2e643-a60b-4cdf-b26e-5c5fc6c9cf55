package cn.com.duiba.activity.center.biz.dao.guess;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.guess.GuessOrdersEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class GuessOrdersDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private GuessOrdersDao guessOrdersDao;
	
	private GuessOrdersEntity info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(GuessOrdersEntity.class);
		info.setOrderStatus(10);
		info.setExchangeStatus(10);
		info.setIsGivePrize(10);
		guessOrdersDao.insert(info);
	}
	
	@Test
	public void findWinByGuessIdAndPrizeIdTest(){
		List<GuessOrdersEntity> list = guessOrdersDao.findWinByGuessIdAndPrizeId(info.getDuibaGuessId(), info.getPrizeId());
		Assert.assertTrue(list.size() > 0);
	}

    /**
     * 精确匹配获取中奖名单
     * @param duibaGuessId
     * @param num
     * @return
     */
	@Test
    public void findWinOrderByExactMatchTest(){
    	GuessOrdersEntity e = TestUtils.createRandomBean(GuessOrdersEntity.class);
    	e.setOrderStatus(2);
    	e.setExchangeStatus(1);
		e.setIsGivePrize(10);
    	guessOrdersDao.insert(e);
    	List<String> winIds = new ArrayList<String>();
    	winIds.add(e.getGuessRandomNum());
    	List<GuessOrdersEntity> list = guessOrdersDao.findWinOrderByExactMatch(e.getDuibaGuessId(), winIds, e.getGuessData());
    	Assert.assertTrue(list.size() > 0);
    }
    /**
     * 模糊匹配获取中奖名单
     * @param duibaGuessId
     * @param num
     * @return
     */
	@Test
    public void findWinOrderByVagueMatchTest(){
    	GuessOrdersEntity e = TestUtils.createRandomBean(GuessOrdersEntity.class);
    	e.setOrderStatus(2);
    	e.setExchangeStatus(1);
    	e.setGuessRandomNum("100");
		e.setIsGivePrize(10);
    	guessOrdersDao.insert(e);
    	List<GuessOrdersEntity> list = guessOrdersDao.findWinOrderByVagueMatch(e.getDuibaGuessId(), 10, "10", e.getGuessData());
    	Assert.assertTrue(list.size() > 0);
    }

    //from manager
	@Test
    public void findExpireOrderTest(){
    	GuessOrdersEntity e = TestUtils.createRandomBean(GuessOrdersEntity.class);
    	e.setOrderStatus(2);
    	e.setExchangeStatus(2);
		e.setIsGivePrize(10);
    	Calendar calendar = Calendar.getInstance();
    	calendar.setTime(e.getPrizeOverdueDate());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	Date start = calendar.getTime();
    	e.setPrizeOverdueDate(start);
    	guessOrdersDao.insert(e);
    	List<GuessOrdersEntity> list = guessOrdersDao.findExpireOrder();
    	Assert.assertTrue(list.size() > 0);
    }

    /**
     * 精确匹配获取中奖名单
     * @param duibaGuessId
     * @param num
     * @return
     */
	@Test
    public void findWinOrderByExactMatch2Test(){
    	GuessOrdersEntity e = TestUtils.createRandomBean(GuessOrdersEntity.class);
    	e.setOrderStatus(2);
		e.setExchangeStatus(10);
		e.setIsGivePrize(10);
    	guessOrdersDao.insert(e);
    	List<String> winIds = new ArrayList<String>();
    	winIds.add(e.getGuessRandomNum());
    	List<GuessOrdersEntity> list = guessOrdersDao.findWinOrderByExactMatch(e.getDuibaGuessId(), winIds, e.getGuessData(), 0, 10, e.getConsumerId());
    	Assert.assertTrue(list.size() > 0);
    }
	
    /**
     * 模糊匹配获取中奖名单
     * @param duibaGuessId
     * @param num
     * @return
     */
	@Test
    public void findWinOrderByVagueMatch2Test(){
    	GuessOrdersEntity e = TestUtils.createRandomBean(GuessOrdersEntity.class);
    	e.setOrderStatus(2);
    	e.setExchangeStatus(1);
    	e.setGuessRandomNum("100");
		e.setIsGivePrize(10);
    	guessOrdersDao.insert(e);
    	List<GuessOrdersEntity> list = guessOrdersDao.findWinOrderByVagueMatch(e.getDuibaGuessId(), 10, "10", e.getGuessData(), 0, 10, e.getConsumerId());
    	Assert.assertTrue(list.size() > 0);
    }

}
