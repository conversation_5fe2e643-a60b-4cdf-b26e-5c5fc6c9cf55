package cn.com.duiba.activity.center.biz.service.game;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.game.GameOptionsDuibaEntity;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by hwq on 16/5/26.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class GameOptionsDuibaServiceTest extends TransactionalTestCaseBase {

    @Resource
    private GameOptionsDuibaService gameOptionsDuibaService;

    private GameOptionsDuibaEntity entity;

//    @Before
//    public void testAdd(){
//        GameOptionsDuibaEntity e=new GameOptionsDuibaEntity();
//        TestUtils.setRandomAttributesForBean(e,false);
//        e.setDeleted(false);
//        gameOptionsDuibaService.(e);
//        entity=e;
//    }

    @Test
    public void testFind() {
        gameOptionsDuibaService.find(1L);
    }

    @Test
    public void testFindByAutoOpen() {
        gameOptionsDuibaService.findByAutoOpen(1L, true);
    }

    @Test
    public void testFindByGameId() {
        gameOptionsDuibaService.findByGameId(1L);
    }

    @Test
    public void testFindByGameIds() {
        List<Long> ids = new ArrayList<>();
        ids.add(1L);
        gameOptionsDuibaService.findByGameIds(ids);
    }

}
