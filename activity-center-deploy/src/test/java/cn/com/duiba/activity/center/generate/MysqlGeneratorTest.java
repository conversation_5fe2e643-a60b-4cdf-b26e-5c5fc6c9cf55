package cn.com.duiba.activity.center.generate;

import cn.com.duiba.mysql.generator.MybatisGenerator;
import cn.com.duiba.mysql.generator.entity.GeneratorConfig;
import org.junit.Test;

import java.text.MessageFormat;

/**
 * 根据数据库表生成代码
 */
public class MysqlGeneratorTest {
	//库名
	private static final String TABLE_SCHEMA = "duiba";
	//表名
	private static final String TABLE_NAME = "tb_single_award_fast_record";

	private static final String PACKAGE_SUFFER = "cn.com.duiba.activity.center.biz.";
	private static final String PACKAGE_SUFFER_API = "cn.com.duiba.activity.center.api.";
	//生成entity代码位置
	private static final String ENTITY_PACKAGE = "cn.com.duiba.activity.center.biz.entity.singleAward";
	//生成dao代码位置
	private static final String DAO_PACKAGE = "cn.com.duiba.activity.center.biz.dao.singleAward";
	//mapper.xml文件位置
	private static final String XML_LOCATION = "src/main/resources/mybatis/act_com_conf";

	private static final String DB_URL_TPL = "***************************/{0}?createDatabaseIfNotExist=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true&connectTimeout=5000&socketTimeout=60000";

	@Test
	public void generateCode(){
		String DB_URL = MessageFormat.format(DB_URL_TPL, TABLE_SCHEMA);
		GeneratorConfig config = new GeneratorConfig(DB_URL, "root", "123456");
		//生成entity代码位置
		config.setEntityPackage(ENTITY_PACKAGE);
		//生成dao代码位置
		config.setDaoPackage(DAO_PACKAGE);
		//mapper.xml文件位置,以测试用例的方式启动则
		config.setXmlLocation(XML_LOCATION);
		config.setServiceLocation(PACKAGE_SUFFER+"service.singleAward");
		config.setServiceImplLocation(PACKAGE_SUFFER+"service.singleAward.impl");
		config.setDtoPackage(PACKAGE_SUFFER_API+"domain.dto.singleAward");
		config.setRemotePackage(PACKAGE_SUFFER_API+"remoteservice.singleAward");
		config.setRemoteImplLocation(PACKAGE_SUFFER+"remoteservice.impl.singleAward");
		//数据库名
		config.setTableSchema(TABLE_SCHEMA);
		//表名
		config.setTableName(TABLE_NAME);
		//表前缀,设置该值后生成的Entity,dao,daoImpl类不会以tb开头
		config.setTablePrefix("tb");
		//设置生成的java文件存放的模块名,如果该代码执行和生成的代码在一个模块下则不需要设置,
		//比如该执行代码在activity-center-deploy模块下,需要生成的entity,dao，daoImpl需要在activity-center-biz模块下,则需要设置ModuleName的值为activity-center-biz
		//如果该执行代码在activity-center-biz下,并且生成的代码也在activity-center-biz下则不需要设置ModuleName的值
		config.setModuleName("activity-center-biz");
		config.setApiModuleName("activity-center-api");
		MybatisGenerator.genCode(config);

	}
}
