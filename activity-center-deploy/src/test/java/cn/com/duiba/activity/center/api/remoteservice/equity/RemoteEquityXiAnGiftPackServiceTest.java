package cn.com.duiba.activity.center.api.remoteservice.equity;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.request.equity.XiAnGiftPackStockBeanRequest;
import cn.com.duiba.activity.center.api.request.equity.XiAnGiftPackStockRequest;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-30
 */
public class RemoteEquityXiAnGiftPackServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteEquityXiAnGiftPackService remoteEquityXiAnGiftPackService;

    @Test
    public void saveXiAnGiftPackStockTest() {
        XiAnGiftPackStockRequest request = new XiAnGiftPackStockRequest();
        request.setPackId("1");
        XiAnGiftPackStockBeanRequest beanRequest = new XiAnGiftPackStockBeanRequest();
        beanRequest.setId(1L);
        beanRequest.setTotalStock(3L);
        beanRequest.setPerNum(3L);

        XiAnGiftPackStockBeanRequest beanRequest2 = new XiAnGiftPackStockBeanRequest();
        beanRequest2.setId(2L);
        beanRequest2.setTotalStock(3L);
        beanRequest2.setPerNum(3L);

        List<XiAnGiftPackStockBeanRequest> list = Lists.newArrayList();
        list.add(beanRequest);
        list.add(beanRequest2);

        request.setGiftList(list);

        remoteEquityXiAnGiftPackService.saveXiAnGiftPackStock(request);

    }

}
