package cn.com.duiba.activity.center.biz.service.game;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionRecordDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/7.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaQuestionRecordServiceTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaQuestionRecordService duibaQuestionRecordService;

    private ThreadLocal<DuibaQuestionRecordDto> duibaQuestionRecordDO=new ThreadLocal<>();
    
    @Before
    public void testInsert() {
        DuibaQuestionRecordDto e=new DuibaQuestionRecordDto();
        TestUtils.setRandomAttributesForBean(e,false);
        e.setDeleted(false);
        duibaQuestionRecordService.insert(e);
        duibaQuestionRecordDO.set(e);
    }

    @Test
    public void testFindById() {
        DuibaQuestionRecordDto e=duibaQuestionRecordDO.get();
        DuibaQuestionRecordDto e1=duibaQuestionRecordService.findById(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindTotalCount() {
        Assert.assertTrue(duibaQuestionRecordService.findTotalCount(duibaQuestionRecordDO.get().getBankId())>0);
    }

    @Test
    public void testDelete() {
        DuibaQuestionRecordDto e=duibaQuestionRecordDO.get();
        Assert.assertTrue(duibaQuestionRecordService.delete(e.getId())>0);
    }


    @Test
    public void testFindByBankIdAndLimit() {
        Assert.assertTrue(duibaQuestionRecordService.findByBankIdAndLimit(duibaQuestionRecordDO.get().getBankId(),10).size()>0);
    }

    @Test
    public void testFindByPage() {
        Assert.assertTrue(duibaQuestionRecordService.findByPage(duibaQuestionRecordDO.get().getBankId(),0,10).size()>0);
    }

    @Test
    public void testFindCountByBankIdsStr() {
        Assert.assertTrue(duibaQuestionRecordService.findCountByBankIdsStr(Arrays.asList(duibaQuestionRecordDO.get().getBankId().toString()))>0);
    }


    private void assertDO(DuibaQuestionRecordDto e, DuibaQuestionRecordDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaQuestionRecordDto e, DuibaQuestionRecordDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

}
