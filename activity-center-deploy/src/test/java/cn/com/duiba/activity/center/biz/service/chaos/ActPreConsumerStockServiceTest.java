package cn.com.duiba.activity.center.biz.service.chaos;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.chaos.ActPreConsumeStockDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/22.
 */
@Transactional(ActPreConsumerStockServiceTest.THIS_DATABASE_SCHEMA)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class ActPreConsumerStockServiceTest extends TransactionalTestCaseBase{
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS_ACTIVITY;


    @Resource
    private ActPreConsumerStockService actPreConsumerStockService;


    @Test
    public void testInsert() {
        ActPreConsumeStockDto e=new ActPreConsumeStockDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        actPreConsumerStockService.insert(e);
    }

    @Test
    public void testFindPreConsumerByBizPay() {
        ActPreConsumeStockDto e=new ActPreConsumeStockDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        e.setAction("pay");
        actPreConsumerStockService.insert(e);
        Assert.assertNotNull(actPreConsumerStockService.findPreConsumerByBizPay(e.getBizId(),e.getBizSource()));
    }

}
