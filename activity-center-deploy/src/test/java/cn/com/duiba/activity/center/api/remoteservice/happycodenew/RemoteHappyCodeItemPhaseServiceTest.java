package cn.com.duiba.activity.center.api.remoteservice.happycodenew;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.happycodenew.HappyCodeItemBasicDto;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

/**
 * Created by Liugq on 2019/4/9.
 */
public class RemoteHappyCodeItemPhaseServiceTest extends TransactionalTestCaseBase {
    @Autowired
    private RemoteHappyCodeItemPhaseService remoteHappyCodeItemPhaseService;
    @Autowired
    private RemoteHappyCodeItemBasicService remoteHappyCodeItemBasicService;

    @Test
    @Rollback(false)
    public void testFindOrInsert(){
        HappyCodeItemBasicDto happyCodeItemBasicDto = remoteHappyCodeItemBasicService.findItemBasicById(46L);
        Assert.assertNotNull(remoteHappyCodeItemPhaseService.findOrInsertPhase(happyCodeItemBasicDto));
    }
}
