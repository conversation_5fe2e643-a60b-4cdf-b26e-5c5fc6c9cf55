package cn.com.duiba.activity.center.biz.dao.quizz;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.quizz.QuizzOrdersEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ)
public class QuizzOrdersDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private QuizzOrdersDao quizzOrdersDao;
	
	private QuizzOrdersEntity info;
	
	private String[] exceptFields = new String[] { "gmtCreate", "gmtModified", "prizeOverdueDate", "quizzData" };
	
	@Before
	public void insertTest(){
		QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		quizzOrdersDao.insert(e);
		info = e;
	}
	
	@Test
	public void findTest(){
		QuizzOrdersEntity test = quizzOrdersDao.find(info.getId());
		TestUtils.assertEqualsReflect(info, test, false, exceptFields);
	}

	@Test
	public void findExpireOrderTest(){
		QuizzOrdersEntity e = new QuizzOrdersEntity(true);
		TestUtils.setRandomAttributesForBean(e, false);
		e.setExchangeStatus(1);
		e.setPrizeType("nothanks");
		Calendar calendar = Calendar.getInstance();
    	calendar.setTime(new Date());
    	calendar.add(Calendar.HOUR_OF_DAY, -2);
    	Date startTime = calendar.getTime();
    	calendar.setTime(startTime);
    	calendar.add(Calendar.DATE, -1);
    	Date endTime = calendar.getTime();
    	e.setGmtCreate(endTime);
    	quizzOrdersDao.insert(e);
		List<Long> list = quizzOrdersDao.findExpireOrder();
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void findAllByIdsTest(){
		List<Long> ids = new ArrayList<Long>();
		ids.add(info.getId());
		List<QuizzOrdersEntity> list = quizzOrdersDao.findAllByIds(ids);
		Assert.assertTrue(list.size() > 0);
	}
}
