package cn.com.duiba.activity.center.biz.dao.guess;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.guess.DuibaGuessSelectionsEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class DuibaGuessSelectionsDaoTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaGuessSelectionsDao duibaGuessSelectionsDao;
	
	private DuibaGuessSelectionsEntity info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(DuibaGuessSelectionsEntity.class);
		duibaGuessSelectionsDao.insert(info);
	}
	
	@Test
	public void findTest(){
		DuibaGuessSelectionsEntity e = duibaGuessSelectionsDao.find(info.getId());
		Assert.assertNotNull(e);
	}

	@Test
	public void update4AdminTest(){
		DuibaGuessSelectionsEntity e = TestUtils.createRandomBean(DuibaGuessSelectionsEntity.class);
		duibaGuessSelectionsDao.update4Admin(info.getId(), e.getDuibaGuessId(), e.getPosition(), e.getContent());
		String content = duibaGuessSelectionsDao.find(info.getId()).getContent();
		Assert.assertTrue(content.equals(e.getContent()));
	}
	
	@Test
	public void findAllByGuessIdTest(){
		DuibaGuessSelectionsEntity e = TestUtils.createRandomBean(DuibaGuessSelectionsEntity.class);
		e.setDeleted(false);
		duibaGuessSelectionsDao.insert(e);
		List<DuibaGuessSelectionsEntity> list = duibaGuessSelectionsDao.findAllByGuessId(e.getDuibaGuessId());
		Assert.assertTrue(list.size() > 0);
	}
	
	@Test
	public void deleteTest(){
		List<Long> ids = new ArrayList<Long>();
		ids.add(info.getId());
		duibaGuessSelectionsDao.delete(ids);
		boolean deleted = duibaGuessSelectionsDao.find(info.getId()).getDeleted();
		Assert.assertTrue(deleted);
	}

}
