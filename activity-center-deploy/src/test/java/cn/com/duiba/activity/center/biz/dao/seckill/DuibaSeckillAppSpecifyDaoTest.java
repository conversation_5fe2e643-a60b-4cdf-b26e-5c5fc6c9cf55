package cn.com.duiba.activity.center.biz.dao.seckill;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.seckill.DuibaSeckillAppSpecifyEntity;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by yansen on 16/6/7.
 */
@Transactional(DsConstants.DATABASE_SECKILL)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaSeckillAppSpecifyDaoTest extends TransactionalTestCaseBase{

    @Resource
    private DuibaSeckillAppSpecifyDao duibaSeckillAppSpecifyDao;

    private ThreadLocal<DuibaSeckillAppSpecifyEntity> duibaSeckillAppSpecifyDO=new ThreadLocal<>();

    @Before
    public void testInsert() {
        DuibaSeckillAppSpecifyEntity e=new DuibaSeckillAppSpecifyEntity();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaSeckillAppSpecifyDao.insert(e);
        duibaSeckillAppSpecifyDO.set(e);
    }

    @Test
    public void testFind() {
        DuibaSeckillAppSpecifyEntity e=duibaSeckillAppSpecifyDO.get();
        DuibaSeckillAppSpecifyEntity e1=duibaSeckillAppSpecifyDao.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testFindActivityIdsByAppId() {
        DuibaSeckillAppSpecifyEntity e=duibaSeckillAppSpecifyDO.get();
        List<Long> list=duibaSeckillAppSpecifyDao.findActivityIdsByAppId(e.getAppId());
        Assert.assertTrue(list.contains(e.getDuibaSeckillId()));
    }

    @Test
    public void testDelete() {
        DuibaSeckillAppSpecifyEntity e=duibaSeckillAppSpecifyDO.get();
        duibaSeckillAppSpecifyDao.delete(e.getId());
        Assert.assertNull(duibaSeckillAppSpecifyDao.find(e.getId()));
    }

    @Test
    public void testFindByDuibaSeckillId() {
        DuibaSeckillAppSpecifyEntity e=duibaSeckillAppSpecifyDO.get();
        Assert.assertTrue(duibaSeckillAppSpecifyDao.findByDuibaSeckillId(e.getDuibaSeckillId()).size()>0);
    }

    @Test
    public void testFindByDuibaSeckillAndApp() {
        DuibaSeckillAppSpecifyEntity e=duibaSeckillAppSpecifyDO.get();
        assertDO(e,duibaSeckillAppSpecifyDao.findByDuibaSeckillAndApp(e.getDuibaSeckillId(),e.getAppId()));
    }

    private void assertDO(DuibaSeckillAppSpecifyEntity e, DuibaSeckillAppSpecifyEntity e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaSeckillAppSpecifyEntity e, DuibaSeckillAppSpecifyEntity e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified","autoOffDate","dateStart","dateEnd","timeStart","timeEnd","deleted"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

}
