package cn.com.duiba.activity.center.biz.service.guess;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.guess.GuessOrdersDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.guess.GuessOrdersConsumerDao;
import cn.com.duiba.activity.center.biz.dao.guess.GuessOrdersSequenceDao;
import cn.com.duiba.activity.center.biz.entity.guess.GuessOrdersEntity;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS_CON)
public class GuessOrdersConsumerServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private GuessOrdersConsumerService guessOrdersConsumerService;
	@Autowired
	private GuessOrdersSequenceDao guessOrdersSequenceDao;
	@Autowired
	private GuessOrdersConsumerDao guessOrdersConsumerDao;

	private GuessOrdersEntity info;
	
	@Before
	public void insertTest(){
		info = TestUtils.createRandomBean(GuessOrdersEntity.class);
		info.setCredits(0L);
		info.setOrderStatus(2);
		info.setIsGivePrize(1);
		info.setExchangeStatus(10);
		guessOrdersConsumerDao.insert(info, guessOrdersSequenceDao.getId());
	}
	
	@Test
    public void findTest(){
		GuessOrdersDto e = guessOrdersConsumerService.find(info.getConsumerId(), info.getId());
		Assert.assertNotNull(e);
    }

	@Test
    public void countByConsumerIdAndOperatingActivityIdTest(){
		int num = guessOrdersConsumerService.countByConsumerIdAndOperatingActivityId(info.getConsumerId(), info.getOperatingActivityId());
		Assert.assertTrue(num > 0);
	}

	@Test
    public void countByConsumerIdAndOperatingActivityIdAndDateTest(){
    	Calendar calendar = Calendar.getInstance();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	Date start = calendar.getTime();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, 1);
    	Date end = calendar.getTime();
    	int num = guessOrdersConsumerService.countByConsumerIdAndOperatingActivityIdAndDate(info.getConsumerId(), info.getOperatingActivityId(), start, end);
    	Assert.assertTrue(num > 0);
    }

	@Test
    public void countFreeByConsumerIdAndOperatingActivityIdAndDateTest(){
    	Calendar calendar = Calendar.getInstance();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, -1);
    	Date start = calendar.getTime();
    	calendar.setTime(info.getGmtCreate());
    	calendar.add(Calendar.HOUR_OF_DAY, 1);
    	Date end = calendar.getTime();
    	int num = guessOrdersConsumerService.countFreeByConsumerIdAndOperatingActivityIdAndDate(info.getConsumerId(), info.getOperatingActivityId(), start, end);
    	Assert.assertTrue(num > 0);
    }

	@Test
    public void countFreeByConsumerIdAndOperatingActivityIdTest(){
    	int num = guessOrdersConsumerService.countFreeByConsumerIdAndOperatingActivityId(info.getConsumerId(), info.getOperatingActivityId());
    	Assert.assertTrue(num > 0);
    }

	@Test
    public void findByIdsTest(){
    	List<Long> ids = new ArrayList<Long>();
    	ids.add(info.getId());
    	List<GuessOrdersDto> list = guessOrdersConsumerService.findByIds(info.getConsumerId(), ids);
    	Assert.assertTrue(list.size() > 0);
    }

	@Test
    public void findWinOrdersTest(){
    	List<GuessOrdersDto> list = guessOrdersConsumerService.findWinOrders(info.getConsumerId(), info.getDuibaGuessId());
    	Assert.assertTrue(list.size() > 0);
    }

	@Test
    public void findGuessOrdersTest(){
    	List<GuessOrdersDto> list = guessOrdersConsumerService.findGuessOrders(info.getConsumerId(), info.getDuibaGuessId());
    	Assert.assertTrue(list.size() > 0);
    }

	@Test
	public void updateDeveloperBizIdTest(){
		GuessOrdersDto e = TestUtils.createRandomBean(GuessOrdersDto.class);
		guessOrdersConsumerService.updateDeveloperBizId(info.getConsumerId(), info.getId(), e.getDeveloperBizId());
		String bizId = guessOrdersConsumerDao.find(info.getConsumerId(), info.getId()).getDeveloperBizId();
		Assert.assertTrue(bizId.equals(e.getDeveloperBizId()));
	}

	@Test
	public void updateMainOrderIdTest(){
		GuessOrdersDto e = TestUtils.createRandomBean(GuessOrdersDto.class);
		guessOrdersConsumerService.updateMainOrderId(info.getConsumerId(), info.getId(), e.getMainOrderId(), e.getMainOrderNum());
		long mainOrderId = guessOrdersConsumerDao.find(info.getConsumerId(), info.getId()).getMainOrderId();
		Assert.assertTrue(mainOrderId == e.getMainOrderId());
	}
}
