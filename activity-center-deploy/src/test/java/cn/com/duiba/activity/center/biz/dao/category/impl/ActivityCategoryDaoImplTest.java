/**
 * Project Name:activity-center-deploy
 * File Name:ActivityCategoryDaoImplTest.java
 * Package Name:cn.com.duiba.activity.center.biz.dao.category.impl
 * Date:2016年6月7日下午12:49:56
 * Copyright (c) 2016, duiba.com.cn All Rights Reserved.
 *
*/

package cn.com.duiba.activity.center.biz.dao.category.impl;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.dao.category.ActivityCategoryDao;
import cn.com.duiba.activity.center.biz.entity.ActivityCategoryEntity;


/**
 * ClassName:ActivityCategoryDaoImplTest <br/>
 * Date:     2016年6月7日 下午12:49:56 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class ActivityCategoryDaoImplTest extends TransactionalTestCaseBase {
    @Autowired
    private ActivityCategoryDao activityCategoryDao;
    @Test
    public void testInsertActivityCategoryEntity() {
        ActivityCategoryEntity instance=new ActivityCategoryEntity();
        instance.setName("名称");
        int ret=activityCategoryDao.insert(instance);
        assertEquals(1, ret);
        assertNotNull(instance.getId());
    }

    @Test
    public void testUpdateCategoryContent() {
        ActivityCategoryEntity instance=new ActivityCategoryEntity();
        instance.setName("名称");
        activityCategoryDao.insert(instance);
        
        String newcontent="newcontent";
        activityCategoryDao.updateCategoryContent(instance.getId(),"test_name", newcontent);
        
        ActivityCategoryEntity e=activityCategoryDao.select(instance.getId());
        assertEquals(newcontent, e.getContent());
    }

    @Test
    public void testUpdateCategoryEnable() {
        ActivityCategoryEntity instance=new ActivityCategoryEntity();
        instance.setName("名称");
        activityCategoryDao.insert(instance);
        
        int ret=activityCategoryDao.updateCategoryEnable(instance.getId());
        assertEquals(1, ret);

        assertTrue(activityCategoryDao.select(instance.getId()).getEnable());
    }

    @Test
    public void testUpdateCategoryDisable() {
        ActivityCategoryEntity instance=new ActivityCategoryEntity();
        instance.setName("名称");
        activityCategoryDao.insert(instance);
        activityCategoryDao.updateCategoryEnable(instance.getId());
        
        activityCategoryDao.updateCategoryDisable(instance.getId());
        assertFalse(activityCategoryDao.select(instance.getId()).getEnable());
    }

    @Test
    public void testSelectAll() {
        activityCategoryDao.selectAll();
    }

    @Test
    public void testSelectEnableCategory() {
        activityCategoryDao.selectEnableCategory();
    }
}

