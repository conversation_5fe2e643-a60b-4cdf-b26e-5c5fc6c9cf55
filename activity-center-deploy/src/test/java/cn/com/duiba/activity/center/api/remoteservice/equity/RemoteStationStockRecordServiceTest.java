package cn.com.duiba.activity.center.api.remoteservice.equity;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.equity.StationStockRecordDto;
import cn.com.duiba.activity.center.api.enums.equity.EquityComfirmEnum;
import cn.com.duiba.activity.center.api.request.equity.StationStockRecordRequest;
import cn.com.duiba.boot.exception.BizException;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.List;

/**
 * <AUTHOR>
 * @Description:
 * @date 2020/1/35:17 下午
 */
public class RemoteStationStockRecordServiceTest extends TransactionalTestCaseBase {


    @Autowired
    private RemoteStationStockRecordService remoteStationStockRecordService;


    @Test
    @Rollback(false)
    public void getById() {

        try {
            StationStockRecordDto stationStockDto =  remoteStationStockRecordService.getById(3L);
            Assert.assertNotNull(stationStockDto);
        } catch (BizException e) {
            e.printStackTrace();
        }
    }


    @Test
    @Rollback(false)
    public void getByStationId() {

        try {
            List<StationStockRecordDto> stationStockDto =  remoteStationStockRecordService.getByStationId(1L);
            Assert.assertNotNull(stationStockDto);
        } catch (BizException e) {
            e.printStackTrace();
        }
    }


    @Test
    @Rollback(false)
    public void listByIds() {

        try {
            List<StationStockRecordDto> stationStockDto =  remoteStationStockRecordService.listByIds(Lists.newArrayList(3L));
            Assert.assertNotNull(stationStockDto);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    @Rollback(false)
    public void findByCondition() {
        StationStockRecordRequest stationStockRecordRequest =new StationStockRecordRequest();
        stationStockRecordRequest.setAppId(1L);
        stationStockRecordRequest.setStationStockIds(Lists.newArrayList(9L));
        stationStockRecordRequest.setRestoreStatus(EquityComfirmEnum.UN_CONFIRM.getCode());
        try {
            List<StationStockRecordDto> stationStockDto =  remoteStationStockRecordService.findByCondition(stationStockRecordRequest);
            Assert.assertNotNull(stationStockDto);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    @Rollback(false)
    public void updateById() {

        try {
            StationStockRecordDto stationStockDto =  remoteStationStockRecordService.getById(3L);
            Integer aa = remoteStationStockRecordService.updateById(stationStockDto);
            Assert.assertNotNull(aa);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }




}
