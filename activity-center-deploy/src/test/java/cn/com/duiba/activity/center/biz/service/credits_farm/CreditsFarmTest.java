package cn.com.duiba.activity.center.biz.service.credits_farm;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmActSaveParam;
import cn.com.duiba.activity.center.biz.service.creditsfarm.CreditsFarmBackendService;
import cn.com.duiba.boot.exception.BizException;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2019/3/29
 */
public class CreditsFarmTest extends TransactionalTestCaseBase {
    @Autowired
    private CreditsFarmBackendService creditsFarmBackendService;

    @Test
    public void testFind(){
        CreditsFarmActSaveParam param = creditsFarmBackendService.getCreditsFarm(1L);
        System.out.println(JSON.toJSONString(param));
    }

    @Test
    public void testInsert() throws BizException {
        CreditsFarmActSaveParam param = creditsFarmBackendService.getCreditsFarm(1L);
        param.setId(null);
//        for (CreditsFarmOptionDto option : param.getOptions()) {
//            option.setSeedType("apple");
//        }
//        creditsFarmBackendService.saveCreditsFarm(param,false);

    }
}
