package cn.com.duiba.activity.center.biz.service.game;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionAnswerAppSpecifyDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/26.
 */
@Transactional(DsConstants.DATABASE_CREDITS_GAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaQuestionAnswerAppSpecifyServiceTest extends TransactionalTestCaseBase {

    @Resource
    private DuibaQuestionAnswerAppSpecifyService duibaQuestionAnswerAppSpecifyService;


    @Test
    public void testInsert() {
        DuibaQuestionAnswerAppSpecifyDto e=new DuibaQuestionAnswerAppSpecifyDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionAnswerAppSpecifyService.insert(e);
    }

    @Test
    public void testFind() {
        DuibaQuestionAnswerAppSpecifyDto e=new DuibaQuestionAnswerAppSpecifyDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionAnswerAppSpecifyService.insert(e);
        DuibaQuestionAnswerAppSpecifyDto e1=duibaQuestionAnswerAppSpecifyService.find(e.getId());
        assertDO(e,e1);
    }

    @Test
    public void testDelete() {
        DuibaQuestionAnswerAppSpecifyDto e=new DuibaQuestionAnswerAppSpecifyDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionAnswerAppSpecifyService.insert(e);
        duibaQuestionAnswerAppSpecifyService.delete(e.getId());
        Assert.assertNull(duibaQuestionAnswerAppSpecifyService.find(e.getId()));
    }

    @Test
    public void testFindByDuibaQuestionAnswerId() {
        DuibaQuestionAnswerAppSpecifyDto e=new DuibaQuestionAnswerAppSpecifyDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionAnswerAppSpecifyService.insert(e);
        List<DuibaQuestionAnswerAppSpecifyDto> e1=duibaQuestionAnswerAppSpecifyService.findByDuibaQuestionAnswerId(e.getDuibaQuestionAnswerId());
        Assert.assertTrue(e1.size()>0);
    }

    @Test
    public void testFindByQuestionAnswerIdAndAppId() {
        DuibaQuestionAnswerAppSpecifyDto e=new DuibaQuestionAnswerAppSpecifyDto();
        TestUtils.setRandomAttributesForBean(e,false);
        duibaQuestionAnswerAppSpecifyService.insert(e);
        assertDO(e,duibaQuestionAnswerAppSpecifyService.findByQuestionAnswerIdAndAppId(e.getDuibaQuestionAnswerId(),e.getAppId()));
    }


    private void assertDO(DuibaQuestionAnswerAppSpecifyDto e, DuibaQuestionAnswerAppSpecifyDto e1){
        assertDO(e,e1,null);
    }

    private void assertDO(DuibaQuestionAnswerAppSpecifyDto e, DuibaQuestionAnswerAppSpecifyDto e1, String[] exculdeFields){
        List<String> exculdeFieldsList=new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[]{"gmtCreate","gmtModified"}));
        if(exculdeFields!=null&&exculdeFields.length>0){
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1,false,exculdeFieldsList.toArray(new String[exculdeFieldsList.size()+1]));//时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr( e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr( e1.getGmtModified()));
    }

}
