package cn.com.duiba.activity.center.api.remoteservice.happycode;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.happy_code.HappyCodePhaseDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;


/**
 * Created by hww on 2017/12/11
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)
public class RemoteHappyCodePhaseServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteHappyCodePhaseService remoteHappyCodePhaseService;

    private long insertTest() {
        HappyCodePhaseDto happyCodePhaseDto = new HappyCodePhaseDto();
        happyCodePhaseDto.setBasicId(1L);
        happyCodePhaseDto.setTitle("title");
        happyCodePhaseDto.setSubTitle("subTitle");
        happyCodePhaseDto.setRule("rule");
        happyCodePhaseDto.setCodeCount(100);
        happyCodePhaseDto.setPhaseStatus(HappyCodePhaseDto.STATUS_WAIT_FOR_START);
        happyCodePhaseDto.setPayload(1);
        happyCodePhaseDto.setLotteryType(HappyCodePhaseDto.LOTTERY_TYPE_NUMBER);
        happyCodePhaseDto.setCodeType(HappyCodePhaseDto.CODE_TYPE_A);
        happyCodePhaseDto.setPhaseNumber("11111");
        //happyCodePhaseDto.setWinnerCount();
        //happyCodePhaseDto.setWinnerId();
        happyCodePhaseDto.setCodeItemId(1L);
        happyCodePhaseDto.setCodeItemName("itemName");
        happyCodePhaseDto.setCodeItemTips("itemTips");
        happyCodePhaseDto.setDeleted(0);
        happyCodePhaseDto.setGmtCreate(new Date());
        happyCodePhaseDto.setGmtModified(new Date());
        return remoteHappyCodePhaseService.insert(happyCodePhaseDto);

    }

    @Test
    public void findByBasicIdsTest() {
    }

    @Test
    public void deleteByIdTest() {
        Long id = insertTest();

    }

    @Test
    public void updateBasicIdTest() {

    }

    @Test
    public void findByPhaseIdTest() {
        Long id = insertTest();
    }

    @Test
    public void findByPhaseIdsTest() {

    }

    @Test
    public void findAllWaitOpenPhaseTest() {

    }

    @Test
    public void openPrizeRandomTest() {
        remoteHappyCodePhaseService.openPrizeRandom(Lists.newArrayList(1L));
    }

    @Test
    public void updatePhaseNumberTest() {
        Long id = insertTest();
        HappyCodePhaseDto phase = remoteHappyCodePhaseService.findByPhaseId(id);
        String oldPhaseNumber = phase.getPhaseNumber();
        HappyCodePhaseDto newPhase = remoteHappyCodePhaseService.updatePhaseNumber(id, oldPhaseNumber + "22");
        String newPhaseNumber = newPhase.getPhaseNumber();
        Assert.assertTrue(!Objects.equals(oldPhaseNumber, newPhaseNumber));
    }

    @Test
    public void updateNextPhaseTest() {
        Long id = insertTest();
        HappyCodePhaseDto phase = remoteHappyCodePhaseService.findByPhaseId(id);
        HappyCodePhaseDto p = new HappyCodePhaseDto();
        p.setId(phase.getId());
        p.setTitle(phase.getTitle() + 1);
        p.setSubTitle(phase.getSubTitle() + 1);
        p.setRule(phase.getRule() + 1);
        int skip = remoteHappyCodePhaseService.updateNextPhase(p);
        Assert.assertTrue(skip == 1);

        Long id2 = insertTest();
        HappyCodePhaseDto phase2 = remoteHappyCodePhaseService.findByPhaseId(id2);
        HappyCodePhaseDto p2 = new HappyCodePhaseDto();
        p2.setId(phase2.getId());
        p2.setTitle(phase2.getTitle() + 1);
        p2.setSubTitle(phase2.getSubTitle() + 1);
        p2.setRule(phase2.getRule() + 1);
        //只能是未开启状态的期次才能改变配置 HappyCodePhaseDto.STATUS_WAIT_FOR_START
        remoteHappyCodePhaseService.updateStatusToOpening(id2);
        int skip2 = remoteHappyCodePhaseService.updateNextPhase(p2);
        Assert.assertTrue(skip2 == 0);
    }

}
