package cn.com.duiba.activity.center.biz.dao.ngame;

import java.util.Date;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.ngame.DuibaNgameStockConsumeEntity;

@Transactional(DsConstants.DATABASE_NGAME)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class DuibaNgameStockConsumeDaoTest extends TransactionalTestCaseBase {

	@Resource
	private DuibaNgameStockConsumeDao duibaNgameStockConsumeDao;

	@Test
	public void findByBizIdTest() {
		System.out.println(duibaNgameStockConsumeDao.findByBizId(16l, "37", "pay").getId());
	}
	
	@Test
	public void insertTest() {
		DuibaNgameStockConsumeEntity c = new DuibaNgameStockConsumeEntity();
		c.setGameStockId(-1l);
		c.setOptionId(-1l);
		c.setBizId("-22");
		c.setAction("-pay");
		c.setQuantity(1);
		c.setGmtCreate(new Date());
		c.setGmtModified(new Date());
		duibaNgameStockConsumeDao.insert(c);
	}
}
