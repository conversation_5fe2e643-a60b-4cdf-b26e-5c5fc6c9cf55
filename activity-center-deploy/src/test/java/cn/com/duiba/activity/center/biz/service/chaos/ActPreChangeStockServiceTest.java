package cn.com.duiba.activity.center.biz.service.chaos;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.chaos.ActPreChangeStockDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/22.
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class ActPreChangeStockServiceTest extends TransactionalTestCaseBase{

    @Resource
    private ActPreChangeStockService actPreChangeStockService;

    @Test
    public void testInsert() {
        ActPreChangeStockDto e=new ActPreChangeStockDto(true);
        TestUtils.setRandomAttributesForBean(e,false);
        actPreChangeStockService.insert(e);
    }
}
