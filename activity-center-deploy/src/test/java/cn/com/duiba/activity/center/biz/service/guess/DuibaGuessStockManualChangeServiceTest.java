package cn.com.duiba.activity.center.biz.service.guess;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.guess.DuibaGuessStockManualChangeDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class DuibaGuessStockManualChangeServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaGuessStockManualChangeService duibaGuessStockManualChangeService;
	
	private DuibaGuessStockManualChangeDto info;
	
	@Before
	public void addTest(){
		info = TestUtils.createRandomBean(DuibaGuessStockManualChangeDto.class);
		info.setChangeKind(10);
		duibaGuessStockManualChangeService.add(info);
	}
	
	@Test
	public void findByStockIdTest(){
		List<DuibaGuessStockManualChangeDto> list = duibaGuessStockManualChangeService.findByStockId(info.getGuessStockId());
		Assert.assertTrue(list.size() > 0);
	}

	@Test
	public void addBatchTest(){
		DuibaGuessStockManualChangeDto e1 = TestUtils.createRandomBean(DuibaGuessStockManualChangeDto.class);
		e1.setChangeKind(10);
		DuibaGuessStockManualChangeDto e2 = TestUtils.createRandomBean(DuibaGuessStockManualChangeDto.class);
		e2.setChangeKind(10);
		List<DuibaGuessStockManualChangeDto> list = new ArrayList<DuibaGuessStockManualChangeDto>();
		list.add(e1);
		list.add(e2);
		list = duibaGuessStockManualChangeService.addBatch(list);
		Assert.assertNotNull(list.get(0).getId());
		Assert.assertNotNull(list.get(1).getId());
	}
	
}
