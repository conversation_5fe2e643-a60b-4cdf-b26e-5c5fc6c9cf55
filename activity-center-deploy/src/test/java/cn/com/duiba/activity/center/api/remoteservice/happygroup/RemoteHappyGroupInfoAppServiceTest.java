package cn.com.duiba.activity.center.api.remoteservice.happygroup;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupBaseConfigDto;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupConfigDto;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupInfoDto;
import cn.com.duiba.wolf.utils.DateUtils;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.Arrays;
import java.util.List;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/3/5 11:41
 * @description:
 */
@Rollback(value = false)
public class RemoteHappyGroupInfoAppServiceTest extends TransactionalTestCaseBase {

    @Autowired
    RemoteHappyGroupInfoAppService remoteHappyGroupInfoAppService;

    @Test
    public void testAdd() {
        HappyGroupInfoDto happyGroupInfoDto = new HappyGroupInfoDto();
        happyGroupInfoDto.setAppId(1L);
        happyGroupInfoDto.setActivityConfigId(1L);
        happyGroupInfoDto.setGroupItemId(1L);
        happyGroupInfoDto.setGroupNumber(5);
        happyGroupInfoDto.setEndTime(DateUtils.getSecondDate("2019-03-05 17:00:00"));
        Long  groupId = remoteHappyGroupInfoAppService.add(happyGroupInfoDto);
        System.out.println(groupId);
        Assert.assertNotNull(groupId);
    }

    @Test
    public void testModifyToSuccess() {
        remoteHappyGroupInfoAppService.modifyToSuccess(1L);
    }

    @Test
    public void testModifyToFailure() {
        remoteHappyGroupInfoAppService.modifyToFailure(1L, 1L);
    }

    @Test
    public void testGetById() {
        HappyGroupInfoDto happyGroupInfoDto = remoteHappyGroupInfoAppService.getById(1L);
        System.out.println(JSON.toJSON(happyGroupInfoDto));
        Assert.assertNotNull(happyGroupInfoDto);
    }

    @Test
    public void testGetByIdList() {
        List<HappyGroupInfoDto> happyGroupInfoDtoList = remoteHappyGroupInfoAppService.getByIdList(Arrays.asList(1L, 2L));
        System.out.println(JSON.toJSON(happyGroupInfoDtoList));
        Assert.assertNotNull(happyGroupInfoDtoList);
    }
}
