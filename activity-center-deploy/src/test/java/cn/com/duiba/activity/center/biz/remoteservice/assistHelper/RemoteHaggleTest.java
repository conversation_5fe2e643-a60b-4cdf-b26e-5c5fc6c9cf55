package cn.com.duiba.activity.center.biz.remoteservice.assistHelper;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.haggle.HaggleOpenRecordDto;
import cn.com.duiba.activity.center.api.params.haggle.OpenRecordPageParams;
import cn.com.duiba.activity.center.api.remoteservice.haggle.RemoteHaggleService;
import cn.com.duiba.api.bo.page.Page;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class RemoteHaggleTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteHaggleService remoteHaggleService;

    @Test
    public void haggleOpenRecords() {
        OpenRecordPageParams params = new OpenRecordPageParams();
        params.setPageNum(1);
        params.setPageSize(10);
        params.setConsumerId(1111L);
        params.setConfigId(3L);
        Page<HaggleOpenRecordDto> result = remoteHaggleService.listRecordByCidAndConfigId(params);
        Assert.assertTrue(result.getTotalCount() > 0);
    }
}
