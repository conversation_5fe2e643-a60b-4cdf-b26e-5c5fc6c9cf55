package cn.com.duiba.activity.center.biz.service.quizz;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.quizz.DuibaQuizzStockDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_QUIZZ)
public class DuibaQuizzStockServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private DuibaQuizzStockService duibaQuizzStockService;
	
	private DuibaQuizzStockDto info;
	
	@Before
	public void addTest(){
		info = TestUtils.createRandomBean(DuibaQuizzStockDto.class);
		info.setStock(100);
		duibaQuizzStockService.add(info);
	}
	
	@Test
	public void subStockTest(){
		int subNumber = 30;
		duibaQuizzStockService.subStock(info.getId(), subNumber);
		
		int stock = duibaQuizzStockService.findById(info.getId()).getStock();
		Assert.assertTrue(stock == (info.getStock() - subNumber));
	}

	@Test
    public void addStockTest(){
    	int addNumber = 30;
    	duibaQuizzStockService.addStock(info.getId(), addNumber);
    	int stock = duibaQuizzStockService.findById(info.getId()).getStock();
    	Assert.assertTrue(stock == (info.getStock() + addNumber));
    }

	@Test
    public void findRemainingTest(){
    	DuibaQuizzStockDto e = duibaQuizzStockService.findRemaining(info.getQuizzOptionId());
    	Assert.assertNotNull(e);
    }

	@Test
    public void findByQuizzOptionIdTest(){
    	DuibaQuizzStockDto e = duibaQuizzStockService.findByQuizzOptionId(info.getQuizzOptionId());
    	Assert.assertNotNull(e);
    }

	@Test
	public void findByQuizzOptionIdsTest(){
		List<Long> list = new ArrayList<Long>();
		list.add(info.getQuizzOptionId());
		List<DuibaQuizzStockDto> test = duibaQuizzStockService.findByQuizzOptionIds(list);
		Assert.assertTrue(test.size() > 0);
    }

	@Test
    public void updateStockAddTest(){
		int stockAdd = 30;
		duibaQuizzStockService.addStock(info.getId(), stockAdd);
    	int stock = duibaQuizzStockService.findById(info.getId()).getStock();
    	Assert.assertTrue(stock == (info.getStock() + stockAdd));
    }
	
	@Test
	public void updateStockSubTest(){
		int stockSub = 30;
		duibaQuizzStockService.subStock(info.getId(), stockSub);
		int stock = duibaQuizzStockService.findById(info.getId()).getStock();
		Assert.assertTrue(stock == (info.getStock() - stockSub));
    }

	@Test
	public void addBatchTest(){
		DuibaQuizzStockDto e1 = TestUtils.createRandomBean(DuibaQuizzStockDto.class);
		DuibaQuizzStockDto e2 = TestUtils.createRandomBean(DuibaQuizzStockDto.class);
		List<DuibaQuizzStockDto> list = new ArrayList<DuibaQuizzStockDto>();
		list.add(e1);
		list.add(e2);
		list = duibaQuizzStockService.addBatch(list);
		
		Assert.assertNotNull(duibaQuizzStockService.findById(list.get(0).getId()));
    }
}
