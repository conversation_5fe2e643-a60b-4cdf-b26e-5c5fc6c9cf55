package cn.com.duiba.activity.center.api.remoteservice.shuqi;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.shuqipk.PkTeamMemberRecordDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.DateUtils;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 18/7/17 21:31
 * @description:
 */
@Transactional(DsConstants.DATABASE_CUSTOM_RECORD)
@Rollback(value = false)
public class RemotePkTeamMemberRecordServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemotePkTeamMemberRecordService remotePkTeamMemberRecordService;

    private Long getRecordDate(Date date){
        return DateUtils.getDayStartTime(date).getTime()/1000;
    }

    @Test
    public void testGetByTeamRecordIds(){
        List<PkTeamMemberRecordDto> list1 = remotePkTeamMemberRecordService.getByTeamRecordIds(Arrays.asList(9L, 10L), null);
        System.out.println(JSON.toJSON(list1));
        Assert.assertNotNull(list1);

        List<PkTeamMemberRecordDto> list2 = remotePkTeamMemberRecordService.getByTeamRecordIds(Arrays.asList(9L, 11L), 111L);
        System.out.println(JSON.toJSON(list2));
        Assert.assertNotNull(list2);
    }

//    @Test
//    public void testUpdatePraiseCount(){
//        PkTeamMemberRecordDto pkTeamMemberRecordDto = remotePkTeamMemberRecordService.getByConsumerIdAndDate(111L, getRecordDate(new Date()));
//        pkTeamMemberRecordDto.setPraiseCount(1);
//        int result = remotePkTeamMemberRecordService.updatePraiseCount(pkTeamMemberRecordDto);
//        System.out.println(result);
//        Assert.assertTrue(result>0);
//    }
//
//    @Test
//    public void testUpdateOrderNum(){
//        PkTeamMemberRecordDto pkTeamMemberRecordDto = remotePkTeamMemberRecordService.getByConsumerIdAndDate(111L, getRecordDate(new Date()));
//        pkTeamMemberRecordDto.setOrderNum("34222423");
//        int result = remotePkTeamMemberRecordService.updateOrderNum(pkTeamMemberRecordDto);
//        System.out.println(result);
//        Assert.assertTrue(result>0);
//    }
}
