package cn.com.duiba.activity.center.api.remoteservice.happycodenew;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.happycodenew.HappyCodeUserRecordDto;
import cn.com.duiba.activity.center.api.enums.happycodenew.HappyRewardStatusEnum;
import cn.com.duiba.activity.center.api.params.HappyCodeExchangeParam;
import cn.com.duiba.api.bo.page.Page;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created by Liugq on 2019/4/9.
 */
public class RemoteHappyCodeUserRecordServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteHappyCodeUserRecordService remoteHappyCodeUserRecordService;
    @Autowired
    private RemoteHappyCodeDetailService remoteHappyCodeDetailService;
    @Autowired
    private RemoteHappyCodeDailyDataService remoteHappyCodeDailyDataService;

    @Test
    public void testPrizeRecord(){
        HappyCodeUserRecordDto happyCodeUserRecordDto = remoteHappyCodeUserRecordService.queryPrizeRecord(16L,1539361L);
        System.out.println(happyCodeUserRecordDto.getId());
    }

    @Test
    public void testUpdateById(){
        HappyCodeUserRecordDto recordDto = new HappyCodeUserRecordDto();
        recordDto.setId(1L);
        recordDto.setRewardStatus(HappyRewardStatusEnum.REWARD_STATUS_YES);
        Assert.assertTrue(remoteHappyCodeUserRecordService.updateById(recordDto) > 0);
    }

    @Test
    public void cleanData (){
        remoteHappyCodeDailyDataService.delDataBeforeToday();
    }

    @Test
    public void openPrize(){
        remoteHappyCodeDetailService.randomPrizeCode();
    }

    @Test
    public void testInitRecord(){
        HappyCodeExchangeParam param = new HappyCodeExchangeParam();
        param.setPartnerUserId("1");
        param.setCodeCount(10);
        param.setConsumerId(1539361L);
        param.setPhaseId(13L);
        param.setAppId(1L);
        param.setActId(16L);
        Assert.assertNotNull(remoteHappyCodeUserRecordService.initUserRecord(param));
    }

    @Test
    public void test(){
      List<Long> list =    remoteHappyCodeUserRecordService.selectLastestJoinData(15L,10);
      System.out.println(list.size());
    }

    @Test
    public void testSelectHistory(){
        Page<HappyCodeUserRecordDto> page =  remoteHappyCodeUserRecordService.selectUserHistory(16L, 1539361L, 1,10);
        Assert.assertTrue(page.getTotalCount() > 0);
    }

    @Test
    public void testDealAfterSubCreditsCallBack(){
        remoteHappyCodeUserRecordService.dealAfterSubCreditsCallBack("122fd158656049858f7f944deb140fa8",false);
    }

    @Test
    public void testDealAfterReturnCreditsCallBack(){
        remoteHappyCodeUserRecordService.dealAfterReturnCreditsCallBack("957598485100790289",true);
    }
}
