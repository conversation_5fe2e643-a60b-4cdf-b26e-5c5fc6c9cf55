package cn.com.duiba.activity.center.api.remoteservice.activity;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.dcommons.domain.Tuple;
import cn.com.duiba.service.exception.BusinessException;
import cn.com.duiba.wolf.dubbo.DubboResult;
import com.alibaba.fastjson.JSON;
import org.javatuples.Pair;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.test.annotation.Rollback;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/2/21 09:58
 * @description:
 */
@Rollback(value = false)
public class RemoteActivityServiceTest extends TransactionalTestCaseBase {

	@Resource
	private RemoteActivityService remoteActivityService;

	@Test
	public void testGetOperatingActivityDtos() throws BusinessException{
		List<Tuple.Tuple2<Integer, Long>> activities = new ArrayList<>();
		activities.add(Tuple.tuple(12, 12461L));
		activities.add(Tuple.tuple(12, 12460L));
		activities.add(Tuple.tuple(60, 86L));
		DubboResult<List<OperatingActivityDto>> result = remoteActivityService.getOperatingActivityDtos(1L, activities);
		System.out.println(JSON.toJSON(result.getResult()));
		Assert.assertTrue(result.isSuccess());
		Assert.assertNotNull(result.getResult());
	}

	@Test
	public void testGetOperatingActivityDtosApi() throws BizException{
		List<Pair<Integer, Long>> activities = new ArrayList<>();
		activities.add(Pair.with(12, 12461L));
		activities.add(Pair.with(12, 12460L));
		activities.add(Pair.with(60, 86L));
		DubboResult<List<OperatingActivityDto>> result = remoteActivityService.getOperatingActivityDtosApi(1L, activities);
		System.out.println(JSON.toJSON(result.getResult()));
		Assert.assertTrue(result.isSuccess());
		Assert.assertNotNull(result.getResult());
	}

	@Test
	public void testGetUrlMap() throws BusinessException{
		List<Tuple.Tuple2<Integer, Long>> activities = new ArrayList<>();
		activities.add(Tuple.tuple(12, 12461L));
		activities.add(Tuple.tuple(12, 12460L));
		activities.add(Tuple.tuple(60, 86L));
		activities.add(Tuple.tuple(7, 12452L));
		DubboResult<Map<Long, String>> result = remoteActivityService.getUrlMap(activities);
		System.out.println(JSON.toJSON(result.getResult()));
		Assert.assertTrue(result.isSuccess());
		Assert.assertNotNull(result.getResult());
	}

	@Test
	public void testGetUrlMapApi() throws BizException{
		List<Pair<Integer, Long>> activities = new ArrayList<>();
		activities.add(Pair.with(12, 12461L));
		activities.add(Pair.with(12, 12460L));
		activities.add(Pair.with(60, 86L));
		activities.add(Pair.with(7, 12452L));
		DubboResult<Map<Long, String>> result = remoteActivityService.getUrlMapApi(activities);
		System.out.println(JSON.toJSON(result.getResult()));
		Assert.assertTrue(result.isSuccess());
		Assert.assertNotNull(result.getResult());
	}

	@Test
	public void testGetUrlMapByAppId() throws BusinessException{
		List<Tuple.Tuple2<Integer, Long>> activities = new ArrayList<>();
		activities.add(Tuple.tuple(12, 12461L));
		activities.add(Tuple.tuple(12, 12460L));
		activities.add(Tuple.tuple(60, 86L));
		activities.add(Tuple.tuple(7, 12452L));
		DubboResult<Map<Long, String>> result = remoteActivityService.getUrlMapByAppId(activities, 1L);
		System.out.println(JSON.toJSON(result.getResult()));
		Assert.assertTrue(result.isSuccess());
		Assert.assertNotNull(result.getResult());
	}

	@Test
	public void testGetUrlMapByAppIdApi() throws BizException{
		List<Pair<Integer, Long>> activities = new ArrayList<>();
		activities.add(Pair.with(12, 12461L));
		activities.add(Pair.with(12, 12460L));
		activities.add(Pair.with(60, 86L));
		activities.add(Pair.with(7, 12452L));
		DubboResult<Map<Long, String>> result = remoteActivityService.getUrlMapByAppIdApi(activities, 1L);
		System.out.println(JSON.toJSON(result.getResult()));
		Assert.assertTrue(result.isSuccess());
		Assert.assertNotNull(result.getResult());
	}
}
