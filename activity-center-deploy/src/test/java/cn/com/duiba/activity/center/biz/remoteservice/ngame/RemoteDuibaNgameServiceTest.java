package cn.com.duiba.activity.center.biz.remoteservice.ngame;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.remoteservice.ngame.RemoteDuibaNgameService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import com.netflix.discovery.converters.Auto;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by sty on 12/10/17.
 */
@Transactional(DsConstants.DATABASE_NGAME)
public class RemoteDuibaNgameServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteDuibaNgameService remoteDuibaNgameService;
    @Test
    public void findByStatusOpen(){
        Assert.assertNotNull(remoteDuibaNgameService.findByStatusOpen(null));
        remoteDuibaNgameService.findByStatusOpen("");
        remoteDuibaNgameService.findByStatusOpen("test");
    }
}
