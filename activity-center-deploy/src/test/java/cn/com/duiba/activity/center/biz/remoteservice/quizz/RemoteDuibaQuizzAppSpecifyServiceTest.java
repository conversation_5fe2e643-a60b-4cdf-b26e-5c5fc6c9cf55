package cn.com.duiba.activity.center.biz.remoteservice.quizz;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import cn.com.duiba.activity.center.api.dto.quizz.DuibaQuizzAppSpecifyDto;
import cn.com.duiba.activity.center.api.remoteservice.quizz.RemoteDuibaQuizzAppSpecifyServiceInner;
import cn.com.duiba.activity.center.biz.dao.quizz.QuizzBaseTest;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/5/26.
 */
@Ignore
public class RemoteDuibaQuizzAppSpecifyServiceTest extends QuizzBaseTest {

    @Resource
    private RemoteDuibaQuizzAppSpecifyServiceInner duibaQuizzAppSpecifyService;

    private DuibaQuizzAppSpecifyDto     DuibaQuizzAppSpecifyDto;

    @Before
    public void testInsert() {
        DuibaQuizzAppSpecifyDto e = new DuibaQuizzAppSpecifyDto();
        TestUtils.setRandomAttributesForBean(e, false);
        duibaQuizzAppSpecifyService.insert(e);
        DuibaQuizzAppSpecifyDto = e;
    }

    @Test
    public void testFind() {
        DuibaQuizzAppSpecifyDto e = DuibaQuizzAppSpecifyDto;
        DuibaQuizzAppSpecifyDto e1 = duibaQuizzAppSpecifyService.find(e.getId()).getResult();
        assertDO(e, e1);
    }

    @Test
    public void testDelete() {
        DuibaQuizzAppSpecifyDto e = DuibaQuizzAppSpecifyDto;
        duibaQuizzAppSpecifyService.delete(e.getId());
        Assert.assertNull(duibaQuizzAppSpecifyService.find(e.getId()).getResult());
    }

    @Test
    public void testfindByQuizzIdAndAppId() {
        DuibaQuizzAppSpecifyDto e = DuibaQuizzAppSpecifyDto;
        DuibaQuizzAppSpecifyDto e1 = duibaQuizzAppSpecifyService.findByQuizzIdAndAppId(e.getDuibaQuizzId(),
                                                                                       e.getAppId()).getResult();
        assertDO(e, e1);
    }

    @Test
    public void testFindByDuibaQuizzId() {
        DuibaQuizzAppSpecifyDto e = DuibaQuizzAppSpecifyDto;
        Assert.assertTrue(duibaQuizzAppSpecifyService.findByDuibaQuizzId(e.getDuibaQuizzId()).getResult().size() > 0);
    }

    @Test
    public void testFindByDuibaQuizzAndApp() {
        DuibaQuizzAppSpecifyDto e = DuibaQuizzAppSpecifyDto;
        DuibaQuizzAppSpecifyDto e1 = duibaQuizzAppSpecifyService.findByDuibaQuizzAndApp(e.getDuibaQuizzId(),
                                                                                        e.getAppId()).getResult();
        assertDO(e, e1);
    }

    private void assertDO(DuibaQuizzAppSpecifyDto e, DuibaQuizzAppSpecifyDto e1) {
        assertDO(e, e1, null);
    }

    private void assertDO(DuibaQuizzAppSpecifyDto e, DuibaQuizzAppSpecifyDto e1, String[] exculdeFields) {
        List<String> exculdeFieldsList = new ArrayList<>();
        exculdeFieldsList.addAll(Arrays.asList(new String[] { "gmtCreate", "gmtModified", "autoOffDate" }));
        if (exculdeFields != null && exculdeFields.length > 0) {
            exculdeFieldsList.addAll(Arrays.asList(exculdeFields));
        }
        TestUtils.assertEqualsReflect(e, e1, false, exculdeFieldsList.toArray(new String[exculdeFieldsList.size() + 1]));// 时间字段是转为String比较的,timeQuantum不存数据库
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtCreate()), DateUtils.getDayStr(e1.getGmtCreate()));
        Assert.assertEquals(DateUtils.getDayStr(e.getGmtModified()), DateUtils.getDayStr(e1.getGmtModified()));
    }

}
