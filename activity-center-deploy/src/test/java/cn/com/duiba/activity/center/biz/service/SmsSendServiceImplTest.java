package cn.com.duiba.activity.center.biz.service;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.tools.service.SmsRemindService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/6/13.
 */
public class SmsSendServiceImplTest extends TransactionalTestCaseBase {

    @Autowired
    private SmsRemindService smsRemindService;

    @Test
    public void xtest() {
        try {
            smsRemindService.sendCreditsAlarmSms("13589128950","testapp",1,2);
            smsRemindService.sendDevExceptionAlarmSms("13589128950","testapp",2);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
