package cn.com.duiba.activity.center.biz.service.label;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.label.ConsumerLabelStrategyDto;
import cn.com.duiba.activity.center.api.dto.seconds_kill.DuibaSecondsKillActivityBrickDto;
import cn.com.duiba.activity.center.api.enums.label.ConsumerLabelType;
import cn.com.duiba.activity.center.api.remoteservice.label.RemoteConsumerLabelService;
import cn.com.duiba.activity.center.api.remoteservice.label.param.LabelPassBaseParam;
import cn.com.duiba.activity.center.api.remoteservice.label.param.LabelPassGoodsFilterParam;
import cn.com.duiba.activity.center.api.remoteservice.label.param.LabelPassGoodsFilterParamBuilder;
import cn.com.duiba.activity.center.api.remoteservice.label.result.LabelPassResult;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.service.label.impl.strategy.impl.GoodsConsumerLabelStrategy;
import cn.com.duiba.activity.center.biz.service.seconds_kill.DuibaSecondsKillActivityBrickService;
import cn.com.duiba.wolf.utils.TestUtils;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-07-28
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class ConsumerLabelServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteConsumerLabelService remoteConsumerLabelService;


    @Test
    public void goodsFilterIsPassTest(){
        LabelPassGoodsFilterParam param = new LabelPassGoodsFilterParamBuilder()
                .setAppId(1L)
                .setUserId("1")
                .setStrategyType(ConsumerLabelType.GOODS_FILTER.getType())
                .setAppItemIds()
                .setStrategyContents(Arrays.asList("1716077"))
                .setCid(1L)
                .builder();
        LabelPassResult result = remoteConsumerLabelService.isPass(param);
        System.out.println(result);
    }

    @Test
    public void getPopConfigTest(){

        ConsumerLabelStrategyDto consumerLabelStrategyDto = remoteConsumerLabelService.getPopConfig(1L, "1", "126363267181481");
        System.out.println(JSON.toJSON(consumerLabelStrategyDto));
    }

}
