package cn.com.duiba.activity.center.biz.remoteservice.rob;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.PaginationDto;
import cn.com.duiba.activity.center.api.dto.rob.TodayRobSeckillConfigDto;
import cn.com.duiba.activity.center.api.dto.rob.TodayRobSeckillDto;
import cn.com.duiba.activity.center.api.dto.rob.TodayRobSeckillListDto;
import cn.com.duiba.activity.center.api.remoteservice.rob.RemoteTodayRobSeckillBackendService;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.rob.TodayRobSeckillConfigEntity;
import cn.com.duiba.activity.center.biz.entity.rob.TodayRobSeckillEntity;
import cn.com.duiba.activity.center.biz.service.rob.TodayRobSeckillService;
import cn.com.duiba.wolf.dubbo.DubboResult;
/**
 * RemoteTodayRobSeckillBackendServiceImplTest <br/>
 * Date:     2016年9月30日 下午4:07:30 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
@Transactional(DsConstants.DATABASE_CREDITS_ACTIVITY)

public class RemoteTodayRobSeckillBackendServiceImplTest extends TransactionalTestCaseBase{
	@Autowired
	private  RemoteTodayRobSeckillBackendService remoteTodayRobSeckillBackendService;
	@Autowired
	private  TodayRobSeckillService todayRobSeckillService;
	
	@Before
	public void before(){
		
	}
		
	
	
	@Test
	public void  findSeckillConfigs(){
		TodayRobSeckillEntity t = getTodayRobSeckillEntity();
		getTodayRobSeckillConfig( t);
		DubboResult<List<TodayRobSeckillConfigDto>> ret = remoteTodayRobSeckillBackendService.findSeckillConfigs(t.getId());
		Assert.assertEquals(2, ret.getResult().size());//断言查询两条
	}
	
	@Test
	public void  findSeckillById(){
		TodayRobSeckillEntity t = getTodayRobSeckillEntity();
		getTodayRobSeckillConfig( t);
		DubboResult<TodayRobSeckillDto> ret = remoteTodayRobSeckillBackendService.findSeckillById(t.getId());
		Assert.assertEquals(true, ret.getResult() !=null);//断言查询1条
	}
	
	
	@Test
	public void seveSeckillConfigs(){
		List<TodayRobSeckillConfigDto> list = new ArrayList<TodayRobSeckillConfigDto>();
		TodayRobSeckillConfigDto trs = new TodayRobSeckillConfigDto();
		TodayRobSeckillConfigDto trs2 = new TodayRobSeckillConfigDto();
		trs.setActivityId(5L);
		trs.setPayload(1);
		trs.setPushTime(new Date());
		trs2.setActivityId(5L);
		trs2.setPayload(1);
		trs2.setPushTime(new Date());
		list.add(trs);
		list.add(trs2);
		TodayRobSeckillDto todayRobSeckillDto= new TodayRobSeckillDto();
		todayRobSeckillDto.setStartTime(new Date());
		DubboResult<Boolean> ret = remoteTodayRobSeckillBackendService.seveSeckillConfigs(list, todayRobSeckillDto);
		Assert.assertEquals(ret.getResult(),true);
	}
	
	@Test
	public void findTodayRobSeckillList(){
		getTodayRobSeckillEntity();
		DubboResult<PaginationDto<TodayRobSeckillListDto>> ret = remoteTodayRobSeckillBackendService.findTodayRobSeckillList(0, 10);
		Assert.assertEquals(ret.getResult() !=null,true);
	}
	
	@Test
	public void delTodayRobSeckill(){
		Assert.assertEquals(remoteTodayRobSeckillBackendService.delTodayRobSeckill(getTodayRobSeckillEntity().getId()) != null,true);
	}
	
	@Test
	public void enableTodayRobSeckill(){
		Assert.assertEquals(remoteTodayRobSeckillBackendService.enableTodayRobSeckill(getTodayRobSeckillEntity().getId(),true)  != null,true);
	}
	
	
	
	
	
	private TodayRobSeckillEntity getTodayRobSeckillEntity(){
		TodayRobSeckillEntity trc = new TodayRobSeckillEntity();
		trc.setStartTime(new Date());
		todayRobSeckillService.saveSeckill(trc);
		return trc;
	}
	
	private List<TodayRobSeckillConfigEntity> getTodayRobSeckillConfig(TodayRobSeckillEntity trs){
		List<TodayRobSeckillConfigEntity> list = new ArrayList<TodayRobSeckillConfigEntity>();
		TodayRobSeckillConfigEntity tc1 = new TodayRobSeckillConfigEntity();
		TodayRobSeckillConfigEntity tc2 = new TodayRobSeckillConfigEntity();
		
		tc1.setActivityId(5L);
		tc1.setPayload(1);
		tc1.setPushTime(new Date());
		tc1.setTodayRobSeckillId(trs.getId());
		
		tc2.setActivityId(5L);
		tc2.setPayload(1);
		tc2.setPushTime(new Date());
		tc2.setTodayRobSeckillId(trs.getId());
		
		list.add(tc1);
		list.add(tc2);
		
		todayRobSeckillService.saveSeckillConfigBatch(list);
		
		return list;
	}
}
