package cn.com.duiba.activity.center.biz.dao.hdtool;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.hdtool.HdtoolSkinDefaultEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/7/6.
 */
@Transactional(DsConstants.DATABASE_CREDITS)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class HdtoolDefaultSkinDataDaoTest extends TransactionalTestCaseBase {
    @Autowired
    private HdtoolDefaultSkinDataDao hdtoolDefaultSkinDataDao;

    @Test
    public void testInsert() throws Exception {
        HdtoolSkinDefaultEntity entity=new HdtoolSkinDefaultEntity();
        TestUtils.setRandomAttributesForBean(entity,false);
        hdtoolDefaultSkinDataDao.insert(entity);
    }

    @Test
    public void testUpdateData() throws Exception {
        HdtoolSkinDefaultEntity entity=new HdtoolSkinDefaultEntity();
        TestUtils.setRandomAttributesForBean(entity,false);
        hdtoolDefaultSkinDataDao.insert(entity);
        entity.setHdtoolType("test");
        Assert.assertTrue(hdtoolDefaultSkinDataDao.updateData(entity)>0);
    }

    @Test
    public void testSelectBaseHdtoolData() throws Exception {
        HdtoolSkinDefaultEntity entity=new HdtoolSkinDefaultEntity();
        TestUtils.setRandomAttributesForBean(entity,false);
        hdtoolDefaultSkinDataDao.insert(entity);
        Assert.assertNotNull(hdtoolDefaultSkinDataDao.selectBaseHdtoolData(entity.getTemplateType(), entity.getType()));
    }

    @Test
    public void testSelectConfigAndStyleData() throws Exception {
        HdtoolSkinDefaultEntity entity=new HdtoolSkinDefaultEntity();
        TestUtils.setRandomAttributesForBean(entity,false);
        hdtoolDefaultSkinDataDao.insert(entity);
        Assert.assertNotNull(hdtoolDefaultSkinDataDao.selectConfigAndStyleData(entity.getTemplateType(),entity.getType()));
    }

    @Test
    public void testSelectAllByPagination() throws Exception {
        HdtoolSkinDefaultEntity entity=new HdtoolSkinDefaultEntity();
        TestUtils.setRandomAttributesForBean(entity,false);
        hdtoolDefaultSkinDataDao.insert(entity);
        Assert.assertTrue(hdtoolDefaultSkinDataDao.selectAllByPagination(0,10).size()>0);
    }

    @Test
    public void testSelectAllByPaginationCount() throws Exception {
        HdtoolSkinDefaultEntity entity=new HdtoolSkinDefaultEntity();
        TestUtils.setRandomAttributesForBean(entity,false);
        hdtoolDefaultSkinDataDao.insert(entity);
        Assert.assertNotNull(hdtoolDefaultSkinDataDao.selectAllByPaginationCount()>0);
    }
}
