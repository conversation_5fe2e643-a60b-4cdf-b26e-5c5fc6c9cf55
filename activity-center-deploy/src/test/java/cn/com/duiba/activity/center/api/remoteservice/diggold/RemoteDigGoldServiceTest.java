package cn.com.duiba.activity.center.api.remoteservice.diggold;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.diggold.DigGoldConfigAndPrizesDto;
import cn.com.duiba.activity.center.api.dto.diggold.DigGoldConfigDto;
import cn.com.duiba.activity.center.api.dto.diggold.DigGoldPrizeDto;
import cn.com.duiba.activity.center.api.dto.diggold.DigGoldRecordDto;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * @Author: lufeng
 * @Description:
 * @Date: Created in 2019/7/1
 */
//@Rollback(value = false)
public class RemoteDigGoldServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteDigGoldService remoteDigGoldService;

    @Test
    public void insertConfigTest() {
        DigGoldConfigDto digGoldConfigDto = new DigGoldConfigDto();
        digGoldConfigDto.setAppId(1L);
        digGoldConfigDto.setBannerImage("bannner");
        digGoldConfigDto.setBox(10);
        digGoldConfigDto.setContinueTime(20);
        digGoldConfigDto.setCredits(5L);
        digGoldConfigDto.setEndTime(new Date());
        digGoldConfigDto.setStartTime(new Date());
        digGoldConfigDto.setFreeScope(1);
        digGoldConfigDto.setFreeTimes(3);
        digGoldConfigDto.setLimitScope(1);
        digGoldConfigDto.setLimitTimes(8);
        digGoldConfigDto.setRule("guizhe");
        digGoldConfigDto.setTitle("titile");
        digGoldConfigDto.setPreventBrush(false);
        digGoldConfigDto.setInterfaceConfig("config");
        Long id = remoteDigGoldService.insertConfig(digGoldConfigDto);
        System.out.println("id=" + id);
    }

    @Test
    public void insertConfigAndPrizesTest() {
        DigGoldConfigAndPrizesDto dto = new DigGoldConfigAndPrizesDto();

        DigGoldConfigDto digGoldConfigDto = new DigGoldConfigDto();
        digGoldConfigDto.setAppId(1L);
        digGoldConfigDto.setBannerImage("bannner");
        digGoldConfigDto.setBox(10);
        digGoldConfigDto.setContinueTime(20);
        digGoldConfigDto.setCredits(5L);
        digGoldConfigDto.setEndTime(new Date());
        digGoldConfigDto.setStartTime(new Date());
        digGoldConfigDto.setFreeScope(1);
        digGoldConfigDto.setFreeTimes(3);
        digGoldConfigDto.setLimitScope(1);
        digGoldConfigDto.setLimitTimes(8);
        digGoldConfigDto.setRule("guizhe");
        digGoldConfigDto.setTitle("titile");
        digGoldConfigDto.setPreventBrush(false);
        digGoldConfigDto.setInterfaceConfig("config");
        dto.setDigGoldConfigDto(digGoldConfigDto);

        List<DigGoldPrizeDto> prizeList = Lists.newArrayList();
        DigGoldPrizeDto prizeDto = new DigGoldPrizeDto();
        prizeDto.setHasLow(true);
        prizeDto.setLow(3);
        prizeDto.setAppItemId(11L);
        prizeDto.setImg("tupian");
        prizeDto.setHasLimit(true);
        prizeDto.setLimitCount(3);
        prizeDto.setName("mingcheng");
        prizeDto.setProbability(50);
        prizeDto.setRemaind(10);
        prizeDto.setTitle("zidingyi");
        prizeDto.setType("object");
        prizeDto.setValue(null);
        prizeList.add(prizeDto);
        dto.setPrizeList(prizeList);

        Long id = remoteDigGoldService.insertConfigAndPrizes(dto);
        System.out.println("id=" + id);
    }


    @Test
    public void insertPrizeTest() {
        List<DigGoldPrizeDto> prizeList = Lists.newArrayList();
        DigGoldPrizeDto prizeDto = new DigGoldPrizeDto();
        prizeDto.setAppId(1L);
        prizeDto.setConfigId(11L);
        prizeDto.setHasLow(true);
        prizeDto.setLow(3);
        prizeDto.setAppItemId(11L);
        prizeDto.setImg("tupian");
        prizeDto.setHasLimit(true);
        prizeDto.setLimitCount(3);
        prizeDto.setName("mingcheng");
        prizeDto.setProbability(50);
        prizeDto.setRemaind(10);
        prizeDto.setTitle("zidingyi");
        prizeDto.setType("object");
        prizeDto.setValue(null);
        prizeList.add(prizeDto);
        boolean isSuc = remoteDigGoldService.batchInsertPrizes(prizeList);
        System.out.println("结果："+isSuc);
    }

    @Test
    public void updateConfigAndPrizesTest() {
        DigGoldConfigAndPrizesDto dto = new DigGoldConfigAndPrizesDto();

        DigGoldConfigDto digGoldConfigDto = new DigGoldConfigDto();
        digGoldConfigDto.setId(19L);
        digGoldConfigDto.setAppId(1L);
        digGoldConfigDto.setBannerImage("bannner2");
        digGoldConfigDto.setSmallImage("small2");
        digGoldConfigDto.setBox(10);
        digGoldConfigDto.setContinueTime(20);
        digGoldConfigDto.setCredits(5L);
        digGoldConfigDto.setEndTime(new Date());
        digGoldConfigDto.setStartTime(new Date());
        digGoldConfigDto.setFreeScope(1);
        digGoldConfigDto.setFreeTimes(3);
        digGoldConfigDto.setLimitScope(1);
        digGoldConfigDto.setLimitTimes(8);
        digGoldConfigDto.setRule("guizhe2");
        digGoldConfigDto.setTitle("titile2");
        digGoldConfigDto.setPreventBrush(false);
        digGoldConfigDto.setInterfaceConfig("config2");
        dto.setDigGoldConfigDto(digGoldConfigDto);

        List<DigGoldPrizeDto> prizeList = Lists.newArrayList();
        DigGoldPrizeDto prizeDto = new DigGoldPrizeDto();
        prizeDto.setHasLow(true);
        prizeDto.setLow(3);
        prizeDto.setAppItemId(111L);
        prizeDto.setImg("tupian1");
        prizeDto.setHasLimit(true);
        prizeDto.setLimitCount(3);
        prizeDto.setName("mingcheng1");
        prizeDto.setProbability(50);
        prizeDto.setRemaind(10);
        prizeDto.setTitle("zidingyi1");
        prizeDto.setType("object1");
        prizeDto.setValue(null);
        prizeList.add(prizeDto);
        dto.setPrizeList(prizeList);

        Boolean isSuccess = remoteDigGoldService.updateConfigAndPrizes(dto);
        System.out.println("结果=" + isSuccess);
    }

    @Test
    public void listRecordsTest() {
        List<Long> ids = Lists.newArrayList();
        ids.add(22L);
        ids.add(23L);
        List<DigGoldRecordDto> list = remoteDigGoldService.listRecords(ids);
        list.forEach(digGoldRecordDto -> System.out.println(digGoldRecordDto.getId()));
    }

    @Test
    public void listPrizesTest() {
        List<Long> ids = Lists.newArrayList();
        ids.add(152L);
        ids.add(153L);
        List<DigGoldPrizeDto> list = remoteDigGoldService.listPrizeByIds(ids);
        list.forEach(dto -> System.out.println(dto.getName()));
    }

    @Test
    public void getConfigTest() {
        remoteDigGoldService.getConfig(1L);
    }


}
