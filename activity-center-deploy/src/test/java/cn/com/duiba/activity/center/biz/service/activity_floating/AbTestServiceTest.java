package cn.com.duiba.activity.center.biz.service.activity_floating;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.activity_floating.AbTestDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

/** 
 * ClassName:AbTestServiceTest.java <br/>
 * <AUTHOR> 
 * @date 创建时间：2017年8月9日 下午7:30:11 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
@Transactional(DsConstants.DATABASE_ACT_COM_CONF)
public class AbTestServiceTest extends TransactionalTestCaseBase {
	/**
	 * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
	 */
	protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS_ACTIVITY;
	@Resource
	private AbTestService abTestService;
	private AbTestDto abtestDto;
	
	@Before
	public void testInsert(){
		List<Long> list = Lists.newArrayList();
		list.add(1L);
		list.add(2L);
		list.add(3L);
		abTestService.saveOrUpdateAbTest(null, AbTestDto.TYPEFLOAT, AbTestDto.RELATION_TYPE_HDTOOL, 123L, list, 1L);
		Map<String, Object> paramMap = Maps.newHashMap();
		paramMap.put("abTesType", AbTestDto.TYPEFLOAT);
		paramMap.put("relationId", 123L);
		paramMap.put("relationType", AbTestDto.RELATION_TYPE_HDTOOL);
		abtestDto = abTestService.findAbTestInfo(paramMap);
	}
	@Test
	public void testSaveOrUpdateAbTest(){
		List<Long> list = Lists.newArrayList();
		list.add(1L);
		list.add(2L);
		list.add(3L);
		list.add(4L);
		abTestService.saveOrUpdateAbTest(abtestDto.getId(), AbTestDto.TYPEFLOAT, AbTestDto.RELATION_TYPE_HDTOOL, 123L, list, 2L);
		Map<String, Object> paramMap = Maps.newHashMap();
		paramMap.put("abTesType", AbTestDto.TYPEFLOAT);
		paramMap.put("relationId", 123L);
		paramMap.put("relationType", AbTestDto.RELATION_TYPE_HDTOOL);
		AbTestDto abtestDtoNew = abTestService.findAbTestInfo(paramMap);
		Assert.assertNotNull(abtestDtoNew);
		List<AbTestDto> lists = abTestService.findAbTestListInfo(abtestDto.getId());
		Assert.assertNotNull(lists);
	}
	@Test
	public void testFindAbTestInfo(){
		List<AbTestDto> list = abTestService.findAbTestList();
		Assert.assertNotNull(list);
	}
	@Test
	public void testFindAbTestListInfo(){
		List<AbTestDto> list = abTestService.findAbTestListInfo(abtestDto.getId());
		Assert.assertNotNull(list);
	}
}
