package cn.com.duiba.activity.center.biz.dao.chaos;

/**
 * Created by yansen on 16/6/22.
 */

import java.util.Date;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.entity.chaos.RetryOrdersFasterEntity;
import cn.com.duiba.wolf.utils.TestUtils;

/**
 * Created by yansen on 16/6/3.
 */
@Transactional(RetryOrdersFasterDaoTest.THIS_DATABASE_SCHEMA)//这行注释用于决定使用哪个事务管理器以支持自动回滚
public class RetryOrdersFasterDaoTest extends TransactionalTestCaseBase {
    /**
     * 支持事务的测试类都需要写这个静态变量,决定使用哪个数据库（会根据这个自动选择对应的事务管理器）,以支持测试事务自动回滚
     */
    protected static final String THIS_DATABASE_SCHEMA = DsConstants.DATABASE_CREDITS_ACTIVITY;

    @Resource
    private RetryOrdersFasterDao retryOrdersFasterDao;

    @Test
    public void testInsert() {
        RetryOrdersFasterEntity r=new RetryOrdersFasterEntity(true);
        TestUtils.setRandomAttributesForBean(r,false);
        retryOrdersFasterDao.insert(r);
    }

    @Test
    public void testDeleteByOrderId() {
        RetryOrdersFasterEntity r=new RetryOrdersFasterEntity(true);
        TestUtils.setRandomAttributesForBean(r,false);
        retryOrdersFasterDao.insert(r);
        Assert.assertNotNull(retryOrdersFasterDao.findByOrderId(r.getOrderId()));
        retryOrdersFasterDao.deleteByOrderId(r.getOrderId());
        Assert.assertNull(retryOrdersFasterDao.findByOrderId(r.getOrderId()));
    }

    @Test
    public void testFindByOrderId() {
        RetryOrdersFasterEntity r=new RetryOrdersFasterEntity(true);
        TestUtils.setRandomAttributesForBean(r,false);
        retryOrdersFasterDao.insert(r);
        Assert.assertNotNull(retryOrdersFasterDao.findByOrderId(r.getOrderId()));

    }

    @Test
    public void testFindEndtimeRetryOrder() {
        RetryOrdersFasterEntity r=new RetryOrdersFasterEntity(true);
        TestUtils.setRandomAttributesForBean(r,false);
        r.setEndTime(new Date(System.currentTimeMillis()+1000000l));
        retryOrdersFasterDao.insert(r);
        Assert.assertTrue(retryOrdersFasterDao.findEndtimeRetryOrder().size()>0);
    }

}
