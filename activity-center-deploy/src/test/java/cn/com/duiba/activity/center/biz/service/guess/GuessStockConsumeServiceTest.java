package cn.com.duiba.activity.center.biz.service.guess;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.guess.GuessStockConsumeDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.wolf.utils.TestUtils;

@Transactional(DsConstants.DATABASE_GUESS)
public class GuessStockConsumeServiceTest extends TransactionalTestCaseBase{
	@Autowired
	private GuessStockConsumeService guessStockConsumeService;
	
	private GuessStockConsumeDto info;
	
	@Before
    public void insertTest(){
		info = TestUtils.createRandomBean(GuessStockConsumeDto.class);
		info.setBizId("10");
		guessStockConsumeService.insert(info);
    }
    
	@Test
    public void findByBizIdTest(){
		GuessStockConsumeDto e = guessStockConsumeService.findByBizId(info.getGuessStockId(), info.getBizId(), info.getAction());
		Assert.assertNotNull(e);
    }

}
