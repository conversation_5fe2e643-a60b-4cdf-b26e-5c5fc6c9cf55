package cn.com.duiba.activity.center.api.remoteservice.shuqi;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.shuqipk.PkTeamMemberInfoDto;
import cn.com.duiba.activity.center.api.dto.shuqipk.PkTeamRecordDto;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.wolf.utils.DateUtils;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 18/7/17 21:31
 * @description:
 */
@Transactional(DsConstants.DATABASE_CUSTOM_RECORD)
@Rollback(value = false)
public class RemotePkTeamRecordServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemotePkTeamRecordService remotePkTeamRecordService;

    @Autowired
    private RemotePkTeamMemberInfoService remotePkTeamMemberInfoService;

    @Test
    public void testBatchInsert() {
        List<PkTeamMemberInfoDto> pkTeamMemberInfoDto = remotePkTeamMemberInfoService.getListByTeamId(12L);
        List<PkTeamRecordDto> records = new ArrayList<>();
//        pkTeamMemberInfoDto.forEach(r -> {
//            PkTeamRecordDto dto = new PkTeamRecordDto();
//            dto.setAppId(r.getAppId());
//            dto.setActivityId(r.getActivityId());
//            dto.setTeamId(r.getTeamId());
//            dto.setTeamName("狗贼");
//            dto.setRecordDate(DateUtils.getStartTime(new Date()).getTime()/1000);
//            dto.setRecordType(3);
//            dto.setReadValue(0L);
//            dto.setRelateId(0L);
//            dto.setEnemyName(null);
//            dto.setEnemyReadValue(0L);
//            dto.setSyncStatus(0);
//            records.add(dto);
//        });
        for (int i =0;i< 4000; i++) {
            PkTeamRecordDto dto = new PkTeamRecordDto();
            dto.setAppId(1L);
            dto.setActivityId(12431L);
            dto.setTeamId(i * 10L);
            dto.setTeamName("狗贼");
            dto.setRecordDate(DateUtils.getDayStartTime(new Date()).getTime()/1000);
            dto.setRecordType(3);
            dto.setReadValue(0L);
            dto.setRelateId(0L);
            dto.setEnemyName(null);
            dto.setEnemyReadValue(0L);
            dto.setSyncStatus(0);
            records.add(dto);
        }
        remotePkTeamRecordService.insertBatch(records);
    }

    @Test
    public void testGetById(){
        PkTeamRecordDto pkTeamRecordDto = remotePkTeamRecordService.getById(9L);
        System.out.println(JSON.toJSON(pkTeamRecordDto));
        Assert.assertNotNull(pkTeamRecordDto);
    }

    @Test
    public void testGetByTeamIdAndDate(){
        PkTeamRecordDto pkTeamRecordDto = remotePkTeamRecordService.getByTeamIdAndDate(3L, getRecordDate(new Date()));
        System.out.println(JSON.toJSON(pkTeamRecordDto));
        Assert.assertNotNull(pkTeamRecordDto);
    }

    private Long getRecordDate(Date date){
        return DateUtils.getDayStartTime(date).getTime()/1000;
    }

    @Test
    public void testAddPkRecords() throws BizException{
        PkTeamRecordDto pkTeamRecordDto = remotePkTeamRecordService.addPkRecords(5L, 4L, getRecordDate(DateUtils.daysAddOrSub(new Date(), -1)));
        System.out.println(JSON.toJSON(pkTeamRecordDto));
        Assert.assertNotNull(pkTeamRecordDto);
    }

    @Test
    public void testUpdateReadValue() throws BizException {
        PkTeamRecordDto pkTeamRecordDto = remotePkTeamRecordService.getById(1000370010004L);
        Map<Long, Long> consumerReadTimeMap = new HashMap<>();
        consumerReadTimeMap.put(92L, 212L);
        consumerReadTimeMap.put(97L, 117L);
        consumerReadTimeMap.put(98L, 218L);
        consumerReadTimeMap.put(115L, 215L);
        consumerReadTimeMap.put(1539361L, 16L);
        consumerReadTimeMap.put(117L, 27L);
        PkTeamRecordDto pkTeamReadValueDto = remotePkTeamRecordService.updateReadValue(pkTeamRecordDto, consumerReadTimeMap, true);
        System.out.println(JSON.toJSON(pkTeamReadValueDto));
        Assert.assertNotNull(pkTeamReadValueDto);
    }

    @Test
    public void testGetPastByTeamId(){
        List<PkTeamRecordDto> pkTeamRecordDtoList = remotePkTeamRecordService.getPastByTeamId(3L);
        System.out.println(JSON.toJSON(pkTeamRecordDtoList));
        Assert.assertNotNull(pkTeamRecordDtoList);
    }
}
