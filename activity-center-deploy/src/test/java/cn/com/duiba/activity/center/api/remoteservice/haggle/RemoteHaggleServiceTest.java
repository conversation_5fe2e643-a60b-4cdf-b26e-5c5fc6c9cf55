package cn.com.duiba.activity.center.api.remoteservice.haggle;

import cn.com.duiba.activity.center.TransactionalTestCaseBase;
import cn.com.duiba.activity.center.api.dto.haggle.HaggleConfigAndPrizeDto;
import cn.com.duiba.activity.center.api.dto.haggle.HaggleHelpRecordDto;
import cn.com.duiba.activity.center.api.dto.haggle.HaggleOpenRecordDto;
import cn.com.duiba.activity.center.api.dto.haggle.HagglePrizeDto;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * @Author: lufeng
 * @Description:
 * @Date: Created in 2019/9/23
 */
public class RemoteHaggleServiceTest extends TransactionalTestCaseBase {

    @Autowired
    private RemoteHaggleService remoteHaggleService;

    @Test
    public void insertConfigAndPrizesTest() {
        HaggleConfigAndPrizeDto dto = new HaggleConfigAndPrizeDto();
        dto.setPublish(true);
        dto.setChannel(1);
        dto.setTitle("砍价活动标题");
        dto.setRule("砍价活动规则");
        dto.setAppId(1L);
        dto.setStartTime(new Date());
        dto.setEndTime(new Date());
        dto.setSmallImage("砍价活动图片");
        dto.setBannerImage("bannerImage");
        dto.setBarrageSwitch(1);
        dto.setHelpUserType(2);
        dto.setHelpLimit(1);
        dto.setHelpLimitCount(2);
        dto.setOpenLimit(1);
        dto.setOpenLimitCount(2);
        dto.setInterfaceConfig("界面配置");
        dto.setQrCode("二维码");
        dto.setShareIcon("分享图标");
        dto.setShareTitle("分享标题");
        dto.setShareViceTitle("分享副标题");
        dto.setCallbackUrl("回调地址");
        dto.setAutoReply("自动回复");

        List<HagglePrizeDto> prizeDtoList = Lists.newArrayList();
        HagglePrizeDto prizeDto = new HagglePrizeDto();
        prizeDto.setPrizeName("奖品名称");
        prizeDto.setPrizePrice(100L);
        prizeDto.setHelpNum(10);
        prizeDto.setPrizeNum(5);
        prizeDto.setPrizeImage("奖品图片");
        prizeDto.setExpireHour(1);
        prizeDto.setStartTime(new Date());
        prizeDto.setEndTime(new Date());
        prizeDtoList.add(prizeDto);
        dto.setPrizeList(prizeDtoList);

        Long id = remoteHaggleService.insertConfigAndPrizes(dto);
        System.out.println("id============"+id);
    }

    @Test
    public void updateConfigAndPrizesTest() {
        HaggleConfigAndPrizeDto dto = new HaggleConfigAndPrizeDto();
        dto.setId(3L);
        dto.setPublish(true);
        dto.setChannel(1);
        dto.setTitle("砍价活动标题1");
        dto.setRule("砍价活动规则1");
        dto.setAppId(1L);
        dto.setStartTime(new Date());
        dto.setEndTime(new Date());
        dto.setSmallImage("砍价活动图片1");
        dto.setBannerImage("bannerImage1");
        dto.setBarrageSwitch(1);
        dto.setHelpUserType(2);
        dto.setHelpLimit(1);
        dto.setHelpLimitCount(2);
        dto.setOpenLimit(1);
        dto.setOpenLimitCount(2);
        dto.setInterfaceConfig("界面配置1");
        dto.setQrCode("二维码1");
        dto.setShareIcon("分享图标1");
        dto.setShareTitle("分享标题1");
        dto.setShareViceTitle("分享副标题1");
        dto.setCallbackUrl("回调地址1");
        dto.setAutoReply("自动回复1");

        List<HagglePrizeDto> prizeDtoList = Lists.newArrayList();
        HagglePrizeDto prizeDto = new HagglePrizeDto();
        prizeDto.setPrizeName("奖品名称1");
        prizeDto.setPrizePrice(100L);
        prizeDto.setHelpNum(101);
        prizeDto.setPrizeNum(51);
        prizeDto.setPrizeImage("奖品图片1");
        prizeDto.setExpireHour(2);
        prizeDto.setStartTime(new Date());
        prizeDto.setEndTime(new Date());
        prizeDtoList.add(prizeDto);
        dto.setPrizeList(prizeDtoList);
        Boolean isSuc = remoteHaggleService.updateConfigAndPrizes(dto);
        System.out.println("isSuc============"+isSuc);
    }

    @Test
    public void getConfigAndPrizesTest() {
        HaggleConfigAndPrizeDto dto = remoteHaggleService.getConfigAndPrizes(3L);
        System.out.println(JSONObject.toJSONString(dto));
    }

    @Test
    public void insertOpenRecordTest() {
        HaggleOpenRecordDto dto = new HaggleOpenRecordDto();
        dto.setConfigId(3L);
        dto.setAppId(1L);
        dto.setPrizeId(3L);
        dto.setConsumerId(1111L);
        dto.setRecordStatus(1);
        dto.setMoney(521L);
        dto.setMoneyRule("521+1000+123");
        dto.setNickName("昵称");
        dto.setAvatar("头像");
        dto.setEndTime(new Date());
        dto.setFinishTime(new Date());
        dto.setExchangeRecordId(111L);
        Long id = remoteHaggleService.insertOpenRecord(dto);
        System.out.println("id======="+id);
    }

    @Test
    public void insertHelpRecordTest() {
        HaggleHelpRecordDto dto = new HaggleHelpRecordDto();
        dto.setConfigId(3L);
        dto.setAppId(1L);
        dto.setOpenRecordId(1L);
        dto.setConsumerId(1111L);
        dto.setMoney(1000L);
        dto.setNickName("昵称");
        dto.setAvatar("头像");
        Long id = remoteHaggleService.insertHelpRecord(dto);
        System.out.println("id======="+id);
    }

}
