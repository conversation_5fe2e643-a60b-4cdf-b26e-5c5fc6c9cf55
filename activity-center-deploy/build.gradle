apply plugin: "org.springframework.boot"

bootJar {
    archiveName = "activity-center.jar"
}

description = "activity-center-deploy"

dependencies {
    compile project(":activity-center-common")
    compile project(":activity-center-biz")
    compile project(":activity-center-api")

    compile fileTree(dir: "lib", include: "*.jar")

    testCompile("cn.com.duiba:mysql-generator")
    testCompile("org.springframework.boot:spring-boot-starter-test")
}

tasks.withType(JavaCompile) {
	options.encoding = "UTF-8"
}

install{
    enabled = false
}

uploadArchives{
    enabled = false
}
