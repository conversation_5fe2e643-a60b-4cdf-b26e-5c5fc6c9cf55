package cn.com.duiba.activity.center.biz.remoteservice.impl.hdtool;

import cn.com.duiba.activity.center.api.dto.ActivityExtraInfoDto;
import cn.com.duiba.activity.center.api.dto.hdtool.DuibaHdtoolDto;
import cn.com.duiba.activity.center.api.dto.hdtool.DuibaHdtoolOptionsDto;
import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolAppSpecifyDto;
import cn.com.duiba.activity.center.api.dto.quizz.AddActivityDto;
import cn.com.duiba.activity.center.api.remoteservice.hdtool.RemoteDuibaHdtoolServiceNew;
import cn.com.duiba.activity.center.biz.bo.hdtool.DuibaHdtoolBo;
import cn.com.duiba.activity.center.biz.service.hdtool.DuibaHdtoolAppSpecifyService;
import cn.com.duiba.activity.center.biz.service.hdtool.DuibaHdtoolOptionsService;
import cn.com.duiba.activity.center.biz.service.hdtool.DuibaHdtoolService;
import cn.com.duiba.activity.center.biz.service.hdtool.HdtoolSkinDefaultDataSerivce;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.wolf.dubbo.DubboResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by wenqi.huang on 16/5/19.
 */
@RestController
public class RemoteDuibaHdtoolServiceNewImpl implements RemoteDuibaHdtoolServiceNew {
    private static final Logger log = LoggerFactory.getLogger(RemoteDuibaHdtoolServiceNewImpl.class);
    @Resource
    private DuibaHdtoolService duibaHdtoolService;
    @Resource
    private DuibaHdtoolOptionsService duibaHdtoolOptionsService;
    @Autowired
    private HdtoolSkinDefaultDataSerivce hdtoolSkinDefaultDataSerivce;
    @Resource
    private DuibaHdtoolAppSpecifyService duibaHdtoolAppSpecifyService;
    @Resource
    private DuibaHdtoolBo duibaHdtoolBo;

    @Override
    public List<DuibaHdtoolDto> findAutoOff() {
        return duibaHdtoolService.findAutoOff();
    }

    @Override
    public DuibaHdtoolDto find(Long id) {
        return duibaHdtoolService.find(id);
    }

    @Override
    public List<DuibaHdtoolDto> findDuibaHdToolsList(Map<String, Object> paramMap) {
        return duibaHdtoolService.findDuibaHdToolsList(paramMap);
    }

    @Override
    public Integer countDuibaHdToolsList(Map<String, Object> queryMap) {
        return duibaHdtoolService.countDuibaHdToolsList(queryMap);
    }

    @Override
    public List<AddActivityDto> findAllDuibaHdTools(final Long appId) {
        List<AddActivityDto> list= duibaHdtoolService.findAllDuibaHdTools(appId);
        Map<String,String> map = hdtoolSkinDefaultDataSerivce.queryHdtoolBaseSkinMap();
        for(AddActivityDto dto:list){
            String hdtoolName = map.get(dto.getSubType());
            if(hdtoolName !=null){
                dto.setSubType(hdtoolName);
            }else{
                dto.setSubType("砸彩蛋");
            }
        }
        return list;
    }

    @Override
    public List<DuibaHdtoolDto> findAllByIds(List<Long> ids) {
        return duibaHdtoolService.findAllByIds(ids);
    }

    @Override
    public List<DuibaHdtoolOptionsDto> findOptionsByDuibaHdtoolId(Long hdtoolId) {
        return duibaHdtoolOptionsService.findOptionsByDuibaHdtoolId(hdtoolId);
    }

    public Map<Long, List<DuibaHdtoolOptionsDto>> findOptionsByDuibaHdtoolIds(List<Long> hdtoolIds){
        return duibaHdtoolOptionsService.findOptionsByDuibaHdtoolIds(hdtoolIds);
    }

    @Override
    public Integer countOptionsByHdtoolId(Long hdtoolId) {
        return duibaHdtoolOptionsService.countOptionsByHdtoolId(hdtoolId);
    }

    @Override
    public DuibaHdtoolOptionsDto findOptionById(Long id) {
        return duibaHdtoolOptionsService.findOptionById(id);
    }

    @Override
    public List<HdtoolAppSpecifyDto> findAllSpecifyByHdToolId(Long hdToolId) {
        return duibaHdtoolAppSpecifyService.findAllSpecifyByHdToolId(hdToolId);
    }

    @Override
    public HdtoolAppSpecifyDto findSpecifyByHdToolIdAndApp(Long duibaHdToolId, Long appId) {
        return duibaHdtoolAppSpecifyService.findSpecifyByHdToolIdAndApp(duibaHdToolId, appId);
    }

    public Map<Long, HdtoolAppSpecifyDto> findSpecifyByHdToolIdsAndApp(List<Long> duibaHdToolIds, Long appId){
        return duibaHdtoolAppSpecifyService.findSpecifyByHdToolIdsAndApp(duibaHdToolIds, appId);
    }

    @Override
    public HdtoolAppSpecifyDto findSpecifyById(Long id) {
        return duibaHdtoolAppSpecifyService.findSpecifyById(id);
    }

    @Override
    public ActivityExtraInfoDto findExtraInfoById(Long id) {
        return duibaHdtoolService.findExtraInfoById(id);
    }

    @Override
    public List<Long> findHasUserdHdIds(Long itemId) {
        return duibaHdtoolOptionsService.findHasUserdHdIds(itemId);
    }

    @Override
    public Long getCountDuibaHdTool(Map<String, Object> queryMap) {
        return duibaHdtoolService.getCountDuibaHdTool(queryMap);
    }

    @Override
    public List<DuibaHdtoolDto> findDuibaToolList(Map<String, Object> queryMap) {
        return duibaHdtoolService.findDuibaToolList(queryMap);
    }

    @Override
    public int updateAutoOffDateNull(Long id) {
        return duibaHdtoolService.updateAutoOffDateNull(id);
    }

    @Override
    public DuibaHdtoolDto insert(DuibaHdtoolDto duibaHdtoolDto) {
        duibaHdtoolService.insert(duibaHdtoolDto);
        return duibaHdtoolDto;
    }

    @Override
    public int deleteById(Long id) {
        return duibaHdtoolService.deleteById(id);
    }

    @Override
    public int update(DuibaHdtoolDto duibaHdtoolDto) {
        return duibaHdtoolService.update(duibaHdtoolDto);
        
    }

    @Override
    public int updateStatus(Long id, Integer status) {
        return duibaHdtoolService.updateStatus(id, status);
    }

    @Override
    public int decrementOptionRemaining(Long id) {
        return duibaHdtoolOptionsService.decrementOptionRemaining(id);
    }

    @Override
    public int incrementOptionRemaining(Long id) {
        return duibaHdtoolOptionsService.incrementOptionRemaining(id);
    }

    @Override
    public int deleteOptions(List<Long> ids) {
        return duibaHdtoolOptionsService.deleteOptions(ids);
    }

    @Override
    public int updateHdtoolPrize(DuibaHdtoolOptionsDto duibaHdToolOptionDO) {
        return duibaHdtoolOptionsService.updateHdtoolPrize(duibaHdToolOptionDO);
    }

    @Override
    public DuibaHdtoolOptionsDto insertHdtoolOption(DuibaHdtoolOptionsDto duibaHdToolOptionDO) {
        duibaHdtoolOptionsService.insertHdtoolOption(duibaHdToolOptionDO);
        return duibaHdToolOptionDO;
    }

    @Override
    public int updateHdtoolOption(DuibaHdtoolOptionsDto duibaHdToolOptionDO) {
        return duibaHdtoolOptionsService.updateHdtoolOption(duibaHdToolOptionDO);
    }

    @Override
    public int deleteSpecifyById(Long id) {
        return duibaHdtoolAppSpecifyService.deleteSpecifyById(id);
    }

    @Override
    public HdtoolAppSpecifyDto insertSpecify(HdtoolAppSpecifyDto hdtoolAppSpecifyDO) {
        duibaHdtoolAppSpecifyService.insertSpecify(hdtoolAppSpecifyDO);
        return hdtoolAppSpecifyDO;
    }

    @Override
    public String findTag(Long id) {
        return duibaHdtoolService.findTag(id);
    }

    @Override
    public int updateActivityCategory(long duibaHdtoolId, long activityCategoryId) {
        return duibaHdtoolService.updateActivityCategory(duibaHdtoolId, activityCategoryId);
    }

	@Override
	public List<HdtoolAppSpecifyDto> findAllSpecifyMap(Map<String, Object> queryMap) {
		return duibaHdtoolAppSpecifyService.findAllSpecifyMap(queryMap);
	}

	@Override
	public Integer findAllSpecifyByCredordCount(Map<String, Object> queryMap) {
		return duibaHdtoolAppSpecifyService.findAllSpecifyByCredordCount(queryMap);
	}


    @Override
    public DubboResult<DuibaHdtoolDto> closeDuibaTurntable(Long hdToolsId, Integer status) {
        return DubboResult.successResult(duibaHdtoolBo.closeDuibaTurntable(hdToolsId, status));
    }

    @Override
    public DubboResult<DuibaHdtoolDto> deleteDuibaHdTool(Long hdToolId) {
        return DubboResult.successResult(duibaHdtoolBo.deleteDuibaHdTool(hdToolId));
    }

    @Override
    public DubboResult<Long> addDuibaHdToolToDeveloper(Long appId, Long duibaHdtoolId) {
        try {
            return DubboResult.successResult(duibaHdtoolBo.addDuibaHdToolToDeveloper(appId, duibaHdtoolId));
        } catch (BizException e) {
            log.warn("", e);
            return DubboResult.failResult(e.getMessage());
        }
    }

    @Override
    public DubboResult<Void> update_duiba(DuibaHdtoolDto duibaHdtoolDto) {
        duibaHdtoolBo.update_duiba(duibaHdtoolDto);
        return DubboResult.successResult(null);
    }

    @Override
    public DubboResult<Void> startDuibaTurntable(Long hdToolsId) {
        duibaHdtoolBo.startDuibaTurntable(hdToolsId);
        return DubboResult.successResult(null);
    }

	@Override
	public DubboResult<Map<String, String>> queryHdtoolBaseSkinMap() {
		return DubboResult.successResult(hdtoolSkinDefaultDataSerivce.queryHdtoolBaseSkinMap());
	}

    @Override
    public DubboResult<Integer> updateExtendJson(Long id, String extJson) {
        return DubboResult.successResult(duibaHdtoolService.updateExtendJson(id, extJson));
    }

    @Override
    public DubboResult<Integer> updateAutoOnDate(Date autoOnDate, Long id) {
        return DubboResult.successResult(duibaHdtoolService.updateAutoOnDate(autoOnDate,id));
    }

    @Override
    public DubboResult<Set<Long>> autoOnHdTool() {
        return DubboResult.successResult(duibaHdtoolService.hdToolAutoOn());
    }

}
