package cn.com.duiba.activity.center.biz.dao.superSurprise;

import cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseRankListDto;
import cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseRankListEntity;
import java.util.List;

public interface SuperSurpriseRankListDao {

	Long  save(SuperSurpriseRankListEntity superSurpriseRankListEntity);

    int deleteById(Long id);

    int deleteBatchByIds(List<Long> ids);

    int updateById(SuperSurpriseRankListEntity superSurpriseRankListEntity);

    int increaseScoreById(SuperSurpriseRankListEntity superSurpriseRankListEntity);


    SuperSurpriseRankListEntity getById(Long id);

    List<SuperSurpriseRankListEntity> listByIds(List<Long> ids);

    SuperSurpriseRankListEntity getByAcidCid(Long activityId,Long consumerId);


    /**
     * 获取当前排行榜
     * @param activityId
     * @return
     */
    List<SuperSurpriseRankListDto> getRank(Long activityId);


    List<SuperSurpriseRankListDto> getRankTwoThousand(Long activityId);

    /**
     * 获取自身排名
     * @param activityId
     * @param consumerId
     * @return
     */
    Integer getSelfRank(Long activityId,Long consumerId);


    /**
     * 查询自身排行榜记录
     * @param activityId
     * @param consumerId
     * @return
     */
    SuperSurpriseRankListEntity getSelfRankRecord(Long activityId,Long consumerId);
}
