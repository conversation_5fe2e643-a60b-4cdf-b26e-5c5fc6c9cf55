package cn.com.duiba.activity.center.biz.service.creditsfarm.impl;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmActDto;
import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmActSaveParam;
import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmOptionDto;
import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmSeedDto;
import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmTaskDto;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteOperatingActivityServiceNew;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.service.activity.OperatingActivityService;
import cn.com.duiba.activity.center.biz.service.creditsfarm.CreditsFarmActService;
import cn.com.duiba.activity.center.biz.service.creditsfarm.CreditsFarmBackendService;
import cn.com.duiba.activity.center.biz.service.creditsfarm.CreditsFarmOptionService;
import cn.com.duiba.activity.center.biz.service.creditsfarm.CreditsFarmSeedService;
import cn.com.duiba.activity.center.biz.service.creditsfarm.CreditsFarmTaskService;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.stock.service.api.remoteservice.RemoteStockBackendService;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.utils.BeanUtils;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2019/8/2
 */
@Service
public class CreditsFarmBackendServiceImpl implements CreditsFarmBackendService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CreditsFarmBackendServiceImpl.class);

    @Autowired
    private CreditsFarmActService creditsFarmActService;
    @Autowired
    private CreditsFarmSeedService creditsFarmSeedService;
    @Autowired
    private CreditsFarmTaskService creditsFarmTaskService;
    @Autowired
    private CreditsFarmOptionService creditsFarmOptionService;
    @Autowired
    private OperatingActivityService operatingActivityService;
    @Autowired
    private RemoteStockBackendService remoteStockBackendService;
    @Autowired
    private RemoteOperatingActivityServiceNew remoteOperatingActivityServiceNew;

    @Override
    public CreditsFarmActSaveParam getCreditsFarm(Long actId) {
        if (actId == null) {
            return null;
        }

        CreditsFarmActDto actDto = creditsFarmActService.getById(actId);

        return toParam(actDto);

    }

    @Override
    @Transactional(DsConstants.DATABASE_ACT_COM_CONF)
    public void updateActivity(CreditsFarmActSaveParam param,boolean publish) throws BizException {//NOSONAR
        CreditsFarmActDto actDto = creditsFarmActService.getById(param.getId());
        if (actDto == null) {
            return;
        }
        Date now = new Date();
        //活动已开始,不可再次开启(发布)
        if (publish) {
            OperatingActivityDto operatingActivityDto = remoteOperatingActivityServiceNew.findByAppIdAndActivityIdAndType(param.getAppId(), param.getId(), ActivityUniformityTypeEnum.CREDITS_FARM.getCode());
            if (operatingActivityDto.getStatus() != OperatingActivityDto.StatusIntOpen && now.after(actDto.getStartTime())) {
                throw new BizException("活动已开始,不可再次开启");
            }
        }

        if (now.after(actDto.getEndTime())) {
            throw new BizException("活动已结束");
        } else if (now.after(actDto.getStartTime()) && now.before(actDto.getEndTime())) {
            //活动进行中，只能修改部分数据 - 活动标题、活动规则、种子素材
            creditsFarmActService.updateTitleAndRuleById(BeanUtils.copy(param, CreditsFarmActDto.class));
            for (CreditsFarmSeedDto seed : param.getSeeds()) {
                creditsFarmSeedService.updatePicById(seed);
            }
        } else {
            //活动未开始，均可修改
            creditsFarmActService.update(BeanUtils.copy(param, CreditsFarmActDto.class));
            //种子:固定展示且不支持删除-1期设定
            for (CreditsFarmSeedDto seed : param.getSeeds()) {
                creditsFarmSeedService.updateById(seed);
            }
            //奖项：新增/删除/更新
            List<CreditsFarmOptionDto> deleteOptions = creditsFarmOptionService.selectByCondition(param.getId(), null);
            List<Long> deleteOptionIds = deleteOptions.stream().map(CreditsFarmOptionDto::getId).collect(Collectors.toList());

            List<CreditsFarmOptionDto> insertOption = param.getOptions().stream()
                    .filter(o -> o.getId() == null)
                    .peek(o -> {
                                o.setActId(param.getId());
                                o.setAppId(param.getAppId());
                            })
                    .collect(Collectors.toList());

            List<CreditsFarmOptionDto> updateOption = param.getOptions().stream()
                    .filter(o -> o.getId() != null)
                    .collect(Collectors.toList());
            for (CreditsFarmOptionDto option : updateOption) {
                //更新stockId
                insertOptionStock(option);
                option.setAppId(param.getAppId());
                creditsFarmOptionService.updateById(option);
                if (deleteOptionIds.contains(option.getId())) {
                    deleteOptionIds = deleteOptionIds.stream().filter(o -> !o.equals(option.getId())).collect(Collectors.toList());
                }
            }
            creditsFarmOptionService.batchDeleteByIds(deleteOptionIds);
            for (CreditsFarmOptionDto insert : insertOption) {
                insertOptionStock(insert);
            }
            creditsFarmOptionService.batchInsert(insertOption);

            //任务：新增/删除/更新
            List<CreditsFarmTaskDto> deleteTask = creditsFarmTaskService.findByActId(param.getId());
            List<Long> deleteIds = deleteTask.stream().map(CreditsFarmTaskDto::getId).collect(Collectors.toList());
            List<CreditsFarmTaskDto> insertTask = param.getTasks().stream()
                    .filter(o -> o.getId() == null)
                    .peek(o->o.setActId(param.getId()))
                    .collect(Collectors.toList());

            List<CreditsFarmTaskDto> updateTask = param.getTasks().stream()
                    .filter(o -> o.getId() != null)
                    .collect(Collectors.toList());

            creditsFarmTaskService.batchInsert(insertTask);
            for (CreditsFarmTaskDto task : updateTask) {
                creditsFarmTaskService.updateById(task);
                if (deleteIds.contains(task.getId())) {
                    deleteIds = deleteIds.stream().filter(o -> !o.equals(task.getId())).collect(Collectors.toList());
                }
            }
            creditsFarmTaskService.batchDeleteByIds(param.getId(), deleteIds);
        }
    }

    @Override
    @Transactional(DsConstants.DATABASE_ACT_COM_CONF)
    public void insertActivity(CreditsFarmActSaveParam param) throws BizException {
        Long actId = creditsFarmActService.insert(BeanUtils.copy(param, CreditsFarmActDto.class));
        param.setId(actId);
        if (CollectionUtils.isNotEmpty(param.getSeeds())) {
            param.getSeeds().forEach(s -> s.setActId(actId));
        }
        Map<Integer, List<CreditsFarmOptionDto>> optionSeedMap = param.getOptions().stream()
                .collect(Collectors.groupingBy(CreditsFarmOptionDto::getSeedType));

        for (CreditsFarmSeedDto seed : param.getSeeds()) {
            Long seedId = creditsFarmSeedService.save(seed);
            List<CreditsFarmOptionDto> options = optionSeedMap.get(seed.getSeedType());
            for (CreditsFarmOptionDto o : options) {
                o.setSeedId(seedId);
                o.setActId(actId);
                o.setAppId(param.getAppId());
                //新增库存
                insertOptionStock(o);
            }
            creditsFarmOptionService.batchInsert(options);
        }
        param.getTasks().forEach(t -> t.setActId(actId));
        creditsFarmTaskService.batchInsert(param.getTasks());
    }

    public OperatingActivityDto saveOrUpdateOperatingActivity(CreditsFarmActDto actDto, boolean publish) {
        OperatingActivityDto operatingActivityDto = operatingActivityService.findByAppIdAndActivityIdAndTypeAndDeleted(actDto.getAppId(), actDto.getId(),
                ActivityUniformityTypeEnum.CREDITS_FARM.getCode(), false);
        if (operatingActivityDto == null) {
            operatingActivityDto = new OperatingActivityDto();
            operatingActivityDto.setDeleted(false);
            operatingActivityDto.setType(ActivityUniformityTypeEnum.CREDITS_FARM.getCode());
            operatingActivityDto.setStatus(OperatingActivityDto.StatusIntComplete);
            operatingActivityDto.setTitle(actDto.getTitle());
            operatingActivityDto.setActivityId(actDto.getId());
            operatingActivityDto.setAppId(actDto.getAppId());
            operatingActivityDto.setSmallImage(actDto.getSmallImage());
            operatingActivityDto.setSmallImgNew(actDto.getSmallImage());
            //自动下架时间
            operatingActivityDto.setAutoOffDate(actDto.getEndTime());
            operatingActivityService.insert(operatingActivityDto);
        } else if (!StringUtils.equals(actDto.getTitle(), operatingActivityDto.getTitle())
                || !StringUtils.equals(actDto.getSmallImage(), operatingActivityDto.getSmallImage())
                || !StringUtils.equals(actDto.getSmallImage(), operatingActivityDto.getSmallImgNew())
                || !actDto.getEndTime().equals(operatingActivityDto.getAutoOffDate())
        ) {
            //有修改的情况下才更新
            operatingActivityDto.setId(operatingActivityDto.getId());
            operatingActivityDto.setTitle(actDto.getTitle());
            operatingActivityDto.setAutoOffDate(actDto.getEndTime());
            operatingActivityDto.setSmallImage(actDto.getSmallImage());
            operatingActivityDto.setSmallImgNew(actDto.getSmallImage());
            operatingActivityService.update(operatingActivityDto);
        }
        //如果是点击发布按钮,则将活动状态置为开启
        if (publish) {
            operatingActivityDto.setStatus(OperatingActivityDto.StatusIntOpen);
            operatingActivityService.update(operatingActivityDto);

        }
        return operatingActivityDto;
    }

    /**
     * 分别生成奖品库存，防止超发
     *
     * @param option
     * @throws BizException
     */
    private void insertOptionStock(CreditsFarmOptionDto option) throws BizException {
        Long gid = option.getAppItemId();
        //如果是配置中心配置的兑吧实物商品，appItemId会为空，此时取itemId。
        if (gid == null) {
            gid = option.getItemId();
        }
        DubboResult<Long> r = remoteStockBackendService.newStock(gid, option.getOptionCount());
        if (r != null && r.isSuccess() && r.getResult() != null) {
            option.setStockId(r.getResult());
        } else {
            if(LOGGER.isWarnEnabled()){
                LOGGER.warn("创建积分农场失败失败 - 保存奖品库存失败 option {}，result {}", JSON.toJSONString(option), JSON.toJSONString(r));
            }
            throw new BizException("创建奖项库存失败");

        }
    }

    @Override
    public CreditsFarmActSaveParam findLatestCreatedAct(Long appId) {
        CreditsFarmActDto dto = creditsFarmActService.findLatestCreatedAct(appId);
        return toParam(dto);
    }

    private CreditsFarmActSaveParam toParam(CreditsFarmActDto dto) {
        if (dto == null) {
            return null;
        }

        List<CreditsFarmSeedDto> seeds = creditsFarmSeedService.listByActId(dto.getId());
        List<CreditsFarmOptionDto> options = creditsFarmOptionService.selectByCondition(dto.getId(), null);
        List<CreditsFarmTaskDto> tasks = creditsFarmTaskService.findByActId(dto.getId());

        CreditsFarmActSaveParam param = BeanUtils.copy(dto, CreditsFarmActSaveParam.class);
        param.setSeeds(seeds);
        param.setOptions(options);
        param.setTasks(tasks);

        return param;
    }
}
