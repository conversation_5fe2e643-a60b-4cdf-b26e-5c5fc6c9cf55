package cn.com.duiba.activity.center.biz.dao.guess.impl;

import cn.com.duiba.activity.center.biz.dao.ActivityBaseDao;
import cn.com.duiba.activity.center.biz.dao.DatabaseSchema;
import cn.com.duiba.activity.center.biz.dao.guess.DuibaGuessStockDao;
import cn.com.duiba.activity.center.biz.entity.guess.DuibaGuessStockEntity;

import com.google.common.collect.Maps;

import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * ClassName: DuibaGuessStockDaoImpl <br/>
 * date: 2016年12月1日 下午7:30:54 <br/>
 *
 * @version 
 * @since JDK 1.6
 */
@Repository("duibaGuessStockDAO")
public class DuibaGuessStockDaoImpl extends ActivityBaseDao implements DuibaGuessStockDao {

	@Override
	public DuibaGuessStockEntity findRemaining(Long relationId) {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("relationId", relationId);
		return selectOne("findRemaining", paramMap);
	}
	
	@Override
	public DuibaGuessStockEntity findById(Long id){
		return selectOne("findById", id);
	}

	@Override
	public int subStock(Long id, Integer subNumber) {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("id", id);
		paramMap.put("subNumber", subNumber);
		return update("subStock", paramMap);
	}

	@Override
	public int addStock(Long id, Integer addNumber) {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("id", id);
		paramMap.put("addNumber", addNumber);
		return update("addStock", paramMap);
	}

	@Override
	public DuibaGuessStockEntity findByGuessOptionId(Long guessOptionId) {
		return selectOne("findByGuessOptionId", guessOptionId);
	}

	@Override
	public List<DuibaGuessStockEntity> findByGuessOptionIds(List<Long> list) {
		Map<String, Object> params = new HashMap<>();
		params.put("list", list);
		return selectList("findByGuessOptionIds", params);
	}

	@Override
	public int updateStockAdd(Long id, Integer stockAdd) {
		Map<String, Object> params = new HashMap<>();
		params.put("id", id);
		params.put("stockAdd", stockAdd);
		return update("updateStockAdd", params);
	}

	@Override
	public int updateStockSub(Long id, Integer stockSub) {
		Map<String, Object> params = new HashMap<>();
		params.put("id", id);
		params.put("stockSub", stockSub);
		return update("updateStockSub", params);
	}

	@Override
	public void add(DuibaGuessStockEntity guessStockDO) {
		insert("add", guessStockDO);
	}

	@Override
	public void addBatch(List<DuibaGuessStockEntity> list) {
		Map<String, Object> params = Maps.newHashMap();
		params.put("list", list);
		insert("addBatch", params);
	}

	/**
	 * 这个方法延迟到子类实现,由子类决定注入哪个sqlSessionTemplate,以使用不同的库
	 *
	 * @return
	 */
	@Override
	protected DatabaseSchema chooseSchema() {
		return DatabaseSchema.GUESS;
	}
}
