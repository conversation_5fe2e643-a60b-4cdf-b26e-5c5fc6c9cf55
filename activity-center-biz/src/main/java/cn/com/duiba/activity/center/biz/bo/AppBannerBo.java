package cn.com.duiba.activity.center.biz.bo;

import cn.com.duiba.activity.center.api.dto.ActivityDto;
import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.biz.service.activity.OperatingActivityService;
import cn.com.duiba.developer.center.api.domain.dto.AppBannerDto;
import cn.com.duiba.developer.center.api.domain.enums.AppBannerSourceTypeEnum;
import cn.com.duiba.developer.center.api.domain.enums.BannerOrButtonEnum;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppBannerServiceNew;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemDto;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by wenqi.huang on 2017/3/8.
 */
@Service
public class AppBannerBo {

    @Autowired
    private OperatingActivityService operatingActivityService;
    @Autowired
    private RemoteAppBannerServiceNew remoteAppBannerServiceNew;
    @Autowired
    private AppItemBo appItemBo;

    private static final ImmutableMap<Integer,Integer> TYPE_TO_BANNER_SOURCE_TYPE_MAP;

    static{
        Map<Integer,Integer> tempTypeMap = new HashMap<>();
        tempTypeMap.put(OperatingActivityDto.TypeShakeLottery,AppBannerSourceTypeEnum.SourceTypeShakeLottery.getCode());
        tempTypeMap.put(OperatingActivityDto.TypeTurnTable,AppBannerSourceTypeEnum.SourceTypeOperatingActivity.getCode());
        tempTypeMap.put(OperatingActivityDto.TypeDuibaTurntable,AppBannerSourceTypeEnum.SourceTypeOperatingActivity.getCode());
        tempTypeMap.put(OperatingActivityDto.TypeDuibaActivity,AppBannerSourceTypeEnum.SourceTypeDuibaActivity.getCode());
        tempTypeMap.put(OperatingActivityDto.TypeDuibaSingleLottery,AppBannerSourceTypeEnum.SourceTypeSingleLottery.getCode());
        tempTypeMap.put(OperatingActivityDto.TypeAppSingleLottery,AppBannerSourceTypeEnum.SourceTypeSingleLottery.getCode());
        tempTypeMap.put(OperatingActivityDto.TypeAppManualLottery,AppBannerSourceTypeEnum.SourceTypeManualLottery.getCode());
        tempTypeMap.put(OperatingActivityDto.TypeScratchCard,AppBannerSourceTypeEnum.SourceTypeScratchCardLottery.getCode());
        tempTypeMap.put(OperatingActivityDto.TypeHdToolTiger,AppBannerSourceTypeEnum.SourceTypeTiger.getCode());
        tempTypeMap.put(OperatingActivityDto.TypeHdToolTurntable,AppBannerSourceTypeEnum.SourceTypeHdturntable.getCode());
        tempTypeMap.put(OperatingActivityDto.TypeHdToolFlop,AppItemDto.SourceTypeHdtoolFlop);
        tempTypeMap.put(OperatingActivityDto.TypeHdToolSmashg,AppItemDto.SourceTypeHdtoolSmashg);
        tempTypeMap.put(OperatingActivityDto.TypeSecondsKill,AppBannerSourceTypeEnum.SourceTypeSecondsKill.getCode());
        tempTypeMap.put(OperatingActivityDto.TypeDuibaQuestionAnswer,AppBannerSourceTypeEnum.SourceTypeDuibaQuestionAnswer.getCode());
        tempTypeMap.put(OperatingActivityDto.TypeDuibaQuizz,AppBannerSourceTypeEnum.SourceTypeDuibaQuizz.getCode());
        tempTypeMap.put(OperatingActivityDto.TypeDuibaSeckill,AppBannerSourceTypeEnum.SourceTypeDuibaSeckill.getCode());
        tempTypeMap.put(OperatingActivityDto.TypeDuibaNgame,AppBannerSourceTypeEnum.SourceTypeDuibaNgame.getCode());
        tempTypeMap.put(OperatingActivityDto.TypeDuibaGuess,AppBannerSourceTypeEnum.SourceTypeDuibaGuess.getCode());
        tempTypeMap.put(OperatingActivityDto.TypeDuibaCreditGame,AppBannerSourceTypeEnum.SourceTypeCreditGame.getCode());
        tempTypeMap.put(OperatingActivityDto.TypeActivityAccessQuestionAnswer,OperatingActivityDto.TypeActivityAccessQuestionAnswer);

        TYPE_TO_BANNER_SOURCE_TYPE_MAP =ImmutableMap.copyOf(tempTypeMap);
    }


    /**
     *  * 保存/修改 大转盘AppBanner
     * 迁移自 cn.com.duiba.service.item.remoteservice.impl.RemoteAppBannerServiceImpl.saveAppBannerByTurntable/saveAppBannerByTurntableNoTranscation
     * @param appBanner
     * @param turntableId
     * @return
     */
    public AppBannerDto saveAppBannerByTurntable(AppBannerDto appBanner, long turntableId) {
        OperatingActivityDto activity = operatingActivityService.find(turntableId);
        if (activity == null) {
            throw new NullPointerException("OperatingActivity is null");
        }
        if (activity.getAppItemId() == null) {
            AppItemDto appItem = new AppItemDto(true);
            appItem.setDeleted(true);
            appItem.setAppId(appBanner.getAppId());
            setAppItemSourceTypeByTurntable(activity,appItem);
            appItemBo.saveAppItemByTurntable(appItem, turntableId);
            activity.setAppItemId(appItem.getId());
        }
        appBanner.setName(activity.getTitle());
        appBanner.setType(BannerOrButtonEnum.BANNER.getCode());
        setAppBannerSourceTypeByTurntable( activity, appBanner);
        appBanner.setSourceRelationId(activity.getId());
        appBanner.setAppItemId(activity.getAppItemId());
        Integer maxPayload = remoteAppBannerServiceNew.findMaxPlayload(appBanner.getAppId(), BannerOrButtonEnum.BANNER.getCode()).getResult();
        appBanner.setPayload(maxPayload + 1);
        appBanner.setClassifyImageSwitch(false);
        Long id = remoteAppBannerServiceNew.insertBanner(appBanner).getResult();
        appBanner.setId(id);
        activity.setAppBannerId(appBanner.getId());
        activity.setGmtModified(new Date());

        OperatingActivityDto activity4u = new OperatingActivityDto(activity.getId());
        activity4u.setAppBannerId(activity.getAppBannerId());
        activity4u.setAppItemId(activity.getAppItemId());
        activity4u.setStatus(activity.getStatus());
        activity4u.setGmtModified(activity.getGmtModified());
        operatingActivityService.update(activity4u);

        Map<String, Object> map = Maps.newHashMap();
        map.put("appId", activity.getAppId());
        map.put("parentId", activity.getId());
        List<ActivityDto> childActivity = operatingActivityService.findActivityList(map);
        for (ActivityDto child : childActivity) {
            OperatingActivityDto childoa = new OperatingActivityDto(child.getId());
            childoa.setStatus(activity.getStatus());
            operatingActivityService.update(childoa);
        }

        return appBanner;
    }

    private void setAppBannerSourceTypeByTurntable(OperatingActivityDto activity,AppBannerDto appBanner){
        Integer sourceType=TYPE_TO_BANNER_SOURCE_TYPE_MAP.get(activity.getType());
        if(sourceType!=null){
            appBanner.setSourceType(sourceType);
        }else if(activity.isHdTool()){
            appBanner.setSourceType(OperatingActivityDto.TypeActivityAccessHdtool);
        } else {
            appBanner.setSourceType(AppBannerSourceTypeEnum.SourceTypeMannual.getCode());
        }
    }
    private void setAppItemSourceTypeByTurntable(OperatingActivityDto activity,AppItemDto appItem){
        if(activity.getType() == OperatingActivityDto.TypeShakeLottery){
            appItem.setSourceType(AppItemDto.SourceTypeAppShakeLottery);
        }else if(activity.isHdTool()){
            appItem.setSourceType(OperatingActivityDto.TypeActivityAccessHdtool);
        }else if (activity.getType() == OperatingActivityDto.TypeTurnTable) {
            appItem.setSourceType(AppItemDto.SourceTypeOperatingActivity);
        } else if (activity.getType() == OperatingActivityDto.TypeDuibaTurntable) {
            appItem.setSourceType(AppItemDto.SourceTypeOperatingActivity);
        } else if (activity.getType() == OperatingActivityDto.TypeDuibaActivity) {
            appItem.setSourceType(AppItemDto.SourceTypeDuibaActivity);
        } else if (activity.getType() == OperatingActivityDto.TypeDuibaSingleLottery || activity.getType() == OperatingActivityDto.TypeAppSingleLottery) {
            appItem.setSourceType(AppItemDto.SourceTypeSingleLottery);
        } else {
            appItemBo.setCommonSourceType(appItem, activity);
        }
    }
}
