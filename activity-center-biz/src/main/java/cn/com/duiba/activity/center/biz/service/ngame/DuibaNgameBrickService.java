package cn.com.duiba.activity.center.biz.service.ngame;

import cn.com.duiba.activity.center.api.dto.ngame.DuibaNgameBrickDto;

import java.util.List;

/**
 * 
 * ClassName: DuibaNgameBrickService <br/>
 * date: 2016年12月1日 下午3:49:11 <br/>
 *
 * @version 
 * @since JDK 1.6
 */
public interface DuibaNgameBrickService {

    /**
     * 
     * find:(这里用一句话描述这个方法的作用). <br/>
     * 
     * @param id
     * @return
     * @since JDK 1.6
     */
	public DuibaNgameBrickDto find(Long id);

	/**
	 * 
	 * insert:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param duibaNgameBrickDto
	 * @since JDK 1.6
	 */
	public void insert(DuibaNgameBrickDto duibaNgameBrickDto);

	/**
	 * 
	 * update4Admin:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param duibaNgameBrickDto
	 * @since JDK 1.6
	 */
	public void update4Admin(DuibaNgameBrickDto duibaNgameBrickDto);

	/**
	 * 
	 * findByTitle:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param title
	 * @return
	 * @since JDK 1.6
	 */
	public DuibaNgameBrickDto findByTitle(String title);
	
	/**
	 * 
	 * open:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param id
	 * @since JDK 1.6
	 */
	public void open(Long id);
	
	/**
	 * 
	 * disable:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param id
	 * @since JDK 1.6
	 */
	public void disable(Long id);
	
	/**
	 * 
	 * findPage:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param offset
	 * @param max
	 * @return
	 * @since JDK 1.6
	 */
	public List<DuibaNgameBrickDto> findPage(Integer offset, Integer max);
	
	/**
	 * 
	 * findPageCount:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @return
	 * @since JDK 1.6
	 */
	public Long findPageCount();
	
	/**
	 * 
	 * findAll:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @return
	 * @since JDK 1.6
	 */
	public List<DuibaNgameBrickDto> findAll();

	/**
	 * 
	 * getBrickContentById:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param id
	 * @return
	 * @since JDK 1.6
	 */
	public String getBrickContentById(Long id);

	/**
	 * 
	 * findNoContent:(这里用一句话描述这个方法的作用). <br/>
	 * 
	 * @param id
	 * @return
	 * @since JDK 1.6
	 */
	public DuibaNgameBrickDto findNoContent(Long id);
}
