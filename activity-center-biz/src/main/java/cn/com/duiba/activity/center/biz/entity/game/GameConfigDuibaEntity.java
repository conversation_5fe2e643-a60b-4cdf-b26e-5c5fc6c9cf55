package cn.com.duiba.activity.center.biz.entity.game;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.exception.ActivityCenterException;

import java.util.Date;

public class GameConfigDuibaEntity {

	public static final String SHARE = "SHARE";
	public static final String PREVIEW = "PREVIEW";

	public static final int STATUS_NOCOMPLETE = 0; // 数据不完整
	public static final int STATUS_OPEN = 1; // 开启且可见
	public static final int STATUS_CLOSE_SHOW = 2; // 关闭且可见
	public static final int STATUS_CLOSE = 3; // 关闭不可见

	public static final String LimitTypeForever = "forever"; // 永久
	public static final String LimitTypeEveryday = "everyday"; // 每天

	public static final int SWITCHES_DIRECT = 0;// 定向发放
	public static final int SWITCHES_DEV_BLACKLIST = 1;// 黑名单
	public static final int SWITCHES_FREE_RULE = 2; // 免费次数使用的活动规则

	public static final int TYPE_SANTA = 1;// 打劫圣诞老人
	public static final int TYPE_YEAR_AWARD = 2;// 数年终奖
	public static final int TYPE_GIRL = 3;//女神pk
	public static final int TYPE_JIONG = 4;//人在囧途

	public static final  int santa_max_time = 600;//圣诞老人最大游戏时间 （秒）
	public static final  int yearAward_max_time = 63;//年终奖最大游戏时间 （秒）
	public static final  int girl = 300; //女神游戏最大时间（秒）

	public static final  int eachNum = 5;//数年终奖每秒最大滑动张数
	private Long id;
	private String title;
	private Integer type;
	private String skin;
	private Integer creditsPrice;
	private Integer freeLimit;
	private String freeScope;
	private Integer limitCount;
	private String limitScope;
	private Integer shareScores;
	private String shareApp;
	private String description;
	private String bannerImage;
	private String smallImage;
	private String logo;
	private Integer status;
	private Boolean isOpenPrize;
	private Integer switches;
	private Date autoOffDate;
	private Boolean deleted;
	private Date gmtCreate;
	private Date gmtModified;
	private String freeRule;
	/*奖项的json字符串*/
	private String optionsJson;
	//是否开启定向
	private Boolean openSpecify;
	//是否开启黑名单
	private Boolean openBlack;
	//免费
	private Boolean openFreeRule;
	//double
	private Double dCreditsPrice;

	private String recommendImage;

	public GameConfigDuibaEntity() {

	}

	public GameConfigDuibaEntity(Long id) {
		this.id = id;
		this.gmtModified = new Date();
	}

	public GameConfigDuibaEntity(boolean init4insert) {
		if (init4insert) {
			status = STATUS_NOCOMPLETE;
			deleted = false;
			gmtCreate = new Date();
			gmtModified = new Date();
		}
	}

	public Double getdCreditsPrice() {
		return dCreditsPrice;
	}
	public void setdCreditsPrice(Double dCreditsPrice) {
		this.dCreditsPrice = dCreditsPrice;
	}

	public void openSwitch(int s) {
		int v = 1 << s;
		switches = switches | v;
	}

	public void closeSwitch(int s) {
		int v = 1 << s;
		v = ~v;
		switches = switches & v;
	}

	public boolean isOpenSwitch(int s) {
		int v = 1 << s;
		int ret = switches & v;
		return ret != 0;
	}
	
	public String getShareApp() {
		return shareApp;
	}

	public void setShareApp(String shareApp) {
		this.shareApp = shareApp;
	}
	public Boolean getOpenSpecify() {
		return openSpecify;
	}
	public void setOpenSpecify(Boolean openSpecify) {
		this.openSpecify = openSpecify;
	}
	public Boolean getOpenBlack() {
		return openBlack;
	}
	public void setOpenBlack(Boolean openBlack) {
		this.openBlack = openBlack;
	}
	public String getOptionsJson() {
		return optionsJson;
	}
	public void setOptionsJson(String optionsJson) {
		this.optionsJson = optionsJson;
	}
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getSkin() {
		return skin;
	}

	public void setSkin(String skin) {
		this.skin = skin;
	}

	public Integer getCreditsPrice() {
		return creditsPrice;
	}

	public void setCreditsPrice(Integer creditsPrice) {
		this.creditsPrice = creditsPrice;
	}

	public Integer getFreeLimit() {
		return freeLimit;
	}

	public void setFreeLimit(Integer freeLimit) {
		this.freeLimit = freeLimit;
	}

	public String getFreeScope() {
		return freeScope;
	}

	public void setFreeScope(String freeScope) {
		this.freeScope = freeScope;
	}

	public Integer getLimitCount() {
		return limitCount;
	}

	public void setLimitCount(Integer limitCount) {
		this.limitCount = limitCount;
	}

	public String getLimitScope() {
		return limitScope;
	}

	public void setLimitScope(String limitScope) {
		this.limitScope = limitScope;
	}

	public Integer getShareScores() {
		return shareScores;
	}

	public void setShareScores(Integer shareScores) {
		this.shareScores = shareScores;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getBannerImage() {
		return bannerImage;
	}

	public void setBannerImage(String bannerImage) {
		this.bannerImage = bannerImage;
	}

	public String getSmallImage() {
		return smallImage;
	}

	public void setSmallImage(String smallImage) {
		this.smallImage = smallImage;
	}

	public String getLogo() {
		return logo;
	}

	public void setLogo(String logo) {
		this.logo = logo;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Boolean getIsOpenPrize() {
		return isOpenPrize;
	}

	public void setIsOpenPrize(Boolean isOpenPrize) {
		this.isOpenPrize = isOpenPrize;
	}

	public Integer getSwitches() {
		return switches;
	}

	public void setSwitches(Integer switches) {
		this.switches = switches;
	}

	public Date getAutoOffDate() {
		return autoOffDate;
	}

	public void setAutoOffDate(Date autoOffDate) {
		this.autoOffDate = autoOffDate;
	}

	public Boolean getDeleted() {
		return deleted;
	}

	public void setDeleted(Boolean deleted) {
		this.deleted = deleted;
	}

	public Date getGmtCreate() {
		return gmtCreate;
	}

	public void setGmtCreate(Date gmtCreate) {
		this.gmtCreate = gmtCreate;
	}

	public Date getGmtModified() {
		return gmtModified;
	}

	public void setGmtModified(Date gmtModified) {
		this.gmtModified = gmtModified;
	}

	//返回游戏类型
	public int opType(){
		Integer opType = -1;
		if(this.getType().intValue() == GameConfigDuibaEntity.TYPE_SANTA){
			opType = OperatingActivityDto.TypeGameSanta;//圣诞老人
		}else if(this.getType().intValue() == GameConfigDuibaEntity.TYPE_YEAR_AWARD){
			opType = OperatingActivityDto.TypeGameYearAward;//数年终奖
		}else if(this.getType().intValue() == GameConfigDuibaEntity.TYPE_GIRL){
			opType = OperatingActivityDto.TypeGameGirl;//女神pk
		}else if(this.getType().intValue() == GameConfigDuibaEntity.TYPE_JIONG){
			opType = OperatingActivityDto.TypeGameJiong;//人在囧途
		}
		if(opType == -1){
			throw new ActivityCenterException("游戏类型异常");
		}
		return opType;
	}

	public String getFreeRule() {
		return freeRule;
	}

	public void setFreeRule(String freeRule) {
		this.freeRule = freeRule;
	}
	public Boolean getOpenFreeRule() {
		return openFreeRule;
	}
	public void setOpenFreeRule(Boolean openFreeRule) {
		this.openFreeRule = openFreeRule;
	}
	public String getRecommendImage() {
		return recommendImage;
	}

	public void setRecommendImage(String recommendImage) {
		this.recommendImage = recommendImage;
	}

}
