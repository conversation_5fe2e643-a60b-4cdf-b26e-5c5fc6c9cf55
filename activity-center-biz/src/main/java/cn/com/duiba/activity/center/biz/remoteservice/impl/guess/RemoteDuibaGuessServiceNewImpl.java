package cn.com.duiba.activity.center.biz.remoteservice.impl.guess;

import cn.com.duiba.activity.center.api.dto.guess.DuibaGuessDto;
import cn.com.duiba.activity.center.api.dto.quizz.AddActivityDto;
import cn.com.duiba.activity.center.api.remoteservice.guess.RemoteDuibaGuessServiceNew;
import cn.com.duiba.activity.center.biz.bo.DuibaGuessBo;
import cn.com.duiba.activity.center.biz.service.guess.DuibaGuessService;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.wolf.dubbo.DubboResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Created by wen<PERSON>.huang on 16/6/7.
 */
@RestController
public class RemoteDuibaGuessServiceNewImpl implements RemoteDuibaGuessServiceNew {
    private static final Logger log = LoggerFactory.getLogger(RemoteDuibaGuessServiceNewImpl.class);
    @Resource
    private DuibaGuessService duibaGuessService;
    @Resource
    private DuibaGuessBo duibaGuessBo;

    @Override
    public DuibaGuessDto find(Long id) {
        return duibaGuessService.find(id);
    }

    @Override
    public String findTagById(Long id){
        return duibaGuessService.findTagById(id);
    }
    
    @Override
    public int updateTagById(Long id, String tag){
        return duibaGuessService.updateTagById(id,tag);
    }
    @Override
    public List<DuibaGuessDto> findByPage(Integer offset, Integer max, String title, Integer id) {
        return duibaGuessService.findByPage(offset, max, title, id);
    }

    @Override
    public Long findPageCount(String title, Integer id) {
        return duibaGuessService.findPageCount(title, id);
    }

    @Override
    public int updateStatus(Long id, int status) {
        return duibaGuessService.updateStatus(id, status);
    }

    @Override
    public int delete(Long id) {
        return duibaGuessService.delete(id);
    }

    @Override
    public DuibaGuessDto insert(DuibaGuessDto duibaGuessDto) {
        duibaGuessService.insert(duibaGuessDto);
        return duibaGuessDto;
    }

    @Override
    public int updateInfoForm(DuibaGuessDto duibaGuessDto) {
        return duibaGuessService.updateInfoForm(duibaGuessDto);
    }

    @Override
    public int updateAutoOffDateNull(Date autoOffDate, Long id) {
        return duibaGuessService.updateAutoOffDateNull(autoOffDate, id);
    }

    @Override
    public void updateSwitches(Long id, Integer switches) {
        duibaGuessService.updateSwitches(id, switches);
    }

    @Override
    public void updateOpenWinning(Long id, String luckNum, Long rightSelectionId, Integer isAccurate) {
        duibaGuessService.updateOpenWinning(id, luckNum, rightSelectionId, isAccurate);
    }

    @Override
    public List<DuibaGuessDto> findAllByIds(List<Long> ids) {
        return duibaGuessService.findAllByIds(ids);
    }

    @Override
    public List<AddActivityDto> findAllGuess(Long appId) {
        return duibaGuessService.findAllGuess(appId);
    }

    @Override
    public List<DuibaGuessDto> findAutoOff() {
        return duibaGuessService.findAutoOff();
    }

    @Override
    public int updateOpenPrize(Long id) {
        return duibaGuessService.updateOpenPrize(id);
    }

    @Override
    public DubboResult<Long> addDuibaGuessToDeveloper(Long appId, Long duibaGuessId) {
        try {
            return DubboResult.successResult(duibaGuessBo.addDuibaGuessToDeveloper(appId, duibaGuessId));
        } catch (BizException e) {
            log.warn("", e);
            return DubboResult.failResult(e.getMessage());
        }
    }

	@Override
	public DubboResult<Boolean> openWinning(Long duibaGuessId) {
        try {
            return DubboResult.successResult(duibaGuessBo.openWinning(duibaGuessId));
        } catch (BizException e) {
            log.warn("", e);
            return DubboResult.failResult(e.getMessage());
        }
	}
}
