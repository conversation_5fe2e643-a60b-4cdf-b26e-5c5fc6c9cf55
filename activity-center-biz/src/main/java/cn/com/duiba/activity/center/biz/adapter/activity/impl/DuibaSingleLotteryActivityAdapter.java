package cn.com.duiba.activity.center.biz.adapter.activity.impl;

import java.util.ArrayList;
import java.util.List;

import cn.com.duiba.activity.center.biz.service.singlelottery.DuibaSingleLotteryAppSpecifyService;
import cn.com.duiba.activity.center.biz.service.singlelottery.DuibaSingleLotteryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.base.Function;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.google.common.collect.Lists;

import cn.com.duiba.activity.center.api.dto.ActivityCommonDto;
import cn.com.duiba.activity.center.api.dto.singlelottery.DuibaSingleLotteryDto;
import cn.com.duiba.activity.center.api.enums.ActivityStatusEnum;
import cn.com.duiba.activity.center.api.enums.ActivityTypeEnum;
import cn.com.duiba.activity.center.common.util.SwitchTool;

/**
 * Created by xiaoxuda on 2017/2/17.
 */
@Component
public class DuibaSingleLotteryActivityAdapter extends AbstractActivityAdapter {
    @Autowired
    private DuibaSingleLotteryService duibaSingleLotteryService;
    @Autowired
    private DuibaSingleLotteryAppSpecifyService duibaSingleLotteryAppSpecifyService;

    private Transform transform = new Transform();

    private static ActivityTypeEnum TYPE = ActivityTypeEnum.SINGLE_LOTTERY;

    private static BiMap<Integer, ActivityStatusEnum> STATUS_MAP = HashBiMap.create();

    static {
        STATUS_MAP.put(DuibaSingleLotteryDto.STATUS_UNPUBLISH, ActivityStatusEnum.STATUS_CLOSE_SHOW);
        STATUS_MAP.put(DuibaSingleLotteryDto.STATUS_STARTUP, ActivityStatusEnum.STATUS_OPEN);
        STATUS_MAP.put(DuibaSingleLotteryDto.STATUS_SHUTDOWN, ActivityStatusEnum.STATUS_CLOSE);
    }

    @Override
    public ActivityTypeEnum getEnumType() {
        return TYPE;
    }

    @Override
    public ActivityCommonDto getOneActivityCommon(Long duibaActivityId) {
        DuibaSingleLotteryDto dto = duibaSingleLotteryService.find(duibaActivityId);
        if (dto == null) {
            return null;
        }
        return transform.apply(dto);
    }

    @Override
    public List<ActivityCommonDto> getActivityCommonByIds(List<Long> duibaActivityIds) {
        List<DuibaSingleLotteryDto> list = duibaSingleLotteryService.findAllByIds(duibaActivityIds);
        if (list == null) {
            return new ArrayList<>();
        }
        return Lists.transform(list, transform);
    }

    @Override
    public List<Long> filterDirectByActivityIdsAndAppId(Long appId, List<Long> activityIds) {
        return duibaSingleLotteryAppSpecifyService.filterDirectByActivityIdsAndAppId(appId, activityIds);
    }

    private class Transform implements Function<DuibaSingleLotteryDto, ActivityCommonDto> {

        @Override
        public ActivityCommonDto apply(DuibaSingleLotteryDto lottery) {
            if (lottery == null) {
                return null;
            }
            ActivityCommonDto dto = new ActivityCommonDto();
            dto.setActivityId(lottery.getId());
            dto.setDelete(lottery.getDeleted());
            dto.setStatus(STATUS_MAP.get(lottery.getStatus()));
            dto.setType(TYPE);
            dto.setTitle(lottery.getTitle());
            dto.setBanner(lottery.getBannerImage());
            dto.setBannerImgNew(lottery.getBannerImgNew());
            dto.setThumb(lottery.getSmallImage());
            dto.setLogo(lottery.getLogo());
            dto.setWhitethumb(lottery.getWhiteImage());
            dto.setCredits(lottery.getCreditsPrice() == null ? null : (long)(lottery.getCreditsPrice()));
            dto.setDescription(lottery.getSubtitle());
            Integer switches = 0;
            switches = SwitchTool.updateSwitch(switches, ActivityCommonDto.SWITCHES_APP_DIRECT,lottery.isOpenSwitch(DuibaSingleLotteryDto.SWITCHES_DIRECT));
            switches = SwitchTool.updateSwitch(switches, ActivityCommonDto.SWITCHES_DEV_BLACKLIST,lottery.isOpenSwitch(DuibaSingleLotteryDto.SWITCHES_DEV_BLACKLIST));
            dto.setSwitches(switches);
            return dto;
        }
    }
}
