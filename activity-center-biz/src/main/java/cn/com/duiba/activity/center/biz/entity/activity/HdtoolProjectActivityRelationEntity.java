package cn.com.duiba.activity.center.biz.entity.activity;

import java.util.Date;

/**
 * Created by hong
 * time 2022/8/10.
 */
public class HdtoolProjectActivityRelationEntity {
    /**
     * id
     */
    private Long id;
    /**
     * 活动标题
     */
    private String activityTitle;
    /**
     * 关联的星速台项目ID
     */
    private String projectId;
    /**
     * 定向的开发者活动id
     */
    private Long operatingActivityId;
    /**
     * 定向的应用ID
     */
    private Long appId;
    /**
     * 所属模板的星速台项目ID
     */
    private String templateProjectId;
    /**
     * 0，关闭；1，开启
     */
    private Integer openStatus;
    /**
     * 0正常，1删除
     */
    private Integer deleted;
    /**
     * 图片字段，包含banner图、small图、logo等
     */
    private String imageJson;

    /**
     * 创建时间
     * @return
     */
    private Date gmtCreate;

    /**
     * 更新时间
     * @return
     */
    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActivityTitle() {
        return activityTitle;
    }

    public void setActivityTitle(String activityTitle) {
        this.activityTitle = activityTitle;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public Long getOperatingActivityId() {
        return operatingActivityId;
    }

    public void setOperatingActivityId(Long operatingActivityId) {
        this.operatingActivityId = operatingActivityId;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getTemplateProjectId() {
        return templateProjectId;
    }

    public void setTemplateProjectId(String templateProjectId) {
        this.templateProjectId = templateProjectId;
    }

    public Integer getOpenStatus() {
        return openStatus;
    }

    public void setOpenStatus(Integer openStatus) {
        this.openStatus = openStatus;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public String getImageJson() {
        return imageJson;
    }

    public void setImageJson(String imageJson) {
        this.imageJson = imageJson;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }
}
