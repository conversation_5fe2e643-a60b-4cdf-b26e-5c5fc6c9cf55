package cn.com.duiba.activity.center.biz.service.hdtool.impl;

import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolOrdersDto;
import cn.com.duiba.activity.center.biz.dao.hdtool.HdtoolOrdersDao;
import cn.com.duiba.activity.center.biz.service.hdtool.HdtoolOrdersService;
import cn.com.duiba.wolf.utils.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Created by wenqi.huang on 16/6/28.
 */
@Service
public class HdtoolOrdersServiceImpl implements HdtoolOrdersService {
    @Resource
    private HdtoolOrdersDao hdtoolOrdersDao;

    @Override
    public HdtoolOrdersDto find(Long id) {
        return BeanUtils.copy(hdtoolOrdersDao.find(id), HdtoolOrdersDto.class);
    }

    @Override
    public List<HdtoolOrdersDto> findInOrderIds(List<Long> ids) {
        return BeanUtils.copyList(hdtoolOrdersDao.findInOrderIds(ids), HdtoolOrdersDto.class);
    }

    @Override
    public List<HdtoolOrdersDto> findExpireOrder() {
        return BeanUtils.copyList(hdtoolOrdersDao.findExpireOrder(), HdtoolOrdersDto.class);
    }

    @Override
    public List<HdtoolOrdersDto> findOrdersByPrizeTypeAndOperatingActivityId(Long operatingActivityId, Date gmtCreate, Set<String> types, Integer number, List<String> prizeIds) {
        return BeanUtils.copyList(hdtoolOrdersDao.findOrdersByPrizeTypeAndOperatingActivityId(operatingActivityId, gmtCreate, types, number, prizeIds), HdtoolOrdersDto.class);
    }
}
