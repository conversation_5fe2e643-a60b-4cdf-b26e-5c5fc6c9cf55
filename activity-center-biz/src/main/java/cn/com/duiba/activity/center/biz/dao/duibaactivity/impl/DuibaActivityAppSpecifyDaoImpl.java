package cn.com.duiba.activity.center.biz.dao.duibaactivity.impl;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import cn.com.duiba.activity.center.biz.dao.ActivityBaseDao;
import cn.com.duiba.activity.center.biz.dao.DatabaseSchema;
import cn.com.duiba.activity.center.biz.dao.duibaactivity.DuibaActivityAppSpecifyDao;
import cn.com.duiba.activity.center.biz.entity.duibaactivity.DuibaActivityAppSpecifyEntity;

/**
 * Created by yansen on 16/6/23.
 */
@Repository
public class DuibaActivityAppSpecifyDaoImpl extends ActivityBaseDao implements DuibaActivityAppSpecifyDao {

    @Override
    public List<DuibaActivityAppSpecifyEntity> findDuiBaActivitySpecifyDO(Long duibaActivityId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("duibaActivityId", duibaActivityId);
        return selectList("findDuiBaActivitySpecifyDO", paramMap);
    }

    @Override
    public void delete(Long id) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id", id);
        delete("delete", paramMap);
    }

    @Override
    public DuibaActivityAppSpecifyEntity findByDuibaActivityAndApp(Long duibaActivityId, Long appId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("duibaActivityId", duibaActivityId);
        paramMap.put("appId", appId);
        return selectOne("findByDuibaActivityAndApp", paramMap);
    }

    @Override
    public List<DuibaActivityAppSpecifyEntity> findByDuibaActivitysAndApp(List<Long> duibaActivityIds, Long appId) {
        if (duibaActivityIds == null || duibaActivityIds.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("duibaActivityIds", duibaActivityIds);
        paramMap.put("appId", appId);
        return selectList("findByDuibaActivitysAndApp", paramMap);
    }

    @Override
    public void insert(DuibaActivityAppSpecifyEntity duibaActivityAppSpecifyDO) {
        insert("insert", duibaActivityAppSpecifyDO);
    }

    @Override
    public DuibaActivityAppSpecifyEntity find(Long id) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id", id);
        return selectOne("find", paramMap);
    }

    @Override
    public List<Long> filterDirectByActivityIdsAndAppId(Long appId, List<Long> activityIds) {
        if(appId == null || activityIds == null || activityIds.isEmpty()){
            return Collections.emptyList();
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("appId", appId);
        paramMap.put("activityIds", activityIds);
        return selectList("filterDirectByActivityIdsAndAppId", paramMap);
    }


    @Override
    protected DatabaseSchema chooseSchema() {
        return DatabaseSchema.CREDITS;
    }
}
