package cn.com.duiba.activity.center.biz.entity.singleAward;

import java.util.Date;

/**
* 发奖活动工具领取记录
*/
public class SingleAwardRecordEntity {

    /**
    * id
    */
    private Long id;

    /**
    * 活动订单号
    */
    private Long activityOrderNum;

    /**
    * appId
    */
    private Long appId;

    /**
     * 应用id
     */
    private Long developerId;

    /**
    * 发奖活动工具id
    */
    private Long actId;

    /**
    * 兑吧活动id
    */
    private Long operatingActivityId;

    /**
    * 用户id
    */
    private Long consumerId;

    /**
    * 奖项id
    */
    private Long optionId;

    /**
    * 中奖商品id
    */
    private Long appItemId;

    /**
    * 支付金额
    */
    private Long payAmount;

    /**
     * 活动订单当前状态
     */
    private String status;

    /**
    * 支付状态
    */
    private String chargeStatus;

    /**
    * 支付时间
    */
    private Date chargeTime;

    /**
    * 退款时间
    */
    private Date refundTime;

    /**
     * 中奖订单号，tb_trade_center_activity_order_0000表的orderNum
     */
    private String prizeOrderNum;

    /**
     * 错误原因，订单失败原因，其他异常信息记录到extraInfo
    */
    private String errorMsg;

    /**
    * 扩展信息
    */
    private String extraInfo;

    /**
    * 创建时间
    */
    private Date gmtCreate;

    /**
    * 修改时间
    */
    private Date gmtModified;


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setActivityOrderNum(Long activityOrderNum) {
        this.activityOrderNum = activityOrderNum;
    }

    public Long getActivityOrderNum() {
        return activityOrderNum;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getAppId() {
        return appId;
    }

    public Long getDeveloperId() {
        return developerId;
    }

    public void setDeveloperId(Long developerId) {
        this.developerId = developerId;
    }

    public void setActId(Long actId) {
        this.actId = actId;
    }

    public Long getActId() {
        return actId;
    }

    public Long getOperatingActivityId() {
        return operatingActivityId;
    }

    public void setOperatingActivityId(Long operatingActivityId) {
        this.operatingActivityId = operatingActivityId;
    }

    public void setConsumerId(Long consumerId) {
        this.consumerId = consumerId;
    }

    public Long getConsumerId() {
        return consumerId;
    }

    public void setOptionId(Long optionId) {
        this.optionId = optionId;
    }

    public Long getOptionId() {
        return optionId;
    }

    public void setAppItemId(Long appItemId) {
        this.appItemId = appItemId;
    }

    public Long getAppItemId() {
        return appItemId;
    }

    public void setPayAmount(Long payAmount) {
        this.payAmount = payAmount;
    }

    public Long getPayAmount() {
        return payAmount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setChargeStatus(String chargeStatus) {
        this.chargeStatus = chargeStatus;
    }

    public String getChargeStatus() {
        return chargeStatus;
    }

    public void setChargeTime(Date chargeTime) {
        this.chargeTime = chargeTime;
    }

    public Date getChargeTime() {
        return chargeTime;
    }

    public void setRefundTime(Date refundTime) {
        this.refundTime = refundTime;
    }

    public Date getRefundTime() {
        return refundTime;
    }

    public String getPrizeOrderNum() {
        return prizeOrderNum;
    }

    public void setPrizeOrderNum(String prizeOrderNum) {
        this.prizeOrderNum = prizeOrderNum;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo;
    }

    public String getExtraInfo() {
        return extraInfo;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

}

