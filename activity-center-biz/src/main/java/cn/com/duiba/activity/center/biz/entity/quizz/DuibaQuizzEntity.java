package cn.com.duiba.activity.center.biz.entity.quizz;

import cn.com.duiba.activity.center.api.dto.quizz.DuibaQuizzDto;
import cn.com.duiba.wolf.utils.BeanUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * DuibaQuizzEntity
 */
public class DuibaQuizzEntity implements Serializable {

    private static final long   serialVersionUID              = -5311980631503669282L;

    public static final Integer SWITCHES_DIRECT               = 0;                    // 定向发放
    public static final Integer SWITCHES_DEV_BLACKLIST        = 1;                    // 黑名单商品
    public static final Integer SWITCHES_FREE_RULE            = 2;                    // 免费次数使用的活动规则

    public static final int     STATUS_OPEN                   = 1;                    // 开启且可见
    public static final int     STATUS_CLOSE_SHOW             = 2;                    // 关闭且可见
    public static final int     STATUS_CLOSE                  = 3;                    // 关闭不可见

    public static final String  LimitTypeForever              = "forever";            // 永久
    public static final String  LimitTypeEveryday             = "everyday";           // 每天

    public static final String  indexStatusNotLogin           = "请先登录";               // 未登陆
    public static final String  indexStatusEnd                = "已结束";                // 已结束
    public static final String  indexStatuNotEnough           = "积分不足";               // 积分不足
    public static final String  amtNotEnough                  = "金额不足";               // 金额不足
    public static final String  ybNotEnough                   = "元宝不足";               // 元宝不足
    public static final String  indexStatuNotEnoughTxt        = "积分";                 // 积分
    public static final String  amtNotEnoughTxt               = "金额";                 // 金额
    public static final String  ybNotEnoughTxt                = "元宝";                 // 元宝
    public static final String  indexStatusStart              = "开始测试";               // 开始测试
    public static final String  indexStatusNotChangeUsed      = "下次再玩吧";              // 机会已用完
    public static final String  indexStatusNotChangeUsedToday = "明天再玩吧";              // 今日机会已用完

    private Long                id;
    private String              title;
    private Integer             status;
    private Long                creditsPrice;
    private Integer             limitCount;
    private String              limitScope;
    private Integer             freeLimit;
    private String              freeScope;
    private Long                duibaQuizzBrickId;
    private String              rule;
    private String              banner;
    private String              smallImage;
    private String              whiteImage;
    private String              logo;
    private String              recommendImage;
    private Integer             switches;
    private Date                autoOffDate;
    private Boolean             deleted;
    private Date                gmtCreate;
    private Date                gmtModified;
    private Long activityCategoryId ;//活动归类 --徐恒飞
	private String tag;

    //json图片字符串
    private String imageJson;

    public DuibaQuizzEntity() {

    }

    /**
     *
     * @param id
     */
    public DuibaQuizzEntity(Long id) {
        this.id = id;
        this.gmtModified = new Date();
    }
	
    public String getTag() {
        return tag;
    }
    
    public void setTag(String tag) {
        this.tag = tag;
    }



    public Long getDuibaQuizzBrickId() {
        return duibaQuizzBrickId;
    }

    public void setDuibaQuizzBrickId(Long duibaQuizzBrickId) {
        this.duibaQuizzBrickId = duibaQuizzBrickId;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     *
     * @param s
     */
    public void openSwitch(int s) {
        int v = 1 << s;
        switches = switches | v;
    }

    /**
     *
     * @param s
     */
    public void closeSwitch(int s) {
        int v = 1 << s;
        v = ~v;
        switches = switches & v;
    }

    /**
     *
     * @param s
     * @return
     */
    public boolean isOpenSwitch(int s) {
        int v = 1 << s;
        int ret = switches & v;
        return ret != 0;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getCreditsPrice() {
        return creditsPrice;
    }

    public void setCreditsPrice(Long creditsPrice) {
        this.creditsPrice = creditsPrice;
    }

    public Integer getLimitCount() {
        return limitCount;
    }

    public void setLimitCount(Integer limitCount) {
        this.limitCount = limitCount;
    }

    public String getLimitScope() {
        return limitScope;
    }

    public void setLimitScope(String limitScope) {
        this.limitScope = limitScope;
    }

    public Integer getFreeLimit() {
        return freeLimit;
    }

    public void setFreeLimit(Integer freeLimit) {
        this.freeLimit = freeLimit;
    }

    public String getFreeScope() {
        return freeScope;
    }

    public void setFreeScope(String freeScope) {
        this.freeScope = freeScope;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }

    public String getBanner() {
        return banner;
    }

    public void setBanner(String banner) {
        this.banner = banner;
    }

    public String getSmallImage() {
        return smallImage;
    }

    public void setSmallImage(String smallImage) {
        this.smallImage = smallImage;
    }

    public String getWhiteImage() {
        return whiteImage;
    }

    public void setWhiteImage(String whiteImage) {
        this.whiteImage = whiteImage;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getRecommendImage() {
        return recommendImage;
    }

    public void setRecommendImage(String recommendImage) {
        this.recommendImage = recommendImage;
    }

    public Integer getSwitches() {
        return switches;
    }

    public void setSwitches(Integer switches) {
        this.switches = switches;
    }

    public Date getAutoOffDate() {
        return autoOffDate;
    }

    public void setAutoOffDate(Date autoOffDate) {
        this.autoOffDate = autoOffDate;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }
    public Long getActivityCategoryId() {
        return activityCategoryId;
    }

    public void setActivityCategoryId(Long activityCategoryId) {
        this.activityCategoryId = activityCategoryId;
    }

    public String getImageJson() {
        return imageJson;
    }

    public void setImageJson(String imageJson) {
        this.imageJson = imageJson;
    }

    public static DuibaQuizzDto toDTO(DuibaQuizzEntity entity) {
        if (entity == null) {
            return null;
        }
        DuibaQuizzDto duibaQuizzDto = BeanUtils.copy(entity, DuibaQuizzDto.class);

        //解析json string并设置相应值
        duibaQuizzDto.setSmallImgNew(duibaQuizzDto.getSmallImgNew());
        duibaQuizzDto.setBannerImgNew(duibaQuizzDto.getBannerImgNew());
        return duibaQuizzDto;
    }

    public static List<DuibaQuizzDto> toDTOList(List<DuibaQuizzEntity> entityList) {
        List<DuibaQuizzDto> list = Lists.newArrayList();
        if (CollectionUtils.isEmpty(entityList)) {
            return list;
        }
        for (DuibaQuizzEntity entity : entityList) {
            list.add(toDTO(entity));
        }
        return list;
    }
}
