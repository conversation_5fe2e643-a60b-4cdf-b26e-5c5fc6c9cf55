/**
 * Project Name:activity-center-biz File Name:TodayRobSeckillBoImpl.java Package
 * Name:cn.com.duiba.activity.center.biz.bo.rob Date:2016年9月30日上午9:43:28 Copyright (c) 2016, duiba.com.cn All Rights
 * Reserved.
 */

package cn.com.duiba.activity.center.biz.bo.rob.impl;

import cn.com.duiba.activity.center.api.dto.PaginationDto;
import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionAnswerDto;
import cn.com.duiba.activity.center.api.dto.permission.PermissionDto;
import cn.com.duiba.activity.center.api.dto.rob.*;
import cn.com.duiba.activity.center.api.dto.seckill.DuibaSeckillDto;
import cn.com.duiba.activity.center.api.dto.seconds_kill.DuibaSecondsKillActivityDto;
import cn.com.duiba.activity.center.api.dto.seconds_kill.ItemsArea;
import cn.com.duiba.activity.center.api.enums.PushStatusEnum;
import cn.com.duiba.activity.center.api.enums.SeckillShowEntranceEnum;
import cn.com.duiba.activity.center.biz.bo.AppBannerBo;
import cn.com.duiba.activity.center.biz.bo.AppItemBo;
import cn.com.duiba.activity.center.biz.bo.permission.PermissionBo;
import cn.com.duiba.activity.center.biz.bo.rob.TodayRobSeckillBo;
import cn.com.duiba.activity.center.biz.dao.DsConstants;
import cn.com.duiba.activity.center.biz.domain.PushAppsConfig;
import cn.com.duiba.activity.center.biz.entity.rob.TodayRobSeckillConfigEntity;
import cn.com.duiba.activity.center.biz.entity.rob.TodayRobSeckillEntity;
import cn.com.duiba.activity.center.biz.factory.SeckillFactory;
import cn.com.duiba.activity.center.biz.service.activity.OperatingActivityService;
import cn.com.duiba.activity.center.biz.service.config.CenterConfigService;
import cn.com.duiba.activity.center.biz.service.rob.TodayRobSeckillService;
import cn.com.duiba.activity.center.biz.service.rob.TodayRobSeckillSpecifyService;
import cn.com.duiba.activity.center.biz.service.seckill.DuibaSeckillService;
import cn.com.duiba.activity.center.biz.service.seckill.SeckillFieldService;
import cn.com.duiba.activity.center.biz.service.seckill.SeckillMethodService;
import cn.com.duiba.activity.center.biz.service.seconds_kill.DuibaSecondsKillActivityService;
import cn.com.duiba.activity.center.common.util.AppLogUtil;
import cn.com.duiba.activity.center.common.util.DateUtil;
import cn.com.duiba.developer.center.api.domain.dto.AppBannerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.CenterConfigDto;
import cn.com.duiba.developer.center.api.domain.enums.AppBannerSourceTypeEnum;
import cn.com.duiba.developer.center.api.domain.enums.BannerOrButtonEnum;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemExtraDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteDuibaItemGoodsService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemExtraService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemKeyService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemotePreStockService;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.redis.RedisClient;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duiba.wolf.utils.ObjectUtil;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;

/**
 * ClassName:TodayRobSeckillBoImpl <br/>
 * Date: 2016年9月30日 上午9:43:28 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.6
 * @see
 */
@Service
public class TodayRobSeckillBoImpl implements TodayRobSeckillBo {

    private static Logger                 log              = LoggerFactory.getLogger(TodayRobSeckillBoImpl.class);

    // 秒杀活动推送的分布式锁前缀名称
    private static final String           REDIS_KEY_PREFIX = "TODAY_ROB_SECKILL";

    @Autowired
    private TodayRobSeckillService        todayRobSeckillService;

    @Autowired
    private CenterConfigService           centerConfigService;

    @Autowired
    private DuibaSeckillService           duibaSeckillService;

    @Resource(name = "redisClient2")
    private RedisClient redisClient;

    @Autowired
    private OperatingActivityService      operatingActivityService;

    @Autowired
    private RemoteItemKeyService          remoteItemKeyService;
    @Autowired
    private RemoteDuibaItemGoodsService   remoteDuibaItemGoodsService;

    @Autowired
    private RemotePreStockService         remotePreStockService;

    @Autowired
    private RemoteAppService              remoteAppService;

    @Autowired
    private ExecutorService executorService;

    @Autowired
    private PermissionBo                  permissionBo;
    @Autowired
    private DuibaSecondsKillActivityService       duibaSecondsKillActivityService;
    @Autowired
    private RemoteItemExtraService        remoteItemExtraService;
    @Autowired
    private AppItemBo                     appItemBo;
    @Autowired
    private AppBannerBo                   appBannerBo;
    @Autowired
    private TodayRobSeckillSpecifyService todayRobSeckillSpecifyService;

    @Override
    public void pushSeckillActivity(List<TodayRobSeckillConfigDto> activityList, final List<Long> appIdList) {
        if (CollectionUtils.isEmpty(activityList) || CollectionUtils.isEmpty(appIdList)) {
            AppLogUtil.warn(log, "今日必抢-秒杀免单活动推送失败，参数错误，activityList={}， appIdList={}", activityList, appIdList);
            return;
        }
        for (final TodayRobSeckillConfigDto dto : activityList) {
            // 判断活动状态是否开启且可见
            final SeckillFieldService duibaSeckill = SeckillFactory.getSeckillFieldService(dto.getSecondType(),
                                                                                           dto.getActivityId());
            if (duibaSeckill == null) {
                AppLogUtil.error(log, "兑吧秒杀活动不存在，activityId={}", dto.getActivityId());
                continue;
            }
            if (!Objects.equal(duibaSeckill.getStatus(), 1)) {// 暂时先改成1,表示开启
                AppLogUtil.warn(log, "活动未开启，不需要推送，activityId={}，status={}", dto.getActivityId(),
                                duibaSeckill.getStatus());
                continue;
            }

            // 开启异步线程推送活动至开发者库
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    pushToApps(dto.getId(), duibaSeckill, appIdList, dto.getSecondType());
                }
            });
        }

    }

    private void pushToApps(Long todayRobSeckillConfigId, SeckillFieldService duibaSeckill, List<Long> appIdList,
                            String secondType) {
        Long reply = redisClient.setnx(REDIS_KEY_PREFIX + duibaSeckill.getId() + "_" + duibaSeckill.getType(),
                                       duibaSeckill.getId().toString());
        if (reply == 0) {
            AppLogUtil.warn(log, "获取分布式锁失败，activityId={}", duibaSeckill.getId());
            return;
        }
        // 设置失效时间，防止系统崩溃导致不能释放锁
        redisClient.expire(REDIS_KEY_PREFIX + duibaSeckill.getId() + "_" + duibaSeckill.getType(), 120);
        // 更新活动推送时间和推送状态
        todayRobSeckillService.updatePushStatus(todayRobSeckillConfigId, PushStatusEnum.EXEC);
        PushStatusEnum pushStatus = PushStatusEnum.SUCC;
        try {
            for (Long appId : appIdList) {
                doPush(duibaSeckill, appId, secondType);
            }
        } catch (Exception e) {
            AppLogUtil.error(log, "秒杀活动推送失败, activityId={}", duibaSeckill.getId(), e);
            pushStatus = PushStatusEnum.FAILED;
        } finally {
            redisClient.del(REDIS_KEY_PREFIX + duibaSeckill.getId() + "_" + duibaSeckill.getType());
        }
        todayRobSeckillService.updatePushStatus(todayRobSeckillConfigId, pushStatus);
    }

    private void doPush(SeckillFieldService duibaSeckill, Long appId, String secondType) {
        SeckillMethodService seckillMethodService = SeckillFactory.getSeckillMethodService(secondType);
        if (seckillMethodService == null) {
            return;
        }
        OperatingActivityDto operatingActivity = seckillMethodService.findByAppIdAndDeleted(appId, duibaSeckill.getId());

        Long operatingActivityId;
        boolean activityExist = operatingActivity != null;
        if (activityExist) {
            OperatingActivityDto o4u = new OperatingActivityDto(operatingActivity.getId());
            o4u.setDeleted(Boolean.FALSE);
            setInsertAndUpdateChildCommonProperties(duibaSeckill, appId, o4u);
            o4u.setStatus(OperatingActivityDto.StatusIntOpen);
            operatingActivityService.update(o4u);
            operatingActivityId = operatingActivity.getId();
        } else {
            OperatingActivityDto o4i = new OperatingActivityDto(Boolean.TRUE);
            o4i.setAppId(appId);
            setInsertAndUpdateChildCommonProperties(duibaSeckill, appId, o4i);
            o4i.setStatus(OperatingActivityDto.StatusIntOpen);
            o4i.setParentActivityId(duibaSeckill.getParentActivityId());
            operatingActivityService.insert(o4i);
            operatingActivityId = o4i.getId();
        }

        // 秒杀专题分支
        if (OperatingActivityDto.TypeSecondsKill == duibaSeckill.getType()) {
            // 秒杀专题授权
            permissionBo.grantPermissionToOperatingActivity(operatingActivityId,
                                                            PermissionDto.SourceTypeDuibaSecondsKill);
            if(!activityExist){//活动不存在需要入库appItem和appBanner
                // 入AppItem库
                Long appItemId = setInsertAppItem(operatingActivityId, appId, duibaSeckill);
                // 入AppBanner库
                Long appBannerId = setInsertAppBanner(operatingActivityId, appId, duibaSeckill);
                // 更新
                updateActivityOperating(appItemId, appBannerId, operatingActivityId);
            }
        }
    }

    private void updateActivityOperating(Long appItemId, Long appBannerId, Long operatingActivityId) {
        OperatingActivityDto activity4u = new OperatingActivityDto(operatingActivityId);
        if (appBannerId != null) {
            activity4u.setAppBannerId(appBannerId);
        }
        activity4u.setAppItemId(appItemId);
        operatingActivityService.update(activity4u);
    }

    private Long setInsertAppItem(Long operatingActivityId, Long appId, SeckillFieldService duibaSeckill) {
        AppItemDto appItemDO = new AppItemDto(true);
        appItemDO.setDeleted(true);
        appItemDO.setAppId(appId);
        appItemDO.setCredits(duibaSeckill.getCredits(appId));
        appItemDO.setSalePrice(duibaSeckill.getSalePrice());//
        appItemDO.setTitle(duibaSeckill.getTitle());
        appItemDO.setDescription(duibaSeckill.getTitle());
        appItemDO.setLogo(duibaSeckill.getLogo());
        appItemDO.setSmallImage(duibaSeckill.getSmallImage());
        appItemDO.setWhiteImage(duibaSeckill.getWhiteImage());
        appItemDO.setType(ItemDto.TypeFake);
        appItemDO.setSourceType(AppItemDto.SourceTypeSecondsKill);
        appItemDO.setSourceRelationId(operatingActivityId);
        appItemBo.saveAppItemByTurntable(appItemDO, operatingActivityId);
        return appItemDO.getId();
    }

    private Long setInsertAppBanner(Long operatingActivityId, Long appId, SeckillFieldService duibaSeckill
                                    ) {
        AppBannerDto appBanner = new AppBannerDto();
        appBanner.setPayload(0);
        appBanner.setAppId(appId);
        appBanner.setDeleted(true);
        appBanner.setImage(duibaSeckill.getBannerImage());
        appBanner.setType(BannerOrButtonEnum.BANNER.getCode());
        appBanner.setSourceType(AppBannerSourceTypeEnum.SourceTypeSecondsKill.getCode());
        appBanner.setSourceRelationId(operatingActivityId);
        appBanner.setClassifyImageSwitch(false);
        appBannerBo.saveAppBannerByTurntable(appBanner, operatingActivityId);
        return appBanner.getId();
    }

    private void setInsertAndUpdateChildCommonProperties(SeckillFieldService duibaSeckill, Long appId,
                                                         OperatingActivityDto entity) {
        entity.setType(duibaSeckill.getType());
        entity.setTitle(duibaSeckill.getTitle());
        entity.setCredits(duibaSeckill.getCredits(appId));
        entity.setRule(duibaSeckill.getRule());
        entity.setActivityId(duibaSeckill.getId());
        entity.setImage(duibaSeckill.getMultiImage());
        entity.setSmallImage(duibaSeckill.getSmallImage());
        entity.setBannerImage(duibaSeckill.getBannerImage());
    }

    @Override
    public PaginationDto<TodayRobSeckillListDto> findTodayRobSeckillList(int pageNum, int pageSize,
                                                                         TodayRobSeckillDto todayRobSeckillDto) {
        // 查询秒杀区间
        List<TodayRobSeckillListDto> trd = Lists.newArrayList();
        TodayRobSeckillEntity todayRobSeckillEntity = new TodayRobSeckillEntity();
        ObjectUtil.convert(todayRobSeckillDto, todayRobSeckillEntity);
        ObjectUtil.convertList(todayRobSeckillService.findTodayRobSeckillList(pageNum, pageSize, todayRobSeckillEntity),
                               trd, TodayRobSeckillListDto.class);

        List<TodayRobSeckillListDto> secondActivitys = Lists.newArrayList();
        List<TodayRobSeckillListDto> secondSubjects = Lists.newArrayList();
        for (TodayRobSeckillListDto t : trd) {
            if (TodayRobSeckillDto.SECOND_SUBJECT.equals(t.getSecondType())) {
                secondSubjects.add(t);
            } else {
                secondActivitys.add(t);
            }
        }

        trd = getSeckills(secondActivitys);
        secondSubjects = getSubjects(secondSubjects);
        if (CollectionUtils.isNotEmpty(secondSubjects)) {
            trd.addAll(getSubjects(secondSubjects));
        }

        Collections.sort(trd, new Comparator<TodayRobSeckillListDto>() {

            @Override
            public int compare(TodayRobSeckillListDto o1, TodayRobSeckillListDto o2) {
                return o2.getStartTime().compareTo(o1.getStartTime());
            }
        });
        PaginationDto<TodayRobSeckillListDto> page = new PaginationDto<>();
        page.setRows(trd);
        page.setTotalCount(todayRobSeckillService.findTodayRobSeckillCount(todayRobSeckillEntity));
        return page;

    }

    @Transactional(value = DsConstants.DATABASE_CREDITS_ACTIVITY)
    @Override
    public Boolean seveSeckillConfigs(List<TodayRobSeckillConfigDto> list, TodayRobSeckillDto todayRobSeckillDto) {
        if (CollectionUtils.isEmpty(list) || todayRobSeckillDto == null) {
            return true;
        }
        todayRobSeckillService.delSeckillConfigBatch(Arrays.asList(todayRobSeckillDto.getId()));
        TodayRobSeckillEntity trce = new TodayRobSeckillEntity();
        ObjectUtil.convert(todayRobSeckillDto, trce);
        // 设置秒杀区间为停用状态
        trce.setEnable(false);
        if (trce.getId() == null) {
            todayRobSeckillService.saveSeckill(trce);
        } else {
            todayRobSeckillService.updateSeckill(trce);
        }

        List<TodayRobSeckillConfigEntity> insertList = Lists.newArrayList();
        for (TodayRobSeckillConfigDto dto : list) {
            TodayRobSeckillConfigEntity tce = new TodayRobSeckillConfigEntity();
            ObjectUtil.convert(dto, tce);
            tce.setTodayRobSeckillId(trce.getId());
            insertList.add(tce);
        }
        todayRobSeckillService.saveSeckillConfigBatch(insertList);
        return true;
    }

    /**
     * @see cn.com.duiba.activity.center.biz.bo.rob.TodayRobSeckillBo#saveSeckillConfigs(java.util.List, cn.com.duiba.activity.center.api.dto.rob.TodayRobSeckillDto)
     */
    @Transactional(value = DsConstants.DATABASE_CREDITS_ACTIVITY)
    @Override
    public Long saveSeckillConfigs(List<TodayRobSeckillConfigDto> list, TodayRobSeckillDto todayRobSeckillDto) {
        if (CollectionUtils.isEmpty(list) || todayRobSeckillDto == null) {
            return null;
        }
        TodayRobSeckillEntity trce = new TodayRobSeckillEntity();
        ObjectUtil.convert(todayRobSeckillDto, trce);
        // 设置秒杀区间为停用状态
        trce.setEnable(false);
        if (trce.getId() == null) {
            todayRobSeckillService.saveSeckill(trce);
        } else {
            todayRobSeckillService.delSeckillConfigBatch(Arrays.asList(todayRobSeckillDto.getId()));
            todayRobSeckillService.updateSeckill(trce);
        }

        List<TodayRobSeckillConfigEntity> insertList = Lists.newArrayList();
        for (TodayRobSeckillConfigDto dto : list) {
            TodayRobSeckillConfigEntity tce = new TodayRobSeckillConfigEntity();
            ObjectUtil.convert(dto, tce);
            tce.setTodayRobSeckillId(trce.getId());
            insertList.add(tce);
        }
        todayRobSeckillService.saveSeckillConfigBatch(insertList);
        return trce.getId();
    }

    private List<Long> findActivityIdsByDeveloperId(String secondType, Long developerId) {
        if (StringUtils.isBlank(secondType)) {
            return Lists.newArrayList();
        }
        SeckillMethodService seckillMethodService = SeckillFactory.getSeckillMethodService(secondType);
        if (seckillMethodService == null) {
            return Lists.newArrayList();
        }
        return seckillMethodService.findActivityIdsByDeveloperId(developerId);
    }

    private List<Long> findActivityIdsByAppId(String secondType, Long appId) {
        if (StringUtils.isBlank(secondType)) {
            return Lists.newArrayList();
        }
        SeckillMethodService seckillMethodService = SeckillFactory.getSeckillMethodService(secondType);
        if (StringUtils.isBlank(secondType)) {
            return Lists.newArrayList();
        }
        return seckillMethodService.findActivityIdsByAppId(appId);
    }

    private List<TodayRobSeckillAppDto> findSecondSubjects(AppSimpleDto appDO) {
        List<TodayRobSeckillSpecifyDto> specifyList = todayRobSeckillSpecifyService.selectByAppId(appDO.getId(),
                                                                                                  SeckillShowEntranceEnum.FLOOR.getCode());
        if (CollectionUtils.isEmpty(specifyList)) {
            return Lists.newArrayList();
        }
        List<Long> secondIds = Lists.newArrayList();
        Map<Long, TodayRobSeckillSpecifyDto> map = Maps.newHashMap();
        for (TodayRobSeckillSpecifyDto dto : specifyList) {
            secondIds.add(dto.getActivityId());
            map.put(dto.getActivityId(), dto);
        }
        List<DuibaSecondsKillActivityDto> secondsKillDtos = duibaSecondsKillActivityService.findAllByIds(secondIds);
        // 升序排序
        Collections.sort(secondsKillDtos, new Comparator<DuibaSecondsKillActivityDto>() {

            @Override
            public int compare(DuibaSecondsKillActivityDto o1, DuibaSecondsKillActivityDto o2) {
                return o1.getAutoOnDate().compareTo(o2.getAutoOnDate());
            }
        });
        SeckillMethodService seckillMethodService = SeckillFactory.getSeckillMethodService(TodayRobSeckillDto.SECOND_SUBJECT);
        // 查询被加入黑名单的秒杀活动
        List<Long> blackActivityIds = seckillMethodService.findActivityIdsByDeveloperId(appDO.getDeveloperId());
        // 查询被定向的活动列表
        List<Long> specifyActivityIds = seckillMethodService.findActivityIdsByAppId(appDO.getId());

        DuibaSecondsKillActivityDto duibaSecondsKillActivityDto = null;
        for (DuibaSecondsKillActivityDto dto : secondsKillDtos) {
            // 1. 判断开发者是否在活动黑名单中
            if (dto.isOpenSwitch(DuibaSecondsKillActivityDto.SWITCHES_DEV_BLACKLIST) && blackActivityIds.contains(dto.getId())) {
                continue;
            }
            // 2. 判断app是否在定向列表中
            if (dto.isOpenSwitch(DuibaSecondsKillActivityDto.SWITCHES_DIRECT) && !specifyActivityIds.contains(dto.getId())) {
                AppLogUtil.info(log, "app没有在活动定向列表中，appId={}, activityId={}", appDO.getId(), dto.getId());
                continue;
            }
            if (dto.getAutoOnDate() != null && dto.getAutoOffDate() != null && !dto.getDeleted()
                && DuibaSecondsKillActivityDto.STATUS_STARTUP == dto.getStatus()
                && new Date().before(dto.getAutoOffDate())) {
                duibaSecondsKillActivityDto = dto;
                break;
            }
        }

        if (duibaSecondsKillActivityDto == null) {
            return Lists.newArrayList();
        }
        TodayRobSeckillSpecifyDto todayRobSeckillSpecifyDto = map.get(duibaSecondsKillActivityDto.getId());
        TodayRobSeckillEntity todayRobSeckillEntity = todayRobSeckillService.findSeckillById(todayRobSeckillSpecifyDto.getTodayRobSeckillId());
        TodayRobSeckillAppDto todayRobSeckillAppDto = BeanUtils.copy(todayRobSeckillEntity, TodayRobSeckillAppDto.class);

        todayRobSeckillAppDto.setStartTime(getStartTime(duibaSecondsKillActivityDto));
        List<OperatingActivityDto> operatingActivityList = operatingActivityService.findDuibaSeckillByAppIdAndActivityId(appDO.getId(),
                                                                                                                         Lists.newArrayList(duibaSecondsKillActivityDto.getId()),
                                                                                                                         OperatingActivityDto.TypeSecondsKill);
        todayRobSeckillAppDto.setOperatingActivityList(operatingActivityList);
        todayRobSeckillAppDto.setSecondType(todayRobSeckillEntity.getSecondType());
        todayRobSeckillAppDto.setImage(todayRobSeckillEntity.getImage());
        return Lists.newArrayList(todayRobSeckillAppDto);
    }

    private Date getStartTime(DuibaSecondsKillActivityDto seckill) {
        List<ItemsArea> list = seckill.getItemsAreaList();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Date now = new Date();
        // 如果当日的秒杀时间都过期则取下一日的日期
        Date killDate = getKillDate(list, now);
        if (killDate != null) {
            return killDate;
        }
        // 取下一日秒杀时间
        Date nextDate = getKillDate(list, DateUtil.getSpecifiedDayAfter(now));
        if (nextDate != null && nextDate.before(seckill.getAutoOffDate())) {
            return nextDate;
        }
        return null;
    }

    private Date getKillDate(List<ItemsArea> list, Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        Date now = new Date();
        for (ItemsArea ia : list) {
            if (StringUtils.isNotBlank(ia.getKillhour())) {
                if (ia.getKillhour().length() == 1) {
                    ia.setKillhour("0" + ia.getKillhour());
                }
                c.set(Calendar.HOUR_OF_DAY, Integer.valueOf(ia.getKillhour()));
                Date killDate = c.getTime();
                if (now.before(killDate)) {
                    return killDate;
                }
            }
        }
        return null;
    }

    @Override
    public List<TodayRobSeckillAppDto> findSeckillListByApp(Long appId, String showEntrance) {
        AppSimpleDto appDO = remoteAppService.getSimpleApp(appId).getResult();
        // 首页楼层，只查询秒杀专题
        if (SeckillShowEntranceEnum.FLOOR.getCode().equals(showEntrance)) {
            return findSecondSubjects(appDO);
        }

        CenterConfigDto entity = centerConfigService.findPushAppsConfig();
        if (null == entity) {
            return Lists.newArrayList();
        }
        PushAppsConfig pushAppsConfig = new PushAppsConfig(entity);
        if (!pushAppsConfig.getAppIdList().contains(appId)) {
            return Lists.newArrayList();
        }
        List<TodayRobSeckillAppDto> list = Lists.newArrayList();
        // 查询上一个秒杀区间
        TodayRobSeckillEntity beforeEntity = todayRobSeckillService.findSeckillBeforeNow(showEntrance);
        TodayRobSeckillAppDto beforeAppDto = null;

        String secondType = beforeEntity == null ? "" : beforeEntity.getSecondType();
        // 查询被加入黑名单的秒杀活动
        List<Long> blackActivityIds = findActivityIdsByDeveloperId(secondType, appDO.getDeveloperId());
        // 查询被定向的活动列表
        beforeAppDto = findDirectActivitys(appId, list, beforeEntity, beforeAppDto, secondType, blackActivityIds);
        List<Long> specifyActivityIds;
        List<TodayRobSeckillEntity> entityList = null;
        if (CollectionUtils.isNotEmpty(list)) {
            entityList = todayRobSeckillService.findSeckillAfterNow(1, showEntrance);
        } else {
            entityList = todayRobSeckillService.findSeckillAfterNow(2, showEntrance);
        }
        if (CollectionUtils.isNotEmpty(entityList)) {
            for (TodayRobSeckillEntity afterEntity : entityList) {
                blackActivityIds = findActivityIdsByDeveloperId(afterEntity.getSecondType(), appDO.getDeveloperId());
                specifyActivityIds = findActivityIdsByAppId(afterEntity.getSecondType(), appId);
                TodayRobSeckillAppDto afterAppDto = convertDto(appId, afterEntity, blackActivityIds, specifyActivityIds);
                if (CollectionUtils.isNotEmpty(afterAppDto.getOperatingActivityList())) {
                    list.add(afterAppDto);
                }
            }
        }
        if (CollectionUtils.isEmpty(list) && beforeAppDto != null) {
            // 如果后面没有配置秒杀活动，持续展示今天最后一个秒杀区间
            list.add(beforeAppDto);
        }
        return list;
    }

    private TodayRobSeckillAppDto findDirectActivitys(Long appId, List<TodayRobSeckillAppDto> list, TodayRobSeckillEntity beforeEntity, TodayRobSeckillAppDto beforeAppDto, String secondType, List<Long> blackActivityIds) {
        List<Long> specifyActivityIds = findActivityIdsByAppId(secondType, appId);
        if (beforeEntity != null) {
            beforeAppDto = convertDto(appId, beforeEntity, blackActivityIds, specifyActivityIds);
            if (beforeAppDto.isActivityEnd()) {// 是否区间内所有活动已结束
                if (new Date().before(DateUtil.minutesAddOrSub(beforeAppDto.getStartTime(), 15))) {
                    // 结束的活动保留15分钟
                    list.add(beforeAppDto);
                }
            } else {
                // 未结束的活动继续展示
                list.add(beforeAppDto);
            }
        }
        return beforeAppDto;
    }

    private List<Long> getSeckillIds(List<TodayRobSeckillConfigEntity> seckillList,
                                     TodayRobSeckillEntity todayRobSeckillEntity) {
        List<Long> seckillIds = Lists.newArrayList();
        for (TodayRobSeckillConfigEntity seckill : seckillList) {
            if (todayRobSeckillEntity.getSecondType().equals(seckill.getSecondType())) {
                seckillIds.add(seckill.getActivityId());
            }
        }
        return seckillIds;
    }

    private TodayRobSeckillAppDto convertDto(Long appId, TodayRobSeckillEntity todayRobSeckillEntity,
                                             List<Long> blackActivityIds, List<Long> specifyActivityIds) {
        List<TodayRobSeckillConfigEntity> seckillList = todayRobSeckillService.findSeckillConfigs(todayRobSeckillEntity.getId());
        return getTodayRobSeckillAppDto(appId, todayRobSeckillEntity, blackActivityIds, specifyActivityIds,
                                        getSeckillIds(seckillList, todayRobSeckillEntity));
    }

    private TodayRobSeckillAppDto getTodayRobSeckillAppDto(Long appId, TodayRobSeckillEntity todayRobSeckillEntity,
                                                           List<Long> blackActivityIds, List<Long> specifyActivityIds,
                                                           List<Long> seckillIds) {
        TodayRobSeckillAppDto todayRobSeckillAppDto = BeanUtils.copy(todayRobSeckillEntity, TodayRobSeckillAppDto.class);
        SeckillMethodService seckillMethodService = SeckillFactory.getSeckillMethodService(todayRobSeckillEntity.getSecondType());
        if (seckillMethodService == null) {
            return todayRobSeckillAppDto;
        }
        // 查询秒杀活动：开启且可见，未删除
        List<SeckillFieldService> duibaSeckillList = seckillMethodService.findAllOpenedByIds(seckillIds);
        if (CollectionUtils.isEmpty(duibaSeckillList)) {
            return todayRobSeckillAppDto;
        }
        List<Long> activityIds = Lists.newArrayList();
        int num = 0;
        Integer operatingActivityType = null;
        for (SeckillFieldService duibaSeckill : duibaSeckillList) {
            if (operatingActivityType == null) {
                operatingActivityType = duibaSeckill.getType();
            }
            // 1. 判断开发者是否在活动黑名单中
            if (isInActivityBlack(blackActivityIds, duibaSeckill)) {
                continue;
            }
            // 2. 判断app是否在定向列表中
            if (isInDirect(specifyActivityIds, duibaSeckill)) {
                AppLogUtil.info(log, "app没有在活动定向列表中，appId={}, activityId={}", appId, duibaSeckill.getId());
                continue;
            }
            /* 以下情况活动需要展示 */
            activityIds.add(duibaSeckill.getId());
            // 3. 判断活动是否已结束
            // (当前时间>结束日期第二天00:00:00 || 当前时间 > 结束时间)
            if (duibaSeckill.checkDateTime()) {
                continue;
            }
            // 4. 判断秒杀库存是否耗尽
            Long stock = seckillMethodService.getStock(appId, duibaSeckill.getId());
            if (stock == null || stock < 1) {
                continue;
            }
            num++;
            todayRobSeckillAppDto.setStartTime(duibaSeckill.getStartTime(todayRobSeckillAppDto.getStartTime()));
        }
        todayRobSeckillAppDto.setActivityEnd(num == 0);
        List<OperatingActivityDto> operatingActivityList = operatingActivityService.findDuibaSeckillByAppIdAndActivityId(appId,
                                                                                                                         activityIds,
                                                                                                                         operatingActivityType);
        // 专题活动只有一条，所以不需要排序
        if (!TodayRobSeckillDto.SECOND_SUBJECT.equals(todayRobSeckillEntity.getSecondType())) {
            // 根据管理员配置的顺序排列
            operatingActivityList = orderBySeckillIds(operatingActivityList, seckillIds);
        }
        todayRobSeckillAppDto.setOperatingActivityList(operatingActivityList);
        todayRobSeckillAppDto.setSecondType(todayRobSeckillEntity.getSecondType());
        todayRobSeckillAppDto.setImage(todayRobSeckillEntity.getImage());
        return todayRobSeckillAppDto;
    }

    private boolean isInDirect(List<Long> specifyActivityIds, SeckillFieldService duibaSeckill) {
        return duibaSeckill.isOpenDirect() && !specifyActivityIds.contains(duibaSeckill.getId());
    }

    private boolean isInActivityBlack(List<Long> blackActivityIds, SeckillFieldService duibaSeckill) {
        return duibaSeckill.isOpenBlack() && blackActivityIds.contains(duibaSeckill.getId());
    }

    private List<OperatingActivityDto> orderBySeckillIds(List<OperatingActivityDto> operatingActivityList,
                                                         List<Long> seckillIds) {
        Map<Long, OperatingActivityDto> map = Maps.newHashMap();
        Set<Long> appIds = Sets.newHashSet();
        for (OperatingActivityDto dto : operatingActivityList) {
            if (map.get(dto.getActivityId()) == null && dto.getStatus() == OperatingActivityDto.StatusIntOpen) {
                map.put(dto.getActivityId(), dto);
                appIds.add(dto.getAppId());
            }
        }
        Map<Long, AppSimpleDto> appMap = Maps.newHashMap();
        for (Long appId : appIds) {
            AppSimpleDto app = remoteAppService.getSimpleApp(appId).getResult();
            appMap.put(app.getId(), app);
        }

        List<OperatingActivityDto> list = Lists.newArrayList();

        for (Long seckillId : seckillIds) {
            if (map.get(seckillId) != null) {
                OperatingActivityDto operatingActivityDto = map.get(seckillId);
                DuibaSeckillDto seckill = duibaSeckillService.find(operatingActivityDto.getActivityId());
                ItemKeyDto itemKey = remoteItemKeyService.findItemKey(null, seckill.getItemId(),
                                                                      operatingActivityDto.getAppId()).getResult();
                DubboResult<Long> dubboResult = remotePreStockService.calculateCreditsByItemKeyAndDegree(itemKey,
                                                                                                         null,
                                                                                                         appMap.get(operatingActivityDto.getAppId()).getCreditsRate().longValue());
                operatingActivityDto.setCredits(dubboResult.getResult());
                list.add(operatingActivityDto);
            }
        }
        return list;
    }

    @Override
    public Boolean enableTodayRobSeckill(long todayRobSeckillId, boolean enable) {
        // 开启状态，推送活动
        if (enable) {
            TodayRobSeckillEntity todayRobSeckillEntity = todayRobSeckillService.findSeckillById(todayRobSeckillId);
            List<TodayRobSeckillConfigDto> pushList = Lists.newArrayList();
            List<TodayRobSeckillConfigEntity> seckillList = todayRobSeckillService.findSeckillConfigs(todayRobSeckillId);
            ObjectUtil.convertList(seckillList, pushList, TodayRobSeckillConfigDto.class);
            List<Long> appIdList = Lists.newArrayList();
            if (StringUtils.equals(todayRobSeckillEntity.getShowEntrance(), SeckillShowEntranceEnum.FLOOR.getCode())) {
                // 首页楼层只支持秒杀专题，从定向列表中获取推送app
                List<TodayRobSeckillSpecifyDto> list = todayRobSeckillSpecifyService.selectByTodayRobSeckillId(todayRobSeckillId);
                for (TodayRobSeckillSpecifyDto dto : list) {
                    appIdList.add(dto.getAppId());
                }
            } else {
                CenterConfigDto entity = centerConfigService.findPushAppsConfig();
                PushAppsConfig pushAppsConfig = new PushAppsConfig(entity);
                appIdList = pushAppsConfig.getAppIdList();
            }
            pushSeckillActivity(pushList, appIdList);
        }
        todayRobSeckillService.enableTodayRobSeckill(todayRobSeckillId, enable);
        return true;
    }

    @Override
    public void disableSecondSubject() {
        // 查询过期秒杀专题id1
        List<Long> secondSeckillIds = duibaSecondsKillActivityService.findDateInvalid();

        List<Long> todaySeckillIds = Lists.newArrayList();
        List<TodayRobSeckillConfigEntity> configs = todayRobSeckillService.findBySeconds(TodayRobSeckillDto.SECOND_SUBJECT);
        for (TodayRobSeckillConfigEntity ts : configs) {
            // 如果当前记录的秒杀专题已失效，取免单秒杀id
            if (secondSeckillIds.contains(ts.getActivityId())) {
                todaySeckillIds.add(ts.getTodayRobSeckillId());
            }
        }

        // 数据量不会大，所以循环更新
        for (Long id : todaySeckillIds) {
            todayRobSeckillService.enableTodayRobSeckill(id, false);
        }
    }

    /*
     * 获取秒杀区间ids
     * @param trd
     * @param type
     * @return
     */
    private List<Long> getTodabRobSeckillIds(List<TodayRobSeckillListDto> trd, String type) {
        List<Long> todayRobSeckillIds = Lists.newArrayList();
        for (TodayRobSeckillListDto trs : trd) {
            if (type.equals(trs.getSecondType())) {
                todayRobSeckillIds.add(trs.getId());
            }
        }
        return todayRobSeckillIds;
    }

    /*
     * @param time 08:46-20:54
     * @return
     */
    private String[] getTime(String time) {
        if (StringUtils.isNotBlank(time) && time.split("-").length == 2) {
            String[] arr = new String[2];
            arr[0] = time.split("-")[0];
            arr[1] = time.split("-")[1];
            return arr;
        }
        return new String[2];
    }

    /*
     * 2016-12-12,2017-01-05
     * @param time
     * @return
     */
    private String[] getLimitDate(String date) {
        if (StringUtils.isNotBlank(date) && !"no".equals(date) && date.split(",").length == 2) {
            String[] arr = new String[2];
            arr[0] = date.split(",")[0];
            arr[1] = date.split(",")[1];
            return arr;
        }
        return new String[2];
    }

    /*
     * 获取秒杀ids
     * @param trsd
     * @return
     */
    private List<Long> getSeckillIds(List<TodayRobSeckillConfigListDto> trsd) {
        List<Long> seckillIds = Lists.newArrayList();// 秒杀id
        for (TodayRobSeckillConfigListDto trs : trsd) {
            if (TodayRobSeckillDto.SECOND_ACTIVITY.equals(trs.getSecondType())) {
                seckillIds.add(trs.getActivityId());
            }
        }
        return seckillIds;
    }

    /*
     * 获取秒杀活动对象
     * @param seckillIds
     * @return
     */
    private Map<Long, DuibaSeckillDto> getSeckillDto(List<Long> seckillIds) {
        if (CollectionUtils.isEmpty(seckillIds)) {
            Maps.newHashMap();
        }
        List<DuibaSeckillDto> seckills = duibaSeckillService.findAllByIds(seckillIds);
        Map<Long, DuibaSeckillDto> seckillMap = Maps.newHashMap();
        for (DuibaSeckillDto d : seckills) {
            seckillMap.put(d.getId(), d);
        }
        return seckillMap;
    }

    /*
     * 获取秒杀区间配置列表
     * @param trd
     * @param type
     * @return
     */
    private List<TodayRobSeckillConfigListDto> getTodayRobSeckillConfigs(List<TodayRobSeckillListDto> trd, String type) {
        // 获取秒杀区间ids
        List<Long> todayRobSeckillIds = getTodabRobSeckillIds(trd, type);
        if (todayRobSeckillIds == null || todayRobSeckillIds.isEmpty()) {
            return Lists.newArrayList();
        }

        // 查询秒杀区间配置
        List<TodayRobSeckillConfigListDto> trsd = Lists.newArrayList();
        ObjectUtil.convertList(todayRobSeckillService.findSeckillConfigByIdss(todayRobSeckillIds), trsd,
                               TodayRobSeckillConfigListDto.class);
        return trsd;
    }

    private Seckill getDuibaSeckillIds(List<ItemDto> items) {
        // 商品id为key取秒杀活动
        Map<Long, List<Long>> seckillIdMaps = Maps.newHashMap();
        List<Long> duibaSeckillIds = Lists.newArrayList();
        for (ItemDto item : items) {
            // 如果是秒杀活动
            if (ItemDto.SourceTypeKillActivity == item.getSourceType()) {
                List<Long> sIds = seckillIdMaps.get(item.getId());
                if (sIds == null) {
                    sIds = Lists.newArrayList();
                    seckillIdMaps.put(item.getId(), sIds);
                }
                sIds.add(item.getSourceRelationId());
                duibaSeckillIds.add(item.getSourceRelationId());
            }

        }
        return new Seckill(seckillIdMaps, duibaSeckillIds);
    }

    private Item getItemIds(List<DuibaSecondsKillActivityDto> seckills) {
        // 秒杀专题id为key 取商品
        Map<Long, List<Long>> itemIdMaps = Maps.newHashMap();
        List<Long> itemIds = Lists.newArrayList();
        for (DuibaSecondsKillActivityDto dsa : seckills) {
            itemIdMaps.put(dsa.getId(), dsa.getItemIdList());
            itemIds.addAll(dsa.getItemIdList());
        }
        if (CollectionUtils.isEmpty(itemIds)) {
            return new Item(itemIdMaps, Lists.<ItemDto> newArrayList(), Maps.<Long, String> newHashMap());
        }
        List<ItemDto> items = remoteDuibaItemGoodsService.findByIds(itemIds).getResult();
        List<ItemExtraDto> itemExtras = remoteItemExtraService.findByItemIds(itemIds).getResult();
        Map<Long, String> itemExtraMap = Maps.newHashMap();
        for (ItemExtraDto itemExtra : itemExtras) {
            itemExtraMap.put(itemExtra.getItemId(), itemExtra.getName4Admin());
        }

        return new Item(itemIdMaps, items, itemExtraMap);
    }

    /*
     * 获取秒杀专题对象
     * @param seckillIds
     * @return
     */
    private Map<Long, List<TodayRobSeckillConfigListDto>> getSeckillSubjectListDto(List<TodayRobSeckillConfigListDto> trsd,
                                                                                   List<Long> subjectIds) {
        if (CollectionUtils.isEmpty(subjectIds)) {
            Maps.newHashMap();
        }
        List<DuibaSecondsKillActivityDto> seckills = duibaSecondsKillActivityService.findAllByIds(subjectIds);
        Item itemClass = getItemIds(seckills);

        // 秒杀专题id为key 取商品
        Map<Long, List<Long>> itemIdMaps = itemClass.getItemIdMaps();
        List<ItemDto> items = itemClass.getItems();
        Map<Long, String> itemExtraMap = itemClass.getItemExtraMap();

        // 商品id为key取秒杀活动
        Seckill seckillClass = getDuibaSeckillIds(items);
        List<DuibaSeckillDto> sDtos = Lists.newArrayList();
        Map<Long, List<TodayRobSeckillConfigListDto>> retMaps = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(seckillClass.getDuibaSeckillIds())) {
            sDtos = duibaSeckillService.findAllByIds(seckillClass.getDuibaSeckillIds());

        }
        for (TodayRobSeckillConfigListDto td : trsd) {
            TodayRobSeckillConfigListDto tcd = null;
            List<Long> itemIdsR = itemIdMaps.get(td.getActivityId());
            if (CollectionUtils.isEmpty(itemIdsR)) {
                continue;
            }
            List<TodayRobSeckillConfigListDto> list = retMaps.get(td.getTodayRobSeckillId());
            if (list == null) {
                list = Lists.newArrayList();
                retMaps.put(td.getTodayRobSeckillId(), list);
            }

            addTodayRobSeckillConfigList(items, itemExtraMap, seckillClass, sDtos, itemIdsR, list);

        }
        return retMaps;
    }

    private void addTodayRobSeckillConfigList(List<ItemDto> items, Map<Long, String> itemExtraMap, Seckill seckillClass, List<DuibaSeckillDto> sDtos, List<Long> itemIdsR, List<TodayRobSeckillConfigListDto> list) {
        TodayRobSeckillConfigListDto tcd;
        for (ItemDto item : items) {
            tcd = new TodayRobSeckillConfigListDto();
            // 查询商品
            if (!itemIdsR.contains(item.getId())) {
                continue;
            }
            updateTodayRobSeckillConfigInfo(itemExtraMap, seckillClass, sDtos, list, tcd, item);
        }
    }

    private void updateTodayRobSeckillConfigInfo(Map<Long, String> itemExtraMap, Seckill seckillClass, List<DuibaSeckillDto> sDtos, List<TodayRobSeckillConfigListDto> list, TodayRobSeckillConfigListDto tcd, ItemDto item) {
        if (!"fake".equals(item.getType())) {
            setTodayRobSeckillConfigInfoTypeIsNotFake(itemExtraMap, tcd, item);
            list.add(tcd);
        } else {
            // 查询秒杀活动
            List<Long> seckillIdss = seckillClass.getSeckillIdMaps().get(item.getId());
            if (CollectionUtils.isEmpty(seckillIdss)) {
                return;
            }
            for (DuibaSeckillDto dsd : sDtos) {
                if (seckillIdss.contains(dsd.getId())) {
                    tcd = getTodayRobSeckillConfigListDto(dsd);
                    list.add(tcd);
                }
            }

        }
    }

    private void setTodayRobSeckillConfigInfoTypeIsNotFake(Map<Long, String> itemExtraMap, TodayRobSeckillConfigListDto tcd, ItemDto item) {
        String[] dates = getLimitDate(item.getLimitDate());
        tcd.setStartDate(dates[0]);
        tcd.setEndDate(dates[1]);
        String[] times = getTime(item.getLimitTimeBetween());
        tcd.setStartTime(times[0]);
        tcd.setEndTime(times[1]);
        tcd.setTitle(itemExtraMap.get(item.getId()));
        // 打上失效标识：已失效，如果秒杀活动处于“关闭且可见”或者“关闭且不可见”状态，或者已删除，则在页面上标记“已失效”；
        if (item.getDeleted() || !item.getEnable()) {
            tcd.setInvalidFlag(true);
        }
        tcd.setSecondType(TodayRobSeckillDto.SECOND_ITEM);
        tcd.setActivityId(item.getId());
    }

    private TodayRobSeckillConfigListDto getTodayRobSeckillConfigListDto(DuibaSeckillDto dsd) {
        TodayRobSeckillConfigListDto trld = new TodayRobSeckillConfigListDto();
        trld.setStartDate(DateUtil.getDayStr(dsd.getDateStart()));
        trld.setEndDate(DateUtil.getDayStr(dsd.getDateEnd()));
        trld.setStartTime(DateUtil.getMinuteOnlyStr(dsd.getTimeStart()));
        trld.setEndTime(DateUtil.getMinuteOnlyStr(dsd.getTimeEnd()));
        trld.setTitle(dsd.getTitle());
        trld.setActivityId(dsd.getId());
        trld.setSecondType(TodayRobSeckillDto.SECOND_ACTIVITY);
        // 打上失效标识：已失效，如果秒杀活动处于“关闭且可见”或者“关闭且不可见”状态，或者已删除，则在页面上标记“已失效”；
        if (dsd.getDeleted() || DuibaQuestionAnswerDto.STATUS_OPEN != dsd.getStatus()) {
            trld.setInvalidFlag(true);
        }

        return trld;
    }

    /**
     * 获取秒杀专题
     *
     * @param trds
     * @return
     */
    private List<TodayRobSeckillListDto> getSubjects(List<TodayRobSeckillListDto> trds) {

        // 查询秒杀区间配置
        List<TodayRobSeckillConfigListDto> trsd = getTodayRobSeckillConfigs(trds, TodayRobSeckillDto.SECOND_SUBJECT);
        List<Long> subjectIds = Lists.newArrayList();
        Map<Long, Long> todayRobSeckillIdMap = Maps.newHashMap();
        Iterator<TodayRobSeckillConfigListDto> subjectIte = trsd.iterator();
        while (subjectIte.hasNext()) {
            TodayRobSeckillConfigListDto trd = subjectIte.next();
            // 秒杀专题下会有秒杀活动，这里只取秒杀专题
            if (TodayRobSeckillDto.SECOND_SUBJECT.equals(trd.getSecondType())) {
                subjectIds.add(trd.getActivityId());
                todayRobSeckillIdMap.put(trd.getTodayRobSeckillId(), trd.getActivityId());
            } else {
                subjectIte.remove();
            }

        }

        Map<Long, List<TodayRobSeckillConfigListDto>> retMaps = getSeckillSubjectListDto(trsd, subjectIds);
        if (MapUtils.isEmpty(retMaps)) {
            retMaps = Maps.newHashMap();
        }
        for (TodayRobSeckillListDto trd : trds) {
            if (TodayRobSeckillDto.SECOND_SUBJECT.equals(trd.getSecondType())) {
                Long secondSubjectId = todayRobSeckillIdMap.get(trd.getId());
                SeckillFieldService seckillFieldService = SeckillFactory.getSeckillFieldService(TodayRobSeckillDto.SECOND_SUBJECT,
                                                                                                secondSubjectId);
                // 如果免单秒杀,秒杀类型配置的是秒杀专题，则秒杀时间取秒杀专题本身开始日期
                trd.setStartTime(seckillFieldService.getDateStart());
                trd.setTodayRobSeckillConfigs(retMaps.get(trd.getId()));

            }
        }
        return trds;
    }

    /**
     * 获取秒杀配置
     *
     * @param trd
     * @return
     */
    private List<TodayRobSeckillListDto> getSeckills(List<TodayRobSeckillListDto> trd) {

        // 查询秒杀区间配置
        List<TodayRobSeckillConfigListDto> trsd = getTodayRobSeckillConfigs(trd, TodayRobSeckillDto.SECOND_ACTIVITY);

        // 查询秒杀
        Map<Long, DuibaSeckillDto> seckillMap = getSeckillDto(getSeckillIds(trsd));

        Map<Long, List<TodayRobSeckillConfigListDto>> configMap = Maps.newHashMap();
        // set秒杀字段
        for (TodayRobSeckillConfigListDto trld : trsd) {
            DuibaSeckillDto dsd = seckillMap.get(trld.getActivityId());
            TodayRobSeckillConfigListDto trldn = new TodayRobSeckillConfigListDto();
            if (dsd != null) {
                trldn = getTodayRobSeckillConfigListDto(dsd);
                trldn.setTodayRobSeckillId(trld.getTodayRobSeckillId());
                trldn.setPayload(trld.getPayload());
                trldn.setActivityId(trld.getActivityId());
            }
            List<TodayRobSeckillConfigListDto> l = configMap.get(trld.getTodayRobSeckillId());
            if (l == null) {
                l = Lists.newArrayList();
                configMap.put(trld.getTodayRobSeckillId(), l);
            }
            l.add(trldn);
        }

        // set秒杀区间配置
        for (TodayRobSeckillListDto trs : trd) {
            List<TodayRobSeckillConfigListDto> ts = configMap.get(trs.getId());
            if (ts != null && !ts.isEmpty()) {
                Collections.sort(ts, new Comparator<TodayRobSeckillConfigListDto>() {

                    @Override
                    public int compare(TodayRobSeckillConfigListDto o1, TodayRobSeckillConfigListDto o2) {
                        return o2.getPayload().compareTo(o1.getPayload());
                    }
                });
                trs.setTodayRobSeckillConfigs(ts);

            }
        }

        return trd;
    }

}

class Item {

    private Map<Long, List<Long>> itemIdMaps;
    private List<ItemDto>         items;
    private Map<Long, String>     itemExtraMap;

    public Item(Map<Long, List<Long>> itemIdMaps, List<ItemDto> items, Map<Long, String> itemExtraMap) {
        this.itemIdMaps = itemIdMaps;
        this.items = items;
        this.itemExtraMap = itemExtraMap;
    }

    public Map<Long, List<Long>> getItemIdMaps() {
        return itemIdMaps;
    }

    public List<ItemDto> getItems() {
        return items;
    }

    public Map<Long, String> getItemExtraMap() {
        return itemExtraMap;
    }

}

class Seckill {

    private Map<Long, List<Long>> seckillIdMaps;
    private List<Long>            duibaSeckillIds;

    public Seckill(Map<Long, List<Long>> seckillIdMaps, List<Long> duibaSeckillIds) {
        this.seckillIdMaps = seckillIdMaps;
        this.duibaSeckillIds = duibaSeckillIds;
    }

    public Map<Long, List<Long>> getSeckillIdMaps() {
        return seckillIdMaps;
    }

    public List<Long> getDuibaSeckillIds() {
        return duibaSeckillIds;
    }
}
