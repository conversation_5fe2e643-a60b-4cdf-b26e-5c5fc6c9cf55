package cn.com.duiba.activity.center.biz.bo.activity.impl;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.hdtool.DuibaHdtoolDto;
import cn.com.duiba.activity.center.api.dto.recommend.RecommendDto;
import cn.com.duiba.activity.center.api.dto.statistics.ActivityJoinStatDto;
import cn.com.duiba.activity.center.api.params.RecommendParams;
import cn.com.duiba.activity.center.biz.bo.activity.RecommendBo;
import cn.com.duiba.activity.center.biz.factory.ActivityFactory;
import cn.com.duiba.activity.center.biz.field.ActivityField;
import cn.com.duiba.activity.center.biz.service.activity.OperatingActivityService;
import cn.com.duiba.activity.center.biz.service.hdtool.DuibaHdtoolService;
import cn.com.duiba.activity.center.biz.service.statistics.ActivityJoinStatService;
import cn.com.duiba.activity.center.biz.utils.ShareLandUrlUtils;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.developer.center.api.domain.dto.appextra.DuibaShareDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppExtraService;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duibabiz.component.domain.DomainService;
import com.google.common.base.Function;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * Created by zhengjy on 2017/2/9.
 */
@Service
public class RecommendBoImpl implements RecommendBo {

    private static Logger            log = LoggerFactory.getLogger(RecommendBoImpl.class);
    @Autowired
    private ActivityJoinStatService  activityJoinStatService;

    @Autowired
    private OperatingActivityService operatingActivityService;
    @Autowired
    private RemoteAppExtraService    remoteAppExtraService;
    @Autowired
    private ActivityFactory activityFactory;
    @Autowired
    private DomainService domainService;
    @Autowired
    private DuibaHdtoolService duibaHdtoolService;

    @Override
    public List<RecommendDto> findRecommends(RecommendParams recommendParams) {
        if (recommendParams == null || recommendParams.getAppId() == null) {
            return Lists.newArrayList();
        }

        // 2017-02-08 00:00:00 上一日时间1
        Date startDate = DateUtils.getDayStartTime(DateUtils.getSecondStr(DateUtils.changeByDay(new Date(), -1)));
        // 2017-02-09 00:00:00，当日时间
        Date curDate = DateUtils.getDayStartTime(DateUtils.getSecondStr(new Date()));

        DuibaShareDto appExtra = remoteAppExtraService.findDuibaShareByAppId(recommendParams.getAppId()).getResult();
        List<RecommendDto> recommendDtos = getActivityJoinRecommendDtos(startDate, curDate, recommendParams, appExtra);

        // 如果昨日该app下没有兑换过商品，则取今日app上架的活动
        if (CollectionUtils.isEmpty(recommendDtos)
            || !recommendParams.getRecommendShowNum().equals(recommendDtos.size())) {
            // 2017-02-10 00:00:00 下一日时间
            Date nextDate = DateUtils.getDayStartTime(DateUtils.getSecondStr(DateUtils.changeByDay(new Date(), 1)));
            List<OperatingActivityDto> oads = operatingActivityService.findByDateAndAppIds(recommendParams.getAppId(),
                                                                                           curDate, nextDate);
            recommendDtos = getActivityRecommendDtos(oads, recommendParams, appExtra);
        }

        if (CollectionUtils.isEmpty(recommendDtos)) {
            return Lists.newArrayList();
        }
        return recommendDtos;
    }

    // 查询活动参与次数表
    private List<RecommendDto> getActivityRecommendDtos(List<OperatingActivityDto> oas,
                                                        RecommendParams recommendParams, DuibaShareDto appExtra) {

        // 取要展示推荐位的dto
        List<RecommendDto> recommendDtos = Lists.newArrayList();
        Map<Integer, ActivityField> activityFieldMap = buildActivityFieldMap(oas);
        if (CollectionUtils.isEmpty(oas)) {
          return recommendDtos;
        }
        if(oas.size()>1) {
          oas = oas.stream()
              .sorted(Comparator.comparing(OperatingActivityDto::getGmtCreate).reversed()).collect(
                  Collectors.toList());
        }
        ConvertDto transformCall = new ConvertDto();
        transformCall.setRecommendParams(recommendParams);
        transformCall.setAppExtra(appExtra);
        transformCall.setActivityFieldMap(activityFieldMap);
        transformCall.setSystemDomain(domainService.getSystemDomain(recommendParams.getAppId()));
        recommendDtos = transformCall.apply(oas);

        return recommendDtos;
    }

    // 查询活动参与次数表
    private List<RecommendDto> getActivityJoinRecommendDtos(Date startDate, Date endDate,
                                                            RecommendParams recommendParams, DuibaShareDto appExtra) {
        // 查询活动参与次数列表
        List<ActivityJoinStatDto> joins;
        try {
            joins = activityJoinStatService.findList(startDate, endDate, recommendParams.getAppId());

        } catch (ExecutionException e) {
            log.error("", e);
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(joins)) {
            return Lists.newArrayList();
        }


        List<OperatingActivityDto> oas = operatingActivityService.findAllByIds(joins.stream().map(ActivityJoinStatDto::getOperationActivityId).collect(
            Collectors.toList()));

        if(joins.size()>1){
          oas = sortOas(joins, oas);
        }

        Map<Integer, ActivityField> activityFieldMap = buildActivityFieldMap(oas);
        List<RecommendDto> recommendDtos = Lists.newArrayList();
        if (CollectionUtils.isEmpty(oas)) {
            //如果为空直接返回
            return recommendDtos;
        }

        ConvertDto transformCall = new ConvertDto();
        transformCall.setRecommendParams(recommendParams);
        transformCall.setAppExtra(appExtra);
        transformCall.setActivityFieldMap(activityFieldMap);
        transformCall.setSystemDomain(domainService.getSystemDomain(recommendParams.getAppId()));
        recommendDtos = transformCall.apply(oas);
        return recommendDtos;
    }

  /**
   * 根据昨日参与数进行排序
   * @param joins
   * @param oas
   * @return
   */
  private List<OperatingActivityDto> sortOas(List<ActivityJoinStatDto> joins,
      List<OperatingActivityDto> oas) {
    Map<Long,Integer> actJoins = Maps.newHashMap();
    joins.forEach(one -> actJoins.put(one.getOperationActivityId(),one.getJoinNum()));

    oas = oas.stream().sorted((o1, o2) -> {
      Integer joinNum1 = actJoins.get(o1.getId());
      Integer joinNum2 = actJoins.get(o2.getId());
      if(joinNum1 == null){
        joinNum1 = 0;
      }
      if(joinNum2 == null){
        joinNum2 = 0;
      }
      if(joinNum1 < joinNum2){
        return 1;
      }
      return -1;
    }).collect(Collectors.toList());
    return oas;
  }


  private Map<Integer, ActivityField> buildActivityFieldMap(List<OperatingActivityDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyMap();
        }
        Map<Integer, Set<Long>> typeIdMap = new HashMap<>();
        Set<Long> ids = null;
        for (OperatingActivityDto oa:dtos) {
            ids = typeIdMap.get(oa.getType());
            if (ids==null) {
                ids = new LinkedHashSet<>();
                typeIdMap.put(oa.getType(), ids);
            }
            ids.add(oa.getActivityId());
        }

        Map<Integer, ActivityField> activityFieldMap = new HashMap<>();
        for (Map.Entry<Integer, Set<Long>> entry: typeIdMap.entrySet()) {
            ActivityField activityField = activityFactory.getActivityField(new ArrayList<Long>(entry.getValue()),entry.getKey());
            if (activityField!=null) {
                activityFieldMap.put(entry.getKey(), activityField);
            }
        }
        return activityFieldMap;
    }

    /*
     * 校验是否可推荐当前活动
     * @param oa
     * @param recommendParams
     * @return true 可推荐活动，false：不可推荐
     */
    private Boolean filterActivity(OperatingActivityDto oa, RecommendParams recommendParams, ActivityField activityField) {
        // 推荐位只推荐这几种活动 兑吧竞猜、活动工具、兑吧新游戏、兑吧答题、测试题
        // if(!(OperatingActivityDto.TypeDuibaGuess == oa.getType() ||
        // OperatingActivityDto.hdToolTypeSet.contains(oa.getType())||
        // OperatingActivityDto.TypeDuibaNgame == oa.getType()||
        // OperatingActivityDto.TypeDuibaQuestionAnswer == oa.getType()||
        // OperatingActivityDto.TypeDuibaQuizz == oa.getType())){
        // return Boolean.valueOf(false);
        // }
        // 2017-03-07暂时只推荐活动工具，等其他类型活动加入cookie统计队列再走上面的逻辑
        if (oa.getDeleted()) {
            return Boolean.FALSE;
        }
        if (!OperatingActivityDto.hdToolTypeSet.contains(oa.getType())) {
            return false;
        }
        if (activityField == null) {
            return Boolean.FALSE;
        }
        // 过滤自有
        if (oa.getActivityId() == null) {
            return Boolean.FALSE;
        }
        //过滤商业活动
        if(activityField.isBussinessActivity(oa.getActivityId())){
            return Boolean.FALSE;
        }

        // 过滤当前活动
        if (recommendParams.getOperatingActivityId() != null
            && oa.getId().equals(recommendParams.getOperatingActivityId())) {
            return Boolean.FALSE;
        }
        // 过滤活动未开启
        if (!Objects.equal(oa.getStatus(), OperatingActivityDto.StatusIntOpen)) {
            return Boolean.FALSE;
        }
        //过滤活动本身关闭的
        if(!activityField.isOpen(oa.getActivityId())){
            return Boolean.FALSE;
        }
        // 过滤已经参加的活动
        if (!filterJoin(oa, recommendParams)) {
            return Boolean.FALSE;
        }

        String img = activityField.getRecommendImage(oa.getActivityId());

        // 请求是活动类型，则过滤未配置推荐位图片
        if (RecommendParams.REQUEST_TYPE_ACTIVITY.equals(recommendParams.getRequestType())
            && StringUtils.isBlank(img)) {
            return Boolean.FALSE;
        }
        
        // 请求指定上下布局,过滤未配置推荐位图片
        if (recommendParams.getLayout()!=null&&1==recommendParams.getLayout().intValue()
            && StringUtils.isBlank(img)) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }


    /*
     * 目前只有活动工具做了将用户参与的活动id放入cookie中。答题、测试题还没有放入，如果web层将答题、测试题id传入进来 这里还是支持验证 过滤已经参加过的活动
     */
    private Boolean filterJoin(OperatingActivityDto oa, RecommendParams recommendParams) {
        // 只有支持这几种活动 活动工具、兑吧答题、测试题
        boolean flag = OperatingActivityDto.hdToolTypeSet.contains(oa.getType())
                       || OperatingActivityDto.TypeDuibaQuestionAnswer == oa.getType()
                       || OperatingActivityDto.TypeDuibaQuizz == oa.getType();

        if (flag) {
            if (StringUtils.isBlank(recommendParams.getActivityIds())) {
                return Boolean.valueOf(true);
            }
            List<String> activityIds = Arrays.asList(recommendParams.getActivityIds().split(","));
            // 如果存在，则是当前用户已经参加过这个活动，则不推荐
            if (activityIds.contains(oa.getId().toString())) {
                return Boolean.valueOf(false);
            }
        }
        return true;
    }

    private class ConvertDto implements Function<List<OperatingActivityDto>, List<RecommendDto>> {

        private RecommendParams recommendParams;
        private DuibaShareDto   appExtra;
        private Map<Integer, ActivityField> activityFieldMap;
        private DomainConfigDto systemDomain;

        public void setSystemDomain(DomainConfigDto systemDomain) {
            this.systemDomain = systemDomain;
        }

        public void setActivityFieldMap(Map<Integer, ActivityField> activityFieldMap) {
            this.activityFieldMap = activityFieldMap;
        }

        public void setAppExtra(DuibaShareDto appExtra) {
            this.appExtra = appExtra;
        }

        public void setRecommendParams(RecommendParams recommendParams) {
            this.recommendParams = recommendParams;
        }

        @Override
        public List<RecommendDto> apply(List<OperatingActivityDto> oas) {
            List<RecommendDto> result = Lists.newArrayList();
            for (OperatingActivityDto oa : oas) {

                if(hiddenForDeveloper(oa)){
                    continue;
                }
                // 过滤不需要推荐的活动
                ActivityField activityField = activityFieldMap.get(oa.getType());
                if (activityField==null||!filterActivity(oa, recommendParams, activityField)) {
                    continue;
                }
                RecommendDto rd = new RecommendDto();
                rd.setOperatingActivityId(oa.getId());
                rd.setTitle(oa.getTitle());
                // 拼接分享地址
                String activityDomain=systemDomain.getActivityDomain();
                rd.setUrl(getUrl(oa,activityDomain,activityField));
                // 如果是兑换项列表下的推荐位，取缩略图
                if (isSmallImage(recommendParams)) {
                    // 如果app本身缩略图不为空
                    if (StringUtils.isNotBlank(oa.getSmallImage())) {
                        rd.setRecommendImage(oa.getSmallImage());
                        // 取兑吧活动本身的缩略图
                    } else {
                        rd.setRecommendImage(activityField.getSmallImage(oa.getActivityId()));
                    }
                    // 活动则取推荐位图片
                } else {
                    rd.setRecommendImage(activityField.getRecommendImage(oa.getActivityId()));
                }
                rd.setBannerNewImage(oa.getBannerImgNew());
                rd.setBannerImage(activityField.getBannerImage(oa.getActivityId()));
                rd.setCredits(oa.getCredits());
                //需要增加兑吧活动id  2018-02-03
                rd.setDuibaActivityId(oa.getActivityId());
                result.add(rd);
                // 已经取到要展示的个数，则返回
                if (result.size() >= recommendParams.getRecommendShowNum()) {
                    break;
                }
            }
            return result;
        }

        private boolean hiddenForDeveloper(OperatingActivityDto oa){
            if(OperatingActivityDto.isHdTool(oa.getType()) && oa.getActivityId() != null){
                DuibaHdtoolDto duibaHdtool = duibaHdtoolService.find(oa.getActivityId());
                if(duibaHdtool == null || Boolean.TRUE.equals(duibaHdtool.getHiddenForDeveloper())){
                    return true;
                }
            }
            return false;
        }

        private String getUrl(OperatingActivityDto oa,String activityDomain,ActivityField activityField){
            if(OperatingActivityDto.TypeActivityAccessQuestionAnswer==oa.getType()){
                return ShareLandUrlUtils.getShareLandUrl(appExtra,
                        activityDomain+"/question/index?id=" + oa.getId() + "&dbnewopen",
                        oa.getId(),
                        activityDomain
                                + "/activityShare/getActivityShareInfo?appId="
                                + oa.getAppId());
            }else{
                return ShareLandUrlUtils.getShareLandUrl(appExtra,
                        activityField.getUrl(oa.getId(),oa.getAppId(),systemDomain),
                        oa.getId(),
                        activityDomain
                                + "/activityShare/getActivityShareInfo?appId="
                                + oa.getAppId());
            }
        }
        
        /**
         * 是否需要缩略图
         * @param recommendParams
         * @return
         */
        private boolean isSmallImage(RecommendParams recommendParams){
            if(recommendParams.getLayout()==null&&RecommendParams.REQUEST_TYPE_ITEM.equals(recommendParams.getRequestType())){
                return true;
            }
            if(recommendParams.getLayout()!=null&&recommendParams.getLayout()==2){
                return true;
            }
            return false;
        }


    }

}
