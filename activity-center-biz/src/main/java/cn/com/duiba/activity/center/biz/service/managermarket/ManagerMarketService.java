package cn.com.duiba.activity.center.biz.service.managermarket;

import cn.com.duiba.activity.center.api.dto.managermarket.*;
import cn.com.duiba.activity.center.api.params.ManagerMarketInviteRecordParam;
import cn.com.duiba.activity.center.biz.entity.managermarket.ManagerMarketInviteRecordEntity;

import java.util.Date;
import java.util.List;

/**
 * @Author: lufeng
 * @Description:
 * @Date: Created in 2020/8/10
 */
public interface ManagerMarketService {

    /**
     * 通过主键id获取（cms管理后台使用）
     * @param id
     * @return
     */
    ManagerMarketBackendConfigDto getBackendConfig(Long id);

    /**
     * 保存活动
     * @param dto
     * @return
     */
    Long saveConfig(ManagerMarketBackendConfigDto dto);

    /**
     * 批量保存员工
     * @param list
     * @return
     */
    boolean batchSaveStaff(List<ManagerMarketStaffDto> list);

    /**
     * 删除员工
     * @param configId 活动主键id
     * @return
     */
    boolean deleteStaff(Long appId, Long configId);

    /**
     * 批量保存机构
     * @param list
     * @return
     */
    boolean batchSaveOrgan(List<ManagerMarketOrganDto> list);

    /**
     * 删除机构
     * @param configId 活动主键id
     * @return
     */
    boolean deleteOrgan(Long appId, Long configId);

    /**
     * 更改置顶状态
     * @param id
     * @param toppingSwitch
     * @param toppingTime
     * @return
     */
    boolean updateTopping(Long id, boolean toppingSwitch, Date toppingTime);

    /**
     * 回填configId到员工信息/机构信息中
     * @param appId
     * @param configId
     * @return
     */
    boolean fillConfigIdByAppId(Long appId, Long configId);

    /**
     * @Description:插入邀请记录
     * @return {@link int}
     */
    int insertInviteRecord(ManagerMarketInviteRecordDTO dto);
    /**
     * @Description:查询邀请记录
     * @param param :
     */
    List<ManagerMarketInviteRecordDTO> selectInviteRecordListByParam(ManagerMarketInviteRecordParam param);
    /**
     * 通过主键id获取活动信息
     * @param id
     * @return
     */
    ManagerMarketConfigDto getConfigById(Long id);

    /**
     * 通过活动id获取任务列表
     * @param configId
     * @return
     */
    List<ManagerMarketTaskDto> listTaskByConfigId(Long configId);

    /**
     * 通过活动配置id获取所有机构
     * @param configId
     * @return
     */
    List<ManagerMarketOrganDto> getAllOrganByConfigId(Long configId);


    /**
     * 通过活动配置id获取所有员工
     * @param configId
     * @return
     */
    List<ManagerMarketStaffDto> getAllStaffByConfigId(Long configId);


    /**
     * 通过配置id和编号查询 员工所有信息（包括所在机构等）
     * @param configId
     * @param number
     * @return
     */
    ManagerMarketStaffAllDto getStaffAllByConfigIdAndNumber(Long configId, String number);

    /**
     * 通过配置id和手机号查询 员工所有信息（包括所在机构等）
     * @param configId
     * @param phone
     * @return
     */
    ManagerMarketStaffAllDto getStaffAllByConfigIdAndPhone(Long configId, String phone);

    /**
     * 通过配置id和手机号查询
     * @param configId
     * @param phone
     * @return
     */
    ManagerMarketStaffDto selectByConfigIdAndPhone(Long configId, String phone);

    /**
     * 通过配置id和机构编号查询
     * @param configId
     * @param number
     * @return
     */
    ManagerMarketOrganDto selectByConfigIdAndNumber(Long configId, String number);

    /**
     * 录入员工附带信息
     * @param dto
     * @return
     */
    boolean enterStaff(ManagerMarketStaffExtDto dto);

    /**
     * 通过主键id获取员工额外信息
     * @param id
     * @return
     */
    ManagerMarketStaffExtDto getStaffExtById(Long id);

    /**
     * 通过用户id获取员工额外信息
     * @param configId
     * @param userId
     * @return
     */
    ManagerMarketStaffExtDto getStaffExtByUserId(Long configId, Long userId);

    /**
     * 查询用户关联表信息
     * @param configId
     * @param userId
     * @return
     */
    ManagerMarketStaffUserRelDto getUserRelByUserId(Long configId, Long userId);

    /**
     * 通过活动id和员工编号批量查询
     * @param configId
     * @param staffNumbers
     * @return
     */
    List<ManagerMarketStaffExtDto> listStaffExtByConfigIdAndStaffNumbers(Long configId, List<String> staffNumbers);

    /**
     * 通过配置主键id和员工编号查询
     * @param configId
     * @param staffNumber
     * @return
     */
    ManagerMarketStaffExtDto getStaffExtByConfigIdAndStaffNumber(Long configId, String staffNumber);

    /**
     * 通过配置id和员工编号查询
     * @param configId
     * @param staffNumber
     * @return
     */
    List<ManagerMarketStaffScoreDetailDto> listByConfigIdAndStaffNumber(Long configId, String staffNumber, Integer pageSize, Integer pageNo);

    /**
     * 通过配置id和员工编号查询总分
     * @param configId
     * @param staffNumber
     * @return
     */
    int getTotalScoreByConfigIdAndStaffNumber(Long configId, String staffNumber);

    /**
     * 通过配置id,任务标示和员工编号查询
     * @param configId
     * @param identification
     * @param staffNumber
     * @return
     */
    int getTotalScoreByConfigIdAndIdentificationAndStaffNumber(Long configId, String identification, String staffNumber);

    /**
     * 保存用户分数记录
     * @param dto
     * @return
     */
    Long saveStaffScoreDetail(ManagerMarketStaffScoreDetailDto dto);

    /**
     * 创建排行榜
     * @param dto
     * @return
     */
    Long insert(ManagerMarketRankDto dto);

    /**
     * 通过活动id和员工编号查询排行榜信息（员工排行榜）
     * @param configId
     * @param staffNumber
     * @return
     */
    List<ManagerMarketRankDto> selectByConfigIdAndStaffNumber(Long configId, String staffNumber);

    /**
     * 通过活动id和机构编号查询排行榜信息（机构排行榜）
     * @param configId
     * @param organNumber
     * @return
     */
    List<ManagerMarketRankDto> selectByConfigIdAndOrganNumber(Long configId, String organNumber);

    /**
     * 修改排行榜数据
     * @param id
     * @param incrScore 添加的分数
     * @param opType 操作类型。1：转发微信好友，2：转发朋友圈，3：活动被打开，4：活动绑定 StaffOpTypeEnum
     * @return
     */
    boolean incrTotalScoreByOpType(Long id, int incrScore, int opType);

    /**
     * 获取排行榜列表
     * @param rankTime 排行榜时间
     * @param rankLatitudeType 排行榜纬度 个人/机构
     * @param pageSize
     * @param pageNo
     * @return
     */
    List<ManagerMarketRankDto> listRankByLatitudeType(Long configId, String rankTime, Integer rankLatitudeType, Integer pageSize, Integer pageNo);

    /**
     * 获取比这分数高的排行榜个数
     * @param rankTime 排行榜期次时间
     * @param totalScore 分数
     * @param rankLatitudeType 排行榜纬度类型。1：个人，2：机构
     * @return
     */
    int getCountByTotalScoreAndDate(Long configId, String rankTime, Long totalScore, Date date, int rankLatitudeType);


    /**
     * 通过userId获取staff-ext表中的数据（通过userRel表）
     * @param configId
     * @param userId
     * @return
     */
    ManagerMarketStaffExtDto getStaffExtByUserIdFromUserRel(Long configId, Long userId);


    /**
     *
     * @param configId
     * @param staffNumber
     * @return
     */
    List<ManagerMarketStaffUserRelDto> findByStaffNumber(Long configId, String staffNumber);

    /**
     * 创建用户关系表
     * @param entity
     * @return
     */
    int insertUserRel(ManagerMarketStaffUserRelDto entity);


}
