package cn.com.duiba.activity.center.biz.service.app_survey.impl;

import cn.com.duiba.activity.center.api.dto.app_survey.AppSurveyLibraryDto;
import cn.com.duiba.activity.center.api.params.app_survey.SurveyLibraryPageParam;
import cn.com.duiba.activity.center.biz.dao.app_survey.AppSurveyLibraryEntityDao;
import cn.com.duiba.activity.center.biz.entity.app_survey.AppSurveyLibraryEntity;
import cn.com.duiba.activity.center.biz.service.app_survey.AppSurveyLibraryService;
import cn.com.duiba.api.bo.page.Page;
import cn.com.duiba.wolf.utils.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class AppSurveyLibraryServiceImpl implements AppSurveyLibraryService {

    @Resource
    private AppSurveyLibraryEntityDao appSurveyLibraryEntityDao;
    @Override
    public Long insert(AppSurveyLibraryDto dto) {
        return appSurveyLibraryEntityDao.insert(BeanUtils.copy(dto, AppSurveyLibraryEntity.class));
    }

    @Override
    public int delete(Long id) {
        return appSurveyLibraryEntityDao.delete(id);
    }

    @Override
    public int update(AppSurveyLibraryDto dto) {
        return appSurveyLibraryEntityDao.update(BeanUtils.copy(dto, AppSurveyLibraryEntity.class));
    }

    @Override
    public AppSurveyLibraryDto load(Long id) {
        return BeanUtils.copy(appSurveyLibraryEntityDao.load(id), AppSurveyLibraryDto.class);
    }

    @Override
    public List<AppSurveyLibraryDto> selectByLibraryTitle(Long appId, String title) {
        return BeanUtils.copyList(appSurveyLibraryEntityDao.selectByLibraryTitle(appId, title), AppSurveyLibraryDto.class);
    }

    @Override
    public Page<AppSurveyLibraryDto> pageList(SurveyLibraryPageParam pageParam) {
        Page<AppSurveyLibraryDto> page = new Page<>();
        if (Objects.isNull(pageParam)) {
            return page;
        }
        int count = appSurveyLibraryEntityDao.pageListCount(pageParam);
        page.setTotalCount(count);
        page.setPageSize(pageParam.getPageSize());
        page.setPageNo(pageParam.getPageNo());
        if (count < 1) {
            return page;
        }
        List<AppSurveyLibraryEntity> entities = appSurveyLibraryEntityDao.pageList(pageParam);
        page.setList(BeanUtils.copyList(entities, AppSurveyLibraryDto.class));
        int totalPages = count % pageParam.getPageSize() > 0 ? count / pageParam.getPageSize() + 1 : count / pageParam.getPageSize();
        page.setTotalPages(totalPages);
        return page;
    }

    @Override
    public int pageListCount(SurveyLibraryPageParam pageParam) {
        return appSurveyLibraryEntityDao.pageListCount(pageParam);
    }
}
