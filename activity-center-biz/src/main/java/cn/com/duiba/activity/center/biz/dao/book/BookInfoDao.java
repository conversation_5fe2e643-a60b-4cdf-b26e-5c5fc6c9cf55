package cn.com.duiba.activity.center.biz.dao.book;

import cn.com.duiba.activity.center.biz.entity.book.BookInfoEntity;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/06/14
 */

public interface BookInfoDao {

    int insertBatch(List<BookInfoEntity> books);

    int countByMap(Map<String, Object> map);

    List<BookInfoEntity> listByMap(Map<String, Object> map);

    List<BookInfoEntity> listPrize(Integer type);

    int updateBatch(List<BookInfoEntity> books);

    int update(BookInfoEntity book);

    BookInfoEntity get(Long bookId);

    /**
     * 根据数据类型查询数据id
     * @param bookType
     * @return
     */
    List<Long> findBookIdsByBookType(Integer bookType);

    /**
     * 根据书籍主键id删除书旗书籍
     *
     * @param ids
     * @return
     */
    int deleteByIds(List<Long> ids);

}
