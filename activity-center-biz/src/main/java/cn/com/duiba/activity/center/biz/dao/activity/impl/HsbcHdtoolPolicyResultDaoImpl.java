package cn.com.duiba.activity.center.biz.dao.activity.impl;

import cn.com.duiba.activity.center.biz.dao.ActivityBaseDao;
import cn.com.duiba.activity.center.biz.dao.DatabaseSchema;
import cn.com.duiba.activity.center.biz.dao.activity.HsbcHdtoolPolicyResultDao;
import cn.com.duiba.activity.center.biz.entity.hsbc.HsbcHdtoolPolicyResultEntity;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: Lu<PERSON>ui
 * @date: 2023/11/12 15:19
 * @description:
 */
@Repository
public class HsbcHdtoolPolicyResultDaoImpl extends ActivityBaseDao implements HsbcHdtoolPolicyResultDao {


    @Override
    protected DatabaseSchema chooseSchema() {
        return DatabaseSchema.ACT_RECORD;
    }


    @Override
    public List<HsbcHdtoolPolicyResultEntity> findByActAndUserId(Long operatingActivityId, Long consumerId) {
        Map<String, Object> param = new HashMap<>();
        param.put("operatingActivityId", operatingActivityId);
        param.put("consumerId", consumerId);

        return selectList("findByActAndUserId", param);
    }

    @Override
    public int batchInsert(List<HsbcHdtoolPolicyResultEntity> list) {
        return insert("batchInsert",list);
    }
}
