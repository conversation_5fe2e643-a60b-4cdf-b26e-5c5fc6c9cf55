package cn.com.duiba.activity.center.biz.service.bet.impl;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.bet.BetActGroupDto;
import cn.com.duiba.activity.center.api.enums.ActivityCenterErrorEnum;
import cn.com.duiba.activity.center.api.enums.AttributionTypeEnum;
import cn.com.duiba.activity.center.api.enums.BetActivityBonusTypeNum;
import cn.com.duiba.activity.center.api.tool.Page;
import cn.com.duiba.activity.center.biz.dao.bet.AppGroupRelationDao;
import cn.com.duiba.activity.center.biz.dao.bet.BetActGroupDao;
import cn.com.duiba.activity.center.biz.dao.bet.BetGroupRelationDao;
import cn.com.duiba.activity.center.biz.entity.activity.OperatingActivityEntity;
import cn.com.duiba.activity.center.biz.entity.bet.BetActGroupEntity;
import cn.com.duiba.activity.center.biz.service.activity.OperatingActivityService;
import cn.com.duiba.activity.center.biz.service.bet.BetActGroupService;
import cn.com.duiba.activity.common.center.api.dto.bigtext.BigTextDto;
import cn.com.duiba.activity.common.center.api.enums.BigTextTypeEnum;
import cn.com.duiba.activity.common.center.api.remoteservice.bigtext.RemoteBigTextService;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duiba.wolf.utils.SwitchUtils;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018/04/28
 */
@Service
public class BetActGroupServiceImpl implements BetActGroupService {
    @Autowired
    private BetActGroupDao betActGroupDao;
    @Autowired
    private AppGroupRelationDao appGroupRelationDao;
    @Autowired
    private BetGroupRelationDao betGroupRelationDao;

    @Autowired
    private OperatingActivityService operatingActivityService;

    private Trans2Dto trans2Dto = new Trans2Dto();

    private Trans2Entity trans2Entity = new Trans2Entity();

    @Autowired
    private RemoteBigTextService remoteBigTextService;


    @Override
    public Long insert(BetActGroupDto betActGroupDto) throws BizException {
        if (betActGroupDto == null
                || StringUtils.isBlank(betActGroupDto.getTitle())) {
            throw new BizException(ActivityCenterErrorEnum.CODE_2004001001.getMsg())
                    .withCode(ActivityCenterErrorEnum.CODE_2004001001.getCode());
        }
        if (betActGroupDto.getTitle().length() > 25) {
            throw new BizException(ActivityCenterErrorEnum.CODE_2004001006.getMsg())
                    .withCode(ActivityCenterErrorEnum.CODE_2004001006.getCode());
        }

        if (betActGroupDto.getAttributionType() == null) {
            betActGroupDto.setAttributionType(AttributionTypeEnum.DUIBA.getCode());
        }
        BetActGroupEntity betActGroupEntity = BeanUtils.copy(betActGroupDto, BetActGroupEntity.class);
        if (betActGroupDto.getAttributionType() != null && AttributionTypeEnum.PK_H5.getCode().equals(betActGroupDto.getAttributionType())) {
            betActGroupEntity.setStyleConfId(updateOrInsertStyleConf(betActGroupDto, null));
        }
        betActGroupDto.setId(betActGroupDao.insert(betActGroupEntity));
        betActGroupEntity.setStyleConfId(updateOrInsertStyleConf(betActGroupDto, null));
        betActGroupDao.update(betActGroupEntity);

        return  betActGroupDto.getId();
    }

    @Override
    public Integer update(Long id, String title) throws BizException {
        if (StringUtils.isBlank(title)) {
            throw new BizException(ActivityCenterErrorEnum.CODE_2004001001.getMsg())
                    .withCode(ActivityCenterErrorEnum.CODE_2004001001.getCode());
        }
        if (title.length() > 25) {
            throw new BizException(ActivityCenterErrorEnum.CODE_2004001006.getMsg())
                    .withCode(ActivityCenterErrorEnum.CODE_2004001006.getCode());
        }
        BetActGroupEntity betActGroupEntity = new BetActGroupEntity();
        betActGroupEntity.setId(id);
        betActGroupEntity.setTitle(title);
        return betActGroupDao.update(betActGroupEntity);
    }

    @Override
    public Integer updateAllField(BetActGroupDto betActGroupDto) {
        BetActGroupEntity betActGroupEntity = trans2Entity.apply(betActGroupDto);

        if (betActGroupDto.getAttributionType() != null && AttributionTypeEnum.PK_H5.getCode().equals(betActGroupDto.getAttributionType())) {
            BetActGroupEntity old = betActGroupDao.findById(betActGroupDto.getId());
            betActGroupEntity.setStyleConfId(updateOrInsertStyleConf(betActGroupDto, old.getStyleConfId()));
        }
        return betActGroupDao.update(betActGroupEntity);
    }

    @Override
    public List<BetActGroupDto> findUnCloseGroup(Long appId, AttributionTypeEnum attributionTypeEnum) {
        return Lists.transform(betActGroupDao.findUnCloseGroup(appId, attributionTypeEnum), trans2Dto);
    }

    /**
     * 查询正在进行中的活动
     * @return 活动
     */
    @Override
    public List<BetActGroupDto> findStartGroup(Long appId, AttributionTypeEnum attributionTypeEnum) {
        return Lists.transform(betActGroupDao.findStartGroup(appId, attributionTypeEnum), trans2Dto);
    }


    @Override
    public Integer deleteByOperationActivityId(Long id) {
        OperatingActivityDto operatingActivityDto = operatingActivityService.find(id);
        if (operatingActivityDto != null && Objects.equals(operatingActivityDto.getType(), OperatingActivityDto.TypePK)) {
            return delete(operatingActivityDto.getActivityId());
        }
        return 0;
    }

    @Override
    public Integer deleteByOperationActivityWithType(Long id, ActivityUniformityTypeEnum activityUniformityTypeEnum) {
        OperatingActivityDto operatingActivityDto = operatingActivityService.find(id);
        if (operatingActivityDto != null && Objects.equals(operatingActivityDto.getType(), activityUniformityTypeEnum.getCode())) {
            return delete(operatingActivityDto.getActivityId());
        }
        return 0;
    }

    @Override
    public Page<BetActGroupDto> list(Long appId, AttributionTypeEnum attributionTypeEnum, Integer pageNo, Integer pageSize) {
        List<BetActGroupEntity> betActGroupEntityList = betActGroupDao.list(appId, attributionTypeEnum, pageNo, pageSize);
        Page<BetActGroupDto> betActGroupDaoPage = new Page<>(pageNo, pageSize);

        betActGroupDaoPage.setTotalCount(betActGroupDao.count(attributionTypeEnum));
        betActGroupDaoPage.setList(BeanUtils.copyList(betActGroupEntityList, BetActGroupDto.class));

        return betActGroupDaoPage;
    }

    @Override
    public Integer delete(Long id) {
        Integer count = betActGroupDao.delete(id);
        if (count == 0) {
            return count;
        }
        //删除app-活动组关系
        appGroupRelationDao.deleteByGroupId(id);
        //删除投注活动-活动组关系
        betGroupRelationDao.deleteByGroupId(id);

        return count;
    }

    @Override
    public BetActGroupDto findById(Long id) {
        return trans2Dto.apply(betActGroupDao.findById(id));
    }

    @Override
    public List<BetActGroupDto> findByIds(List<Long> ids) {
        return Lists.transform(betActGroupDao.findByIds(ids), trans2Dto);
    }


    /**
     * 该接口-不试用PK——H5 活动，会报错！！！返回多条记录
     * @param appId
     * @param attributionTypeEnum
     * @return
     */
    @Deprecated
    @Override
    public BetActGroupDto findByAppIdIdAndType(Long appId, AttributionTypeEnum attributionTypeEnum) {
        return trans2Dto.apply(betActGroupDao.findByAppIdIdAndType(appId, attributionTypeEnum));
    }

    @Override
    public List<BetActGroupDto> getListByAppIdIdAndType(Long appId, AttributionTypeEnum attributionTypeEnum) {
        return Lists.transform(betActGroupDao.getListByAppIdIdAndType(appId,attributionTypeEnum), trans2Dto);
    }

    private class Trans2Dto implements Function<BetActGroupEntity, BetActGroupDto> {

        @Override
        public BetActGroupDto apply(BetActGroupEntity input) {
            if (input == null) {
                return null;
            }
            BetActGroupDto dto = cn.com.duiba.wolf.utils.BeanUtils.copy(input, BetActGroupDto.class);
            dto.setBonusType(input.getBonusType() != null ? BetActivityBonusTypeNum.getBetActivityBonusTypeNum(input.getBonusType()) : null);
            if (input.getAttributionType() != null && AttributionTypeEnum.PK_H5.getCode().equals(input.getAttributionType())) {
                dto.setStyleConf(remoteBigTextService.selectById(input.getStyleConfId()).getContent());
                OperatingActivityDto operatingActivityDto = operatingActivityService.findByAppIdAndActivityIdAndTypeAndDeleted(dto.getAppId(), dto.getId(), ActivityUniformityTypeEnum.PKH5.getCode(), false);
                if (Objects.nonNull(operatingActivityDto)) {
                    dto.setOpenBrush(SwitchUtils.switchIsOpen(operatingActivityDto.getSwitches(), OperatingActivityDto.ANTICHEAT_LIMIT_RULE));
                }
            }
            return dto;
        }
    }

    @Override
    public Long updateOrInsertStyleConf(BetActGroupDto update, Long styleConfId) {
        if (null == update.getStyleConf()) {
            return null;
        }
        if (styleConfId != null) {
            remoteBigTextService.updateById(styleConfId, update.getStyleConf());
            return styleConfId;
        } else {
            BigTextDto styleConf = new BigTextDto();
            styleConf.setRelType(BigTextTypeEnum.PK_H5);
            styleConf.setRelId(update.getId());
            styleConf.setContent(update.getStyleConf());
            return remoteBigTextService.insert(styleConf);
        }
    }




    private class Trans2Entity implements Function<BetActGroupDto, BetActGroupEntity> {

        @Override
        public BetActGroupEntity apply(BetActGroupDto input) {
            if (input == null) {
                return null;
            }
            BetActGroupEntity entity = cn.com.duiba.wolf.utils.BeanUtils.copy(input, BetActGroupEntity.class);
            entity.setBonusType(input.getBonusType() != null ? input.getBonusType().getCode() : null);
            return entity;
        }
    }


}
