package cn.com.duiba.activity.center.biz.service.hsbc.impl;

import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolOrdersDto;
import cn.com.duiba.activity.center.api.dto.hsbc.HsbcHdtoolTaskPrizeDto;
import cn.com.duiba.activity.center.biz.bo.AppItemBo;
import cn.com.duiba.activity.center.biz.dao.activity.HsbcHdtoolTaskPrizeDao;
import cn.com.duiba.activity.center.biz.entity.hsbc.HsbcHdtoolTaskPrizeEntity;
import cn.com.duiba.activity.center.biz.service.hsbc.HsbcHdtoolTaskPrizeService;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemKeyService;
import cn.com.duiba.wolf.utils.BeanUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @author: LuHui
 * @date: 2023/11/6 11:46
 * @description:
 */
@Service
public class HsbcHdtoolTaskPrizeServiceImpl implements HsbcHdtoolTaskPrizeService {
    @Resource
    private HsbcHdtoolTaskPrizeDao hsbcHdtoolTaskPrizeDao;
    @Resource
    private AppItemBo appItemBo;
    @Resource
    private RemoteItemKeyService remoteItemKeyService;


    @Override
    public int deleteByActId(Long operatingActivityId) {
        if (Objects.isNull(operatingActivityId)) {
            return 0;
        }
        return hsbcHdtoolTaskPrizeDao.deleteByActId(operatingActivityId);
    }

    @Override
    public int batchInsert(List<HsbcHdtoolTaskPrizeDto> prizeList) throws BizException {
        if (CollectionUtils.isEmpty(prizeList)) {
            return 0;
        }
        for (HsbcHdtoolTaskPrizeDto prizeDto : prizeList) {
            if(Objects.equals("virtual", prizeDto.getPrizeType())){
                prizeDto.setFacePrice(StringUtils.isBlank(prizeDto.getFacePrice()) ? "0" : prizeDto.getFacePrice());
            }else{
                prizeDto.setFacePrice(getVaue(prizeDto.getFacePrice()));
            }
            prizeDto.setAppItemId(getAppItemId(prizeDto));
        }
        return hsbcHdtoolTaskPrizeDao.batchInsert(BeanUtils.copyList(prizeList, HsbcHdtoolTaskPrizeEntity.class));
    }

    @Override
    public List<HsbcHdtoolTaskPrizeDto> findByActAndTaskId(Long operatingActivityId, Long taskId) {
        if (Objects.isNull(operatingActivityId) || Objects.isNull(taskId)) {
            return Lists.newArrayList();
        }
        return BeanUtils.copyList(hsbcHdtoolTaskPrizeDao.findByActAndTaskId(operatingActivityId, taskId), HsbcHdtoolTaskPrizeDto.class);
    }

    @Override
    public List<HsbcHdtoolTaskPrizeDto> findByActId(Long operatingActivityId) {
        if (Objects.isNull(operatingActivityId)) {
            return Lists.newArrayList();
        }
        return BeanUtils.copyList(hsbcHdtoolTaskPrizeDao.findByActId(operatingActivityId), HsbcHdtoolTaskPrizeDto.class);
    }

    @Override
    public int decrPrizeStock(Long id) {
        if (Objects.isNull(id)) {
            return 0;
        }
        return hsbcHdtoolTaskPrizeDao.decrPrizeStock(id);
    }

    @Override
    public int incrPrizeStock(Long id) {
        if (Objects.isNull(id)) {
            return 0;
        }
        return hsbcHdtoolTaskPrizeDao.incrPrizeStock(id);
    }

    @Override
    public HsbcHdtoolTaskPrizeDto findOnePrize(Long operatingActivityId, Long taskId, String bizId, Long appItemId) {
        if (Objects.isNull(operatingActivityId) || Objects.isNull(taskId) || StringUtils.isBlank(bizId) || Objects.isNull(appItemId)) {
            return null;
        }
        return BeanUtils.copy(hsbcHdtoolTaskPrizeDao.findOnePrize(operatingActivityId, taskId, bizId, appItemId), HsbcHdtoolTaskPrizeDto.class);
    }

    private String getVaue(String value) {
        if(StringUtils.isBlank(value)) {
            return "0";
        }else {
            BigDecimal tmp = new BigDecimal(value).multiply(new BigDecimal(100));
            return String.valueOf(tmp.intValue());
        }
    }

    private Long getAppItemId(HsbcHdtoolTaskPrizeDto prizeDto) throws BizException {
        Long appItemId = prizeDto.getAppItemId();
        if (prizeDto.getPrizeType().equals(HdtoolOrdersDto.PrizeTypeCollectCard)) {
            return appItemId;
        }
        if (null == prizeDto.getAppItemId()) {
            appItemId = appItemBo.saveAppItemByActivity(prizeDto.getAppId(), prizeDto.getPrizeType(), true).getId();
        }
        if (null == appItemId) {
            throw new BizException("奖项appItemId不能为空");
        }
        ItemKeyDto key = remoteItemKeyService.findItemKey(appItemId, null, prizeDto.getAppId()).getResult();
        if (prizeContainsDuibaForbiden(key)) {
            throw new BizException("奖项不能包含兑吧限制兑换商品");
        }
        if (isAppCannotAddThisPrize(prizeDto.getAppId(), key)) {
            throw new BizException("APP不能添加此奖品");
        }
        return appItemId;
    }

    private boolean prizeContainsDuibaForbiden(ItemKeyDto key) {
        return key.isItemMode() && key.getItem().getLimitCount() != null && key.getItem().getLimitCount() > 0;
    }

    private boolean isAppCannotAddThisPrize(Long appId, ItemKeyDto key) {
        return key.getAppItem() != null && !key.getAppItem().getAppId().equals(appId);
    }


}
