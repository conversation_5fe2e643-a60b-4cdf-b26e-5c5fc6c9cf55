package cn.com.duiba.activity.center.biz.rocketmq.impl;

import cn.com.duiba.activity.center.api.dto.activity_order.ActivityOrderDto;
import cn.com.duiba.activity.center.api.dto.betv2.BetV2RecordDto;
import cn.com.duiba.activity.center.api.enums.ActCenterHBaseKeyFactoryEnum;
import cn.com.duiba.activity.center.biz.dao.betv2.BetRecordV2Dao;
import cn.com.duiba.activity.center.biz.entity.betv2.BetRecordV2Entity;
import cn.com.duiba.activity.center.biz.rocketmq.AbstractRocketConsumerHandler;
import cn.com.duiba.activity.center.biz.rocketmq.RocketMqMessageListener;
import cn.com.duiba.activity.center.biz.service.activity_order.ActivityOrderService;
import cn.com.duiba.activity.common.center.api.msg.consumeraccount.BatchIncreaseBalanceResultMsg;
import cn.com.duiba.activity.common.center.api.msg.consumeraccount.BatchIncreaseBalanceResultParam;
import cn.com.duiba.kvtable.service.api.enums.HbaseKeySpaceEnum;
import cn.com.duiba.kvtable.service.api.params.HbBatchIncreaseParam;
import cn.com.duiba.kvtable.service.api.params.HbConsumerIncrParam;
import cn.com.duiba.kvtable.service.api.remoteservice.hbase.RemoteDuibaKvtableService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/06/30
 */
@Component
public class BetAddBalanceHandler extends AbstractRocketConsumerHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(BetAddBalanceHandler.class);
    @Autowired
    private BetRecordV2Dao betRecordV2Dao;
    @Autowired
    private ActivityOrderService activityOrderService;
    @Autowired
    private RemoteDuibaKvtableService remoteDuibaKvtableService;

    @PostConstruct
    public void init(){
        RocketMqMessageListener.register(this);
    }

    @Override
    public ConsumeConcurrentlyStatus consume(MessageExt messageExt, ConsumeConcurrentlyContext context) {
        BatchIncreaseBalanceResultMsg consumeAccount = null;
        try {
            consumeAccount = BatchIncreaseBalanceResultMsg.decode(messageExt.getBody());
        } catch (Exception e) {
            LOGGER.error("消息解析失败", e);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        if (consumeAccount == null) {
            LOGGER.error("消息返回为空");
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        if(CollectionUtils.isEmpty(consumeAccount.getResult())){
            LOGGER.error("消息返回为空");
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        List<BatchIncreaseBalanceResultParam> result = consumeAccount.getResult();

        if (CollectionUtils.isEmpty(result)) {
            LOGGER.error("消息结果参数为空");
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        try {
            activityOrderService.batchUpdate(convert2ActivityOrder(result));
        } catch (Exception e) {
            LOGGER.error("更新订单状态失败", e);
            LOGGER.info("失败的订单{}", result.stream().map(BatchIncreaseBalanceResultParam::getBizId).toArray());
        }

        try {
            betRecordV2Dao.batchUpdateExchangeStatus(convert2BetRecord(result));
        } catch (Exception e) {
            LOGGER.error("更新中间表状态失败", e);
            LOGGER.info("失败的订单号{}", result.stream().map(BatchIncreaseBalanceResultParam::getBizId).toArray());
        }

        try {
            //批量更新累计弹窗奖励
            int opCount = remoteDuibaKvtableService.batchIncrease(buildHbaseParam(consumeAccount, result));
            if (opCount == -1) {
                throw new Exception("更新Hbase参数错误");
            }
        } catch (Exception e) {
            LOGGER.error("更新Hbase累计获奖金额失败", e);
            LOGGER.info("失败的Record记录{}", consumeAccount.getTransfer().get("recordList"));

        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    @Override
    public String getListenTopic() {
        return rocketMQTopicConstant.getBetAddBalanceCallback();
    }

    private HbBatchIncreaseParam buildHbaseParam(BatchIncreaseBalanceResultMsg msg, List<BatchIncreaseBalanceResultParam> list) {
        Map<String, String> params = msg.getTransfer();
        List<BetV2RecordDto> recordList = JSONObject.parseArray(params.get("recordList"), BetV2RecordDto.class);
        HbBatchIncreaseParam batchIncreaseParam = new HbBatchIncreaseParam();
        batchIncreaseParam.setKeySpaceEnum(HbaseKeySpaceEnum.K03);
        batchIncreaseParam.setVkey(ActCenterHBaseKeyFactoryEnum.K002.toString());


        Map<String,BatchIncreaseBalanceResultParam> orderNumMap = new HashMap<>(list.size());

        //根据bizId的去重
        for (BatchIncreaseBalanceResultParam param : list) {
            orderNumMap.put(param.getBizId(), param);
        }

        List<HbConsumerIncrParam> consumerIncrParams = new ArrayList<>();
        recordList.stream().forEach(o -> {
            BatchIncreaseBalanceResultParam result = orderNumMap.get(o.getOrderNum());
            if (null == result || !result.isSuccess()) {
                LOGGER.info("钱包回调返回错误 {} 不累加用户中奖金额 押注订单信息 {}", JSON.toJSONString(result), JSON.toJSONString(o));
                return;
            }
            HbConsumerIncrParam incrParam = new HbConsumerIncrParam();
            incrParam.setConsumerId(o.getConsumerId());
            incrParam.setIncrCount(o.getBonus());
            consumerIncrParams.add(incrParam);
        });
        batchIncreaseParam.setConsumerIncrParams(consumerIncrParams);

        return batchIncreaseParam;
    }

    private List<BetRecordV2Entity> convert2BetRecord(List<BatchIncreaseBalanceResultParam> list) {
        return list.stream().map(batchIncreaseBalanceResultParam -> {
            BetRecordV2Entity betRecordV2Entity = new BetRecordV2Entity();

            betRecordV2Entity.setOrderNum(batchIncreaseBalanceResultParam.getBizId());
            betRecordV2Entity.setExchangeStatus(
                    batchIncreaseBalanceResultParam.isSuccess()
                            ? ActivityOrderDto.ExchangeSuccess
                            : ActivityOrderDto.ExchangeFail);

            return betRecordV2Entity;
        }).collect(Collectors.toList());
    }

    private List<ActivityOrderDto> convert2ActivityOrder(List<BatchIncreaseBalanceResultParam> list) {
        return list.stream().map(batchIncreaseBalanceResultParam -> {
            ActivityOrderDto activityOrderDto = new ActivityOrderDto();

            activityOrderDto.setOrderNum(batchIncreaseBalanceResultParam.getBizId());
            if (batchIncreaseBalanceResultParam.isSuccess()) {
                activityOrderDto.setExchangeStatus(ActivityOrderDto.ExchangeSuccess);
            } else {
                activityOrderDto.setExchangeStatus(ActivityOrderDto.ExchangeFail);
                activityOrderDto.setError4consumer(batchIncreaseBalanceResultParam.getErrorMsg());
            }

            return activityOrderDto;
        }).collect(Collectors.toList());
    }
}
