package cn.com.duiba.activity.center.biz.dao.hdtool.impl;

import cn.com.duiba.activity.center.api.dto.advertising.DuibaAdActivityDto;
import cn.com.duiba.activity.center.api.dto.hdtool.DuibaHdtoolDto;
import cn.com.duiba.activity.center.biz.dao.ActivityBaseDao;
import cn.com.duiba.activity.center.biz.dao.DatabaseSchema;
import cn.com.duiba.activity.center.biz.dao.hdtool.DuibaHdtoolDao;
import cn.com.duiba.activity.center.biz.entity.ActivityExtraInfoEntity;
import cn.com.duiba.activity.center.biz.entity.AddActivityEntity;
import cn.com.duiba.activity.center.biz.entity.hdtool.DuibaHdtoolEntity;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 兑吧活动工具业务DAO
 */
@Repository
public class DuibaHdtoolDaoImpl extends ActivityBaseDao implements DuibaHdtoolDao {

	@Override
	protected DatabaseSchema chooseSchema() {
		 return DatabaseSchema.HDTOOL_CONF;
	}
	/**
	 * 查询一天内，需要定时下架的转盘抽奖
	 * @return
	 */
	@Override
	public List<DuibaHdtoolEntity> findAutoOff(){
		Calendar cal1 = Calendar.getInstance();
		cal1.setTime(new Date());
		cal1.add(Calendar.DATE, -1);
		Date startTime = cal1.getTime();
		
		Map<String, Object> paramMap = Maps.newHashMap();
		paramMap.put("startTime", startTime);
		paramMap.put("endTime", new Date());
		return selectList("findAutoOff", paramMap);	
	}

	/**
	 * 根据ID查询
	 */
	@Override
	public DuibaHdtoolEntity find(Long id) {
		Map<String, Object> paramMap = Maps.newHashMap();
		paramMap.put("id", id);
		return selectOne("find", paramMap);
	}

	/**
	 * 活动工具分页列表
	 */
	@Override
	public List<DuibaHdtoolEntity> findDuibaHdToolsList(Map<String, Object> paramMap) {
	    //活动工具新增 商业活动类型字段，为兼容老的，没传参数 默认查询普通活动
	    Object type = paramMap.get("activityActionType");
	    if(type==null){
	        paramMap.put("activityActionType",DuibaAdActivityDto.Activity_Action_common );
	    }
		return selectList("findDuibaHdToolsList",paramMap);
	}

	/**
	 * 统计活动工具条数
	 */
	@Override
	public Integer countDuibaHdToolsList(Map<String, Object> queryMap) {
	    //活动工具新增 商业活动类型字段，为兼容老的，没传参数 默认查询普通活动
        Object type = queryMap.get("activityActionType");
        if(type==null){
            queryMap.put("activityActionType",DuibaAdActivityDto.Activity_Action_common );
        }
		return selectOne("countDuibaHdToolsList",queryMap);
	}

	/**
	 * 根据APP查询活动工具
	 */
	@Override
	public List<AddActivityEntity> findAllDuibaHdTools(Long appId) {
		Map<String, Object> paramMap = Maps.newHashMap();
		paramMap.put("appId", appId);
		paramMap.put("activityActionType",DuibaAdActivityDto.Activity_Action_common);
		return selectList("findAllDuibaHdTools", paramMap);
	}

	/**
	 * 根据ID集合查询活动工具
	 */
	@Override
	public List<DuibaHdtoolEntity> findAllByIds(List<Long> ids) {
		if(CollectionUtils.isEmpty(ids)){
			return Lists.newArrayList();
		}
		Map<String, Object> paramMap = Maps.newHashMap();
		paramMap.put("ids", ids);
		return selectList("findAllByIds", paramMap);
	}

	/**
	 * 根据ID查询额外信息
	 */
	@Override
	public ActivityExtraInfoEntity findExtraInfoById(Long id) {
		Map<String, Object> paramMap = Maps.newHashMap();
		paramMap.put("id", id);
		return selectOne("findExtraInfoById", paramMap);
	}

	/**
	 * @param queryMap
	 * @return
	 */
	@Override
	public Long getCountDuibaHdTool(Map<String, Object> queryMap) {
		return selectOne("getCountDuibaHdTool", queryMap);
	}

	/**
	 * @param queryMap
	 * @return
	 */
	@Override
	public List<DuibaHdtoolEntity> findDuibaToolList(Map<String, Object> queryMap) {
		return selectList("findDuibaToolList", queryMap);
	}

	@Override
	public int updateAutoOffDateNull(Long id){
		Map<String, Object> paramMap = Maps.newHashMap();
		paramMap.put("id", id);
		return update("updateAutoOffDateNull", paramMap);
	}

	/**
	 * 新增兑吧活动工具
	 */
	@Override
	public void insert(DuibaHdtoolEntity duibaHdtoolDto) {
		insert("insert", duibaHdtoolDto);
	}

	/**
	 * 删除兑吧活动
	 */
	@Override
	public int deleteById(Long id) {
		Map<String, Object> paramMap = Maps.newHashMap();
		paramMap.put("id", id);
		return update("deleteById", paramMap);
	}

	/**
	 * 修改兑吧活动工具
	 */
	@Override
	public int update(DuibaHdtoolEntity duibaHdtoolDto) {
		return update("update", duibaHdtoolDto);
	}

	/**
	 * 修改活动工具状态
	 */
	@Override
	public int updateStatus(Long id, Integer status) {
		Map<String, Object> paramMap = Maps.newHashMap();
		paramMap.put("id", id);
		paramMap.put("status", status);
		return update("updateStatus", paramMap);
	}

	@Override
	public int updateActivityCategory(long  duibaHdtoolId,long activityCategoryId){
	    Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("id", duibaHdtoolId);
        paramMap.put("activityCategoryId", activityCategoryId);
	    return update("updateActivityCategory", paramMap);
	}

	/**
     * 根据ID查询标签
     */
	@Override
    public String findTag(Long id) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("id", id);
        return selectOne("findTag", paramMap);
    }

	@Override
	public int updateExtendJson(Long id, String extJson) {
		Map<String, Object> params = new HashMap<>();
		params.put("id", id);
		params.put("extendJson", extJson);
		return update("updateExtendJson", params);
	}

	@Override
	public Integer updateAutoOnDate(Date autoOnDate, Long id) {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("id", id);
		paramMap.put("autoOnDate", autoOnDate);
		return update("updateAutoOnDate", paramMap);
	}

	@Override
	public List<Long> findAutoOnHdTool() {
		return selectList("findAutoOnHdTool");
	}

	@Override
	public int updateAutoOnHdToolStatus(List<Long> ids) {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("list", ids);
		paramMap.put("status", DuibaHdtoolDto.STATUS_OPEN);
		return update("batchUpdateStatus", paramMap);
	}
}