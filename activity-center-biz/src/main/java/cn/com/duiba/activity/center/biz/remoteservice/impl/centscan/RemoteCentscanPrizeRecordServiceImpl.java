package cn.com.duiba.activity.center.biz.remoteservice.impl.centscan;

import cn.com.duiba.activity.center.api.dto.centscan.CentscanPrizeRecordDto;
import cn.com.duiba.activity.center.api.remoteservice.centscan.RemoteCentscanPrizeRecordService;
import cn.com.duiba.activity.center.biz.service.centscan.CentscanPrizeRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 〈功能概述〉<br>
 *
 * @author: xuwei
 * @date: 2020/6/19
 */
@RestController
public class RemoteCentscanPrizeRecordServiceImpl implements RemoteCentscanPrizeRecordService {
    @Autowired
    private CentscanPrizeRecordService centscanPrizeRecordService;

    @Override
    public CentscanPrizeRecordDto find(Long id) {
        return centscanPrizeRecordService.find(id);
    }

    @Override
    public List<CentscanPrizeRecordDto> findByIds(List<Long> ids) {
        return centscanPrizeRecordService.findByIds(ids);
    }

    @Override
    public Long insert(CentscanPrizeRecordDto record) {
        return centscanPrizeRecordService.insert(record);
    }

    @Override
    public void updateOrderId(Long id, Long orderId) {
        centscanPrizeRecordService.updateOrderId(id, orderId);
    }

    @Override
    public void updateStatus(Long id, Integer status) {
        centscanPrizeRecordService.updateStatus(id, status);
    }

    @Override
    public void updateExtraInfo(Long id, String extraInfo) {
        centscanPrizeRecordService.updateExtraInfo(id, extraInfo);
    }

    @Override
    public CentscanPrizeRecordDto findByOrderId(Long orderId) {
        return centscanPrizeRecordService.findByOrderId(orderId);
    }

    @Override
    public List<CentscanPrizeRecordDto> findByMainRecordId(Long mainRecordId) {
        return centscanPrizeRecordService.findByMainRecordId(mainRecordId);
    }
}
