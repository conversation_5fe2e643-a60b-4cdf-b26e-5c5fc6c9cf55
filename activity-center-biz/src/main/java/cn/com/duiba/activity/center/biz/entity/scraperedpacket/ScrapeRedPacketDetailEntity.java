package cn.com.duiba.activity.center.biz.entity.scraperedpacket;

import java.io.Serializable;
import java.util.Date;

/**
 * 微信刮红包用户红包详情
 */
public class ScrapeRedPacketDetailEntity implements Serializable {

    private static final long serialVersionUID = 8836593332727530838L;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * appid
     */
    private Long appId;

    /**
     * 用户id
     */
    private Long consumerId;

    /**
     * 渠道类型
     */
    private Integer fromType;

    /**
     * 当前红包位置
     */
    private Integer redPacketLoc;

    /**
     * 红包刮刮码
     */
    private String redPacketCode;

    /**
     * 已邀请好友数
     */
    private Integer inviteFriends;

    /**
     * 应邀请好友数
     */
    private Integer allFriends;

    /**
     * 红包刮出金额
     */
    private Integer amountReceived;

    /**
     * 领取状态
     */
    private Integer detailStatus;

    /**
     * 翻倍卡
     */
    private String multipleCard;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getConsumerId() {
        return consumerId;
    }

    public void setConsumerId(Long consumerId) {
        this.consumerId = consumerId;
    }

    public Integer getFromType() {
        return fromType;
    }

    public void setFromType(Integer fromType) {
        this.fromType = fromType;
    }

    public Integer getRedPacketLoc() {
        return redPacketLoc;
    }

    public void setRedPacketLoc(Integer redPacketLoc) {
        this.redPacketLoc = redPacketLoc;
    }

    public String getRedPacketCode() {
        return redPacketCode;
    }

    public void setRedPacketCode(String redPacketCode) {
        this.redPacketCode = redPacketCode;
    }

    public Integer getInviteFriends() {
        return inviteFriends;
    }

    public void setInviteFriends(Integer inviteFriends) {
        this.inviteFriends = inviteFriends;
    }

    public Integer getAllFriends() {
        return allFriends;
    }

    public void setAllFriends(Integer allFriends) {
        this.allFriends = allFriends;
    }

    public Integer getAmountReceived() {
        return amountReceived;
    }

    public void setAmountReceived(Integer amountReceived) {
        this.amountReceived = amountReceived;
    }

    public Integer getDetailStatus() {
        return detailStatus;
    }

    public void setDetailStatus(Integer detailStatus) {
        this.detailStatus = detailStatus;
    }

    public String getMultipleCard() {
        return multipleCard;
    }

    public void setMultipleCard(String multipleCard) {
        this.multipleCard = multipleCard;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

}
