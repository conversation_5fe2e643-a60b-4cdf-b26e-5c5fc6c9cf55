package cn.com.duiba.activity.center.biz.dao.understandlevel.impl;

import cn.com.duiba.activity.center.biz.dao.ActivityBaseDao;
import cn.com.duiba.activity.center.biz.dao.DatabaseSchema;
import cn.com.duiba.activity.center.biz.dao.understandlevel.UnderstandLevelPrizeDao;
import cn.com.duiba.activity.center.biz.entity.understandlevel.UnderstandLevelPrizeEntity;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class UnderstandLevelPrizeDaoImpl extends ActivityBaseDao implements UnderstandLevelPrizeDao {

    @Override
    protected DatabaseSchema chooseSchema() {
        return DatabaseSchema.ACT_COM_CONF;
    }

    @Override
    public int deleteByConfigId(Long configId) {
        Map<String, Long> map = Maps.newHashMap();
        map.put("configId", configId);
        return delete("deleteByConfigId", map);
    }

    @Override
    public int deleteNoUse(Long configId, List<Long> prizeIdList) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("configId", configId);
        map.put("prizeIdList", prizeIdList);
        return delete("deleteNoUse", map);
    }

    @Override
    public int batchInsertSelective(List<UnderstandLevelPrizeEntity> entityList) {
        return insert("batchInsertSelective", entityList);
    }

    @Override
    public int updateById(UnderstandLevelPrizeEntity understandLevelPrizeEntity) {
        return update("updateById", understandLevelPrizeEntity);
    }

    @Override
    public List<UnderstandLevelPrizeEntity> listByConfigId(Long configId) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("configId", configId);
        return selectList("listByConfigId", map);
    }

    @Override
    public List<UnderstandLevelPrizeEntity> getShowListByConfigId(Long configId) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("configId", configId);
        return selectList("getShowListByConfigId", configId);
    }

    @Override
    public Boolean casPrizeRemind(Long prizeId, Integer expectStock, Integer newStock) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("id", prizeId);
        map.put("expectStock", expectStock);
        map.put("newStock", newStock);
        return update("casPrizeRemind", map) > 0;
    }

    @Override
    public Boolean decrStockById(Long prizeId) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("id", prizeId);
        return update("decrStockById", map) > 0;
    }

    @Override
    public Boolean incrStockById(Long prizeId) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("id", prizeId);
        return update("incrStockById", map) > 0;
    }
}
