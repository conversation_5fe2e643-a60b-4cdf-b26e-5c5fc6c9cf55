package cn.com.duiba.activity.center.biz.dao.ngame_con;


import cn.com.duiba.activity.center.biz.entity.ngame.NgameOrdersEntity;

import java.util.Date;
import java.util.List;

/**
 * ngame_orders按consumer分表
 */
public interface NgameOrdersConsumerDao {

    NgameOrdersEntity find(Long consumerId, Long gameOrderId);

    Integer findConsumerFreeNumber(Long consumerId, Long operatingActivityId);

    Integer findConsumerFreeNumberByDate(Long consumerId, Long operatingActivityId, Date start, Date end);

    Integer findConsumerLimitNumber(Long consumerId, Long operatingActivityId);

    Integer findConsumerLimitNumberByDate(Long consumerId, Long operatingActivityId, Date start, Date end);

    List<NgameOrdersEntity> findByIds(Long consumerId, List<Long> ids);

    /**
     * 添加活动工具订单
     */
    int insert(NgameOrdersEntity ngameOrdersDto);

    int updateDeveloperBizId(Long consumerId, long id, String bizId);

    int updateMainOrderId(Long consumerId, long id, Long mainOrderId, String mainOrderNum);

    int updateStatusToConsumeSuccess(Long consumerId, Long gameOrderId);

    int updateStatusToSuccess(Long consumerId, Long gameOrderId, Long gameOrderExtraId);

    int updateStatusToFail(Long consumerId, Long gameOrderId, String error4admin, String error4developer, String error4consumer);

    int updateExchangeStatusToWaitOpen(Long consumerId, Long gameOrderId, String gameDataStr);

    int updateExchangeStatusToWaitOpenAndExtraId(Long consumerId, Long gameOrderId, String gameDataStr, Long gameOrderExtraId);

    int updateExchangeStatusToWait(Long consumerId, Long gameOrderId, String gameDataStr, Long itemId, Long appItemId, Long prizeId, String prizeType, String prizeName, String prizeFacePrice, Long couponId);

    int updateExchangeStatusToOverdue(Long gameOrderId, Long consumerId, String error4admin, String error4developer, String error4consumer);

    int updateExchangeStatusToFail(Long consumerId, Long gameOrderId, String error4admin, String error4developer, String error4consumer);

    int doTakePrize(Long consumerId, Long gameOrderId);

    int rollbackTakePrize(Long consumerId, Long gameOrderId);

    int updateManualOpenPrizeExchangeStatusToWait(Long consumerId, Long gameOrderId, Long itemId, Long appItemId, Long prizeId, String prizeType, String prizeName, String prizeFacePrice, Long couponId);

    int updateExchangeStatusToWaitOpenAndExtraIdForLuck(Long consumerId, Long gameOrderId, Long gameOrderExtraId);

    int updateScore(Long consumerId, Long gameOrderId, Long score);

    int doTakeVirtualPrize(Long consumerId, Long id);
}
