package cn.com.duiba.activity.center.biz.service.activity;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityOptionsDto;
import cn.com.duiba.api.bo.activity.SyncMsgData;

import java.util.List;

/**
 * Created by yanse<PERSON> on 16/6/12.
 */
public interface OperatingActivityOptionsService {
    //from HdtoolSimpleDAO

    public OperatingActivityOptionsDto findOptionById(Long id);

    public List<OperatingActivityOptionsDto> findByOperatingActivityId(Long operatingActivityId);

    public Integer countByOperatingActivityId(Long operatingActivityId);

    //from HdtoolTextChangeDAO


    public int decrementOptionRemaining(Long optionId);

    public int incrementOptionRemaining(Long optionId);

    public int updateOptionDeleteStatus(List<Long> oaoList);

    public int updatePrize(OperatingActivityOptionsDto operatingActivityOptionsDto);

    public void insertOption(OperatingActivityOptionsDto operatingActivityOptionsDto);

    public int updateOption(OperatingActivityOptionsDto operatingActivityOptionsDto);

    public OperatingActivityOptionsDto findForupdate(Long id);

    //from OperatingActivityOptionsStatusChangeDAO

    /**
     * 增加奖品数量
     */
    public int addRemainingById(Long id, Integer addRemaining);

    /**
     * 减少奖品数量
     */
    public int subRemainingById(Long id, Integer subRemaining);

    /**
     * 更新库存
     */
    public int updateRemainingById(Long id, Integer remaining);

    /**
     * 查询奖项库存，加锁
     * @param id
     * @return
     */
    public Integer findRemaingForupdate(Long id);

    /**
     * 根据ids批量查询
     * @param ids
     * @return
     */
    List<OperatingActivityOptionsDto> findByIds(List<Long> ids);

    /**
     * 同步商家编码
     *
     * @param syncMsgData 数据
     * @return 影响行数
     */
    Integer syncMerchantCoding(SyncMsgData syncMsgData);

}
