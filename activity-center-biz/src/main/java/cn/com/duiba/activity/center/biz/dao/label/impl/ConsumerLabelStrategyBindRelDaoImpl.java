package cn.com.duiba.activity.center.biz.dao.label.impl;

import cn.com.duiba.activity.center.biz.dao.ActivityBaseDao;
import cn.com.duiba.activity.center.biz.dao.DatabaseSchema;
import cn.com.duiba.activity.center.biz.dao.label.ConsumerLabelStrategyBindRelDao;
import cn.com.duiba.activity.center.biz.entity.label.ConsumerLabelStrategyBindRelEntity;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

@Repository
public class ConsumerLabelStrategyBindRelDaoImpl extends ActivityBaseDao implements ConsumerLabelStrategyBindRelDao {

    @Override
    public int save(ConsumerLabelStrategyBindRelEntity consumerLabelStrategyBindRelEntity){
        return insert("save", consumerLabelStrategyBindRelEntity);
    }

    @Override
    public int deleteById(Long id){
        return delete("deleteById",id);
    }

    @Override
    public int deleteBatchByIds(List<Long> ids){
        return batchDelete("deleteBatchByIds",ids);
    }

    @Override
    public int updateById(ConsumerLabelStrategyBindRelEntity consumerLabelStrategyBindRelEntity){
        return update("updateById", consumerLabelStrategyBindRelEntity);
    }

    @Override
    public ConsumerLabelStrategyBindRelEntity getById(Long id){
        return selectOne("getById",id);
    }

    @Override
    public List<ConsumerLabelStrategyBindRelEntity> listByIds(List<Long> ids){
        return selectList("listByIds",ids);
    }

    @Override
    public List<Long> batchInsert(List<ConsumerLabelStrategyBindRelEntity> consumerLabelStrategyTaskRelEntities) {
        insert("batchInsert",consumerLabelStrategyTaskRelEntities);
        return consumerLabelStrategyTaskRelEntities.stream().map(ConsumerLabelStrategyBindRelEntity::getId).collect(Collectors.toList());
    }

    @Override
    public List<ConsumerLabelStrategyBindRelEntity> queryByStrategyId(Long strategyId) {
        return selectList("queryByStrategyId",strategyId);
    }

    @Override
    public List<ConsumerLabelStrategyBindRelEntity> queryByPackIds(List<String> packIds) {
        Map<String,Object> param = Maps.newHashMap();
        param.put("list",packIds);
        return selectList("queryByPackIds",param);
    }

    @Override
    public void deleteByStrategyId(Long strategyId) {
        delete("deleteByStrategyId",strategyId);
    }

    @Override
    public List<ConsumerLabelStrategyBindRelEntity> queryByStrategyIdInAndType(List<Long> strategyIds, Integer type) {
        if(CollectionUtils.isEmpty(strategyIds)){
            return Lists.newArrayList();
        }
        Map<String, Object> param = Maps.newHashMap();
        param.put("list",strategyIds);
        param.put("relType", Objects.isNull(type)?1:type);
        return selectList("queryByStrategyIds",param);
    }

    @Override
    protected DatabaseSchema chooseSchema() {
        return DatabaseSchema.ACT_COM_CONF;
    }
}
