package cn.com.duiba.activity.center.biz.service.seckill;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2016/12/5.
 */
public interface SeckillFieldService {
    /**
     * 获取id
     * @return
     */
    public Long getId();

    /**
     * 获取类型
     * @return
     */
    public Integer getType();

    /**
     * 获取标题
     * @return
     */
    String getTitle();

    /**
     * 获取状态
     * @return
     */
    Integer getStatus();

    /**
     * 获取积分
     * @param appId
     * @return
     */
    Long getCredits(Long appId);

    /**
     * 获取规则
     * @return
     */
    String getRule();

    /**
     * 获取详情图
     * @return
     */
    String getMultiImage();

    /**
     * 获取缩略图
     * @return
     */
    String getSmallImage();

    /**
     * 开始日期
     * @return
     */
    Date getDateStart();

    /**
     * 结束日期
     * @return
     */
    Date getDateEnd();

    /**
     * 开始时间
     * @return
     */
    Date getTimeStart();

    /**
     * 结束时间
     * @return
     */
    Date getTimeEnd();
    /**
     * 是否开启黑名单
     * @return
     */
    boolean isOpenBlack();

    /**
     * 是否开启定向
     * @return
     */
    boolean isOpenDirect();
    /**
     * 验证时间
     */
    boolean checkDateTime();

    /**
     *
     * @return
     */
    Long getParentActivityId();

    /**
     * banner图片
     * @return
     */
    String getBannerImage();

    /**
     * WhiteImage
     * @return
     */
    String getWhiteImage();
    /**
     *
     * @return
     */
    String getLogo();

    /**
     *
     * @return
     */
    Long getSalePrice();

    /**
     *
      * @param startTime
     * @return
     */
    Date getStartTime(Date startTime);

}
