package cn.com.duiba.activity.center.biz.dao.superSurprise.impl;

import cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseRankListDto;
import cn.com.duiba.activity.center.biz.dao.ActivityBaseDao;
import cn.com.duiba.activity.center.biz.dao.DatabaseSchema;
import cn.com.duiba.activity.center.biz.dao.superSurprise.SuperSurpriseRankListDao;
import cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseRankListEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

@Repository
public class SuperSurpriseRankListDaoImpl extends ActivityBaseDao implements SuperSurpriseRankListDao{

    @Override
    public Long save(SuperSurpriseRankListEntity superSurpriseRankListEntity){
        insert("save",superSurpriseRankListEntity);
        return superSurpriseRankListEntity.getId();
    }

    @Override
    public int deleteById(Long id){
        return delete("deleteById",id);
    }

    @Override
    public int deleteBatchByIds(List<Long> ids){
        return delete("deleteBatchByIds",ids);
    }

    @Override
    public int updateById(SuperSurpriseRankListEntity superSurpriseRankListEntity){
        return update("updateById",superSurpriseRankListEntity);
    }

    @Override
    public int increaseScoreById(SuperSurpriseRankListEntity superSurpriseRankListEntity) {
        return update("increaseScoreById",superSurpriseRankListEntity);
    }

    @Override
    public SuperSurpriseRankListEntity getById(Long id){
        return selectOne("getById",id);
    }

    @Override
    public List<SuperSurpriseRankListEntity> listByIds(List<Long> ids){
        return selectList("listByIds",ids);
    }

    @Override
    public SuperSurpriseRankListEntity getByAcidCid(Long activityId,Long consumerId){
        Map<String,Object> param = new HashMap<>(4);
        param.put("activityId",activityId);
        param.put("consumerId",consumerId);
        return selectOne("getByAcidCid",param);
    }

    @Override
    public List<SuperSurpriseRankListDto> getRank(Long activityId) {
        return selectList("getRank",activityId);
    }

    @Override
    public List<SuperSurpriseRankListDto> getRankTwoThousand(Long activityId) {
        return selectList("getRankTwoThousand",activityId);
    }

    @Override
    public Integer getSelfRank(Long activityId, Long consumerId) {
        List<SuperSurpriseRankListDto> rank = getRankTwoThousand(activityId);
        //获取排行榜最后一名进行比较，如果比最后一名的分数高，才返回用户真实排名，否则返回 999
        if (CollectionUtils.isNotEmpty(rank)) {
            SuperSurpriseRankListDto lastRank = rank.get(rank.size() - 1);
            SuperSurpriseRankListEntity selfRankRecord = getSelfRankRecord(activityId, consumerId);
            if (selfRankRecord.getScore() < lastRank.getScore()) {
                return 9999;
            }
        }
        //比最后一名高的话，才算真实排名
        Map<String,Object> param = new HashMap<>();
        param.put("activityId",activityId);
        param.put("consumerId",consumerId);
        return selectOne("getSelfRank",param);
    }

    @Override
    public SuperSurpriseRankListEntity getSelfRankRecord(Long activityId, Long consumerId) {
        Map<String,Object> param = new HashMap<>();
        param.put("activityId",activityId);
        param.put("consumerId",consumerId);
        return selectOne("getSelfRankRecord",param);
    }


    @Override
    protected DatabaseSchema chooseSchema() {
        return DatabaseSchema.ACT_COM_CONF;
    }
}
