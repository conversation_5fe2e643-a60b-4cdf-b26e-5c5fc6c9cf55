package cn.com.duiba.activity.center.biz.service.chaos.impl;

import cn.com.duiba.activity.center.api.dto.chaos.ActPreChangeStockDto;
import cn.com.duiba.activity.center.biz.dao.chaos.ActPreChangeStockDao;
import cn.com.duiba.activity.center.biz.entity.chaos.ActPreChangeStockEntity;
import cn.com.duiba.activity.center.biz.service.chaos.ActPreChangeStockService;
import cn.com.duiba.wolf.utils.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by yansen on 16/6/22.
 */
@Service
public class ActPreChangeStockServiceImpl implements ActPreChangeStockService {
    @Resource
    private ActPreChangeStockDao actPreChangeStockDao;

    @Override
    public void insert(ActPreChangeStockDto actPreChangeStockDto) {
        ActPreChangeStockEntity entity=new ActPreChangeStockEntity(true);
        BeanUtils.copy(actPreChangeStockDto,entity);
        actPreChangeStockDao.insert(entity);
        actPreChangeStockDto.setId(entity.getId());
    }
}
