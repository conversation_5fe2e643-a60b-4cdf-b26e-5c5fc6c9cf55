package cn.com.duiba.activity.center.biz.remoteservice.impl.managermarket;

import cn.com.duiba.activity.center.api.dto.managermarket.ManagerMarketRankDto;
import cn.com.duiba.activity.center.api.dto.managermarket.ManagerMarketStatisticsDataDto;
import cn.com.duiba.activity.center.api.remoteservice.managermarket.RemoteManagerMarketDataService;
import cn.com.duiba.activity.center.biz.service.managermarket.ManagerMarketDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @date 2020-08-19
 * <AUTHOR>
 * @email <EMAIL>
 * @desc 客户经理营销中心数据相关的接口
 * */
@RestController
public class RemoteManagerMarketDataServiceImpl implements RemoteManagerMarketDataService {

    @Autowired
    private ManagerMarketDataService managerMarketDataService;

    private static  final Logger LOGGER = LoggerFactory.getLogger(RemoteManagerMarketDataServiceImpl.class);

    /**
     * 查询营销数据明细列表支持分页和不分页
     *
     * @param configId  配置主键id
     * @param startDate 开始时间 yyyy-MM-dd
     * @param endDate   结束时间 yyyy-MM-dd
     * @param taskId    任务id
     * @param staffNum  员工编号
     * @param organNum  机构编号
     * @param pageSize  页面大小 可空，空则不分页
     * @param pageNum   页码 可空，空则不分页
     */
    @Override
    public List<ManagerMarketStatisticsDataDto> marketDataDetailList(Long configId, String startDate, String endDate
            , String taskId, String staffNum, String organNum, Integer pageSize, Integer pageNum) {
        return managerMarketDataService.marketDataDetailList(configId,startDate,endDate
                ,taskId,staffNum,organNum,pageSize,pageNum);
    }

    /**
     * 查询营销数据明细列表总数
     *
     * @param configId  配置主键id
     * @param startDate 开始时间 yyyy-MM-dd
     * @param endDate   结束时间 yyyy-MM-dd
     * @param taskId    任务id
     * @param staffNum  员工编号
     * @param organNum  机构编号
     */
    @Override
    public long marketDataDetailListCount(Long configId, String startDate, String endDate
            , String taskId, String staffNum, String organNum) {
        return managerMarketDataService.marketDataDetailListCount(configId,startDate,endDate
                ,taskId,staffNum,organNum);
    }

    /**
     * 任务推广情况列表支持分页和不分页
     *
     * @param configId  配置主键id
     * @param startDate 开始时间 yyyy-MM-dd
     * @param endDate   结束时间 yyyy-MM-dd
     * @param pageSize  页面大小 可空，空则不分页
     * @param pageNum   页码 可空，空则不分页
     */
    @Override
    public List<ManagerMarketStatisticsDataDto> taskPromotionDataList(Long configId, String startDate, String endDate
            , Integer pageSize, Integer pageNum) {
        return managerMarketDataService.taskPromotionDataList(configId,startDate,endDate,pageSize,pageNum);
    }

    /**
     * 任务推广情况列表总数
     *
     * @param configId  配置主键id
     * @param startDate 开始时间 yyyy-MM-dd
     * @param endDate   结束时间 yyyy-MM-dd
     */
    @Override
    public long taskPromotionDataListCount(Long configId, String startDate, String endDate) {
        return managerMarketDataService.taskPromotionDataListCount(configId,startDate,endDate);
    }

    /**
     * 单营销任务转发排名列表支持分页和不分页
     *
     * @param taskId   任务id
     * @param rankBy   排序方式 1机构，2员工
     * @param configId 配置主键id
     * @param pageSize 页面大小 可空，空则不分页
     * @param pageNum  页码 可空，空则不分页
     */
    @Override
    public List<ManagerMarketStatisticsDataDto> taskForwardRankList(String taskId, Integer rankBy
            , Long configId, Integer pageSize, Integer pageNum) {
        return managerMarketDataService.taskForwardRankList(taskId,rankBy,configId,pageSize,pageNum);
    }

    /**
     * 单营销任务转发排名列表总数
     *
     * @param taskId   任务id
     * @param rankBy   排序方式 1机构，2员工
     * @param configId 配置主键id
     */
    @Override
    public long taskForwardRankListCount(String taskId, Integer rankBy, Long configId) {
        return managerMarketDataService.taskForwardRankListCount(taskId,rankBy,configId);
    }

    /**
     * 员工/机构转发排名 列表支持分页和不分页
     *
     * @param timeNum  第几周或第几月
     * @param sortNum  排名时间，1按月，2按周
     * @param rankBy   排序方式 1机构，2员工
     * @param pageSize 页面大小 可空，空则不分页
     * @param pageNum  页码 可空，空则不分页
     */
    @Override
    public List<ManagerMarketRankDto> staffForwardRankList(Long configId,Integer year,Integer timeNum, Integer sortNum
            , Integer rankBy, Integer pageSize, Integer pageNum) {
        return managerMarketDataService.staffForwardRankList(configId,year,timeNum,sortNum,rankBy,pageSize,pageNum);
    }

    /**
     * 员工/机构转发排名 总数
     *
     * @param timeNum 第几周或第几月
     * @param sortNum 排名时间，1按月，2按周
     * @param rankBy  排序方式 1机构，2员工
     */
    @Override
    public long staffForwardRankListCount(Long configId,Integer year,Integer timeNum, Integer sortNum, Integer rankBy) {
        return managerMarketDataService.staffForwardRankListCount(configId,year,timeNum,sortNum,rankBy);
    }

    /**
     * 插入统计数据
     *
     * @param dto 计算好的统计数据
     */
    @Override
    public int insertStatisticsData(ManagerMarketStatisticsDataDto dto) {
        return managerMarketDataService.insertStatisticsData(dto);
    }

    /****
     * 提供给定时任务调用的统计方法
     */
    @Override
    public void doManagerMarketStatistic() {
        managerMarketDataService.doManagerMarketStatistic();
    }
}
