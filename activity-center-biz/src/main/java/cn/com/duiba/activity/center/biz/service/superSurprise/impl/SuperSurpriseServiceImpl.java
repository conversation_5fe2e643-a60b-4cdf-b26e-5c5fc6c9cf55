package cn.com.duiba.activity.center.biz.service.superSurprise.impl;

import cn.com.duiba.activity.center.api.dto.superSurprise.RankListProvidePrizeRecordDto;
import cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseConfigDto;
import cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseJoinRecordDto;
import cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseJoinRecordQuery;
import cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseRankListDto;
import cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseRankListOptionDto;
import cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseRebirthQueryDto;
import cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseRebirthRecordDto;
import cn.com.duiba.activity.center.biz.dao.superSurprise.RankListProvidePrizeRecordDao;
import cn.com.duiba.activity.center.biz.dao.superSurprise.SuperSurpriseConfigDao;
import cn.com.duiba.activity.center.biz.dao.superSurprise.SuperSurpriseJoinRecordDao;
import cn.com.duiba.activity.center.biz.dao.superSurprise.SuperSurpriseRankListDao;
import cn.com.duiba.activity.center.biz.dao.superSurprise.SuperSurpriseRankListOptionDao;
import cn.com.duiba.activity.center.biz.dao.superSurprise.SuperSurpriseRebirthRecordDao;
import cn.com.duiba.activity.center.biz.entity.superSurprise.RankListProvidePrizeRecordEntity;
import cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseConfigEntity;
import cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseJoinRecordEntity;
import cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseRankListEntity;
import cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseRankListOptionEntity;
import cn.com.duiba.activity.center.biz.entity.superSurprise.SuperSurpriseRebirthRecordEntity;
import cn.com.duiba.activity.center.biz.service.superSurprise.SuperSurpriseService;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.wolf.utils.BeanUtils;
import com.google.common.base.Optional;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Created by zhangyongjie on 2022/3/23 2:33 下午
 */
@Service
public class SuperSurpriseServiceImpl implements SuperSurpriseService {

    @Resource
    private SuperSurpriseConfigDao superSurpriseConfigDao;

    @Resource
    private SuperSurpriseRankListOptionDao superSurpriseRankListOptionDao;

    @Resource
    private SuperSurpriseJoinRecordDao superSurpriseJoinRecordDao;

    @Resource
    private SuperSurpriseRebirthRecordDao superSurpriseRebirthRecordDao;

    @Resource
    private SuperSurpriseRankListDao superSurpriseRankListDao;

    @Resource
    private RankListProvidePrizeRecordDao rankListProvidePrizeRecordDao;

    // 批量缓存用户信息
    private Cache<List<Long>, List<ConsumerDto>> consumerListCache =
            CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES).maximumSize(1000).build();


    @Override
    public Long saveConfig(SuperSurpriseConfigDto superSurpriseConfigDto) {
        return superSurpriseConfigDao.save(BeanUtils.copy(superSurpriseConfigDto, SuperSurpriseConfigEntity.class));
    }

    @Override
    public SuperSurpriseConfigDto getById(Long id) {
        return BeanUtils.copy(superSurpriseConfigDao.getById(id),SuperSurpriseConfigDto.class);
    }

    @Override
    public int updateConfig(SuperSurpriseConfigDto superSurpriseConfigDto) {
        return superSurpriseConfigDao.updateById(BeanUtils.copy(superSurpriseConfigDto,SuperSurpriseConfigEntity.class));
    }

    @Override
    public void batchInsertRankOption(List<SuperSurpriseRankListOptionDto> list) {
        superSurpriseRankListOptionDao.batchInsert(BeanUtils.copyList(list, SuperSurpriseRankListOptionEntity.class));
    }

    @Override
    public SuperSurpriseRankListOptionDto findByActivityIdAndRankRange(Long activityId, Integer rank) {
        return BeanUtils.copy(superSurpriseRankListOptionDao.findByActivityIdAndRankRange(activityId, rank),SuperSurpriseRankListOptionDto.class);
    }

    @Override
    public int deleteRankOptionByActivityId(Long activityId) {
        return superSurpriseRankListOptionDao.deleteByActivityId(activityId);
    }

    @Override
    public List<SuperSurpriseRankListOptionDto> findByOperatingActivityId(Long opId) {
        return BeanUtils.copyList(superSurpriseRankListOptionDao.queryByActivityId(opId),SuperSurpriseRankListOptionDto.class);
    }

    @Override
    public Long saveJoinRecord(SuperSurpriseJoinRecordDto superSurpriseJoinRecordDto) {
        return superSurpriseJoinRecordDao.save(BeanUtils.copy(superSurpriseJoinRecordDto, SuperSurpriseJoinRecordEntity.class));
    }

    @Override
    public Integer countUserJoinTimes(SuperSurpriseJoinRecordQuery query) {
        return superSurpriseJoinRecordDao.countByCondition(query);
    }

    @Override
    public int updateCreditsStatusById(Long id, Integer consumeCreditsStatus) {
        return superSurpriseJoinRecordDao.updateCreditsStatusById(id,consumeCreditsStatus);
    }

    @Override
    public int updateExchangeStatusById(Long id, Integer exchangeStatus) {
        return superSurpriseJoinRecordDao.updateExchangeStatusById(id,exchangeStatus);
    }

    @Override
    public int updateExchangeStatus(Long id, Integer preExchangeStatus, Integer newExchangeStatus) {
        return superSurpriseJoinRecordDao.updateExchangeStatus(id, preExchangeStatus, newExchangeStatus);
    }

    @Override
    public SuperSurpriseJoinRecordDto getJoinRecordById(Long id) {
        return BeanUtils.copy(superSurpriseJoinRecordDao.getById(id),SuperSurpriseJoinRecordDto.class);
    }

    @Override
    public int updateJoinRecordById(SuperSurpriseJoinRecordDto superSurpriseJoinRecordDto) {
        return superSurpriseJoinRecordDao.updateById(BeanUtils.copy(superSurpriseJoinRecordDto,SuperSurpriseJoinRecordEntity.class));
    }

    @Override
    public Long saveRebirthRecord(SuperSurpriseRebirthRecordDto superSurpriseRebirthRecordDto) {
        return superSurpriseRebirthRecordDao.save(BeanUtils.copy(superSurpriseRebirthRecordDto, SuperSurpriseRebirthRecordEntity.class));
    }

    @Override
    public SuperSurpriseRebirthRecordDto findRebirthRecordById(Long id) {
        return BeanUtils.copy(superSurpriseRebirthRecordDao.getById(id),SuperSurpriseRebirthRecordDto.class);
    }

    @Override
    public int updateRebirthRecordById(SuperSurpriseRebirthRecordDto superSurpriseRebirthRecordDto) {
        return superSurpriseRebirthRecordDao.updateById(BeanUtils.copy(superSurpriseRebirthRecordDto,SuperSurpriseRebirthRecordEntity.class));
    }

    @Override
    public int updateRebirthStatus(Long id, Integer status) {
        return superSurpriseRebirthRecordDao.updateStatus(id,status);
    }

    @Override
    public Integer countRebirthRecordByCondition(SuperSurpriseRebirthQueryDto superSurpriseRebirthQueryDto) {
        return superSurpriseRebirthRecordDao.countByCondition(superSurpriseRebirthQueryDto);
    }

    @Override
    public Long saveRankList(SuperSurpriseRankListDto superSurpriseRankListDto) {
        return superSurpriseRankListDao.save(BeanUtils.copy(superSurpriseRankListDto, SuperSurpriseRankListEntity.class));
    }

    @Override
    public List<SuperSurpriseRankListDto> getRankInfo(Long activityId) {
        return superSurpriseRankListDao.getRank(activityId);
    }

    @Override
    public Integer getSelfRank(Long activityId, Long consumerId) {
        return superSurpriseRankListDao.getSelfRank(activityId,consumerId);
    }

    @Override
    public SuperSurpriseRankListDto getSelfRankRecord(Long activityId, Long consumerId) {
        return BeanUtils.copy(superSurpriseRankListDao.getSelfRankRecord(activityId, consumerId),SuperSurpriseRankListDto.class);
    }

    @Override
    public int updateRank(SuperSurpriseRankListDto superSurpriseRankListDto) {
        return superSurpriseRankListDao.updateById(BeanUtils.copy(superSurpriseRankListDto,SuperSurpriseRankListEntity.class));
    }

    @Override
    public int increaseRankScore(SuperSurpriseRankListDto superSurpriseRankListDto) {
        return superSurpriseRankListDao.increaseScoreById(BeanUtils.copy(superSurpriseRankListDto,SuperSurpriseRankListEntity.class));
    }

    @Override
    public Long saveRankProvidePrizeRecord(RankListProvidePrizeRecordDto rankListProvidePrizeRecordDto) throws BizException {
        try{
            return rankListProvidePrizeRecordDao.save(BeanUtils.copy(rankListProvidePrizeRecordDto, RankListProvidePrizeRecordEntity.class));
        }catch (Exception e){
            throw new BizException("创建排行榜发奖记录失败");
        }
    }

    @Override
    public RankListProvidePrizeRecordDto getRankProvidePrizeRecordByActIdAndTypeAndCid(Long activityId, String type, Long consumerId) {
        return BeanUtils.copy(rankListProvidePrizeRecordDao.getByAcidTypeCid(activityId, type, consumerId),RankListProvidePrizeRecordDto.class);
    }

    @Override
    public RankListProvidePrizeRecordDto getByRankListProvidePrizeRecordId(Long id) {
        return BeanUtils.copy(rankListProvidePrizeRecordDao.getById(id),RankListProvidePrizeRecordDto.class);
    }

    @Override
    public RankListProvidePrizeRecordDto getRankListProvidePrizeRecordByOrderNum(String orderNum) {
        return BeanUtils.copy(rankListProvidePrizeRecordDao.findByOrderNum(orderNum),RankListProvidePrizeRecordDto.class);
    }

    @Override
    public SuperSurpriseJoinRecordDto findJoinRecordByOrderNum(String orderNum) {
        return BeanUtils.copy(superSurpriseJoinRecordDao.findByOrderNum(orderNum),SuperSurpriseJoinRecordDto.class);
    }

    @Override
    public int updateRankListProvidePrizeRecordById(RankListProvidePrizeRecordDto rankListProvidePrizeRecordDto) {
        return rankListProvidePrizeRecordDao.updateById(BeanUtils.copy(rankListProvidePrizeRecordDto,RankListProvidePrizeRecordEntity.class));
    }

    @Override
    public int updateRankListProvidePrizeStatus(Long id, Integer preStatus, Integer newStatus) {
        return rankListProvidePrizeRecordDao.updateStatus(id, preStatus, newStatus);
    }
}