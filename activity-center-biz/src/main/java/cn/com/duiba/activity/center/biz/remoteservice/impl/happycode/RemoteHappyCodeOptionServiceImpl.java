package cn.com.duiba.activity.center.biz.remoteservice.impl.happycode;

import cn.com.duiba.activity.center.api.dto.happy_code.HappyCodeOptionDto;
import cn.com.duiba.activity.center.api.remoteservice.happycode.RemoteHappyCodeOptionService;
import cn.com.duiba.activity.center.biz.service.happycode.HappyCodeOptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created by hww on 2017/12/6
 */
@RestController
public class RemoteHappyCodeOptionServiceImpl implements RemoteHappyCodeOptionService {

    @Autowired
    private HappyCodeOptionService happyCodeOptionService;

    @Override
    public Long insert(HappyCodeOptionDto optionDto) {
        return happyCodeOptionService.insert(optionDto);
    }

    @Override
    public Boolean batchInsert(List<HappyCodeOptionDto> optionDto) {
        return happyCodeOptionService.batchInsert(optionDto);
    }

    @Override
    public HappyCodeOptionDto findOneByPhaseId(Long phaseId) {
        return happyCodeOptionService.findOneByPhaseId(phaseId);
    }

    @Override
    public List<HappyCodeOptionDto> findByPhaseId(Long phaseId) {
        return happyCodeOptionService.findByPhaseId(phaseId);
    }

    @Override
    public List<HappyCodeOptionDto> findByPhaseIds(List<Long> phaseIds) {
        return happyCodeOptionService.findByPhaseIds(phaseIds);
    }

    @Override
    public HappyCodeOptionDto findById(Long id) {
        return happyCodeOptionService.findById(id);
    }

    @Override
    public Integer updateOption(HappyCodeOptionDto option) {
        return happyCodeOptionService.updateOption(option);
    }

    @Override
    public Integer deleteOption(Long id, Long phaseId) {
        return happyCodeOptionService.deleteOption(id, phaseId);
    }
}
